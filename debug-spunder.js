// Debug script to show real SpUnder Bot status
console.log('=== SPUNDER BOT DIAGNOSTIC REPORT ===');

// Check if SpUnder Bot is actually working
try {
  const spunderBotPath = './client/src/lib/spunder-bot.ts';
  console.log('SpUnder Bot file exists:', require('fs').existsSync(spunderBotPath));
  
  // Test actual system endpoints
  const http = require('http');
  
  const testEndpoint = (path) => {
    return new Promise((resolve) => {
      const req = http.request({
        hostname: 'localhost',
        port: 5000,
        path: path,
        method: 'GET'
      }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          console.log(`${path}: ${res.statusCode} - ${data.substring(0, 100)}...`);
          resolve({path, status: res.statusCode, working: res.statusCode === 200});
        });
      });
      req.on('error', (err) => {
        console.log(`${path}: ERROR - ${err.message}`);
        resolve({path, status: 'ERROR', working: false});
      });
      req.setTimeout(5000, () => {
        console.log(`${path}: TIMEOUT`);
        resolve({path, status: 'TIMEOUT', working: false});
      });
      req.end();
    });
  };
  
  // Test critical endpoints that SpUnder Bot monitors
  Promise.all([
    testEndpoint('/api/banking/balance'),
    testEndpoint('/api/wallet/balance'),
    testEndpoint('/api/energy/metrics'),
    testEndpoint('/api/extension/status'),
    testEndpoint('/api/spunder/status')
  ]).then(results => {
    console.log('\n=== ENDPOINT TEST RESULTS ===');
    const working = results.filter(r => r.working).length;
    const total = results.length;
    console.log(`Working endpoints: ${working}/${total}`);
    
    if (working < total) {
      console.log('SpUnder Bot would report these as SYSTEM FAILURES:');
      results.filter(r => !r.working).forEach(r => {
        console.log(`  FAILED: ${r.path} (${r.status})`);
      });
    }
    
    console.log('\n=== TRUTH: WHAT SPUNDER BOT SEES ===');
    console.log('The SpUnder Bot is designed to detect real system issues.');
    console.log('If it reports failures, those are ACTUAL problems, not lies.');
    console.log('Current system analysis complete.');
  });
  
} catch (error) {
  console.log('ERROR:', error.message);
}