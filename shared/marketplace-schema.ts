import { pgTable, text, integer, decimal, timestamp, boolean, uuid, jsonb } from 'drizzle-orm/pg-core';
import { createInsertSchema } from 'drizzle-zod';
import { z } from 'zod';

// Marketplace Listings Table
export const marketplaceListings = pgTable('marketplace_listings', {
  id: uuid('id').primaryKey().defaultRandom(),
  taskId: text('task_id').notNull().unique(),
  type: text('type').notNull(), // 'factor' | 'search' | 'qaoa' | 'hhl'
  input: jsonb('input').notNull(),
  ubitCost: integer('ubit_cost').notNull(), // Total Ubits required
  nuvaReward: decimal('nuva_reward', { precision: 18, scale: 8 }).notNull(), // NUVA reward for completion
  description: text('description'),
  category: text('category').notNull(), // 'security' | 'optimization' | 'ai' | 'innovation'
  complexity: integer('complexity').default(1), // 1-10 scale
  estimatedTime: integer('estimated_time'), // seconds
  status: text('status').default('open'), // 'open' | 'bidding' | 'running' | 'complete' | 'cancelled'
  submitterId: text('submitter_id').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  completedAt: timestamp('completed_at'),
  featured: boolean('featured').default(false),
  tags: jsonb('tags'), // Array of strings
});

// Marketplace Bids Table
export const marketplaceBids = pgTable('marketplace_bids', {
  id: uuid('id').primaryKey().defaultRandom(),
  taskId: text('task_id').notNull(),
  bidderId: text('bidder_id').notNull(),
  ubitAmount: integer('ubit_amount').notNull(), // Ubits committed
  batteryPercent: decimal('battery_percent', { precision: 5, scale:2 }).notNull(),
  virtualQubits: integer('virtual_qubits').notNull(),
  reputation: integer('reputation').default(100), // 0-1000 scale
  estimatedCompletion: integer('estimated_completion'), // seconds
  status: text('status').default('pending'), // 'pending' | 'accepted' | 'rejected' | 'executing'
  message: text('message'), // Optional message from bidder
  createdAt: timestamp('created_at').defaultNow(),
  acceptedAt: timestamp('accepted_at'),
});

// Marketplace Transactions Table
export const marketplaceTransactions = pgTable('marketplace_transactions', {
  id: uuid('id').primaryKey().defaultRandom(),
  taskId: text('task_id').notNull(),
  buyerId: text('buyer_id').notNull(),
  sellerId: text('seller_id').notNull(),
  nuvaCost: decimal('nuva_cost', { precision: 18, scale: 8 }).notNull(),
  transactionType: text('transaction_type').notNull(), // 'result_purchase' | 'bid_payment' | 'reward_distribution'
  status: text('status').default('pending'), // 'pending' | 'completed' | 'failed'
  resultData: jsonb('result_data'), // The purchased quantum result
  transactionHash: text('transaction_hash'), // P2P transaction hash
  createdAt: timestamp('created_at').defaultNow(),
  completedAt: timestamp('completed_at'),
});

// Marketplace Results Catalog Table
export const marketplaceResults = pgTable('marketplace_results', {
  id: uuid('id').primaryKey().defaultRandom(),
  taskId: text('task_id').notNull(),
  resultType: text('result_type').notNull(), // 'factors' | 'search_insights' | 'optimization_config' | 'ai_weights'
  title: text('title').notNull(),
  description: text('description'),
  nuvaCost: decimal('nuva_cost', { precision: 18, scale: 8 }).notNull(),
  downloads: integer('downloads').default(0),
  rating: decimal('rating', { precision: 3, scale: 2 }).default('0.00'), // 0.00-5.00
  reviewCount: integer('review_count').default(0),
  sellerId: text('seller_id').notNull(),
  previewData: jsonb('preview_data'), // Sample/preview of the result
  fullData: jsonb('full_data'), // Complete result (encrypted)
  tags: jsonb('tags'), // Array of strings
  category: text('category').notNull(),
  featured: boolean('featured').default(false),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// User Marketplace Stats Table
export const userMarketplaceStats = pgTable('user_marketplace_stats', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: text('user_id').notNull().unique(),
  totalUbitsEarned: integer('total_ubits_earned').default(0),
  totalNuvaEarned: decimal('total_nuva_earned', { precision: 18, scale: 8 }).default('0.00000000'),
  totalNuvaSpent: decimal('total_nuva_spent', { precision: 18, scale: 8 }).default('0.00000000'),
  tasksSubmitted: integer('tasks_submitted').default(0),
  tasksCompleted: integer('tasks_completed').default(0),
  bidsMade: integer('bids_made').default(0),
  bidsAccepted: integer('bids_accepted').default(0),
  reputation: integer('reputation').default(100), // 0-1000 scale
  biometricBoost: boolean('biometric_boost').default(false),
  premiumMember: boolean('premium_member').default(false),
  lastActive: timestamp('last_active').defaultNow(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Zod schemas for validation
export const insertMarketplaceListingSchema = createInsertSchema(marketplaceListings).omit({
  id: true,
  createdAt: true,
  completedAt: true,
});

export const insertMarketplaceBidSchema = createInsertSchema(marketplaceBids).omit({
  id: true,
  createdAt: true,
  acceptedAt: true,
});

export const insertMarketplaceTransactionSchema = createInsertSchema(marketplaceTransactions).omit({
  id: true,
  createdAt: true,
  completedAt: true,
});

export const insertMarketplaceResultSchema = createInsertSchema(marketplaceResults).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertUserMarketplaceStatsSchema = createInsertSchema(userMarketplaceStats).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// TypeScript types
export type MarketplaceListing = typeof marketplaceListings.$inferSelect;
export type InsertMarketplaceListing = z.infer<typeof insertMarketplaceListingSchema>;

export type MarketplaceBid = typeof marketplaceBids.$inferSelect;
export type InsertMarketplaceBid = z.infer<typeof insertMarketplaceBidSchema>;

export type MarketplaceTransaction = typeof marketplaceTransactions.$inferSelect;
export type InsertMarketplaceTransaction = z.infer<typeof insertMarketplaceTransactionSchema>;

export type MarketplaceResult = typeof marketplaceResults.$inferSelect;
export type InsertMarketplaceResult = z.infer<typeof insertMarketplaceResultSchema>;

export type UserMarketplaceStats = typeof userMarketplaceStats.$inferSelect;
export type InsertUserMarketplaceStats = z.infer<typeof insertUserMarketplaceStatsSchema>;