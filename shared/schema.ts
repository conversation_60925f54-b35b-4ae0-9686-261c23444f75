import {
  pgTable,
  text,
  varchar,
  timestamp,
  jsonb,
  index,
  serial,
  boolean,
  integer,
  real,
  uuid,
} from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { sql } from "drizzle-orm";

// Session storage table - mandatory for Replit Auth
export const sessions = pgTable(
  "sessions",
  {
    sid: varchar("sid").primaryKey(),
    sess: jsonb("sess").notNull(),
    expire: timestamp("expire").notNull(),
  },
  (table) => [index("IDX_session_expire").on(table.expire)],
);

// User storage table - mandatory for Replit Auth
export const users = pgTable("users", {
  id: varchar("id").primaryKey().notNull(),
  email: varchar("email").unique(),
  firstName: varchar("first_name"),
  lastName: varchar("last_name"),
  profileImageUrl: varchar("profile_image_url"),
  companyName: varchar("company_name"),
  accountType: varchar("account_type").default("user"),
  dataMonetizationEnabled: boolean("data_monetization_enabled").default(false),
  // Banking system DID and identity
  did: varchar("did").unique(), // Decentralized Identifier
  walletAddress: varchar("wallet_address").unique(),
  kycStatus: varchar("kyc_status").default("pending"), // pending, verified, rejected
  accountStatus: varchar("account_status").default("active"), // active, suspended, closed
  // Energy tracking permissions
  energyTrackingEnabled: boolean("energy_tracking_enabled").default(true),
  biometricDataSharing: boolean("biometric_data_sharing").default(false),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Enhanced energy balances - persistent storage for all tokens
export const energyBalancesDetailed = pgTable("energy_balances_detailed", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().unique().references(() => users.id),
  // Primary energy tokens
  umatterBalance: real("umatter_balance").default(0),
  truBalance: real("tru_balance").default(0),
  nuvaBalance: real("nuva_balance").default(0),
  inurtiaBalance: real("inurtia_balance").default(0),
  ubitsBalance: real("ubits_balance").default(0),
  // Derived balances
  totalEnergyGenerated: real("total_energy_generated").default(0),
  dailyEnergyGenerated: real("daily_energy_generated").default(0),
  weeklyEnergyGenerated: real("weekly_energy_generated").default(0),
  monthlyEnergyGenerated: real("monthly_energy_generated").default(0),
  // Last update tracking
  lastEnergyUpdate: timestamp("last_energy_update").defaultNow(),
  lastDeviceSync: timestamp("last_device_sync"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Energy transaction history
export const energyTransactions = pgTable("energy_transactions", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  transactionType: varchar("transaction_type").notNull(), // generation, transfer, conversion, marketplace
  tokenType: varchar("token_type").notNull(), // umatter, tru, nuva, inurtia, ubits
  amount: real("amount").notNull(),
  balanceBefore: real("balance_before").notNull(),
  balanceAfter: real("balance_after").notNull(),
  source: varchar("source"), // device_sync, extension, marketplace, manual
  metadata: jsonb("metadata"), // Additional transaction data
  transactionHash: varchar("transaction_hash").unique(),
  createdAt: timestamp("created_at").defaultNow(),
});

// Device registration for energy generation
export const userDevices = pgTable("user_devices", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  deviceId: varchar("device_id").notNull(),
  deviceName: varchar("device_name"),
  deviceType: varchar("device_type"), // laptop, phone, tablet, iot
  platform: varchar("platform"), // MacIntel, Win32, Linux, iOS, Android
  isActive: boolean("is_active").default(true),
  lastSeen: timestamp("last_seen").defaultNow(),
  energyContribution: real("energy_contribution").default(0),
  registeredAt: timestamp("registered_at").defaultNow(),
});

// User Data Subscription Plans
export const userDataPlans = pgTable("user_data_plans", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  planName: varchar("plan_name").notNull(),
  description: text("description"),
  weeklyPrice: real("weekly_price").default(0),
  monthlyPrice: real("monthly_price").default(0),
  yearlyPrice: real("yearly_price").default(0),
  dataCategories: text("data_categories").array().default([]),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Data Subscriptions
export const dataSubscriptions = pgTable("data_subscriptions", {
  id: uuid("id").primaryKey().defaultRandom(),
  subscriberId: varchar("subscriber_id").notNull().references(() => users.id),
  dataOwnerId: varchar("data_owner_id").notNull().references(() => users.id),
  planId: uuid("plan_id").notNull().references(() => userDataPlans.id),
  status: varchar("status").default("active"),
  subscriptionType: varchar("subscription_type").notNull(),
  amount: real("amount").notNull(),
  startDate: timestamp("start_date").defaultNow(),
  nextBillingDate: timestamp("next_billing_date"),
  createdAt: timestamp("created_at").defaultNow(),
});

// User Earnings
export const userEarnings = pgTable("user_earnings", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  earningType: varchar("earning_type").notNull(),
  amount: real("amount").notNull(),
  source: varchar("source").notNull(),
  metadata: jsonb("metadata"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Data Access Logs
export const dataAccessLogs = pgTable("data_access_logs", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  accessorId: varchar("accessor_id").notNull().references(() => users.id),
  dataType: varchar("data_type").notNull(),
  accessMethod: varchar("access_method").notNull(),
  metadata: jsonb("metadata"),
  accessedAt: timestamp("accessed_at").defaultNow(),
});

// Ad Campaigns
export const adCampaigns = pgTable("ad_campaigns", {
  id: uuid("id").primaryKey().defaultRandom(),
  advertiserId: varchar("advertiser_id").notNull().references(() => users.id),
  name: varchar("name").notNull(),
  description: text("description"),
  budget: real("budget").notNull(),
  targetAudience: jsonb("target_audience"),
  startDate: timestamp("start_date").notNull(),
  endDate: timestamp("end_date").notNull(),
  status: varchar("status").default("draft"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Ad Views
export const adViews = pgTable("ad_views", {
  id: uuid("id").primaryKey().defaultRandom(),
  campaignId: uuid("campaign_id").notNull().references(() => adCampaigns.id),
  userId: varchar("user_id").notNull().references(() => users.id),
  adContent: jsonb("ad_content"),
  viewDuration: integer("view_duration"),
  clicked: boolean("clicked").default(false),
  earnings: real("earnings").default(0),
  viewedAt: timestamp("viewed_at").defaultNow(),
});

// User Ad Preferences
export const userAdPreferences = pgTable("user_ad_preferences", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  categories: text("categories").array().default([]),
  maxAdsPerDay: integer("max_ads_per_day").default(10),
  minViewDuration: integer("min_view_duration").default(5),
  allowTargeted: boolean("allow_targeted").default(true),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Ad Analytics
export const adAnalytics = pgTable("ad_analytics", {
  id: uuid("id").primaryKey().defaultRandom(),
  campaignId: uuid("campaign_id").notNull().references(() => adCampaigns.id),
  date: timestamp("date").notNull(),
  impressions: integer("impressions").default(0),
  clicks: integer("clicks").default(0),
  spend: real("spend").default(0),
  revenue: real("revenue").default(0),
});

// Interaction Nodes
export const interactionNodes = pgTable("interaction_nodes", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  nodeType: varchar("node_type").notNull(),
  data: jsonb("data"),
  timestamp: timestamp("timestamp").defaultNow(),
  encrypted: boolean("encrypted").default(true),
});

// Interaction Connections
export const interactionConnections = pgTable("interaction_connections", {
  id: uuid("id").primaryKey().defaultRandom(),
  fromNodeId: uuid("from_node_id").notNull().references(() => interactionNodes.id),
  toNodeId: uuid("to_node_id").notNull().references(() => interactionNodes.id),
  connectionType: varchar("connection_type").notNull(),
  strength: real("strength").default(1.0),
  metadata: jsonb("metadata"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Interaction Sessions
export const interactionSessions = pgTable("interaction_sessions", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  sessionType: varchar("session_type").notNull(),
  startTime: timestamp("start_time").defaultNow(),
  endTime: timestamp("end_time"),
  metadata: jsonb("metadata"),
  nodeCount: integer("node_count").default(0),
});

// Memvid Storage
export const memvidStorage = pgTable("memvid_storage", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  chunkId: varchar("chunk_id").notNull(),
  sessionId: varchar("session_id"),
  chunkData: text("chunk_data").notNull(),
  embeddings: jsonb("embeddings"),
  keywords: text("keywords").array().default([]),
  timestamp: timestamp("timestamp").defaultNow(),
  encrypted: boolean("encrypted").default(true),
});

// AI Analysis
export const aiAnalysis = pgTable("ai_analysis", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  analysisType: varchar("analysis_type").notNull(),
  inputData: jsonb("input_data"),
  results: jsonb("results"),
  confidence: real("confidence"),
  processingTime: integer("processing_time"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Privacy Alerts
export const privacyAlerts = pgTable("privacy_alerts", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  alertType: varchar("alert_type").notNull(),
  severity: varchar("severity").notNull(),
  message: text("message").notNull(),
  actionRequired: boolean("action_required").default(false),
  resolved: boolean("resolved").default(false),
  createdAt: timestamp("created_at").defaultNow(),
});

// Data Marketplace
export const dataMarketplace = pgTable("data_marketplace", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  dataPackageId: varchar("data_package_id").notNull().unique(),
  packageName: varchar("package_name").notNull(),
  description: text("description"),
  dataTypes: jsonb("data_types").default([]),
  pricePerAccess: integer("price_per_access"),
  monthlyPrice: integer("monthly_price"),
  accessCount: integer("access_count").default(0),
  totalEarnings: integer("total_earnings").default(0),
  isActive: boolean("is_active").default(true),
  privacyLevel: integer("privacy_level").default(3),
  expiresAt: timestamp("expires_at"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Data Purchase Requests
export const dataPurchaseRequests = pgTable("data_purchase_requests", {
  id: uuid("id").primaryKey().defaultRandom(),
  buyerId: varchar("buyer_id").notNull().references(() => users.id),
  sellerId: varchar("seller_id").notNull().references(() => users.id),
  dataId: uuid("data_id").notNull().references(() => dataMarketplace.id),
  requestedPrice: real("requested_price"),
  status: varchar("status").default("pending"),
  message: text("message"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Legacy Data Access Logs
export const legacyDataAccessLogs = pgTable("legacy_data_access_logs", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  dataType: varchar("data_type").notNull(),
  accessMethod: varchar("access_method").notNull(),
  metadata: jsonb("metadata"),
  accessedAt: timestamp("accessed_at").defaultNow(),
});

// Advertiser profiles table
export const advertisers = pgTable("advertisers", {
  id: varchar("id").primaryKey().notNull(),
  companyName: varchar("company_name").notNull(),
  contactEmail: varchar("contact_email").notNull(),
  industry: varchar("industry").notNull(),
  website: varchar("website").notNull(),
  description: text("description").notNull(),
  truBalance: integer("tru_balance").default(0),
  monthlySpend: integer("monthly_spend").default(0),
  registeredAt: timestamp("registered_at").defaultNow(),
});

// Advertiser Purchase Requests
export const advertiserPurchaseRequests = pgTable("advertiser_purchase_requests", {
  id: uuid("id").primaryKey().defaultRandom(),
  advertiserId: varchar("advertiser_id").notNull().references(() => advertisers.id),
  dataType: varchar("data_type").notNull(),
  targetAudience: jsonb("target_audience"),
  budget: real("budget").notNull(),
  duration: integer("duration").notNull(),
  message: text("message"),
  status: varchar("status").default("pending"),
  offeredPrice: real("offered_price"),
  resolvedAt: timestamp("resolved_at"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Messages
export const messages = pgTable("messages", {
  id: uuid("id").primaryKey().defaultRandom(),
  senderId: varchar("sender_id").notNull().references(() => users.id),
  receiverId: varchar("receiver_id").notNull().references(() => users.id),
  content: text("content").notNull(),
  messageType: varchar("message_type").default("text"),
  isEncrypted: boolean("is_encrypted").default(true),
  status: varchar("status").default("sent"),
  metadata: jsonb("metadata"),
  createdAt: timestamp("created_at").defaultNow(),
  readAt: timestamp("read_at"),
});

// nU Universe Energy Tracking
export const numentumTracking = pgTable("numentum_tracking", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  sessionId: varchar("session_id").notNull(),
  umatterGenerated: real("umatter_generated").default(0),
  truTokens: real("tru_tokens").default(0),
  nuvaTokens: real("nuva_tokens").default(0),
  batteryDrainWh: real("battery_drain_wh").default(0),
  energyEfficiency: real("energy_efficiency").default(1.0),
  timestamp: timestamp("timestamp").defaultNow(),
});

// InUrtia Compound Interest Balances
export const inurtiaBalances = pgTable("inurtia_balances", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  currentBalance: real("current_balance").default(0),
  totalEarned: real("total_earned").default(0),
  compoundRate: real("compound_rate").default(1.05),
  lastCompoundAt: timestamp("last_compound_at").defaultNow(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// InUrtia Redemptions
export const inurtiaRedemptions = pgTable("inurtia_redemptions", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  amount: real("amount").notNull(),
  redemptionType: varchar("redemption_type").notNull(),
  status: varchar("status").default("pending"),
  metadata: jsonb("metadata"),
  processedAt: timestamp("processed_at"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Energy Pools
export const energyPools = pgTable("energy_pools", {
  id: uuid("id").primaryKey().defaultRandom(),
  creatorId: varchar("creator_id").notNull().references(() => users.id),
  name: varchar("name").notNull(),
  description: text("description"),
  poolType: varchar("pool_type").notNull(),
  totalEnergy: real("total_energy").default(0),
  maxParticipants: integer("max_participants").default(100),
  currentParticipants: integer("current_participants").default(0),
  entryRequirement: real("entry_requirement").default(0),
  distributionMethod: varchar("distribution_method").default("equal"),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Pool Participants
export const poolParticipants = pgTable("pool_participants", {
  id: uuid("id").primaryKey().defaultRandom(),
  poolId: uuid("pool_id").notNull().references(() => energyPools.id),
  userId: varchar("user_id").notNull().references(() => users.id),
  contributedEnergy: real("contributed_energy").default(0),
  sharePercentage: real("share_percentage").default(0),
  joinedAt: timestamp("joined_at").defaultNow(),
});

// LinkVibe bookmarks - TikTok-inspired interactive link saving
export const linkVibes = pgTable("link_vibes", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  title: varchar("title").notNull(),
  url: text("url").notNull(),
  description: text("description"),
  tags: text("tags").array().default([]),
  thumbnail: varchar("thumbnail"),
  favicon: varchar("favicon"),
  category: varchar("category").default("general"),
  vibeScore: real("vibe_score").default(0),
  isPublic: boolean("is_public").default(false),
  isEncrypted: boolean("is_encrypted").default(true),
  accessCount: integer("access_count").default(0),
  lastAccessedAt: timestamp("last_accessed_at"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Vibe Collections - Organized bookmark collections
export const vibeCollections = pgTable("vibe_collections", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  name: varchar("name").notNull(),
  description: text("description"),
  color: varchar("color").default("#6366f1"),
  icon: varchar("icon").default("📁"),
  isPublic: boolean("is_public").default(false),
  sortOrder: integer("sort_order").default(0),
  linkCount: integer("link_count").default(0),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Collection Links - Links within collections
export const collectionLinks = pgTable("collection_links", {
  id: uuid("id").primaryKey().defaultRandom(),
  collectionId: uuid("collection_id").notNull().references(() => vibeCollections.id),
  linkId: uuid("link_id").notNull().references(() => linkVibes.id),
  sortOrder: integer("sort_order").default(0),
  addedAt: timestamp("added_at").defaultNow(),
});

// Vibe Interactions - User interactions with links
export const vibeInteractions = pgTable("vibe_interactions", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  linkId: uuid("link_id").notNull().references(() => linkVibes.id),
  interactionType: varchar("interaction_type").notNull(),
  value: real("value").default(1.0),
  metadata: jsonb("metadata"),
  timestamp: timestamp("timestamp").defaultNow(),
});

// Vibe Marketplace - Social marketplace for bookmarks
export const vibeMarketplace = pgTable("vibe_marketplace", {
  id: uuid("id").primaryKey().defaultRandom(),
  sellerId: varchar("seller_id").notNull().references(() => users.id),
  linkId: uuid("link_id").notNull().references(() => linkVibes.id),
  price: real("price").notNull(),
  currency: varchar("currency").default("TRU"),
  category: varchar("category").notNull(),
  tags: text("tags").array().default([]),
  description: text("description"),
  isActive: boolean("is_active").default(true),
  purchaseCount: integer("purchase_count").default(0),
  rating: real("rating").default(0),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Vibe Purchases
export const vibePurchases = pgTable("vibe_purchases", {
  id: uuid("id").primaryKey().defaultRandom(),
  buyerId: varchar("buyer_id").notNull().references(() => users.id),
  sellerId: varchar("seller_id").notNull().references(() => users.id),
  marketplaceId: uuid("marketplace_id").notNull().references(() => vibeMarketplace.id),
  linkId: uuid("link_id").notNull().references(() => linkVibes.id),
  price: real("price").notNull(),
  currency: varchar("currency").default("TRU"),
  status: varchar("status").default("completed"),
  purchasedAt: timestamp("purchased_at").defaultNow(),
});

// Vibe Suggestions
export const vibeSuggestions = pgTable("vibe_suggestions", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  linkId: uuid("link_id").notNull().references(() => linkVibes.id),
  suggestionType: varchar("suggestion_type").notNull(),
  confidence: real("confidence").default(0.5),
  metadata: jsonb("metadata"),
  isViewed: boolean("is_viewed").default(false),
  createdAt: timestamp("created_at").defaultNow(),
});

// Vibe Feed Data
export const vibeFeedData = pgTable("vibe_feed_data", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  linkId: uuid("link_id").notNull().references(() => linkVibes.id),
  feedType: varchar("feed_type").notNull(),
  score: real("score").default(0),
  engagement: jsonb("engagement"),
  isViewed: boolean("is_viewed").default(false),
  viewedAt: timestamp("viewed_at"),
  createdAt: timestamp("created_at").defaultNow(),
});

// Web Ad Interceptions
export const webAdInterceptions = pgTable("web_ad_interceptions", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  url: text("url").notNull(),
  adContent: text("ad_content"),
  adType: varchar("ad_type"),
  domain: varchar("domain"),
  umatterValue: real("umatter_value").default(0),
  timestamp: timestamp("timestamp").defaultNow(),
  blocked: boolean("blocked").default(true),
});

// nU Universe Events
export const numentumEvents = pgTable('numentum_events', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: varchar('user_id').notNull().references(() => users.id),
  eventType: varchar('event_type').notNull(),
  eventData: jsonb('event_data'),
  energyGenerated: real('energy_generated').default(0),
  timestamp: timestamp('timestamp').defaultNow(),
});

// SpUnder Interactions
export const spunderInteractions = pgTable('spunder_interactions', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: varchar('user_id').notNull().references(() => users.id),
  interactionType: varchar('interaction_type').notNull(),
  targetUrl: text('target_url'),
  encryptionLevel: varchar('encryption_level').default('standard'),
  dataSize: integer('data_size').default(0),
  processed: boolean('processed').default(false),
  timestamp: timestamp('timestamp').defaultNow(),
});

// Memvid Data
export const memvidData = pgTable('memvid_data', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: varchar('user_id').notNull().references(() => users.id),
  sessionId: varchar('session_id').notNull(),
  dataChunk: text('data_chunk').notNull(),
  chunkIndex: integer('chunk_index').notNull(),
  totalChunks: integer('total_chunks').notNull(),
  metadata: jsonb('metadata'),
  timestamp: timestamp('timestamp').defaultNow(),
});

// Biometric Data
export const biometricData = pgTable('biometric_data', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: varchar('user_id').notNull().references(() => users.id),
  dataType: varchar('data_type').notNull(),
  value: real('value').notNull(),
  unit: varchar('unit').notNull(),
  deviceId: varchar('device_id'),
  timestamp: timestamp('timestamp').defaultNow(),
});

// Biometric Readings
export const biometricReadings = pgTable("biometric_readings", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  heartRate: real("heart_rate").notNull(),
  energyLevel: real("energy_level").notNull(),
  stressLevel: real("stress_level").default(0),
  focusLevel: real("focus_level").default(0),
  timestamp: timestamp("timestamp").defaultNow(),
});

// Energy Metrics
export const energyMetrics = pgTable("energy_metrics", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  metricType: varchar("metric_type").notNull(),
  value: real("value").notNull(),
  multiplier: real("multiplier").default(1.0),
  source: varchar("source").notNull(),
  timestamp: timestamp("timestamp").defaultNow(),
});

// Energy Balances
export const energyBalances = pgTable("energy_balances", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  balance: real("balance").default(0),
  lastUpdated: timestamp("last_updated").defaultNow(),
});

// Device States
export const deviceStates = pgTable("device_states", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  deviceType: varchar("device_type").notNull(),
  state: jsonb("state").notNull(),
  batteryLevel: real("battery_level"),
  energyConsumption: real("energy_consumption").default(0),
  timestamp: timestamp("timestamp").defaultNow(),
});

// Neural States
export const neuralStates = pgTable("neural_states", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: varchar("user_id").notNull().references(() => users.id),
  stateType: varchar("state_type").notNull(),
  stateData: jsonb("state_data").notNull(),
  confidence: real("confidence").default(0.5),
  timestamp: timestamp("timestamp").defaultNow(),
});

// Insert schemas
export const insertUserSchema = createInsertSchema(users).pick({
  email: true,
  firstName: true,
  lastName: true,
  profileImageUrl: true,
  companyName: true,
  accountType: true,
  dataMonetizationEnabled: true,
});

export const insertInteractionNodeSchema = createInsertSchema(interactionNodes).omit({
  id: true,
  timestamp: true,
});

export const insertInteractionConnectionSchema = createInsertSchema(interactionConnections).omit({
  id: true,
  createdAt: true,
});

export const insertInteractionSessionSchema = createInsertSchema(interactionSessions).omit({
  id: true,
  startTime: true,
});

export const insertMemvidStorageSchema = createInsertSchema(memvidStorage).omit({
  id: true,
  timestamp: true,
});

export const insertAiAnalysisSchema = createInsertSchema(aiAnalysis).omit({
  id: true,
  createdAt: true,
});

export const insertPrivacyAlertSchema = createInsertSchema(privacyAlerts).omit({
  id: true,
  createdAt: true,
});

export const insertDataMarketplaceSchema = createInsertSchema(dataMarketplace).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertDataPurchaseRequestSchema = createInsertSchema(dataPurchaseRequests).omit({
  id: true,
  createdAt: true,
});

export const insertDataAccessLogSchema = createInsertSchema(dataAccessLogs).omit({
  id: true,
  accessedAt: true,
});

export const insertUserEarningsSchema = createInsertSchema(userEarnings).omit({
  id: true,
  createdAt: true,
});

export const insertNumentumTrackingSchema = createInsertSchema(numentumTracking).omit({
  id: true,
  timestamp: true,
});

export const insertInurtiaBalanceSchema = createInsertSchema(inurtiaBalances).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertInurtiaRedemptionSchema = createInsertSchema(inurtiaRedemptions).omit({
  id: true,
  createdAt: true,
});

export const insertEnergyPoolSchema = createInsertSchema(energyPools).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertPoolParticipantSchema = createInsertSchema(poolParticipants).omit({
  id: true,
  joinedAt: true,
});

export const insertLinkVibeSchema = createInsertSchema(linkVibes).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertVibeCollectionSchema = createInsertSchema(vibeCollections).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertCollectionLinkSchema = createInsertSchema(collectionLinks).omit({
  id: true,
  addedAt: true,
});

export const insertVibeInteractionSchema = createInsertSchema(vibeInteractions).omit({
  id: true,
  timestamp: true,
});

export const insertVibeMarketplaceSchema = createInsertSchema(vibeMarketplace).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertVibePurchaseSchema = createInsertSchema(vibePurchases).omit({
  id: true,
  purchasedAt: true,
});

export const insertVibeSuggestionSchema = createInsertSchema(vibeSuggestions).omit({
  id: true,
  createdAt: true,
});

export const insertVibeFeedDataSchema = createInsertSchema(vibeFeedData).omit({
  id: true,
  createdAt: true,
});

export const insertAdvertiserSchema = createInsertSchema(advertisers).omit({
  id: true,
  registeredAt: true,
});

export const insertAdvertiserPurchaseRequestSchema = createInsertSchema(advertiserPurchaseRequests).omit({
  id: true,
  createdAt: true,
});

export const insertMessageSchema = createInsertSchema(messages).omit({
  id: true,
  createdAt: true,
});

export const insertWebAdInterceptionSchema = createInsertSchema(webAdInterceptions).omit({
  id: true,
  timestamp: true,
});

// Export types
export type UpsertUser = typeof users.$inferInsert;
export type User = typeof users.$inferSelect;

// Enhanced energy balance types
export type EnergyBalanceDetailed = typeof energyBalancesDetailed.$inferSelect;
export type InsertEnergyBalanceDetailed = typeof energyBalancesDetailed.$inferInsert;

// Transaction types
export type EnergyTransaction = typeof energyTransactions.$inferSelect;
export type InsertEnergyTransaction = typeof energyTransactions.$inferInsert;

// Device types
export type UserDevice = typeof userDevices.$inferSelect;
export type InsertUserDevice = typeof userDevices.$inferInsert;

export type Advertiser = typeof advertisers.$inferSelect;
export type InsertAdvertiser = z.infer<typeof insertAdvertiserSchema>;

export type InteractionNode = typeof interactionNodes.$inferSelect;
export type InsertInteractionNode = z.infer<typeof insertInteractionNodeSchema>;

export type InteractionConnection = typeof interactionConnections.$inferSelect;
export type InsertInteractionConnection = z.infer<typeof insertInteractionConnectionSchema>;

export type InteractionSession = typeof interactionSessions.$inferSelect;
export type InsertInteractionSession = z.infer<typeof insertInteractionSessionSchema>;

export type MemvidStorage = typeof memvidStorage.$inferSelect;
export type InsertMemvidStorage = z.infer<typeof insertMemvidStorageSchema>;

export type AiAnalysis = typeof aiAnalysis.$inferSelect;
export type InsertAiAnalysis = z.infer<typeof insertAiAnalysisSchema>;

export type PrivacyAlert = typeof privacyAlerts.$inferSelect;
export type InsertPrivacyAlert = z.infer<typeof insertPrivacyAlertSchema>;

export type DataMarketplace = typeof dataMarketplace.$inferSelect;
export type InsertDataMarketplace = z.infer<typeof insertDataMarketplaceSchema>;

export type DataPurchaseRequest = typeof dataPurchaseRequests.$inferSelect;
export type InsertDataPurchaseRequest = z.infer<typeof insertDataPurchaseRequestSchema>;

export type DataAccessLog = typeof dataAccessLogs.$inferSelect;
export type InsertDataAccessLog = z.infer<typeof insertDataAccessLogSchema>;

export type UserEarnings = typeof userEarnings.$inferSelect;
export type InsertUserEarnings = z.infer<typeof insertUserEarningsSchema>;

export type LinkVibe = typeof linkVibes.$inferSelect;
export type InsertLinkVibe = z.infer<typeof insertLinkVibeSchema>;

export type VibeCollection = typeof vibeCollections.$inferSelect;
export type InsertVibeCollection = z.infer<typeof insertVibeCollectionSchema>;

export type CollectionLink = typeof collectionLinks.$inferSelect;
export type InsertCollectionLink = z.infer<typeof insertCollectionLinkSchema>;

export type VibeInteraction = typeof vibeInteractions.$inferSelect;
export type InsertVibeInteraction = z.infer<typeof insertVibeInteractionSchema>;

export type VibeMarketplace = typeof vibeMarketplace.$inferSelect;
export type InsertVibeMarketplace = z.infer<typeof insertVibeMarketplaceSchema>;

export type VibePurchase = typeof vibePurchases.$inferSelect;
export type InsertVibePurchase = z.infer<typeof insertVibePurchaseSchema>;

export type VibeSuggestion = typeof vibeSuggestions.$inferSelect;
export type InsertVibeSuggestion = z.infer<typeof insertVibeSuggestionSchema>;

export type VibeFeedData = typeof vibeFeedData.$inferSelect;
export type InsertVibeFeedData = z.infer<typeof insertVibeFeedDataSchema>;

export type AdvertiserPurchaseRequest = typeof advertiserPurchaseRequests.$inferSelect;
export type InsertAdvertiserPurchaseRequest = z.infer<typeof insertAdvertiserPurchaseRequestSchema>;

export type Message = typeof messages.$inferSelect;
export type InsertMessage = z.infer<typeof insertMessageSchema>;

export type WebAdInterception = typeof webAdInterceptions.$inferSelect;
export type InsertWebAdInterception = z.infer<typeof insertWebAdInterceptionSchema>;

export type NumentumTracking = typeof numentumTracking.$inferSelect;
export type InsertNumentumTracking = z.infer<typeof insertNumentumTrackingSchema>;

export type InurtiaBalance = typeof inurtiaBalances.$inferSelect;
export type InsertInurtiaBalance = z.infer<typeof insertInurtiaBalanceSchema>;

export type InurtiaRedemption = typeof inurtiaRedemptions.$inferSelect;
export type InsertInurtiaRedemption = z.infer<typeof insertInurtiaRedemptionSchema>;

export type EnergyPool = typeof energyPools.$inferSelect;
export type InsertEnergyPool = z.infer<typeof insertEnergyPoolSchema>;

export type PoolParticipant = typeof poolParticipants.$inferSelect;
export type InsertPoolParticipant = z.infer<typeof insertPoolParticipantSchema>;

export type EnergyBalance = typeof energyBalances.$inferSelect;
export type InsertEnergyBalance = typeof energyBalances.$inferInsert;

export type EnergyMetric = typeof energyMetrics.$inferSelect;
export type InsertEnergyMetric = typeof energyMetrics.$inferInsert;

export type BiometricReading = typeof biometricReadings.$inferSelect;
export type InsertBiometricReading = typeof biometricReadings.$inferInsert;

export type DeviceState = typeof deviceStates.$inferSelect;
export type InsertDeviceState = typeof deviceStates.$inferInsert;

export type NeuralState = typeof neuralStates.$inferSelect;
export type InsertNeuralState = typeof neuralStates.$inferInsert;

// nUQuantum Emulator (nQE) Tables
export const nqeTasks = pgTable("nqe_tasks", {
  id: uuid("id").primaryKey().defaultRandom(),
  taskId: varchar("task_id", { length: 36 }).notNull().unique(),
  userId: varchar("user_id").notNull().references(() => users.id),
  type: varchar("type").notNull(), // 'factor' or 'search'
  input: jsonb("input").notNull(), // Stores number or query
  status: varchar("status").notNull().default("submitted"), // 'submitted', 'running', 'complete', 'failed'
  createdAt: timestamp("created_at").defaultNow(),
}, (table) => [
  index("idx_nqe_tasks_user_id").on(table.userId),
  index("idx_nqe_tasks_status").on(table.status),
]);

export const nqeResults = pgTable("nqe_results", {
  taskId: varchar("task_id", { length: 36 }).primaryKey().references(() => nqeTasks.taskId, { onDelete: 'cascade' }),
  output: jsonb("output").notNull(), // Stores factors or search result
  energyCost: real("energy_cost").notNull(), // UMatter
  completedAt: timestamp("completed_at").defaultNow(),
});

// nQE schemas
export const insertNqeTaskSchema = createInsertSchema(nqeTasks).omit({ id: true, createdAt: true });
export type InsertNqeTask = z.infer<typeof insertNqeTaskSchema>;
export type SelectNqeTask = typeof nqeTasks.$inferSelect;

export const insertNqeResultSchema = createInsertSchema(nqeResults).omit({ completedAt: true });
export type InsertNqeResult = z.infer<typeof insertNqeResultSchema>;
export type SelectNqeResult = typeof nqeResults.$inferSelect;