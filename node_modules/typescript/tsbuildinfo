{"fileNames": ["../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es5.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2015.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2016.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2017.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2018.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2019.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2020.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.dom.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2015.core.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2016.intl.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2017.date.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2017.object.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2017.string.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2019.array.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2019.object.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2019.string.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2020.date.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2020.string.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.es2020.number.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.esnext.intl.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.decorators.d.ts", "../../../../.nvm/versions/node/v18.20.8/lib/node_modules/typescript/lib/lib.decorators.legacy.d.ts", "../@types/react/global.d.ts", "../csstype/index.d.ts", "../@types/prop-types/index.d.ts", "../@types/react/index.d.ts", "../@types/react/jsx-runtime.d.ts", "../wouter/types/location-hook.d.ts", "../wouter/types/use-browser-location.d.ts", "../wouter/types/router.d.ts", "../regexparam/index.d.ts", "../wouter/types/index.d.ts", "../@tanstack/query-core/build/modern/removable.d.ts", "../@tanstack/query-core/build/modern/subscribable.d.ts", "../@tanstack/query-core/build/modern/hydration-cr-4kky1.d.ts", "../@tanstack/query-core/build/modern/queriesobserver.d.ts", "../@tanstack/query-core/build/modern/infinitequeryobserver.d.ts", "../@tanstack/query-core/build/modern/notifymanager.d.ts", "../@tanstack/query-core/build/modern/focusmanager.d.ts", "../@tanstack/query-core/build/modern/onlinemanager.d.ts", "../@tanstack/query-core/build/modern/streamedquery.d.ts", "../@tanstack/query-core/build/modern/index.d.ts", "../@tanstack/react-query/build/modern/types.d.ts", "../@tanstack/react-query/build/modern/usequeries.d.ts", "../@tanstack/react-query/build/modern/queryoptions.d.ts", "../@tanstack/react-query/build/modern/usequery.d.ts", "../@tanstack/react-query/build/modern/usesuspensequery.d.ts", "../@tanstack/react-query/build/modern/usesuspenseinfinitequery.d.ts", "../@tanstack/react-query/build/modern/usesuspensequeries.d.ts", "../@tanstack/react-query/build/modern/useprefetchquery.d.ts", "../@tanstack/react-query/build/modern/useprefetchinfinitequery.d.ts", "../@tanstack/react-query/build/modern/infinitequeryoptions.d.ts", "../@tanstack/react-query/build/modern/queryclientprovider.d.ts", "../@tanstack/react-query/build/modern/queryerrorresetboundary.d.ts", "../@tanstack/react-query/build/modern/hydrationboundary.d.ts", "../@tanstack/react-query/build/modern/useisfetching.d.ts", "../@tanstack/react-query/build/modern/usemutationstate.d.ts", "../@tanstack/react-query/build/modern/usemutation.d.ts", "../@tanstack/react-query/build/modern/useinfinitequery.d.ts", "../@tanstack/react-query/build/modern/isrestoringprovider.d.ts", "../@tanstack/react-query/build/modern/index.d.ts", "../../client/src/lib/queryclient.ts", "../@radix-ui/react-context/dist/index.d.mts", "../@radix-ui/react-primitive/dist/index.d.mts", "../@radix-ui/react-dismissable-layer/dist/index.d.mts", "../@radix-ui/react-toast/dist/index.d.mts", "../clsx/clsx.d.mts", "../class-variance-authority/dist/types.d.ts", "../class-variance-authority/dist/index.d.ts", "../lucide-react/dist/lucide-react.d.ts", "../tailwind-merge/dist/types.d.ts", "../../client/src/lib/utils.ts", "../../client/src/components/ui/toast.tsx", "../../client/src/hooks/use-toast.ts", "../../client/src/components/ui/toaster.tsx", "../@radix-ui/react-arrow/dist/index.d.mts", "../@radix-ui/rect/dist/index.d.mts", "../@radix-ui/react-popper/dist/index.d.mts", "../@radix-ui/react-portal/dist/index.d.mts", "../@radix-ui/react-tooltip/dist/index.d.mts", "../../client/src/components/ui/tooltip.tsx", "../../client/src/components/ui/card.tsx", "../@radix-ui/react-slot/dist/index.d.mts", "../../client/src/components/ui/button.tsx", "../../client/src/components/errorboundary.tsx", "../drizzle-orm/entity.d.ts", "../drizzle-orm/logger.d.ts", "../drizzle-orm/utils.d.ts", "../drizzle-orm/casing.d.ts", "../drizzle-orm/subquery.d.ts", "../drizzle-orm/query-builders/select.types.d.ts", "../drizzle-orm/sql/sql.d.ts", "../drizzle-orm/table.d.ts", "../drizzle-orm/mysql-core/checks.d.ts", "../drizzle-orm/mysql-core/columns/binary.d.ts", "../drizzle-orm/mysql-core/columns/boolean.d.ts", "../drizzle-orm/mysql-core/columns/char.d.ts", "../drizzle-orm/mysql-core/columns/custom.d.ts", "../drizzle-orm/mysql-core/columns/date.d.ts", "../drizzle-orm/mysql-core/columns/datetime.d.ts", "../drizzle-orm/mysql-core/columns/decimal.d.ts", "../drizzle-orm/mysql-core/columns/double.d.ts", "../drizzle-orm/mysql-core/columns/enum.d.ts", "../drizzle-orm/mysql-core/columns/float.d.ts", "../drizzle-orm/mysql-core/columns/int.d.ts", "../drizzle-orm/mysql-core/columns/json.d.ts", "../drizzle-orm/mysql-core/columns/mediumint.d.ts", "../drizzle-orm/mysql-core/columns/real.d.ts", "../drizzle-orm/mysql-core/columns/serial.d.ts", "../drizzle-orm/mysql-core/columns/smallint.d.ts", "../drizzle-orm/mysql-core/columns/text.d.ts", "../drizzle-orm/mysql-core/columns/time.d.ts", "../drizzle-orm/mysql-core/columns/date.common.d.ts", "../drizzle-orm/mysql-core/columns/timestamp.d.ts", "../drizzle-orm/mysql-core/columns/tinyint.d.ts", "../drizzle-orm/mysql-core/columns/varbinary.d.ts", "../drizzle-orm/mysql-core/columns/varchar.d.ts", "../drizzle-orm/mysql-core/columns/year.d.ts", "../drizzle-orm/mysql-core/columns/all.d.ts", "../drizzle-orm/mysql-core/indexes.d.ts", "../drizzle-orm/mysql-core/primary-keys.d.ts", "../drizzle-orm/mysql-core/unique-constraint.d.ts", "../drizzle-orm/mysql-core/table.d.ts", "../drizzle-orm/mysql-core/foreign-keys.d.ts", "../drizzle-orm/mysql-core/columns/common.d.ts", "../drizzle-orm/mysql-core/columns/bigint.d.ts", "../drizzle-orm/mysql-core/columns/index.d.ts", "../drizzle-orm/sql/expressions/conditions.d.ts", "../drizzle-orm/sql/expressions/select.d.ts", "../drizzle-orm/sql/expressions/index.d.ts", "../drizzle-orm/sql/functions/aggregate.d.ts", "../drizzle-orm/sql/functions/vector.d.ts", "../drizzle-orm/sql/functions/index.d.ts", "../drizzle-orm/sql/index.d.ts", "../drizzle-orm/query-builders/query-builder.d.ts", "../drizzle-orm/expressions.d.ts", "../drizzle-orm/relations.d.ts", "../drizzle-orm/migrator.d.ts", "../drizzle-orm/query-promise.d.ts", "../drizzle-orm/mysql-core/query-builders/delete.d.ts", "../drizzle-orm/runnable-query.d.ts", "../drizzle-orm/mysql-core/subquery.d.ts", "../drizzle-orm/mysql-core/view-base.d.ts", "../drizzle-orm/mysql-core/query-builders/select.d.ts", "../drizzle-orm/mysql-core/query-builders/query-builder.d.ts", "../drizzle-orm/mysql-core/query-builders/update.d.ts", "../drizzle-orm/mysql-core/query-builders/insert.d.ts", "../drizzle-orm/mysql-core/dialect.d.ts", "../drizzle-orm/mysql-core/query-builders/count.d.ts", "../drizzle-orm/mysql-core/query-builders/index.d.ts", "../drizzle-orm/mysql-core/query-builders/query.d.ts", "../drizzle-orm/mysql-core/db.d.ts", "../drizzle-orm/mysql-core/session.d.ts", "../drizzle-orm/mysql-core/view-common.d.ts", "../drizzle-orm/mysql-core/view.d.ts", "../drizzle-orm/mysql-core/query-builders/select.types.d.ts", "../drizzle-orm/mysql-core/alias.d.ts", "../drizzle-orm/mysql-core/schema.d.ts", "../drizzle-orm/alias.d.ts", "../drizzle-orm/errors.d.ts", "../drizzle-orm/view-common.d.ts", "../drizzle-orm/index.d.ts", "../drizzle-orm/mysql-core/utils.d.ts", "../drizzle-orm/mysql-core/index.d.ts", "../drizzle-orm/singlestore-core/columns/binary.d.ts", "../drizzle-orm/singlestore-core/columns/boolean.d.ts", "../drizzle-orm/singlestore-core/columns/char.d.ts", "../drizzle-orm/singlestore-core/columns/custom.d.ts", "../drizzle-orm/singlestore-core/columns/date.d.ts", "../drizzle-orm/singlestore-core/columns/datetime.d.ts", "../drizzle-orm/singlestore-core/columns/decimal.d.ts", "../drizzle-orm/singlestore-core/columns/double.d.ts", "../drizzle-orm/singlestore-core/columns/enum.d.ts", "../drizzle-orm/singlestore-core/columns/float.d.ts", "../drizzle-orm/singlestore-core/columns/int.d.ts", "../drizzle-orm/singlestore-core/columns/json.d.ts", "../drizzle-orm/singlestore-core/columns/mediumint.d.ts", "../drizzle-orm/singlestore-core/columns/real.d.ts", "../drizzle-orm/singlestore-core/columns/serial.d.ts", "../drizzle-orm/singlestore-core/columns/smallint.d.ts", "../drizzle-orm/singlestore-core/columns/text.d.ts", "../drizzle-orm/singlestore-core/columns/time.d.ts", "../drizzle-orm/singlestore-core/columns/date.common.d.ts", "../drizzle-orm/singlestore-core/columns/timestamp.d.ts", "../drizzle-orm/singlestore-core/columns/tinyint.d.ts", "../drizzle-orm/singlestore-core/columns/varbinary.d.ts", "../drizzle-orm/singlestore-core/columns/varchar.d.ts", "../drizzle-orm/singlestore-core/columns/vector.d.ts", "../drizzle-orm/singlestore-core/columns/year.d.ts", "../drizzle-orm/singlestore-core/columns/all.d.ts", "../drizzle-orm/singlestore-core/indexes.d.ts", "../drizzle-orm/singlestore-core/primary-keys.d.ts", "../drizzle-orm/singlestore-core/unique-constraint.d.ts", "../drizzle-orm/singlestore-core/table.d.ts", "../drizzle-orm/singlestore-core/columns/common.d.ts", "../drizzle-orm/singlestore-core/columns/bigint.d.ts", "../drizzle-orm/singlestore-core/columns/index.d.ts", "../drizzle-orm/singlestore-core/query-builders/delete.d.ts", "../drizzle-orm/singlestore-core/query-builders/update.d.ts", "../drizzle-orm/singlestore-core/query-builders/insert.d.ts", "../drizzle-orm/singlestore-core/dialect.d.ts", "../drizzle-orm/singlestore/session.d.ts", "../drizzle-orm/singlestore/driver.d.ts", "../drizzle-orm/singlestore-core/query-builders/count.d.ts", "../drizzle-orm/singlestore-core/subquery.d.ts", "../drizzle-orm/singlestore-core/query-builders/select.d.ts", "../drizzle-orm/singlestore-core/query-builders/query-builder.d.ts", "../drizzle-orm/singlestore-core/query-builders/index.d.ts", "../drizzle-orm/singlestore-core/db.d.ts", "../drizzle-orm/singlestore-core/session.d.ts", "../drizzle-orm/singlestore-core/query-builders/select.types.d.ts", "../drizzle-orm/singlestore-core/alias.d.ts", "../drizzle-orm/singlestore-core/schema.d.ts", "../drizzle-orm/singlestore-core/utils.d.ts", "../drizzle-orm/singlestore-core/index.d.ts", "../drizzle-orm/sqlite-core/checks.d.ts", "../drizzle-orm/sqlite-core/columns/custom.d.ts", "../drizzle-orm/sqlite-core/indexes.d.ts", "../drizzle-orm/sqlite-core/primary-keys.d.ts", "../drizzle-orm/sqlite-core/unique-constraint.d.ts", "../drizzle-orm/session.d.ts", "../drizzle-orm/sqlite-core/query-builders/count.d.ts", "../drizzle-orm/sqlite-core/query-builders/query.d.ts", "../drizzle-orm/sqlite-core/subquery.d.ts", "../drizzle-orm/sqlite-core/view-base.d.ts", "../drizzle-orm/sqlite-core/db.d.ts", "../drizzle-orm/sqlite-core/query-builders/raw.d.ts", "../drizzle-orm/sqlite-core/session.d.ts", "../drizzle-orm/sqlite-core/query-builders/delete.d.ts", "../drizzle-orm/sqlite-core/query-builders/update.d.ts", "../drizzle-orm/sqlite-core/query-builders/insert.d.ts", "../drizzle-orm/sqlite-core/query-builders/select.d.ts", "../drizzle-orm/sqlite-core/query-builders/index.d.ts", "../drizzle-orm/sqlite-core/dialect.d.ts", "../drizzle-orm/sqlite-core/query-builders/query-builder.d.ts", "../drizzle-orm/sqlite-core/view.d.ts", "../drizzle-orm/sqlite-core/utils.d.ts", "../drizzle-orm/sqlite-core/columns/integer.d.ts", "../drizzle-orm/sqlite-core/columns/numeric.d.ts", "../drizzle-orm/sqlite-core/columns/real.d.ts", "../drizzle-orm/sqlite-core/columns/text.d.ts", "../drizzle-orm/sqlite-core/columns/all.d.ts", "../drizzle-orm/sqlite-core/table.d.ts", "../drizzle-orm/sqlite-core/foreign-keys.d.ts", "../drizzle-orm/sqlite-core/columns/common.d.ts", "../drizzle-orm/sqlite-core/columns/blob.d.ts", "../drizzle-orm/sqlite-core/columns/index.d.ts", "../drizzle-orm/sqlite-core/query-builders/select.types.d.ts", "../drizzle-orm/sqlite-core/alias.d.ts", "../drizzle-orm/sqlite-core/index.d.ts", "../drizzle-orm/column-builder.d.ts", "../drizzle-orm/column.d.ts", "../drizzle-orm/operations.d.ts", "../drizzle-orm/pg-core/checks.d.ts", "../drizzle-orm/pg-core/columns/bigserial.d.ts", "../drizzle-orm/pg-core/columns/boolean.d.ts", "../drizzle-orm/pg-core/columns/char.d.ts", "../drizzle-orm/pg-core/columns/cidr.d.ts", "../drizzle-orm/pg-core/columns/custom.d.ts", "../drizzle-orm/pg-core/columns/date.common.d.ts", "../drizzle-orm/pg-core/columns/date.d.ts", "../drizzle-orm/pg-core/columns/double-precision.d.ts", "../drizzle-orm/pg-core/columns/inet.d.ts", "../drizzle-orm/pg-core/sequence.d.ts", "../drizzle-orm/pg-core/columns/int.common.d.ts", "../drizzle-orm/pg-core/columns/integer.d.ts", "../drizzle-orm/pg-core/columns/timestamp.d.ts", "../drizzle-orm/pg-core/columns/interval.d.ts", "../drizzle-orm/pg-core/columns/json.d.ts", "../drizzle-orm/pg-core/columns/jsonb.d.ts", "../drizzle-orm/pg-core/columns/line.d.ts", "../drizzle-orm/pg-core/columns/macaddr.d.ts", "../drizzle-orm/pg-core/columns/macaddr8.d.ts", "../drizzle-orm/pg-core/columns/numeric.d.ts", "../drizzle-orm/pg-core/columns/point.d.ts", "../drizzle-orm/pg-core/columns/postgis_extension/geometry.d.ts", "../drizzle-orm/pg-core/columns/real.d.ts", "../drizzle-orm/pg-core/columns/serial.d.ts", "../drizzle-orm/pg-core/columns/smallint.d.ts", "../drizzle-orm/pg-core/columns/smallserial.d.ts", "../drizzle-orm/pg-core/columns/text.d.ts", "../drizzle-orm/pg-core/columns/time.d.ts", "../drizzle-orm/pg-core/columns/uuid.d.ts", "../drizzle-orm/pg-core/columns/varchar.d.ts", "../drizzle-orm/pg-core/columns/vector_extension/bit.d.ts", "../drizzle-orm/pg-core/columns/vector_extension/halfvec.d.ts", "../drizzle-orm/pg-core/columns/vector_extension/sparsevec.d.ts", "../drizzle-orm/pg-core/columns/vector_extension/vector.d.ts", "../drizzle-orm/pg-core/columns/all.d.ts", "../drizzle-orm/pg-core/indexes.d.ts", "../drizzle-orm/pg-core/roles.d.ts", "../drizzle-orm/pg-core/policies.d.ts", "../drizzle-orm/pg-core/primary-keys.d.ts", "../drizzle-orm/pg-core/unique-constraint.d.ts", "../drizzle-orm/pg-core/table.d.ts", "../drizzle-orm/pg-core/foreign-keys.d.ts", "../drizzle-orm/pg-core/columns/common.d.ts", "../drizzle-orm/pg-core/columns/bigint.d.ts", "../drizzle-orm/pg-core/columns/enum.d.ts", "../drizzle-orm/pg-core/columns/index.d.ts", "../drizzle-orm/pg-core/view-base.d.ts", "../drizzle-orm/pg-core/query-builders/count.d.ts", "../drizzle-orm/pg-core/query-builders/query.d.ts", "../drizzle-orm/pg-core/query-builders/raw.d.ts", "../drizzle-orm/pg-core/query-builders/refresh-materialized-view.d.ts", "../drizzle-orm/pg-core/subquery.d.ts", "../drizzle-orm/pg-core/db.d.ts", "../drizzle-orm/pg-core/session.d.ts", "../drizzle-orm/pg-core/query-builders/delete.d.ts", "../drizzle-orm/pg-core/query-builders/update.d.ts", "../drizzle-orm/pg-core/query-builders/insert.d.ts", "../drizzle-orm/pg-core/query-builders/select.d.ts", "../drizzle-orm/pg-core/query-builders/index.d.ts", "../drizzle-orm/pg-core/dialect.d.ts", "../drizzle-orm/pg-core/query-builders/query-builder.d.ts", "../drizzle-orm/pg-core/view-common.d.ts", "../drizzle-orm/pg-core/view.d.ts", "../drizzle-orm/pg-core/query-builders/select.types.d.ts", "../drizzle-orm/pg-core/alias.d.ts", "../drizzle-orm/pg-core/schema.d.ts", "../drizzle-orm/pg-core/utils.d.ts", "../drizzle-orm/pg-core/utils/array.d.ts", "../drizzle-orm/pg-core/utils/index.d.ts", "../drizzle-orm/pg-core/index.d.ts", "../zod/lib/helpers/typealiases.d.ts", "../zod/lib/helpers/util.d.ts", "../zod/lib/zoderror.d.ts", "../zod/lib/locales/en.d.ts", "../zod/lib/errors.d.ts", "../zod/lib/helpers/parseutil.d.ts", "../zod/lib/helpers/enumutil.d.ts", "../zod/lib/helpers/errorutil.d.ts", "../zod/lib/helpers/partialutil.d.ts", "../zod/lib/standard-schema.d.ts", "../zod/lib/types.d.ts", "../zod/lib/external.d.ts", "../zod/lib/index.d.ts", "../zod/index.d.ts", "../drizzle-zod/column.d.mts", "../drizzle-zod/utils.d.mts", "../drizzle-zod/column.types.d.mts", "../drizzle-zod/schema.types.internal.d.mts", "../drizzle-zod/schema.types.d.mts", "../drizzle-zod/schema.d.mts", "../drizzle-zod/index.d.mts", "../../shared/schema.ts", "../../client/src/hooks/useauth.ts", "../../client/src/components/ui/badge.tsx", "../../client/src/pages/landing.tsx", "../@radix-ui/react-dialog/node_modules/@radix-ui/react-primitive/dist/index.d.mts", "../@radix-ui/react-dialog/node_modules/@radix-ui/react-dismissable-layer/dist/index.d.mts", "../@radix-ui/react-dialog/node_modules/@radix-ui/react-focus-scope/dist/index.d.mts", "../@radix-ui/react-dialog/node_modules/@radix-ui/react-portal/dist/index.d.mts", "../@radix-ui/react-dialog/dist/index.d.mts", "../../client/src/components/ui/sheet.tsx", "../../client/src/components/unifiednavigation.tsx", "../../client/src/components/ui/breadcrumb.tsx", "../../client/src/components/pagelayout.tsx", "../@radix-ui/react-progress/dist/index.d.mts", "../../client/src/components/ui/progress.tsx", "../../client/src/lib/real-battery-api.ts", "../../client/src/lib/native-battery-detector.ts", "../../client/src/lib/authentic-device-manager.ts", "../../client/src/hooks/useauthenticenergydata.ts", "../../client/src/lib/authentic-hardware-energy.ts", "../../client/src/lib/authentic-spunder.ts", "../../client/src/lib/energy-sync-controller.ts", "../../client/src/lib/real-hardware-connector.ts", "../../client/src/hooks/userealtimedata.ts", "../../client/src/hooks/userealbalance.ts", "../../client/src/components/unifiedsimpleumatterdisplay.tsx", "../@radix-ui/react-scroll-area/dist/index.d.mts", "../../client/src/components/ui/scroll-area.tsx", "../../client/src/lib/activity-dispatcher.ts", "../../client/src/components/liveactivityfeed.tsx", "../../client/src/components/statscards.tsx", "../../client/src/pages/dashboard.tsx", "../@radix-ui/react-roving-focus/dist/index.d.mts", "../@radix-ui/react-tabs/dist/index.d.mts", "../../client/src/components/ui/tabs.tsx", "../zustand/esm/vanilla.d.mts", "../zustand/esm/react.d.mts", "../zustand/esm/index.d.mts", "../zustand/esm/middleware/redux.d.mts", "../zustand/esm/middleware/devtools.d.mts", "../zustand/esm/middleware/subscribewithselector.d.mts", "../zustand/esm/middleware/combine.d.mts", "../zustand/esm/middleware/persist.d.mts", "../zustand/esm/middleware.d.mts", "../../client/src/lib/stores/simplenuvastore.ts", "../../client/src/components/unifiedenergydisplay.tsx", "../../client/src/components/realtimeenergydisplay.tsx", "../../client/src/components/energyflowvisualization.tsx", "../../client/src/components/nuvaenergystorage.tsx", "../../client/src/pages/energyhub.tsx", "../../client/src/pages/datamarketplace.tsx", "../../client/src/pages/partyportal.tsx", "../motion-utils/types/errors.d.ts", "../motion-utils/types/noop.d.ts", "../motion-utils/types/index.d.ts", "../motion-dom/types/utils/resolve-elements.d.ts", "../motion-dom/types/gestures/hover.d.ts", "../motion-dom/types/gestures/drag/state/is-active.d.ts", "../motion-dom/types/gestures/drag/state/set-active.d.ts", "../motion-dom/types/index.d.ts", "../framer-motion/dist/index.d.ts", "../../client/src/lib/energyeconomy.ts", "../../client/src/lib/realbatterydrain.ts", "../../client/src/lib/realtimedevicemanager.ts", "../../client/src/components/energydashboard.tsx", "../@radix-ui/react-switch/dist/index.d.mts", "../../client/src/components/ui/switch.tsx", "../../client/src/components/iotenergymanager.tsx", "../../client/src/lib/stores/nuvastore.ts", "../../client/src/pages/energymarketplace.tsx", "../../client/src/pages/energystorage.tsx", "../../client/src/pages/energybanking.tsx", "../../client/src/pages/unifiedwallet.tsx", "../../client/src/pages/wallet.tsx", "../../client/src/components/ui/input.tsx", "../../client/src/pages/trading.tsx", "../../client/src/pages/marketplace.tsx", "../@radix-ui/react-label/dist/index.d.mts", "../../client/src/components/ui/label.tsx", "../../client/src/components/ui/textarea.tsx", "../../client/src/components/ui/dialog.tsx", "../../client/src/lib/invitation-system.ts", "../../client/src/lib/sbu-generator.ts", "../../client/src/lib/proximity-device-discovery.ts", "../../client/src/pages/socialsync.tsx", "../@radix-ui/react-separator/dist/index.d.mts", "../../client/src/components/ui/separator.tsx", "../../client/src/pages/invitationacceptance.tsx", "../../client/src/pages/inurtia.tsx", "../../client/src/pages/linkvibe.tsx", "../../client/src/pages/datamonetization.tsx", "../../client/src/pages/aisearchpage.tsx", "../../client/src/components/universalpwainstaller.tsx", "../../client/src/lib/universal-pwa-installer.ts", "../../client/src/pages/extensions.tsx", "../../client/src/components/mobileresponsivenavigation.tsx", "../../client/src/components/mobilefirstlayout.tsx", "../../client/src/lib/mobile-device-manager.ts", "../../client/src/hooks/usemobilesync.ts", "../../client/src/components/pwainstaller.tsx", "../../client/src/components/mobilenativesync.tsx", "../../client/src/pages/mobilesyncpage.tsx", "../../client/src/pages/nuquantum.tsx", "../../client/src/pages/quantummarketplace.tsx", "../../client/src/components/advancedspunderdashboard.tsx", "../../client/src/components/realdataintegritymonitor.tsx", "../../client/src/pages/not-found.tsx", "../../client/src/lib/server-log-receiver.ts", "../../client/src/app.tsx", "../@types/react-dom/client.d.ts", "../../client/src/lib/performance-controller.ts", "../../client/src/main.tsx", "../../client/src/components/aianalysispanel.tsx", "../../client/src/components/aisearch.tsx", "../../client/src/hooks/useextensiondata.ts", "../../client/src/components/activityfeed.tsx", "../../client/src/components/authenticenergydisplay.tsx", "../../client/src/lib/realbiometrictracking.ts", "../../client/src/components/biometricenergypanel.tsx", "../../client/src/components/consolelogger.tsx", "../../client/src/components/demosyncsimulation.tsx", "../recharts/types/container/surface.d.ts", "../recharts/types/container/layer.d.ts", "../@types/d3-time/index.d.ts", "../@types/d3-scale/index.d.ts", "../victory-vendor/d3-scale.d.ts", "../recharts/types/cartesian/xaxis.d.ts", "../recharts/types/cartesian/yaxis.d.ts", "../recharts/types/util/types.d.ts", "../recharts/types/component/defaultlegendcontent.d.ts", "../recharts/types/util/payload/getuniqpayload.d.ts", "../recharts/types/component/legend.d.ts", "../recharts/types/component/defaulttooltipcontent.d.ts", "../recharts/types/component/tooltip.d.ts", "../recharts/types/component/responsivecontainer.d.ts", "../recharts/types/component/cell.d.ts", "../recharts/types/component/text.d.ts", "../recharts/types/component/label.d.ts", "../recharts/types/component/labellist.d.ts", "../recharts/types/component/customized.d.ts", "../recharts/types/shape/sector.d.ts", "../@types/d3-path/index.d.ts", "../@types/d3-shape/index.d.ts", "../victory-vendor/d3-shape.d.ts", "../recharts/types/shape/curve.d.ts", "../recharts/types/shape/rectangle.d.ts", "../recharts/types/shape/polygon.d.ts", "../recharts/types/shape/dot.d.ts", "../recharts/types/shape/cross.d.ts", "../recharts/types/shape/symbols.d.ts", "../recharts/types/polar/polargrid.d.ts", "../recharts/types/polar/polarradiusaxis.d.ts", "../recharts/types/polar/polarangleaxis.d.ts", "../recharts/types/polar/pie.d.ts", "../recharts/types/polar/radar.d.ts", "../recharts/types/polar/radialbar.d.ts", "../recharts/types/cartesian/brush.d.ts", "../recharts/types/util/ifoverflowmatches.d.ts", "../recharts/types/cartesian/referenceline.d.ts", "../recharts/types/cartesian/referencedot.d.ts", "../recharts/types/cartesian/referencearea.d.ts", "../recharts/types/cartesian/cartesianaxis.d.ts", "../recharts/types/cartesian/cartesiangrid.d.ts", "../recharts/types/cartesian/line.d.ts", "../recharts/types/cartesian/area.d.ts", "../recharts/types/util/barutils.d.ts", "../recharts/types/cartesian/bar.d.ts", "../recharts/types/cartesian/zaxis.d.ts", "../recharts/types/cartesian/errorbar.d.ts", "../recharts/types/cartesian/scatter.d.ts", "../recharts/types/util/getlegendprops.d.ts", "../recharts/types/util/chartutils.d.ts", "../recharts/types/chart/accessibilitymanager.d.ts", "../recharts/types/chart/types.d.ts", "../recharts/types/chart/generatecategoricalchart.d.ts", "../recharts/types/chart/linechart.d.ts", "../recharts/types/chart/barchart.d.ts", "../recharts/types/chart/piechart.d.ts", "../recharts/types/chart/treemap.d.ts", "../recharts/types/chart/sankey.d.ts", "../recharts/types/chart/radarchart.d.ts", "../recharts/types/chart/scatterchart.d.ts", "../recharts/types/chart/areachart.d.ts", "../recharts/types/chart/radialbarchart.d.ts", "../recharts/types/chart/composedchart.d.ts", "../recharts/types/chart/sunburstchart.d.ts", "../recharts/types/shape/trapezoid.d.ts", "../recharts/types/numberaxis/funnel.d.ts", "../recharts/types/chart/funnelchart.d.ts", "../recharts/types/util/global.d.ts", "../recharts/types/index.d.ts", "../../client/src/components/earningsflowvisualization.tsx", "../../client/src/components/earningspopup.tsx", "../../client/src/components/earningsvisualization.tsx", "../../client/src/components/encryptedbrowser.tsx", "../../client/src/components/enhancedrealtimesync.tsx", "../../client/src/components/extensiondownload.tsx", "../../client/src/components/functioncallmonitor.tsx", "../../client/src/lib/communitynumentummultiplier.ts", "../../client/src/lib/iotenergyintegration.ts", "../../client/src/lib/biometricenergytracking.ts", "../../client/src/components/simplebatterymonitor.tsx", "../../client/src/components/inurtiadashboard.tsx", "../../client/src/components/livedatastream.tsx", "../../client/src/components/marketplacedatastream.tsx", "../../client/src/components/marketplacenavigation.tsx", "../../client/src/components/messagingdemo.tsx", "../../client/src/lib/realmobileapis.ts", "../../client/src/components/mobileenergydashboard.tsx", "../../client/src/components/mobilefirststatus.tsx", "../../client/src/components/multiusersync.tsx", "../../client/src/components/navigationheader.tsx", "../../client/src/components/networkvisualization.tsx", "../../client/src/lib/nu-core.ts", "../../client/src/lib/drain-bot.ts", "../../client/src/lib/nutshell-network.ts", "../../client/src/lib/ghost-bot.ts", "../../client/src/lib/iot-manager.ts", "../../client/src/lib/ipfs-storage.ts", "../../client/src/lib/meta-ai-orchestrator.ts", "../../client/src/lib/real-world-energy-api.ts", "../../client/src/components/nucore.tsx", "../../client/src/components/pagenavigation.tsx", "../../client/src/lib/spunder.ts", "../../client/src/lib/memvid.ts", "../../client/src/hooks/useinteractiontracking.ts", "../../client/src/components/privacycontrols.tsx", "../../client/src/components/realdatavalidator.tsx", "../../client/src/lib/hooks/use-real-time-device-manager.ts", "../../client/src/lib/hooks/usebattery.ts", "../../client/src/components/realdevicesync.tsx", "../../client/src/components/realtimesync.tsx", "../../client/src/components/safecomponent.tsx", "../../client/src/components/securemessaging.tsx", "../../client/src/components/simplemobilestatus.tsx", "../../client/src/components/simplespunderdashboard.tsx", "../../client/src/components/simpleumatterdisplay.tsx", "../../client/src/components/truvaluecard.tsx", "../../client/src/components/unifiedbatterymonitor.tsx", "../../client/src/components/walletwidget.tsx", "../../client/src/lib/websocket-manager.ts", "../../client/src/components/websocketstatus.tsx", "../../client/src/components/workingspunderdashboard.tsx", "../@radix-ui/react-collapsible/dist/index.d.mts", "../@radix-ui/react-accordion/dist/index.d.mts", "../../client/src/components/ui/accordion.tsx", "../@radix-ui/react-focus-scope/dist/index.d.mts", "../@radix-ui/react-alert-dialog/node_modules/@radix-ui/react-dialog/dist/index.d.mts", "../@radix-ui/react-alert-dialog/dist/index.d.mts", "../../client/src/components/ui/alert-dialog.tsx", "../../client/src/components/ui/alert.tsx", "../@radix-ui/react-aspect-ratio/dist/index.d.mts", "../../client/src/components/ui/aspect-ratio.tsx", "../@radix-ui/react-avatar/dist/index.d.mts", "../../client/src/components/ui/avatar.tsx", "../date-fns/locale/types.d.ts", "../date-fns/fp/types.d.ts", "../date-fns/types.d.ts", "../date-fns/add.d.ts", "../date-fns/addbusinessdays.d.ts", "../date-fns/adddays.d.ts", "../date-fns/addhours.d.ts", "../date-fns/addisoweekyears.d.ts", "../date-fns/addmilliseconds.d.ts", "../date-fns/addminutes.d.ts", "../date-fns/addmonths.d.ts", "../date-fns/addquarters.d.ts", "../date-fns/addseconds.d.ts", "../date-fns/addweeks.d.ts", "../date-fns/addyears.d.ts", "../date-fns/areintervalsoverlapping.d.ts", "../date-fns/clamp.d.ts", "../date-fns/closestindexto.d.ts", "../date-fns/closestto.d.ts", "../date-fns/compareasc.d.ts", "../date-fns/comparedesc.d.ts", "../date-fns/constructfrom.d.ts", "../date-fns/constructnow.d.ts", "../date-fns/daystoweeks.d.ts", "../date-fns/differenceinbusinessdays.d.ts", "../date-fns/differenceincalendardays.d.ts", "../date-fns/differenceincalendarisoweekyears.d.ts", "../date-fns/differenceincalendarisoweeks.d.ts", "../date-fns/differenceincalendarmonths.d.ts", "../date-fns/differenceincalendarquarters.d.ts", "../date-fns/differenceincalendarweeks.d.ts", "../date-fns/differenceincalendaryears.d.ts", "../date-fns/differenceindays.d.ts", "../date-fns/differenceinhours.d.ts", "../date-fns/differenceinisoweekyears.d.ts", "../date-fns/differenceinmilliseconds.d.ts", "../date-fns/differenceinminutes.d.ts", "../date-fns/differenceinmonths.d.ts", "../date-fns/differenceinquarters.d.ts", "../date-fns/differenceinseconds.d.ts", "../date-fns/differenceinweeks.d.ts", "../date-fns/differenceinyears.d.ts", "../date-fns/eachdayofinterval.d.ts", "../date-fns/eachhourofinterval.d.ts", "../date-fns/eachminuteofinterval.d.ts", "../date-fns/eachmonthofinterval.d.ts", "../date-fns/eachquarterofinterval.d.ts", "../date-fns/eachweekofinterval.d.ts", "../date-fns/eachweekendofinterval.d.ts", "../date-fns/eachweekendofmonth.d.ts", "../date-fns/eachweekendofyear.d.ts", "../date-fns/eachyearofinterval.d.ts", "../date-fns/endofday.d.ts", "../date-fns/endofdecade.d.ts", "../date-fns/endofhour.d.ts", "../date-fns/endofisoweek.d.ts", "../date-fns/endofisoweekyear.d.ts", "../date-fns/endofminute.d.ts", "../date-fns/endofmonth.d.ts", "../date-fns/endofquarter.d.ts", "../date-fns/endofsecond.d.ts", "../date-fns/endoftoday.d.ts", "../date-fns/endoftomorrow.d.ts", "../date-fns/endofweek.d.ts", "../date-fns/endofyear.d.ts", "../date-fns/endofyesterday.d.ts", "../date-fns/_lib/format/formatters.d.ts", "../date-fns/_lib/format/longformatters.d.ts", "../date-fns/format.d.ts", "../date-fns/formatdistance.d.ts", "../date-fns/formatdistancestrict.d.ts", "../date-fns/formatdistancetonow.d.ts", "../date-fns/formatdistancetonowstrict.d.ts", "../date-fns/formatduration.d.ts", "../date-fns/formatiso.d.ts", "../date-fns/formatiso9075.d.ts", "../date-fns/formatisoduration.d.ts", "../date-fns/formatrfc3339.d.ts", "../date-fns/formatrfc7231.d.ts", "../date-fns/formatrelative.d.ts", "../date-fns/fromunixtime.d.ts", "../date-fns/getdate.d.ts", "../date-fns/getday.d.ts", "../date-fns/getdayofyear.d.ts", "../date-fns/getdaysinmonth.d.ts", "../date-fns/getdaysinyear.d.ts", "../date-fns/getdecade.d.ts", "../date-fns/_lib/defaultoptions.d.ts", "../date-fns/getdefaultoptions.d.ts", "../date-fns/gethours.d.ts", "../date-fns/getisoday.d.ts", "../date-fns/getisoweek.d.ts", "../date-fns/getisoweekyear.d.ts", "../date-fns/getisoweeksinyear.d.ts", "../date-fns/getmilliseconds.d.ts", "../date-fns/getminutes.d.ts", "../date-fns/getmonth.d.ts", "../date-fns/getoverlappingdaysinintervals.d.ts", "../date-fns/getquarter.d.ts", "../date-fns/getseconds.d.ts", "../date-fns/gettime.d.ts", "../date-fns/getunixtime.d.ts", "../date-fns/getweek.d.ts", "../date-fns/getweekofmonth.d.ts", "../date-fns/getweekyear.d.ts", "../date-fns/getweeksinmonth.d.ts", "../date-fns/getyear.d.ts", "../date-fns/hourstomilliseconds.d.ts", "../date-fns/hourstominutes.d.ts", "../date-fns/hourstoseconds.d.ts", "../date-fns/interval.d.ts", "../date-fns/intervaltoduration.d.ts", "../date-fns/intlformat.d.ts", "../date-fns/intlformatdistance.d.ts", "../date-fns/isafter.d.ts", "../date-fns/isbefore.d.ts", "../date-fns/isdate.d.ts", "../date-fns/isequal.d.ts", "../date-fns/isexists.d.ts", "../date-fns/isfirstdayofmonth.d.ts", "../date-fns/isfriday.d.ts", "../date-fns/isfuture.d.ts", "../date-fns/islastdayofmonth.d.ts", "../date-fns/isleapyear.d.ts", "../date-fns/ismatch.d.ts", "../date-fns/ismonday.d.ts", "../date-fns/ispast.d.ts", "../date-fns/issameday.d.ts", "../date-fns/issamehour.d.ts", "../date-fns/issameisoweek.d.ts", "../date-fns/issameisoweekyear.d.ts", "../date-fns/issameminute.d.ts", "../date-fns/issamemonth.d.ts", "../date-fns/issamequarter.d.ts", "../date-fns/issamesecond.d.ts", "../date-fns/issameweek.d.ts", "../date-fns/issameyear.d.ts", "../date-fns/issaturday.d.ts", "../date-fns/issunday.d.ts", "../date-fns/isthishour.d.ts", "../date-fns/isthisisoweek.d.ts", "../date-fns/isthisminute.d.ts", "../date-fns/isthismonth.d.ts", "../date-fns/isthisquarter.d.ts", "../date-fns/isthissecond.d.ts", "../date-fns/isthisweek.d.ts", "../date-fns/isthisyear.d.ts", "../date-fns/isthursday.d.ts", "../date-fns/istoday.d.ts", "../date-fns/istomorrow.d.ts", "../date-fns/istuesday.d.ts", "../date-fns/isvalid.d.ts", "../date-fns/iswednesday.d.ts", "../date-fns/isweekend.d.ts", "../date-fns/iswithininterval.d.ts", "../date-fns/isyesterday.d.ts", "../date-fns/lastdayofdecade.d.ts", "../date-fns/lastdayofisoweek.d.ts", "../date-fns/lastdayofisoweekyear.d.ts", "../date-fns/lastdayofmonth.d.ts", "../date-fns/lastdayofquarter.d.ts", "../date-fns/lastdayofweek.d.ts", "../date-fns/lastdayofyear.d.ts", "../date-fns/_lib/format/lightformatters.d.ts", "../date-fns/lightformat.d.ts", "../date-fns/max.d.ts", "../date-fns/milliseconds.d.ts", "../date-fns/millisecondstohours.d.ts", "../date-fns/millisecondstominutes.d.ts", "../date-fns/millisecondstoseconds.d.ts", "../date-fns/min.d.ts", "../date-fns/minutestohours.d.ts", "../date-fns/minutestomilliseconds.d.ts", "../date-fns/minutestoseconds.d.ts", "../date-fns/monthstoquarters.d.ts", "../date-fns/monthstoyears.d.ts", "../date-fns/nextday.d.ts", "../date-fns/nextfriday.d.ts", "../date-fns/nextmonday.d.ts", "../date-fns/nextsaturday.d.ts", "../date-fns/nextsunday.d.ts", "../date-fns/nextthursday.d.ts", "../date-fns/nexttuesday.d.ts", "../date-fns/nextwednesday.d.ts", "../date-fns/parse/_lib/types.d.ts", "../date-fns/parse/_lib/setter.d.ts", "../date-fns/parse/_lib/parser.d.ts", "../date-fns/parse/_lib/parsers.d.ts", "../date-fns/parse.d.ts", "../date-fns/parseiso.d.ts", "../date-fns/parsejson.d.ts", "../date-fns/previousday.d.ts", "../date-fns/previousfriday.d.ts", "../date-fns/previousmonday.d.ts", "../date-fns/previoussaturday.d.ts", "../date-fns/previoussunday.d.ts", "../date-fns/previousthursday.d.ts", "../date-fns/previoustuesday.d.ts", "../date-fns/previouswednesday.d.ts", "../date-fns/quarterstomonths.d.ts", "../date-fns/quarterstoyears.d.ts", "../date-fns/roundtonearesthours.d.ts", "../date-fns/roundtonearestminutes.d.ts", "../date-fns/secondstohours.d.ts", "../date-fns/secondstomilliseconds.d.ts", "../date-fns/secondstominutes.d.ts", "../date-fns/set.d.ts", "../date-fns/setdate.d.ts", "../date-fns/setday.d.ts", "../date-fns/setdayofyear.d.ts", "../date-fns/setdefaultoptions.d.ts", "../date-fns/sethours.d.ts", "../date-fns/setisoday.d.ts", "../date-fns/setisoweek.d.ts", "../date-fns/setisoweekyear.d.ts", "../date-fns/setmilliseconds.d.ts", "../date-fns/setminutes.d.ts", "../date-fns/setmonth.d.ts", "../date-fns/setquarter.d.ts", "../date-fns/setseconds.d.ts", "../date-fns/setweek.d.ts", "../date-fns/setweekyear.d.ts", "../date-fns/setyear.d.ts", "../date-fns/startofday.d.ts", "../date-fns/startofdecade.d.ts", "../date-fns/startofhour.d.ts", "../date-fns/startofisoweek.d.ts", "../date-fns/startofisoweekyear.d.ts", "../date-fns/startofminute.d.ts", "../date-fns/startofmonth.d.ts", "../date-fns/startofquarter.d.ts", "../date-fns/startofsecond.d.ts", "../date-fns/startoftoday.d.ts", "../date-fns/startoftomorrow.d.ts", "../date-fns/startofweek.d.ts", "../date-fns/startofweekyear.d.ts", "../date-fns/startofyear.d.ts", "../date-fns/startofyesterday.d.ts", "../date-fns/sub.d.ts", "../date-fns/subbusinessdays.d.ts", "../date-fns/subdays.d.ts", "../date-fns/subhours.d.ts", "../date-fns/subisoweekyears.d.ts", "../date-fns/submilliseconds.d.ts", "../date-fns/subminutes.d.ts", "../date-fns/submonths.d.ts", "../date-fns/subquarters.d.ts", "../date-fns/subseconds.d.ts", "../date-fns/subweeks.d.ts", "../date-fns/subyears.d.ts", "../date-fns/todate.d.ts", "../date-fns/transpose.d.ts", "../date-fns/weekstodays.d.ts", "../date-fns/yearstodays.d.ts", "../date-fns/yearstomonths.d.ts", "../date-fns/yearstoquarters.d.ts", "../date-fns/index.d.mts", "../react-day-picker/dist/index.d.ts", "../../client/src/components/ui/calendar.tsx", "../embla-carousel/esm/components/alignment.d.ts", "../embla-carousel/esm/components/noderects.d.ts", "../embla-carousel/esm/components/axis.d.ts", "../embla-carousel/esm/components/slidestoscroll.d.ts", "../embla-carousel/esm/components/limit.d.ts", "../embla-carousel/esm/components/scrollcontain.d.ts", "../embla-carousel/esm/components/dragtracker.d.ts", "../embla-carousel/esm/components/utils.d.ts", "../embla-carousel/esm/components/animations.d.ts", "../embla-carousel/esm/components/counter.d.ts", "../embla-carousel/esm/components/eventhandler.d.ts", "../embla-carousel/esm/components/eventstore.d.ts", "../embla-carousel/esm/components/percentofview.d.ts", "../embla-carousel/esm/components/resizehandler.d.ts", "../embla-carousel/esm/components/vector1d.d.ts", "../embla-carousel/esm/components/scrollbody.d.ts", "../embla-carousel/esm/components/scrollbounds.d.ts", "../embla-carousel/esm/components/scrolllooper.d.ts", "../embla-carousel/esm/components/scrollprogress.d.ts", "../embla-carousel/esm/components/slideregistry.d.ts", "../embla-carousel/esm/components/scrolltarget.d.ts", "../embla-carousel/esm/components/scrollto.d.ts", "../embla-carousel/esm/components/slidefocus.d.ts", "../embla-carousel/esm/components/translate.d.ts", "../embla-carousel/esm/components/slidelooper.d.ts", "../embla-carousel/esm/components/slideshandler.d.ts", "../embla-carousel/esm/components/slidesinview.d.ts", "../embla-carousel/esm/components/engine.d.ts", "../embla-carousel/esm/components/optionshandler.d.ts", "../embla-carousel/esm/components/plugins.d.ts", "../embla-carousel/esm/components/emblacarousel.d.ts", "../embla-carousel/esm/components/draghandler.d.ts", "../embla-carousel/esm/components/options.d.ts", "../embla-carousel/esm/index.d.ts", "../embla-carousel-react/esm/components/useemblacarousel.d.ts", "../embla-carousel-react/esm/index.d.ts", "../../client/src/components/ui/carousel.tsx", "../../client/src/components/ui/chart.tsx", "../@radix-ui/react-checkbox/dist/index.d.mts", "../../client/src/components/ui/checkbox.tsx", "../../client/src/components/ui/collapsible.tsx", "../cmdk/dist/index.d.ts", "../../client/src/components/ui/command.tsx", "../@radix-ui/react-menu/dist/index.d.mts", "../@radix-ui/react-context-menu/dist/index.d.mts", "../../client/src/components/ui/context-menu.tsx", "../vaul/dist/index.d.mts", "../../client/src/components/ui/drawer.tsx", "../@radix-ui/react-dropdown-menu/dist/index.d.mts", "../../client/src/components/ui/dropdown-menu.tsx", "../react-hook-form/dist/constants.d.ts", "../react-hook-form/dist/utils/createsubject.d.ts", "../react-hook-form/dist/types/events.d.ts", "../react-hook-form/dist/types/path/common.d.ts", "../react-hook-form/dist/types/path/eager.d.ts", "../react-hook-form/dist/types/path/index.d.ts", "../react-hook-form/dist/types/fieldarray.d.ts", "../react-hook-form/dist/types/resolvers.d.ts", "../react-hook-form/dist/types/form.d.ts", "../react-hook-form/dist/types/utils.d.ts", "../react-hook-form/dist/types/fields.d.ts", "../react-hook-form/dist/types/errors.d.ts", "../react-hook-form/dist/types/validator.d.ts", "../react-hook-form/dist/types/controller.d.ts", "../react-hook-form/dist/types/index.d.ts", "../react-hook-form/dist/controller.d.ts", "../react-hook-form/dist/form.d.ts", "../react-hook-form/dist/logic/appenderrors.d.ts", "../react-hook-form/dist/logic/createformcontrol.d.ts", "../react-hook-form/dist/logic/index.d.ts", "../react-hook-form/dist/usecontroller.d.ts", "../react-hook-form/dist/usefieldarray.d.ts", "../react-hook-form/dist/useform.d.ts", "../react-hook-form/dist/useformcontext.d.ts", "../react-hook-form/dist/useformstate.d.ts", "../react-hook-form/dist/usewatch.d.ts", "../react-hook-form/dist/utils/get.d.ts", "../react-hook-form/dist/utils/set.d.ts", "../react-hook-form/dist/utils/index.d.ts", "../react-hook-form/dist/index.d.ts", "../../client/src/components/ui/form.tsx", "../@radix-ui/react-hover-card/dist/index.d.mts", "../../client/src/components/ui/hover-card.tsx", "../input-otp/dist/index.d.ts", "../../client/src/components/ui/input-otp.tsx", "../@radix-ui/react-menubar/dist/index.d.mts", "../../client/src/components/ui/menubar.tsx", "../@radix-ui/react-visually-hidden/dist/index.d.mts", "../@radix-ui/react-navigation-menu/dist/index.d.mts", "../../client/src/components/ui/navigation-menu.tsx", "../../client/src/components/ui/navigation.tsx", "../../client/src/components/ui/pagination.tsx", "../@radix-ui/react-popover/dist/index.d.mts", "../../client/src/components/ui/popover.tsx", "../@radix-ui/react-radio-group/dist/index.d.mts", "../../client/src/components/ui/radio-group.tsx", "../react-resizable-panels/dist/declarations/src/vendor/react.d.ts", "../react-resizable-panels/dist/declarations/src/panel.d.ts", "../react-resizable-panels/dist/declarations/src/types.d.ts", "../react-resizable-panels/dist/declarations/src/panelgroup.d.ts", "../react-resizable-panels/dist/declarations/src/panelresizehandleregistry.d.ts", "../react-resizable-panels/dist/declarations/src/panelresizehandle.d.ts", "../react-resizable-panels/dist/declarations/src/utils/assert.d.ts", "../react-resizable-panels/dist/declarations/src/utils/csp.d.ts", "../react-resizable-panels/dist/declarations/src/utils/cursor.d.ts", "../react-resizable-panels/dist/declarations/src/utils/dom/getpanelelement.d.ts", "../react-resizable-panels/dist/declarations/src/utils/dom/getpanelelementsforgroup.d.ts", "../react-resizable-panels/dist/declarations/src/utils/dom/getpanelgroupelement.d.ts", "../react-resizable-panels/dist/declarations/src/utils/dom/getresizehandleelement.d.ts", "../react-resizable-panels/dist/declarations/src/utils/dom/getresizehandleelementindex.d.ts", "../react-resizable-panels/dist/declarations/src/utils/dom/getresizehandleelementsforgroup.d.ts", "../react-resizable-panels/dist/declarations/src/utils/dom/getresizehandlepanelids.d.ts", "../react-resizable-panels/dist/declarations/src/utils/rects/types.d.ts", "../react-resizable-panels/dist/declarations/src/utils/rects/getintersectingrectangle.d.ts", "../react-resizable-panels/dist/declarations/src/utils/rects/intersects.d.ts", "../react-resizable-panels/dist/declarations/src/index.d.ts", "../react-resizable-panels/dist/react-resizable-panels.cjs.d.mts", "../../client/src/components/ui/resizable.tsx", "../@radix-ui/react-select/dist/index.d.mts", "../../client/src/components/ui/select.tsx", "../../client/src/hooks/use-mobile.tsx", "../../client/src/components/ui/skeleton.tsx", "../../client/src/components/ui/sidebar.tsx", "../@radix-ui/react-slider/dist/index.d.mts", "../../client/src/components/ui/slider.tsx", "../../client/src/components/ui/table.tsx", "../@radix-ui/react-toggle/dist/index.d.mts", "../@radix-ui/react-toggle-group/dist/index.d.mts", "../../client/src/components/ui/toggle.tsx", "../../client/src/components/ui/toggle-group.tsx", "../../client/src/hooks/useearningsopportunities.ts", "../../client/src/lib/real-hardware-energy-measurement.ts", "../../client/src/hooks/userealpowermeasurement.ts", "../../client/src/hooks/useunifiedrealtimedata.ts", "../../client/src/lib/advanced-spunder-bot.ts", "../../client/src/lib/apirequest.ts", "../../client/src/lib/authutils.ts", "../../client/src/lib/authenticbiometrics.ts", "../../client/src/lib/authenticdevicedetection.ts", "../../client/src/lib/deviceintegration.ts", "../../client/src/lib/energy-batch-manager.ts", "../../client/src/lib/energycalculations.ts", "../../client/src/lib/extension-reliability.ts", "../../client/src/lib/hashbot.ts", "../../client/src/lib/marketplaceauth.ts", "../../client/src/lib/nuos-social-control.ts", "../../client/src/lib/performance-optimizer.ts", "../../client/src/lib/optimized-api-client.ts", "../../client/src/lib/push-notification-service.ts", "../../client/src/lib/real-device-manager.ts", "../../client/src/lib/real-energy-measurement.ts", "../../client/src/lib/real-network-scanner.ts", "../../client/src/lib/real-spunder-system.ts", "../../client/src/lib/realenergycalculations.ts", "../../client/src/lib/realtimeenergysync.ts", "../../client/src/lib/realtimenumentum.ts", "../../client/src/lib/realtimesync.ts", "../../client/src/lib/realtimesyncmanager.ts", "../../client/src/lib/sbu-token-manager.ts", "../../client/src/lib/spunder-bot.ts", "../../client/src/lib/spunder-integration.ts", "../../client/src/lib/wallet-fix.ts", "../../client/src/lib/system-initializer.ts", "../../client/src/pages/energyflow.tsx", "../../client/src/pages/extensionpage.tsx", "../../shared/marketplace-schema.ts", "../@types/node/globals.typedarray.d.ts", "../@types/node/buffer.buffer.d.ts", "../@types/node/assert.d.ts", "../@types/node/assert/strict.d.ts", "../buffer/index.d.ts", "../undici-types/header.d.ts", "../undici-types/readable.d.ts", "../undici-types/file.d.ts", "../undici-types/fetch.d.ts", "../undici-types/formdata.d.ts", "../undici-types/connector.d.ts", "../undici-types/client.d.ts", "../undici-types/errors.d.ts", "../undici-types/dispatcher.d.ts", "../undici-types/global-dispatcher.d.ts", "../undici-types/global-origin.d.ts", "../undici-types/pool-stats.d.ts", "../undici-types/pool.d.ts", "../undici-types/handlers.d.ts", "../undici-types/balanced-pool.d.ts", "../undici-types/agent.d.ts", "../undici-types/mock-interceptor.d.ts", "../undici-types/mock-agent.d.ts", "../undici-types/mock-client.d.ts", "../undici-types/mock-pool.d.ts", "../undici-types/mock-errors.d.ts", "../undici-types/proxy-agent.d.ts", "../undici-types/env-http-proxy-agent.d.ts", "../undici-types/retry-handler.d.ts", "../undici-types/retry-agent.d.ts", "../undici-types/api.d.ts", "../undici-types/interceptors.d.ts", "../undici-types/util.d.ts", "../undici-types/cookies.d.ts", "../undici-types/patch.d.ts", "../undici-types/websocket.d.ts", "../undici-types/eventsource.d.ts", "../undici-types/filereader.d.ts", "../undici-types/diagnostics-channel.d.ts", "../undici-types/content-type.d.ts", "../undici-types/cache.d.ts", "../undici-types/index.d.ts", "../@types/node/globals.d.ts", "../@types/node/async_hooks.d.ts", "../@types/node/buffer.d.ts", "../@types/node/child_process.d.ts", "../@types/node/cluster.d.ts", "../@types/node/console.d.ts", "../@types/node/constants.d.ts", "../@types/node/crypto.d.ts", "../@types/node/dgram.d.ts", "../@types/node/diagnostics_channel.d.ts", "../@types/node/dns.d.ts", "../@types/node/dns/promises.d.ts", "../@types/node/domain.d.ts", "../@types/node/dom-events.d.ts", "../@types/node/events.d.ts", "../@types/node/fs.d.ts", "../@types/node/fs/promises.d.ts", "../@types/node/http.d.ts", "../@types/node/http2.d.ts", "../@types/node/https.d.ts", "../@types/node/inspector.d.ts", "../@types/node/module.d.ts", "../@types/node/net.d.ts", "../@types/node/os.d.ts", "../@types/node/path.d.ts", "../@types/node/perf_hooks.d.ts", "../@types/node/process.d.ts", "../@types/node/punycode.d.ts", "../@types/node/querystring.d.ts", "../@types/node/readline.d.ts", "../@types/node/readline/promises.d.ts", "../@types/node/repl.d.ts", "../@types/node/sea.d.ts", "../@types/node/stream.d.ts", "../@types/node/stream/promises.d.ts", "../@types/node/stream/consumers.d.ts", "../@types/node/stream/web.d.ts", "../@types/node/string_decoder.d.ts", "../@types/node/test.d.ts", "../@types/node/timers.d.ts", "../@types/node/timers/promises.d.ts", "../@types/node/tls.d.ts", "../@types/node/trace_events.d.ts", "../@types/node/tty.d.ts", "../@types/node/url.d.ts", "../@types/node/util.d.ts", "../@types/node/v8.d.ts", "../@types/node/vm.d.ts", "../@types/node/wasi.d.ts", "../@types/node/worker_threads.d.ts", "../@types/node/zlib.d.ts", "../@types/node/globals.global.d.ts", "../@types/node/index.d.ts", "../@types/mime/index.d.ts", "../@types/send/index.d.ts", "../@types/qs/index.d.ts", "../@types/range-parser/index.d.ts", "../@types/express-serve-static-core/index.d.ts", "../@types/http-errors/index.d.ts", "../@types/serve-static/index.d.ts", "../@types/connect/index.d.ts", "../@types/body-parser/index.d.ts", "../@types/express/index.d.ts", "../postgres/types/index.d.ts", "../drizzle-orm/postgres-js/session.d.ts", "../drizzle-orm/postgres-js/driver.d.ts", "../drizzle-orm/postgres-js/index.d.ts", "../../server/db.ts", "../../server/ai-analysis-routes.ts", "../../server/ai-integrations.ts", "../../server/replitauth.ts", "../../server/ai-search-routes.ts", "../@types/ms/index.d.ts", "../@types/jsonwebtoken/index.d.ts", "../@types/bcrypt/index.d.ts", "../nanoid/index.d.ts", "../../server/storage.ts", "../../server/auth.ts", "../@types/ws/index.d.mts", "../../server/websocket.ts", "../../server/biometric-routes.ts", "../../server/data-monetization-routes.ts", "../../server/download-routes.ts", "../../server/earnings-api.ts", "../@sendgrid/helpers/classes/attachment.d.ts", "../@sendgrid/helpers/classes/email-address.d.ts", "../@sendgrid/helpers/classes/personalization.d.ts", "../@sendgrid/helpers/classes/mail.d.ts", "../@sendgrid/helpers/classes/response.d.ts", "../@sendgrid/helpers/classes/response-error.d.ts", "../@sendgrid/helpers/classes/index.d.ts", "../@sendgrid/helpers/classes/request.d.ts", "../@sendgrid/client/src/request.d.ts", "../@sendgrid/client/src/response.d.ts", "../@sendgrid/client/src/client.d.ts", "../@sendgrid/client/index.d.ts", "../@sendgrid/mail/src/mail.d.ts", "../@sendgrid/mail/index.d.ts", "../../server/email-service.ts", "../../server/storage-interface.ts", "../../server/real-market-data.ts", "../../server/energy-banking-routes.ts", "../../server/energy-hub-routes.ts", "../../server/real-data-connector.ts", "../../server/energy-storage.ts", "../../server/extension-bridge.ts", "../../server/extension-package.ts", "../../server/spunder.ts", "../../server/real-device-messenger.ts", "../../server/memvid-processor.ts", "../../server/nuphysics.ts", "../../server/numentum-processor.ts", "../../server/nquf-client-2.0.ts", "../../server/nqe-processor.ts", "../../server/wallet-routes.ts", "../../server/trading-routes.ts", "../../server/marketplace-routes.ts", "../../server/social-routes.ts", "../../server/network-routes.ts", "../../server/iot-routes.ts", "../../server/real-notification-service.ts", "../../server/lib/request-validation.ts", "../../server/routes/mobile-sync.ts", "../../server/routes.ts", "../../server/routes/real-balance-fix.ts", "../../server/routes/marketplace-functional.ts", "../../server/real-hardware-energy-route.ts", "../@types/estree/index.d.ts", "../rollup/dist/rollup.d.ts", "../rollup/dist/parseast.d.ts", "../vite/types/hmrpayload.d.ts", "../vite/types/customevent.d.ts", "../vite/types/hot.d.ts", "../vite/dist/node/types.d-agj9qkwt.d.ts", "../vite/node_modules/esbuild/lib/main.d.ts", "../source-map-js/source-map.d.ts", "../postcss/lib/previous-map.d.ts", "../postcss/lib/input.d.ts", "../postcss/lib/css-syntax-error.d.ts", "../postcss/lib/declaration.d.ts", "../postcss/lib/root.d.ts", "../postcss/lib/warning.d.ts", "../postcss/lib/lazy-result.d.ts", "../postcss/lib/no-work-result.d.ts", "../postcss/lib/processor.d.ts", "../postcss/lib/result.d.ts", "../postcss/lib/document.d.ts", "../postcss/lib/rule.d.ts", "../postcss/lib/node.d.ts", "../postcss/lib/comment.d.ts", "../postcss/lib/container.d.ts", "../postcss/lib/at-rule.d.ts", "../postcss/lib/list.d.ts", "../postcss/lib/postcss.d.ts", "../postcss/lib/postcss.d.mts", "../vite/dist/node/runtime.d.ts", "../vite/types/importglob.d.ts", "../vite/types/metadata.d.ts", "../vite/dist/node/index.d.ts", "../@babel/types/lib/index.d.ts", "../@types/babel__generator/index.d.ts", "../@babel/parser/typings/babel-parser.d.ts", "../@types/babel__template/index.d.ts", "../@types/babel__traverse/index.d.ts", "../@types/babel__core/index.d.ts", "../@vitejs/plugin-react/dist/index.d.mts", "../@replit/vite-plugin-runtime-error-modal/dist/index.d.mts", "../@replit/vite-plugin-cartographer/dist/index.d.mts", "../../vite.config.ts", "../../server/vite.ts", "../../server/index.ts", "../../server/logger.ts", "../../server/market-data.ts", "../../server/marketplace-controller.ts", "../../server/middleware.ts", "../../server/minimal-energy-system.ts", "../../server/production-config.ts", "../../server/real-power-measurement-api.ts", "../../server/smart-contract.ts", "../../server/spunder-system-manager.ts", "../../server/trading-engine.ts", "../vite/types/importmeta.d.ts", "../vite/client.d.ts"], "fileIdsList": [[1053, 1096], [53, 58, 87, 88, 101, 107, 111, 373, 375, 403, 421, 422, 423, 441, 442, 443, 444, 445, 447, 448, 456, 459, 460, 461, 462, 463, 466, 473, 474, 475, 476, 477, 478, 479, 1053, 1096, 1276], [52, 53, 87, 96, 108, 399, 486, 1053, 1096], [52, 53, 96, 108, 110, 374, 1053, 1096], [52, 53, 87, 96, 108, 110, 1053, 1096], [52, 53, 87, 88, 96, 108, 110, 374, 406, 432, 446, 1053, 1096], [53, 96, 108, 374, 386, 390, 1053, 1096], [52, 53, 96, 108, 374, 386, 489, 1053, 1096], [52, 53, 108, 1053, 1096], [52, 53, 87, 88, 96, 100, 108, 110, 374, 1053, 1096], [52, 53, 87, 108, 562, 1053, 1096], [52, 53, 87, 88, 96, 100, 108, 110, 374, 452, 1053, 1096], [52, 53, 96, 108, 374, 1053, 1096], [52, 53, 96, 108, 110, 374, 446, 1053, 1096], [52, 53, 96, 108, 374, 386, 387, 432, 433, 435, 1053, 1096], [52, 53, 96, 108, 374, 386, 1053, 1096], [52, 53, 96, 108, 374, 386, 389, 395, 432, 1053, 1096], [52, 53, 96, 108, 110, 1053, 1096], [52, 53, 87, 88, 96, 108, 110, 374, 386, 432, 570, 571, 572, 573, 1053, 1096], [52, 53, 96, 100, 108, 110, 374, 386, 406, 419, 432, 433, 438, 1053, 1096], [52, 53, 96, 108, 110, 374, 399, 400, 1053, 1096], [52, 53, 87, 96, 108, 110, 374, 386, 1053, 1096], [52, 53, 96, 108, 110, 374, 386, 1053, 1096], [53, 58, 96, 110, 374, 1053, 1096], [52, 53, 96, 108, 110, 374, 386, 579, 1053, 1096], [52, 53, 467, 1053, 1096], [52, 53, 96, 108, 110, 374, 386, 432, 470, 471, 1053, 1096], [52, 53, 58, 96, 110, 374, 381, 1053, 1096], [52, 53, 87, 88, 96, 100, 108, 110, 373, 374, 446, 1053, 1096], [52, 53, 87, 96, 108, 110, 374, 432, 1053, 1096], [52, 53, 96, 108, 110, 374, 386, 585, 588, 589, 590, 591, 592, 1053, 1096], [52, 53, 382, 383, 1053, 1096], [52, 53, 58, 96, 110, 1053, 1096], [52, 53, 87, 88, 96, 100, 108, 110, 374, 432, 438, 450, 597, 1053, 1096], [52, 53, 96, 108, 110, 374, 432, 1053, 1096], [52, 53, 96, 108, 110, 374, 432, 600, 601, 1053, 1096], [52, 53, 96, 108, 374, 386, 395, 1053, 1096], [52, 53, 87, 96, 108, 110, 374, 1053, 1096], [52, 53, 96, 108, 374, 386, 388, 1053, 1096], [52, 53, 96, 108, 1053, 1096], [53, 87, 96, 108, 374, 1053, 1096], [52, 53, 87, 88, 96, 100, 108, 110, 374, 432, 446, 450, 1053, 1096], [52, 53, 96, 98, 616, 1053, 1096], [52, 53, 98, 110, 620, 1053, 1096], [52, 53, 95, 98, 1053, 1096], [53, 623, 1053, 1096], [52, 53, 98, 625, 1053, 1096], [52, 53, 96, 98, 109, 1053, 1096], [52, 53, 95, 98, 109, 1053, 1096], [52, 53, 96, 98, 110, 884, 1053, 1096], [52, 53, 98, 1053, 1096], [52, 53, 96, 98, 110, 921, 1053, 1096], [52, 53, 98, 562, 1053, 1096], [52, 53, 96, 98, 924, 1053, 1096], [53, 615, 1053, 1096], [52, 53, 96, 98, 380, 452, 927, 1053, 1096], [52, 53, 96, 98, 930, 1053, 1096], [52, 53, 96, 98, 380, 1053, 1096], [52, 53, 98, 932, 1053, 1096], [52, 53, 96, 98, 934, 1053, 1096], [52, 53, 98, 109, 449, 450, 965, 1053, 1096], [52, 53, 98, 967, 1053, 1096], [52, 53, 96, 98, 969, 1053, 1096], [52, 53, 95, 98, 449, 1053, 1096], [52, 53, 96, 98, 971, 1053, 1096], [52, 53, 95, 96, 98, 974, 1053, 1096], [53, 58, 96, 98, 1053, 1096], [52, 53, 96, 98, 110, 1053, 1096], [52, 53, 98, 978, 1053, 1096], [52, 53, 98, 385, 1053, 1096], [52, 53, 96, 98, 980, 1053, 1096], [53, 96, 98, 1002, 1053, 1096], [52, 53, 98, 398, 1053, 1096], [52, 53, 96, 98, 1004, 1053, 1096], [52, 53, 98, 457, 1053, 1096], [52, 53, 95, 96, 98, 380, 1053, 1096], [52, 53, 95, 96, 98, 107, 109, 110, 381, 446, 458, 1006, 1007, 1053, 1096], [53, 98, 1053, 1096], [52, 53, 98, 1009, 1053, 1096], [52, 53, 98, 437, 1053, 1096], [52, 53, 98, 405, 1053, 1096], [52, 53, 92, 95, 96, 98, 1053, 1096], [53, 99, 100, 1053, 1096], [52, 53, 95, 98, 1013, 1014, 1053, 1096], [52, 53, 95, 98, 1012, 1053, 1096], [52, 53, 98, 106, 1053, 1096], [52, 53, 96, 108, 374, 386, 395, 416, 1053, 1096], [52, 53, 96, 108, 374, 386, 390, 395, 396, 1053, 1096], [52, 53, 96, 108, 110, 374, 452, 1053, 1096], [52, 53, 58, 87, 96, 108, 110, 374, 1053, 1096], [52, 53, 96, 108, 110, 374, 416, 432, 612, 1053, 1096], [52, 53, 96, 108, 110, 374, 406, 438, 450, 1053, 1096], [52, 53, 1053, 1096], [52, 53, 99, 1053, 1096], [53, 87, 372, 1053, 1096], [52, 53, 387, 389, 1053, 1096], [52, 53, 373, 1053, 1096], [52, 53, 373, 595, 596, 1053, 1096], [52, 53, 393, 469, 1053, 1096], [52, 53, 1017, 1053, 1096], [52, 53, 387, 388, 389, 393, 394, 1053, 1096], [52, 53, 395, 416, 1053, 1096], [53, 1053, 1096], [53, 388, 1053, 1096], [53, 387, 391, 392, 1053, 1096], [53, 586, 587, 1053, 1096], [52, 53, 435, 1053, 1096], [53, 393, 1053, 1096], [53, 1032, 1053, 1096], [53, 87, 1053, 1096], [53, 389, 1053, 1096], [53, 88, 1053, 1096], [53, 434, 1053, 1096], [53, 572, 1053, 1096], [53, 1020, 1045, 1053, 1096], [53, 409, 415, 1053, 1096], [53, 393, 585, 588, 589, 590, 591, 592, 1020, 1031, 1044, 1045, 1046, 1047, 1053, 1096], [53, 93, 97, 1053, 1096], [52, 53, 392, 480, 481, 482, 1053, 1096, 1276], [52, 53, 96, 108, 110, 374, 384, 406, 446, 1053, 1096], [52, 53, 87, 96, 108, 110, 374, 384, 397, 401, 402, 1053, 1096], [52, 53, 87, 96, 108, 110, 374, 384, 406, 1053, 1096], [52, 53, 96, 108, 110, 374, 384, 386, 406, 1053, 1096], [52, 53, 87, 88, 96, 100, 108, 110, 374, 384, 406, 1053, 1096], [53, 384, 419, 1053, 1096], [52, 53, 87, 96, 108, 110, 374, 384, 386, 406, 417, 418, 419, 420, 1053, 1096], [52, 53, 87, 88, 96, 100, 108, 110, 374, 384, 386, 406, 417, 420, 432, 433, 436, 439, 440, 1053, 1096], [52, 53, 87, 96, 108, 110, 374, 384, 386, 1053, 1096], [53, 96, 108, 374, 568, 1053, 1096], [52, 53, 87, 96, 108, 110, 374, 384, 406, 464, 465, 1053, 1096], [52, 53, 58, 96, 100, 108, 110, 374, 458, 1053, 1096], [52, 53, 58, 96, 108, 110, 374, 1053, 1096], [52, 53, 96, 108, 110, 374, 384, 396, 446, 1053, 1096], [52, 53, 96, 108, 374, 432, 468, 472, 1053, 1096], [53, 58, 96, 110, 1053, 1096], [52, 53, 96, 108, 110, 374, 384, 406, 1053, 1096], [52, 53, 96, 100, 108, 110, 374, 406, 446, 450, 451, 452, 453, 454, 455, 1053, 1096], [52, 53, 96, 108, 110, 374, 384, 396, 1053, 1096], [1053, 1096, 1253], [52, 89, 90, 615, 1053, 1096], [52, 89, 619, 1053, 1096], [52, 89, 90, 91, 105, 618, 1053, 1096], [52, 90, 1053, 1096], [52, 89, 90, 1053, 1096], [52, 89, 90, 929, 1053, 1096], [52, 1053, 1096], [52, 89, 376, 377, 378, 379, 1053, 1096], [52, 376, 1053, 1096], [52, 89, 90, 91, 104, 105, 1053, 1096], [52, 89, 90, 91, 104, 105, 404, 618, 1053, 1096], [52, 53, 89, 90, 404, 929, 1053, 1096], [52, 89, 90, 91, 973, 1053, 1096], [52, 89, 90, 91, 104, 105, 618, 1053, 1096], [52, 89, 90, 102, 103, 1053, 1096], [52, 89, 90, 404, 1053, 1096], [52, 89, 90, 91, 1053, 1096], [52, 89, 90, 404, 1012, 1053, 1096], [1053, 1096, 1252], [1053, 1096, 1188], [1053, 1096, 1184, 1186, 1187], [1053, 1096, 1185], [1053, 1096, 1182], [1053, 1096, 1178, 1179, 1180, 1181, 1182, 1183], [1053, 1096, 1178, 1179, 1180], [1053, 1096, 1179], [1053, 1096, 1113], [1053, 1096, 1190], [1053, 1096, 1181, 1184, 1187, 1189], [60, 1053, 1096], [59, 60, 1053, 1096], [59, 60, 61, 62, 63, 64, 65, 66, 67, 1053, 1096], [59, 60, 61, 1053, 1096], [52, 68, 1053, 1096], [52, 53, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 1053, 1096], [68, 69, 1053, 1096], [68, 1053, 1096], [68, 69, 78, 1053, 1096], [68, 69, 71, 1053, 1096], [1053, 1096, 1253, 1254, 1255, 1256, 1257], [1053, 1096, 1253, 1255], [1053, 1096, 1146], [1053, 1096, 1111, 1146, 1154], [1053, 1096, 1111, 1146], [495, 1053, 1096], [513, 1053, 1096], [1053, 1096, 1108, 1111, 1146, 1148, 1149, 1150], [1053, 1096, 1149, 1151, 1153, 1155], [1053, 1096, 1101, 1146, 1166], [1053, 1054, 1096], [1053, 1095, 1096], [1096], [1053, 1096, 1101, 1130], [1053, 1096, 1097, 1102, 1108, 1109, 1116, 1127, 1138], [1053, 1096, 1097, 1098, 1108, 1116], [1053, 1096, 1099, 1139], [1053, 1096, 1100, 1101, 1109, 1117], [1053, 1096, 1101, 1127, 1135], [1053, 1096, 1102, 1104, 1108, 1116], [1053, 1095, 1096, 1103], [1053, 1096, 1104, 1105], [1053, 1096, 1108], [1053, 1096, 1106, 1108], [1053, 1095, 1096, 1108], [1053, 1096, 1108, 1109, 1110, 1127, 1138], [1053, 1096, 1108, 1109, 1110, 1123, 1127, 1130], [1053, 1093, 1096, 1143], [1053, 1096, 1104, 1108, 1111, 1116, 1127, 1138], [1053, 1096, 1108, 1109, 1111, 1112, 1116, 1127, 1135, 1138], [1053, 1096, 1111, 1113, 1127, 1135, 1138], [1052, 1053, 1054, 1055, 1094, 1095, 1096, 1097, 1098, 1099, 1100, 1101, 1102, 1103, 1104, 1105, 1106, 1107, 1108, 1109, 1110, 1111, 1112, 1113, 1114, 1115, 1116, 1117, 1118, 1119, 1120, 1121, 1122, 1123, 1124, 1125, 1126, 1127, 1128, 1129, 1130, 1131, 1132, 1133, 1134, 1135, 1136, 1137, 1138, 1139, 1140, 1141, 1142, 1143, 1144, 1145], [1053, 1096, 1108, 1114], [1053, 1096, 1115, 1138, 1143], [1053, 1096, 1104, 1108, 1116, 1127], [1053, 1096, 1117], [1053, 1096, 1118], [1053, 1095, 1096, 1119], [1053, 1054, 1055, 1095, 1096, 1097, 1098, 1099, 1100, 1101, 1102, 1103, 1104, 1105, 1106, 1108, 1109, 1110, 1111, 1112, 1113, 1114, 1115, 1116, 1117, 1118, 1119, 1120, 1121, 1122, 1123, 1124, 1125, 1126, 1127, 1128, 1129, 1130, 1131, 1132, 1133, 1134, 1135, 1136, 1137, 1138, 1139, 1140, 1141, 1142, 1143, 1144], [1053, 1096, 1121], [1053, 1096, 1122], [1053, 1096, 1108, 1123, 1124], [1053, 1096, 1123, 1125, 1139, 1141], [1053, 1096, 1108, 1127, 1128, 1129, 1130], [1053, 1096, 1127, 1129], [1053, 1096, 1127, 1128], [1053, 1096, 1130], [1053, 1096, 1131], [1053, 1054, 1096, 1127], [1053, 1096, 1108, 1133, 1134], [1053, 1096, 1133, 1134], [1053, 1096, 1101, 1116, 1127, 1135], [1053, 1096, 1136], [1053, 1096, 1116, 1137], [1053, 1096, 1111, 1122, 1138], [1053, 1096, 1101, 1139], [1053, 1096, 1127, 1140], [1053, 1096, 1115, 1141], [1053, 1096, 1142], [1053, 1096, 1101, 1108, 1110, 1119, 1127, 1138, 1141, 1143], [1053, 1096, 1127, 1144], [49, 50, 51, 1053, 1096], [1053, 1096, 1109, 1127, 1146, 1147], [1053, 1096, 1111, 1146, 1148, 1152], [1053, 1096, 1108, 1111, 1113, 1116, 1127, 1135, 1138, 1144, 1146], [1053, 1096, 1252, 1258], [93, 94, 1053, 1096], [93, 1053, 1096], [52, 380, 1053, 1096], [629, 1053, 1096], [627, 629, 1053, 1096], [627, 1053, 1096], [629, 693, 694, 1053, 1096], [696, 1053, 1096], [697, 1053, 1096], [714, 1053, 1096], [629, 630, 631, 632, 633, 634, 635, 636, 637, 638, 639, 640, 641, 642, 643, 644, 645, 646, 647, 648, 649, 650, 651, 652, 653, 654, 655, 656, 657, 658, 659, 660, 661, 662, 663, 664, 665, 666, 667, 668, 669, 670, 671, 672, 673, 674, 675, 676, 677, 678, 679, 680, 681, 682, 683, 684, 685, 686, 687, 688, 689, 690, 691, 692, 695, 696, 697, 698, 699, 700, 701, 702, 703, 704, 705, 706, 707, 708, 709, 710, 711, 712, 713, 715, 716, 717, 718, 719, 720, 721, 722, 723, 724, 725, 726, 727, 728, 729, 730, 731, 732, 733, 734, 735, 736, 737, 738, 739, 740, 741, 742, 743, 744, 745, 746, 747, 748, 749, 750, 751, 752, 753, 754, 755, 756, 757, 758, 759, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 776, 777, 778, 779, 780, 781, 782, 783, 784, 785, 786, 787, 788, 789, 791, 792, 793, 794, 795, 796, 797, 798, 799, 800, 801, 802, 803, 804, 805, 806, 807, 808, 809, 810, 815, 816, 817, 818, 819, 820, 821, 822, 823, 824, 825, 826, 827, 828, 829, 830, 831, 832, 833, 834, 835, 836, 837, 838, 839, 840, 841, 842, 843, 844, 845, 846, 847, 848, 849, 850, 851, 852, 853, 854, 855, 856, 857, 858, 859, 860, 861, 862, 863, 864, 865, 866, 867, 868, 869, 870, 871, 872, 873, 874, 875, 876, 877, 878, 879, 880, 881, 882, 1053, 1096], [790, 1053, 1096], [629, 694, 814, 1053, 1096], [627, 811, 812, 1053, 1096], [813, 1053, 1096], [811, 1053, 1096], [627, 628, 1053, 1096], [112, 118, 119, 163, 278, 1053, 1096], [112, 114, 278, 1053, 1096], [112, 114, 118, 190, 241, 276, 278, 350, 1053, 1096], [112, 114, 118, 119, 277, 1053, 1096], [112, 1053, 1096], [156, 1053, 1096], [112, 113, 114, 116, 119, 160, 162, 163, 165, 185, 186, 187, 277, 278, 279, 1053, 1096], [149, 169, 182, 1053, 1096], [112, 118, 149, 1053, 1096], [121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 140, 141, 142, 143, 144, 152, 1053, 1096], [112, 151, 277, 278, 1053, 1096], [112, 114, 151, 277, 278, 1053, 1096], [112, 114, 118, 149, 150, 277, 278, 1053, 1096], [112, 114, 118, 149, 151, 277, 278, 1053, 1096], [112, 114, 149, 151, 277, 278, 1053, 1096], [121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 140, 141, 142, 143, 144, 151, 152, 1053, 1096], [112, 131, 151, 277, 278, 1053, 1096], [112, 114, 139, 277, 278, 1053, 1096], [112, 114, 116, 118, 149, 163, 168, 169, 174, 175, 176, 177, 179, 182, 1053, 1096], [112, 114, 118, 149, 151, 163, 164, 166, 172, 173, 179, 182, 1053, 1096], [112, 149, 153, 1053, 1096], [120, 146, 147, 148, 149, 150, 153, 168, 174, 176, 178, 179, 180, 181, 183, 184, 189, 1053, 1096], [112, 118, 149, 153, 1053, 1096], [112, 118, 149, 169, 179, 1053, 1096], [112, 114, 116, 118, 149, 151, 165, 174, 179, 182, 1053, 1096], [166, 170, 171, 172, 173, 182, 1053, 1096], [112, 118, 119, 149, 151, 161, 165, 167, 171, 172, 174, 179, 182, 1053, 1096], [112, 116, 168, 170, 174, 182, 1053, 1096], [112, 114, 118, 149, 163, 165, 174, 179, 1053, 1096], [112, 114, 116, 117, 118, 146, 149, 153, 161, 165, 168, 169, 174, 179, 182, 1053, 1096], [114, 116, 117, 118, 119, 149, 153, 161, 169, 170, 179, 181, 279, 1053, 1096], [112, 114, 116, 118, 149, 151, 165, 174, 179, 182, 278, 1053, 1096], [112, 149, 181, 1053, 1096], [112, 114, 118, 163, 174, 178, 182, 1053, 1096], [116, 117, 118, 161, 171, 1053, 1096], [112, 119, 120, 145, 146, 147, 148, 150, 151, 277, 1053, 1096], [120, 146, 147, 148, 149, 150, 170, 181, 188, 190, 277, 278, 1053, 1096], [112, 118, 1053, 1096], [112, 117, 118, 153, 161, 169, 171, 180, 277, 1053, 1096], [118, 119, 278, 1053, 1096], [321, 327, 344, 1053, 1096], [112, 160, 321, 1053, 1096], [281, 282, 283, 284, 285, 287, 288, 289, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 324, 1053, 1096], [112, 277, 278, 291, 323, 1053, 1096], [112, 277, 278, 323, 1053, 1096], [112, 114, 277, 278, 323, 1053, 1096], [112, 114, 118, 277, 278, 316, 321, 322, 1053, 1096], [112, 114, 118, 277, 278, 321, 323, 1053, 1096], [112, 277, 323, 1053, 1096], [112, 114, 277, 278, 286, 323, 1053, 1096], [112, 114, 277, 278, 321, 323, 1053, 1096], [281, 282, 283, 284, 285, 287, 288, 289, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 323, 324, 325, 1053, 1096], [112, 277, 290, 323, 1053, 1096], [112, 277, 278, 293, 323, 1053, 1096], [112, 277, 278, 321, 323, 1053, 1096], [112, 277, 278, 286, 293, 321, 323, 1053, 1096], [112, 114, 277, 278, 286, 321, 323, 1053, 1096], [112, 114, 116, 118, 163, 321, 326, 327, 328, 329, 330, 331, 332, 334, 339, 340, 343, 344, 1053, 1096], [112, 114, 118, 163, 164, 321, 326, 334, 339, 343, 344, 1053, 1096], [112, 321, 326, 1053, 1096], [280, 290, 316, 317, 318, 319, 320, 321, 322, 326, 332, 333, 334, 339, 340, 342, 343, 345, 346, 347, 349, 1053, 1096], [112, 118, 321, 326, 1053, 1096], [112, 118, 317, 321, 1053, 1096], [112, 114, 118, 321, 334, 1053, 1096], [112, 116, 117, 118, 161, 165, 167, 321, 334, 340, 344, 1053, 1096], [331, 335, 336, 337, 338, 341, 344, 1053, 1096], [112, 116, 117, 118, 119, 161, 165, 167, 316, 321, 323, 334, 336, 340, 341, 344, 1053, 1096], [112, 116, 118, 326, 332, 338, 340, 344, 1053, 1096], [112, 114, 118, 163, 165, 167, 321, 334, 340, 1053, 1096], [112, 118, 165, 167, 247, 1053, 1096], [112, 118, 165, 167, 334, 340, 343, 1053, 1096], [112, 114, 116, 117, 118, 161, 165, 167, 321, 326, 327, 332, 334, 340, 344, 1053, 1096], [114, 116, 117, 118, 119, 161, 279, 321, 326, 327, 334, 338, 343, 1053, 1096], [112, 114, 116, 117, 118, 119, 161, 165, 167, 278, 321, 323, 327, 334, 340, 344, 1053, 1096], [112, 118, 290, 321, 325, 343, 1053, 1096], [112, 114, 160, 163, 247, 333, 340, 344, 1053, 1096], [116, 117, 118, 161, 341, 1053, 1096], [112, 119, 277, 280, 315, 316, 318, 319, 320, 322, 323, 1053, 1096], [188, 277, 278, 280, 316, 318, 319, 320, 321, 322, 326, 343, 350, 1053, 1096], [348, 1053, 1096], [112, 114, 117, 118, 161, 277, 323, 327, 341, 342, 1053, 1096], [112, 114, 333, 1053, 1096, 1157, 1158], [1053, 1096, 1158, 1159], [112, 113, 114, 118, 163, 334, 340, 344, 350, 1053, 1096, 1157], [112, 160, 1053, 1096], [114, 116, 118, 119, 277, 278, 279, 1053, 1096], [112, 114, 118, 119, 156, 162, 278, 1053, 1096], [277, 1053, 1096], [188, 1053, 1096], [220, 237, 1053, 1096], [191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 210, 211, 212, 213, 214, 215, 222, 1053, 1096], [112, 221, 277, 278, 1053, 1096], [112, 114, 221, 277, 278, 1053, 1096], [112, 114, 220, 277, 278, 1053, 1096], [112, 114, 118, 220, 221, 277, 278, 1053, 1096], [112, 114, 220, 221, 277, 278, 1053, 1096], [112, 114, 160, 221, 277, 278, 1053, 1096], [191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 210, 211, 212, 213, 214, 215, 221, 222, 1053, 1096], [112, 201, 221, 277, 278, 1053, 1096], [112, 114, 209, 277, 278, 1053, 1096], [112, 116, 118, 163, 220, 227, 229, 230, 231, 234, 236, 237, 1053, 1096], [112, 114, 118, 163, 164, 220, 221, 224, 225, 226, 236, 237, 1053, 1096], [217, 218, 219, 220, 223, 227, 231, 234, 235, 236, 238, 239, 240, 1053, 1096], [112, 118, 220, 223, 1053, 1096], [112, 220, 223, 1053, 1096], [112, 118, 220, 236, 1053, 1096], [112, 114, 116, 118, 165, 220, 221, 227, 236, 237, 1053, 1096], [224, 225, 226, 232, 233, 237, 1053, 1096], [112, 118, 119, 165, 167, 220, 221, 225, 227, 236, 237, 1053, 1096], [112, 116, 227, 231, 232, 237, 1053, 1096], [112, 114, 116, 117, 118, 161, 165, 220, 223, 227, 231, 236, 237, 1053, 1096], [114, 116, 117, 118, 119, 161, 220, 223, 232, 236, 279, 1053, 1096], [112, 114, 116, 118, 165, 220, 221, 227, 236, 237, 278, 1053, 1096], [112, 220, 1053, 1096], [112, 114, 118, 163, 227, 235, 237, 1053, 1096], [116, 117, 118, 161, 233, 1053, 1096], [112, 119, 216, 217, 218, 219, 221, 277, 1053, 1096], [217, 218, 219, 220, 241, 277, 278, 1053, 1096], [112, 113, 114, 163, 227, 228, 235, 1053, 1096], [112, 113, 114, 118, 163, 227, 236, 237, 1053, 1096], [118, 278, 1053, 1096], [154, 155, 1053, 1096], [157, 158, 1053, 1096], [118, 161, 278, 1053, 1096], [118, 156, 159, 1053, 1096], [112, 114, 115, 116, 117, 119, 278, 1053, 1096], [251, 269, 274, 1053, 1096], [112, 118, 269, 1053, 1096], [243, 264, 265, 266, 267, 272, 1053, 1096], [112, 114, 271, 277, 278, 1053, 1096], [112, 114, 118, 269, 270, 277, 278, 1053, 1096], [112, 114, 118, 269, 271, 277, 278, 1053, 1096], [243, 264, 265, 266, 267, 271, 272, 1053, 1096], [112, 114, 263, 269, 271, 277, 278, 1053, 1096], [112, 271, 277, 278, 1053, 1096], [112, 114, 269, 271, 277, 278, 1053, 1096], [112, 114, 116, 118, 163, 248, 249, 250, 251, 254, 259, 260, 269, 274, 1053, 1096], [112, 114, 118, 163, 164, 254, 259, 269, 273, 274, 1053, 1096], [112, 269, 273, 1053, 1096], [242, 244, 245, 246, 250, 252, 254, 259, 260, 262, 263, 269, 270, 273, 275, 1053, 1096], [112, 118, 269, 273, 1053, 1096], [112, 118, 254, 262, 269, 1053, 1096], [112, 114, 116, 117, 118, 165, 167, 254, 260, 269, 271, 274, 1053, 1096], [255, 256, 257, 258, 261, 274, 1053, 1096], [112, 114, 116, 117, 118, 161, 165, 167, 244, 254, 256, 260, 261, 269, 271, 274, 1053, 1096], [112, 116, 250, 258, 260, 274, 1053, 1096], [112, 114, 118, 163, 165, 167, 254, 260, 269, 1053, 1096], [112, 118, 165, 167, 247, 260, 1053, 1096], [112, 114, 116, 117, 118, 161, 165, 167, 250, 251, 254, 260, 269, 273, 274, 1053, 1096], [114, 116, 117, 118, 119, 161, 251, 254, 258, 262, 269, 273, 279, 1053, 1096], [112, 114, 116, 117, 118, 165, 167, 251, 254, 260, 269, 271, 274, 278, 1053, 1096], [112, 118, 163, 165, 247, 252, 253, 260, 274, 1053, 1096], [116, 117, 118, 161, 261, 1053, 1096], [112, 119, 242, 244, 245, 246, 268, 270, 271, 277, 1053, 1096], [112, 269, 271, 1053, 1096], [188, 242, 244, 245, 246, 262, 269, 270, 276, 1053, 1096], [112, 117, 118, 161, 251, 261, 271, 277, 1053, 1096], [112, 114, 118, 278, 279, 1053, 1096], [113, 118, 119, 278, 1053, 1096], [188, 364, 366, 369, 1053, 1096], [188, 364, 366, 1053, 1096], [368, 369, 370, 1053, 1096], [369, 1053, 1096], [188, 350, 364, 368, 1053, 1096], [188, 364, 366, 367, 1053, 1096], [188, 350, 364, 365, 1053, 1096], [919, 1053, 1096], [920, 1053, 1096], [893, 913, 1053, 1096], [887, 1053, 1096], [888, 892, 893, 894, 895, 896, 898, 900, 901, 906, 907, 916, 1053, 1096], [888, 893, 1053, 1096], [896, 913, 915, 918, 1053, 1096], [887, 888, 889, 890, 893, 894, 895, 896, 897, 898, 899, 900, 901, 902, 903, 904, 905, 906, 907, 908, 909, 910, 911, 912, 917, 918, 1053, 1096], [916, 1053, 1096], [886, 888, 889, 891, 899, 908, 911, 912, 917, 1053, 1096], [893, 918, 1053, 1096], [914, 916, 918, 1053, 1096], [887, 888, 893, 896, 916, 1053, 1096], [900, 1053, 1096], [890, 898, 900, 901, 1053, 1096], [890, 1053, 1096], [890, 900, 1053, 1096], [894, 895, 896, 900, 901, 906, 1053, 1096], [896, 897, 901, 905, 907, 916, 1053, 1096], [888, 900, 909, 1053, 1096], [889, 890, 891, 1053, 1096], [896, 916, 1053, 1096], [896, 1053, 1096], [887, 888, 1053, 1096], [888, 1053, 1096], [892, 1053, 1096], [896, 901, 913, 914, 915, 916, 918, 1053, 1096], [52, 53, 426, 431, 1053, 1096], [427, 1053, 1096], [427, 428, 429, 430, 1053, 1096], [424, 425, 1053, 1096], [1053, 1096, 1244], [1053, 1096, 1242, 1244], [1053, 1096, 1233, 1241, 1242, 1243, 1245], [1053, 1096, 1231], [1053, 1096, 1234, 1239, 1244, 1247], [1053, 1096, 1230, 1247], [1053, 1096, 1234, 1235, 1238, 1239, 1240, 1247], [1053, 1096, 1234, 1235, 1236, 1238, 1239, 1247], [1053, 1096, 1231, 1232, 1233, 1234, 1235, 1239, 1240, 1241, 1243, 1244, 1245, 1247], [1053, 1096, 1247], [1053, 1096, 1229, 1231, 1232, 1233, 1234, 1235, 1236, 1238, 1239, 1240, 1241, 1242, 1243, 1244, 1245, 1246], [1053, 1096, 1229, 1247], [1053, 1096, 1234, 1236, 1237, 1239, 1240, 1247], [1053, 1096, 1238, 1247], [1053, 1096, 1239, 1240, 1244, 1247], [1053, 1096, 1232, 1242], [1053, 1096, 1127], [52, 883, 1053, 1096], [52, 950, 1053, 1096], [950, 951, 952, 955, 956, 957, 958, 959, 960, 961, 964, 1053, 1096], [950, 1053, 1096], [953, 954, 1053, 1096], [52, 948, 950, 1053, 1096], [945, 946, 948, 1053, 1096], [941, 944, 946, 948, 1053, 1096], [945, 948, 1053, 1096], [52, 936, 937, 938, 941, 942, 943, 945, 946, 947, 948, 1053, 1096], [938, 941, 942, 943, 944, 945, 946, 947, 948, 949, 1053, 1096], [945, 1053, 1096], [939, 945, 946, 1053, 1096], [939, 940, 1053, 1096], [944, 946, 947, 1053, 1096], [944, 1053, 1096], [936, 941, 946, 947, 1053, 1096], [962, 963, 1053, 1096], [983, 985, 986, 987, 988, 989, 990, 991, 992, 993, 994, 995, 996, 997, 999, 1000, 1053, 1096], [52, 982, 1053, 1096], [52, 982, 984, 1053, 1096], [982, 986, 1053, 1096], [984, 1053, 1096], [983, 1053, 1096], [998, 1053, 1096], [1001, 1053, 1096], [52, 498, 499, 500, 516, 519, 1053, 1096], [52, 498, 499, 500, 509, 517, 537, 1053, 1096], [52, 497, 500, 1053, 1096], [52, 500, 1053, 1096], [52, 498, 499, 500, 1053, 1096], [52, 498, 499, 500, 535, 538, 541, 1053, 1096], [52, 498, 499, 500, 509, 516, 519, 1053, 1096], [52, 498, 499, 500, 509, 517, 529, 1053, 1096], [52, 498, 499, 500, 509, 519, 529, 1053, 1096], [52, 498, 499, 500, 509, 529, 1053, 1096], [52, 498, 499, 500, 504, 510, 516, 521, 539, 540, 1053, 1096], [500, 1053, 1096], [52, 500, 544, 545, 546, 1053, 1096], [52, 500, 543, 544, 545, 1053, 1096], [52, 500, 517, 1053, 1096], [52, 500, 543, 1053, 1096], [52, 500, 509, 1053, 1096], [52, 500, 501, 502, 1053, 1096], [52, 500, 502, 504, 1053, 1096], [493, 494, 498, 499, 500, 501, 503, 504, 505, 506, 507, 508, 509, 510, 511, 512, 516, 517, 518, 519, 520, 521, 522, 523, 524, 525, 526, 527, 528, 530, 531, 532, 533, 534, 535, 536, 538, 539, 540, 541, 547, 548, 549, 550, 551, 552, 553, 554, 555, 556, 557, 558, 559, 560, 561, 1053, 1096], [52, 500, 558, 1053, 1096], [52, 500, 512, 1053, 1096], [52, 500, 519, 523, 524, 1053, 1096], [52, 500, 510, 512, 1053, 1096], [52, 500, 515, 1053, 1096], [52, 500, 538, 1053, 1096], [52, 500, 515, 542, 1053, 1096], [52, 503, 543, 1053, 1096], [52, 497, 498, 499, 1053, 1096], [1053, 1096, 1222, 1251], [1053, 1096, 1221, 1222], [1053, 1065, 1069, 1096, 1138], [1053, 1065, 1096, 1127, 1138], [1053, 1060, 1096], [1053, 1062, 1065, 1096, 1135, 1138], [1053, 1096, 1116, 1135], [1053, 1060, 1096, 1146], [1053, 1062, 1065, 1096, 1116, 1138], [1053, 1057, 1058, 1061, 1064, 1096, 1108, 1127, 1138], [1053, 1065, 1072, 1096], [1053, 1057, 1063, 1096], [1053, 1065, 1086, 1087, 1096], [1053, 1061, 1065, 1096, 1130, 1138, 1146], [1053, 1086, 1096, 1146], [1053, 1059, 1060, 1096, 1146], [1053, 1065, 1096], [1053, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 1066, 1067, 1069, 1070, 1071, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1080, 1081, 1082, 1083, 1084, 1085, 1087, 1088, 1089, 1090, 1091, 1092, 1096], [1053, 1065, 1080, 1096], [1053, 1065, 1072, 1073, 1096], [1053, 1063, 1065, 1073, 1074, 1096], [1053, 1064, 1096], [1053, 1057, 1060, 1065, 1096], [1053, 1065, 1069, 1073, 1074, 1096], [1053, 1069, 1096], [1053, 1063, 1065, 1068, 1096, 1138], [1053, 1057, 1062, 1065, 1072, 1096], [1053, 1060, 1065, 1086, 1096, 1143, 1146], [496, 1053, 1096], [514, 1053, 1096], [1053, 1096, 1275], [1053, 1096, 1108, 1109, 1111, 1112, 1113, 1116, 1127, 1135, 1138, 1144, 1146, 1222, 1223, 1224, 1225, 1226, 1227, 1228, 1248, 1249, 1250, 1251], [1053, 1096, 1224, 1225, 1226, 1227], [1053, 1096, 1224, 1225, 1226], [1053, 1096, 1224], [1053, 1096, 1225], [1053, 1096, 1226, 1250], [1053, 1096, 1222], [52, 54, 55, 56, 57, 1053, 1096], [54, 1053, 1096], [363, 1053, 1096], [353, 354, 1053, 1096], [351, 352, 353, 355, 356, 361, 1053, 1096], [352, 353, 1053, 1096], [362, 1053, 1096], [353, 1053, 1096], [351, 352, 353, 356, 357, 358, 359, 360, 1053, 1096], [351, 352, 363, 1053, 1096], [407, 408, 410, 411, 412, 414, 1053, 1096], [410, 411, 412, 413, 414, 1053, 1096], [407, 410, 411, 412, 414, 1053, 1096], [53, 188, 1053, 1096, 1156, 1161], [53, 1053, 1096, 1156, 1164], [53, 364, 1053, 1096, 1156, 1167, 1168, 1170], [53, 364, 1053, 1096, 1101, 1156, 1164, 1173], [53, 364, 1053, 1096, 1156, 1170], [53, 372, 1053, 1096, 1157, 1160], [53, 1053, 1096, 1109, 1118, 1156], [53, 1053, 1096, 1101, 1156], [53, 1053, 1096, 1191], [53, 1053, 1096, 1101, 1156, 1164, 1193, 1194], [53, 1053, 1096, 1101, 1117, 1156, 1193], [53, 1053, 1096, 1197], [53, 1053, 1096, 1156, 1193], [53, 1053, 1096, 1156], [53, 1053, 1096, 1109, 1118, 1156, 1161, 1173, 1176, 1204, 1217, 1218, 1219, 1220, 1263], [53, 188, 1053, 1096, 1117, 1156, 1161], [53, 364, 1053, 1096, 1156], [53, 188, 372, 1051, 1053, 1096, 1101, 1156, 1161, 1204, 1207], [53, 1053, 1096, 1101, 1193], [53, 1053, 1096, 1156, 1265], [53, 188, 372, 1053, 1096, 1161, 1204, 1206], [53, 1053, 1096, 1101, 1108, 1204], [53, 1053, 1096, 1173, 1193], [53, 1053, 1096, 1101, 1108, 1161], [53, 1053, 1096, 1119], [53, 1053, 1096, 1169], [53, 372, 1053, 1096, 1101, 1156, 1161, 1197], [53, 1053, 1096, 1109, 1117, 1156], [53, 364, 372, 1053, 1096, 1109, 1111, 1118, 1138, 1156, 1162, 1165, 1174, 1193, 1195, 1197, 1199, 1201, 1202, 1203, 1204, 1205, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1216], [53, 364, 1053, 1096, 1156, 1215], [53, 372, 1053, 1096, 1156, 1161], [53, 188, 372, 1051, 1053, 1096, 1108, 1161], [53, 1053, 1096, 1101, 1170], [53, 188, 372, 1053, 1096, 1101, 1161, 1170], [53, 188, 372, 1053, 1096, 1161, 1169], [53, 1053, 1096, 1101, 1108, 1161, 1170], [53, 1053, 1096, 1156, 1161, 1169, 1193], [53, 1053, 1096, 1109, 1111, 1118, 1156, 1169, 1252, 1262], [53, 1053, 1096, 1101, 1156, 1169, 1193, 1198], [53, 1053, 1096, 1111, 1172], [53, 350, 364, 371, 1053, 1096], [53, 188, 350, 364, 371, 1053, 1096], [53, 1053, 1096, 1118, 1252, 1259, 1260, 1261]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07f073f19d67f74d732b1adea08e1dc66b1b58d77cb5b43931dee3d798a2fd53", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fef8cfad2e2dc5f5b3d97a6f4f2e92848eb1b88e897bb7318cef0e2820bceaab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811c71eee4aa0ac5f7adf713323a5c41b0cf6c4e17367a34fbce379e12bbf0a4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb5b19b86227ace1d29ea4cf81387279d04bb34051e944bc53df69f58914b788", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "impliedFormat": 1}, {"version": "ed6b820c54de95b2510bb673490d61c7f2187f532a339d8d04981645a918961f", "impliedFormat": 1}, {"version": "472f5aab7edc498a0a761096e8e254c5bc3323d07a1e7f5f8b8ec0d6395b60a0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "42c169fb8c2d42f4f668c624a9a11e719d5d07dacbebb63cbcf7ef365b0a75b3", "impliedFormat": 1}, {"version": "85dc31cf44666b6e709227156b39c37c75cbeae41d1897af5a695f5debd4d6df", "impliedFormat": 99}, {"version": "29c46c39d154af65f4b33dc49e04c2f69c193445bee5e9c57948ee029c5a4392", "impliedFormat": 99}, {"version": "2ac737e0cf3541e3968d3b0817c98eff476f90cf0372dbdb824336590d37c61e", "impliedFormat": 99}, {"version": "7bf07341d24034cb6c5b7bedb74b97f39ac74e9e1c95ffc8e879ec841caae18b", "impliedFormat": 1}, {"version": "934877d321700e479a672f71ae3f487a692b70a753c8d6e8178d90ddc65b3cc5", "impliedFormat": 99}, {"version": "9971931daaf18158fc38266e838d56eb5d9d1f13360b1181bb4735a05f534c03", "impliedFormat": 99}, {"version": "50cf7a23fc93928995caec8d7956206990f82113beeb6b3242dae8124edc3ca0", "impliedFormat": 99}, {"version": "62443d6fbad6830cc1ec6a96ccb346b9c8fac320d954f7ba14ec84ac489c89e5", "impliedFormat": 99}, {"version": "bd85074aed3cfd83d906643c80cda912732d688b0a20791cd8df5b7ff0eba59e", "impliedFormat": 99}, {"version": "909e8848dd7005329d4841027b51d09efcfb131e14268b091e830ab7adea9849", "impliedFormat": 99}, {"version": "0c5b705d31420477189618154d1b6a9bb62a34fa6055f56ade1a316f6adb6b3a", "impliedFormat": 99}, {"version": "352031ac2e53031b69a09355e09ad7d95361edf32cc827cfe2417d80247a5a50", "impliedFormat": 99}, {"version": "853b8bdb5da8c8e5d31e4d715a8057d8e96059d6774b13545c3616ed216b890c", "impliedFormat": 99}, {"version": "b9bd72693123f4548f67d5ba060cedc22755606d3bd63bb1d719970086799965", "impliedFormat": 99}, {"version": "9bd5be6049c58f5a7a1699c3c8c4db44d634f2a861de445dda907011167317b1", "impliedFormat": 99}, {"version": "1af42015f6353472dd424dcaa8b6909dfe90ce03978e1755e356780ff4ed0eb5", "impliedFormat": 99}, {"version": "c363b57a3dfab561bfe884baacf8568eea085bd5e11ccf0992fac67537717d90", "impliedFormat": 99}, {"version": "1757a53a602a8991886070f7ba4d81258d70e8dca133b256ae6a1a9f08cd73b3", "impliedFormat": 99}, {"version": "084c09a35a9611e1777c02343c11ab8b1be48eb4895bbe6da90222979940b4a6", "impliedFormat": 99}, {"version": "4b3049a2c849f0217ff4def308637931661461c329e4cf36aeb31db34c4c0c64", "impliedFormat": 99}, {"version": "6245aa515481727f994d1cf7adfc71e36b5fc48216a92d7e932274cee3268000", "impliedFormat": 99}, {"version": "d542fb814a8ceb7eb858ecd5a41434274c45a7d511b9d46feb36d83b437b08d5", "impliedFormat": 99}, {"version": "998d9f1da9ec63fca4cc1acb3def64f03d6bd1df2da1519d9249c80cfe8fece6", "impliedFormat": 99}, {"version": "b7d9ca4e3248f643fa86ff11872623fdc8ed2c6009836bec0e38b163b6faed0c", "impliedFormat": 99}, {"version": "ac7a28ab421ea564271e1a9de78d70d68c65fab5cbb6d5c5568afcf50496dd61", "impliedFormat": 99}, {"version": "d4f7a7a5f66b9bc6fbfd53fa08dcf8007ff752064df816da05edfa35abd2c97c", "impliedFormat": 99}, {"version": "1f38ecf63dead74c85180bf18376dc6bc152522ef3aedf7b588cadbbd5877506", "impliedFormat": 99}, {"version": "24af06c15fba5a7447d97bcacbcc46997c3b023e059c040740f1c6d477929142", "impliedFormat": 99}, {"version": "facde2bec0f59cf92f4635ece51b2c3fa2d0a3bbb67458d24af61e7e6b8f003c", "impliedFormat": 99}, {"version": "4669194e4ca5f7c160833bbb198f25681e629418a6326aba08cf0891821bfe8f", "impliedFormat": 99}, {"version": "f919471289119d2e8f71aba81869b01f30f790e8322cf5aa7e7dee8c8dadd00a", "impliedFormat": 99}, {"version": "a95cd11c5c8bc03eab4011f8e339a48f9a87293e90c0bf3e9003d7a6f833f557", "impliedFormat": 99}, {"version": "e9bc0db0144701fab1e98c4d595a293c7c840d209b389144142f0adbc36b5ec2", "impliedFormat": 99}, {"version": "b1f00d7e185339b76f12179fa934088e28a92eb705f512fbe813107f0e2e2eb8", "impliedFormat": 99}, "188808c486a5568a8fb7bb53ee05d6c5eba658266fedd7bc6d2e408c24dbc972", {"version": "a9373d52584b48809ffd61d74f5b3dfd127da846e3c4ee3c415560386df3994b", "impliedFormat": 99}, {"version": "ea7f3d87bb25b8cf26c1b440de31b628c53b5e72e8f1ab1726356bf58acf5946", "impliedFormat": 99}, {"version": "7ec047b73f621c526468517fea779fec2007dd05baa880989def59126c98ef79", "impliedFormat": 99}, {"version": "148ad734850375f1a3d51523b329997d20d661381c7e9cbe26dd35e5238f8778", "impliedFormat": 99}, {"version": "c57b441e0c0a9cbdfa7d850dae1f8a387d6f81cbffbc3cd0465d530084c2417d", "impliedFormat": 99}, {"version": "2fbe402f0ee5aa8ab55367f88030f79d46211c0a0f342becaa9f648bf8534e9d", "impliedFormat": 1}, {"version": "b94258ef37e67474ac5522e9c519489a55dcb3d4a8f645e335fc68ea2215fe88", "impliedFormat": 1}, {"version": "e37cfae909b88f73b73267bde749725425f46356f62b448566dc9ff4509073a4", "impliedFormat": 1}, {"version": "26c57c9f839e6d2048d6c25e81f805ba0ca32a28fd4d824399fd5456c9b0575b", "impliedFormat": 1}, "7c8c3dfc0cdd370d44932828eb067ef771c8fe7996693221d5d4b90af6d54f2d", "99f9f4a3e897f71d89b01c6772b2f5db51b888cecb2c8214fb2c2dbb2b3e6356", "5aab0623ee24bddf9420c259a23a06996292e38ea70eccbac2715516f6d01a0b", "1348f1a1f9820e2f1394b93cecc156a7e22f03636ab9270753985549aae2be74", {"version": "6b5f886fe41e2e767168e491fe6048398ed6439d44e006d9f51cc31265f08978", "impliedFormat": 99}, {"version": "56a87e37f91f5625eb7d5f8394904f3f1e2a90fb08f347161dc94f1ae586bdd0", "impliedFormat": 99}, {"version": "6b863463764ae572b9ada405bf77aac37b5e5089a3ab420d0862e4471051393b", "impliedFormat": 99}, {"version": "904d6ad970b6bd825449480488a73d9b98432357ab38cf8d31ffd651ae376ff5", "impliedFormat": 99}, {"version": "233267a4a036c64aee95f66a0d31e3e0ef048cccc57dd66f9cf87582b38691e4", "impliedFormat": 99}, "2f26d20816448c630faccbda5ae9b4fe47c36f1fb14dbbd85b6652b7c24c1d51", "69afcf9c8e58ca6c3acf43c5b1ec9186bb6c88952f0ff49345dd2666f93d5480", {"version": "a80ec72f5e178862476deaeed532c305bdfcd3627014ae7ac2901356d794fc93", "impliedFormat": 99}, "415ccc47cf69a2a87db699cc6da7f1fdcb799a1f40dab3a6220d91ac8da8abbe", "9e61627948ed45ada5d812d4a6d023370611f5a6ef9c60ed9b3ecc8cd6348c75", {"version": "c6fe327c538417b8dd5b9bb32abcd7911534b10da3a4514f3445cdb28cf3abf2", "impliedFormat": 99}, {"version": "0065cdb7ac9f5b19921632de63f888ec2cc11ad57f7fc868f44bf0faad2fce3e", "impliedFormat": 99}, {"version": "03200d03f2b750f0bc64cbbeac20d5bacb986dc6b2de4e464b47589ad9c6b740", "impliedFormat": 99}, {"version": "8c1adc3171d0287f3a26f4891a7d1834c89999573a9b444aa5ff519dcc43a2b7", "impliedFormat": 99}, {"version": "a16b99c0d3511955f5abc6c01590b01b062e8375f43816e831cb402c03a09400", "impliedFormat": 99}, {"version": "d03f3549a814f5c5d1a62950349aad23dcf9f830873b78ac59ab7266e5b4a14a", "impliedFormat": 99}, {"version": "5a2cf4cd852a58131b320da62269b2143850920ce27e8fdec41fed5c2c54ec95", "impliedFormat": 99}, {"version": "16de02d4e7ae55933e1e310af296e8802753f2f9c60bf7436413b51cae0a451c", "impliedFormat": 99}, {"version": "4f97089fe15655ae448c9d005bb9a87cc4e599b155edc9e115738c87aa788464", "impliedFormat": 99}, {"version": "2fff037c771e3fe6108b14137e56827197944b855aa2df40f21fa2d8a2758e1e", "impliedFormat": 99}, {"version": "22929f9874783b059156ee3cfa864d6f718e1abf9c139f298a037ae0274186f6", "impliedFormat": 99}, {"version": "c72a7c316459b2e872ca4a9aca36cc05d1354798cee10077c57ff34a34440ac2", "impliedFormat": 99}, {"version": "3e5bbf8893b975875f5325ebf790ab1ab38a4173f295ffea2ed1f108d9b1512c", "impliedFormat": 99}, {"version": "9e4a38448c1d26d4503cf408cc96f81b7440a3f0a95d2741df2459fe29807f67", "impliedFormat": 99}, {"version": "84124d21216da35986f92d4d7d1192ca54620baeca32b267d6d7f08b5db00df9", "impliedFormat": 99}, {"version": "c08976f55a00ddbb3b13a68a9a0d418117f35c6e2d40f1f6f55468fc180a01f0", "impliedFormat": 99}, {"version": "25f5bf39f0785a2976d0af5ac02f5c18ca759cde62bc48dd1d0d99871d9ad86f", "impliedFormat": 99}, {"version": "877c73fdbe90937b3c16b5827526a428bf053957a202ac8c2fd88d6eab437764", "impliedFormat": 99}, {"version": "e324b2143fa6e32fac37ed9021b88815e181b045a9f17dbb555b72d55e47cdc1", "impliedFormat": 99}, {"version": "3e90ea83e3803a3da248229e3027a01428c3b3de0f3029f86c121dc76c5cdcc2", "impliedFormat": 99}, {"version": "9368c3e26559a30ad3431d461f3e1b9060ab1d59413f9576e37e19aaf2458041", "impliedFormat": 99}, {"version": "915e5bb8e0e5e65f1dc5f5f36b53872ffcdcaef53903e1c5db7338ea0d57587a", "impliedFormat": 99}, {"version": "92cf986f065f18496f7fcb4f135bff8692588c5973e6c270d523191ef13525ad", "impliedFormat": 99}, {"version": "652f2bd447e7135918bc14c74b964e5fe48f0ba10ff05e96ed325c45ac2e65fb", "impliedFormat": 99}, {"version": "cc2156d0ec0f00ff121ce1a91e23bd2f35b5ab310129ad9f920ddaf1a18c2a4d", "impliedFormat": 99}, {"version": "7b371e5d6e44e49b5c4ff88312ae00e11ab798cfcdd629dee13edc97f32133d8", "impliedFormat": 99}, {"version": "e9166dab89930e97bb2ce6fc18bcc328de1287b1d6e42c2349a0f136fc1f73e6", "impliedFormat": 99}, {"version": "6dc0813d9091dfaed7d19df0c5a079ee72e0248ce5e412562c5633913900be25", "impliedFormat": 99}, {"version": "e704c601079399b3f2ec4acdfc4c761f5fe42f533feaaab7d2c1c1528248ca3e", "impliedFormat": 99}, {"version": "49104d28daa32b15716179e61d76b343635c40763d75fe11369f681a8346b976", "impliedFormat": 99}, {"version": "6d414a0690dd5e23448958babe29b6aeb984faf8ff79248310f6c9718a9196ad", "impliedFormat": 99}, {"version": "0bfdb8230777769f3953fd41cf29c3cb61262a0be678dd5c899e859c0d159efe", "impliedFormat": 99}, {"version": "97e685ac984fc93dcdae6c24f733a7a466274c103fdcf5d3b028eaa9245f59d6", "impliedFormat": 99}, {"version": "68526ea8f3bbf75a95f63a3629bebe3eb8a8d2f81af790ce40bc6aad352a0c12", "impliedFormat": 99}, {"version": "bcab57f5fe8791f2576249dfcc21a688ecf2a5929348cfe94bf3eb152cff8205", "impliedFormat": 99}, {"version": "b5428f35f4ebf7ea46652b0158181d9c709e40a0182e93034b291a9dc53718d8", "impliedFormat": 99}, {"version": "0afcd28553038bca2db622646c1e7fcf3fb6a1c4d3b919ef205a6014edeeae0f", "impliedFormat": 99}, {"version": "ee016606dd83ceedc6340f36c9873fbc319a864948bc88837e71bd3b99fdb4f6", "impliedFormat": 99}, {"version": "0e09ffe659fdd2e452e1cbe4159a51059ae4b2de7c9a02227553f69b82303234", "impliedFormat": 99}, {"version": "4126cb6e6864f09ca50c23a6986f74e8744e6216f08c0e1fe91ab16260dab248", "impliedFormat": 99}, {"version": "4927dba9193c224e56aa3e71474d17623d78a236d58711d8f517322bd752b320", "impliedFormat": 99}, {"version": "3d3f189177511d1452e7095471e3e7854b8c44d94443485dc21f6599c2161921", "impliedFormat": 99}, {"version": "99f169da66be3a487ce1fe30b11f33ed2bdf57893729caaea453517d9a7fa523", "impliedFormat": 99}, {"version": "043195af0b52aadd10713870dd60369df0377ed153104b26e6bac1213b19f63e", "impliedFormat": 99}, {"version": "ad17a36132569045ab97c8e5badf8febb556011a8ed7b2776ff823967d6d5aca", "impliedFormat": 99}, {"version": "698d2b22251dbbfc0735e2d6ed350addead9ad031fac48b8bb316e0103d865db", "impliedFormat": 99}, {"version": "ff10facf373a13d2864ff4de38c4892d74be27d9c6468dac49c08adabbf9b0eb", "impliedFormat": 99}, {"version": "97b1cf4599cc3bc2e84b997aa1af60d91ca489d96bea0e20aaff0e52a5504b29", "impliedFormat": 99}, {"version": "853dfbcd0999d3edc6be547d83dc0e0d75bf44530365b9583e75519d35984c35", "impliedFormat": 99}, {"version": "abbb31e3da98902306359386224021bfb6cfa2496c89bbbde7ee2065cf58297c", "impliedFormat": 99}, {"version": "6139824680a34eba08979f2e21785a761870384a4df16c143b19288aced9c346", "impliedFormat": 99}, {"version": "c7d89156a4f0313c66377afd14063a9e5be3f8e01a7b5fae4723ef07a2628e55", "impliedFormat": 99}, {"version": "bb0519ff5ef245bbf829d51ad1f90002de702b536691f25334136864be259ec5", "impliedFormat": 99}, {"version": "e2381c64702025b4d57b005e94ed0b994b5592488d76f1e5f67f59d1860ebb70", "impliedFormat": 99}, {"version": "a64e28f2333ea0324632cf81fd73dc0f7090525547a76308cb1dfe5dab96596a", "impliedFormat": 99}, {"version": "d7dfcb039ff9cff38ccd48d2cc1ba95ca45c316670eddbcf81784e21b7128692", "impliedFormat": 99}, {"version": "883f9faa0229f5d114f8c89dadd186d0bdf60bdafe94d67d886e0e3b81a3372e", "impliedFormat": 99}, {"version": "d204b9ae964f73721d593e97c54fc55f7fd67de826ce9e9f14b1e762190f23d1", "impliedFormat": 99}, {"version": "c010c1317efa90a9b10d67f0ad6b96bde45818b6cdca32afababcf7a2dd7ecc3", "impliedFormat": 99}, {"version": "68115cdc58303bad32e2b6d59e821ccaada2c3fb63f964df7bd4b2ebd6735e80", "impliedFormat": 99}, {"version": "7db31e5afa6ffa20d6e65505d1af449415e8a489d628f93a9a1f487d89a218c6", "impliedFormat": 99}, {"version": "db5968a602bb6c07ab2d608e3035489d443f3556209ded7c0679e0c9c7b671ed", "impliedFormat": 99}, {"version": "d010efe139c8bb78497dc7185dddbbcefc84d3059b5d8549c26221257818a961", "impliedFormat": 99}, {"version": "85059ed9b6605d92c753daf3a534855ba944be69ff1a12ab4eca28cefbabd07a", "impliedFormat": 99}, {"version": "687208233ae7a969baa2d0c565c9f24eb4cb1e64d6cfb30f71afec9e929e58c2", "impliedFormat": 99}, {"version": "ea68a96f4e2ba9ca97d557b7080fbdb7f6e6cf781bb6d2e084e54da2ac2bb36c", "impliedFormat": 99}, {"version": "00173ffba39168fe3027099da73666fbedfb305284b64eaaee25bb0037e354b2", "impliedFormat": 99}, {"version": "f3ed9a4ec3123351b2a8cba473e9a6f173eab5458309f380fe0039642f70bcae", "impliedFormat": 99}, {"version": "21f96085ed19d415725c5a7d665de964f8283cacef43957de10bdd0333721cc4", "impliedFormat": 99}, {"version": "e8d4da9e0859c6d41c4f1c3f4d0e70446554ba6a6ab91e470f01af6a2dcac9bf", "impliedFormat": 99}, {"version": "39defc828dbdf47affd1e83ae63798fbb0224e158549db98e632742ab5ddaebd", "impliedFormat": 99}, {"version": "a10fd5d76a2aaba572bec4143a35ff58912e81f107aa9e6d97f0cd11e4f12483", "impliedFormat": 99}, {"version": "1215f54401c4af167783d0f88f5bfb2dcb6f0dacf48495607920229a84005538", "impliedFormat": 99}, {"version": "6d640d840f53fb5f1613829a7627096717b9b0d98356fb86bb771b6168299e2e", "impliedFormat": 99}, {"version": "07603bb68d27ff41499e4ed871cde4f6b4bb519c389dcf25d7f0256dfaa56554", "impliedFormat": 99}, {"version": "6bd4aa523d61e94da44cee0ee0f3b6c8d5f1a91ef0bd9e8a8cf14530b0a1a6df", "impliedFormat": 99}, {"version": "6b6e2508f79513e01386273e63d0fc3617613d80a5aca950a2b0fc33d90ad0b4", "impliedFormat": 99}, {"version": "52869a2597d5c33241d1debc4dfb0c1c0a5a05b8a7b5f85de5cfe0e553e86f47", "impliedFormat": 99}, {"version": "2fe93aef0ee58eaa1b22a9b93c8d8279fe94490160703e1aabeff026591f8300", "impliedFormat": 99}, {"version": "d2c74e0608352d6eb35b35633cdbce7ed49c3c4498720c9dd8053fdc47a9db8a", "impliedFormat": 99}, {"version": "bca335fd821572e3f8f1522f6c3999b0bc1fe3782b4d443c317df57c925543ed", "impliedFormat": 99}, {"version": "73332a05f142e33969f9a9b4fb9c12b08b57f09ada25eb3bb94194ca035dc83d", "impliedFormat": 99}, {"version": "c366621e6a8febe9bbca8c26275a1272d99a45440156ca11c860df7aa9d97e6d", "impliedFormat": 99}, {"version": "d9397a54c21d12091a2c9f1d6e40d23baa327ae0b5989462a7a4c6e88e360781", "impliedFormat": 99}, {"version": "dc0e2f7f4d1f850eb20e226de8e751d29d35254b36aa34412509e74d79348b75", "impliedFormat": 99}, {"version": "c7bc760336ac14f9423c83ca2eec570a2be6f73d2ce7f890c6cce76c931c8e15", "impliedFormat": 99}, {"version": "dea1773a15722931fbfe48c14a2a1e1ad4b06a9d9f315b6323ee112c0522c814", "impliedFormat": 99}, {"version": "b26e3175cf5cee8367964e73647d215d1bf38be594ac5362a096c611c0e2eea8", "impliedFormat": 99}, {"version": "4280093ace6386de2a0d941b04cff77dda252f59a0c08282bd3d41ccc79f1a50", "impliedFormat": 99}, {"version": "fe17427083904947a4125a325d5e2afa3a3d34adaedf6630170886a74803f4a2", "impliedFormat": 99}, {"version": "0246f9f332b3c3171dcdd10edafab6eccb918c04b2509a74e251f82e8d423fb7", "impliedFormat": 99}, {"version": "f6ef33c2ff6bbdf1654609a6ca52e74600d16d933fda1893f969fc922160d4d7", "impliedFormat": 99}, {"version": "1abd22816a0d992fd33b3465bf17a5c8066bf13a8c6ca4fc0cd28884b495762d", "impliedFormat": 99}, {"version": "82032a08169ea01cf01aa5fd3f7a02f1f417697df5e42fc27d811d23450bc28d", "impliedFormat": 99}, {"version": "9c8cbd1871126e98602502444cffb28997e6aa9fbc62d85a844d9fd142e9ae1b", "impliedFormat": 99}, {"version": "b0e20abc4a73df8f97b3f1223cc330e9ba3b2062db1908aa2a97754a792139ac", "impliedFormat": 99}, {"version": "bc1f2428d738ab789339030078adf313100471c37d8d69f6cf512a5715333afc", "impliedFormat": 99}, {"version": "dc500c6a23c9432849c82478bdab762fa7bdf9245298c2279a7063dd05ae9f9a", "impliedFormat": 99}, {"version": "cd1b6a2503fc554dcab602e053565c4696e4262b641b897664d840a61f519229", "impliedFormat": 99}, {"version": "af1580cd202df0e33fc592fe1d75d720c15930a4127a87633542b33811316724", "impliedFormat": 99}, {"version": "af6106fc6900a51b49a8e6f4c5a8f18295eb9ae34efbea3c2b7db45e42b41b5e", "impliedFormat": 99}, {"version": "cd090c8806133525e1085f6b820618194d0133e6fc328d03956969d211a9c885", "impliedFormat": 99}, {"version": "9ea6fea875302b2bb3976f7431680affc45a4319499d057ce12be04e4f487bf9", "impliedFormat": 99}, {"version": "66e0c3f9875da7be383d0c78c8b8940b6ebae3c6a0fbfd7e77698b5e8ade3b05", "impliedFormat": 99}, {"version": "da38d326fe6a72491cad23ea22c4c94dfc244363b6a3ec8a03b5ad5f4ee6337b", "impliedFormat": 99}, {"version": "da587bf084b08ea4e36a134ec5fb19ae71a0f32ec3ec2a22158029cb2b671e28", "impliedFormat": 99}, {"version": "517a31c520e08c51cfe6d372bc0f5a6bf7bd6287b670bcaa180a1e05c6d4c4da", "impliedFormat": 99}, {"version": "0263d94b7d33716a01d3e3a348b56c2c59e6d897d89b4210bdbf27311127223c", "impliedFormat": 99}, {"version": "d0120e583750834bf1951c8b9936781a98426fe8d3ad3d951f96e12f43090469", "impliedFormat": 99}, {"version": "a2e6a99c0fb4257e9301d592da0834a2cb321b9b1e0a81498424036109295f8b", "impliedFormat": 99}, {"version": "c6b5ae9f99f1fccadc23d56307a28c8490c48e687678f2cafa006b3b9b8e73e4", "impliedFormat": 99}, {"version": "eae178ee8d7292bcd23be2b773dda60b055bc008a0ddce2acc1bfe30cc36cf04", "impliedFormat": 99}, {"version": "e0b5f197fb47b39a4689ad356b8488e335bbf399b283492c0ffae0cfda88837b", "impliedFormat": 99}, {"version": "adb7aa4b8d8b423d0d7e78b6a8affb88c3a32a98e21cd54fcafd570ad8588d0c", "impliedFormat": 99}, {"version": "643e22362c15304f344868ec0e7c0b4a1bc2b56c8b81d5b9f0ee0a6f3c690fff", "impliedFormat": 99}, {"version": "f89e713e33bfcc7cc1d505a1e76f260b7aae72f8ba83f800ab47b5db2fed8653", "impliedFormat": 99}, {"version": "4c3be904cab639b22989d13a9c4ea1184388af2ff27c4f5b39960628a76629db", "impliedFormat": 99}, {"version": "307009cbc7927f6c2e7b482db913589f8093108b8bd4a450cfe749b80476aea0", "impliedFormat": 99}, {"version": "a5bf6d947ce6a4f1935e692c376058493dbfbd9f69d9b60bbaf43fd5d22c324b", "impliedFormat": 99}, {"version": "4927ef881b202105603e8416d63f317a1f1ea47d321e32826b9b20a44caa55e2", "impliedFormat": 99}, {"version": "7deb9fb41fbf45e79da80de7e0eb10437cd81a36024edff239aa59228849b2d3", "impliedFormat": 99}, {"version": "f9fdd2efc37eefc321338d39b5bd341b2aa82292b72610cb900f205f6803ff66", "impliedFormat": 99}, {"version": "687208233ae7a969baa2d0c565c9f24eb4cb1e64d6cfb30f71afec9e929e58c2", "impliedFormat": 99}, {"version": "7139f89a25baa378770397bf9efd6e15061eb63d42df3591e946a87ef2197fea", "impliedFormat": 99}, {"version": "956aeea3c94b894b3ae95a9691c1a8fa6f9eae47d30817a59c14908113322caa", "impliedFormat": 99}, {"version": "87cbb57d0f80470800378bff30f8bc7e2c99a7b30a818bf7ccbf049407714a10", "impliedFormat": 99}, {"version": "cc411cd97607f993efb008c8b8a67207e50fdd927b7e33657e8e332c2326c9f3", "impliedFormat": 99}, {"version": "b144c6cdf6525af185cd417dc85fd680a386f0840d7135932a8b6839fdee4da6", "impliedFormat": 99}, {"version": "2125e8c5695ddfded3b93c3537b379df2b4dcd3cdad97fa6ec87d51beda0bef1", "impliedFormat": 99}, {"version": "572ee8f367fe4068ccb83f44028ddb124c93e3b2dcc20d65e27544d77a0b84d3", "impliedFormat": 99}, {"version": "7d604c1d876ef8b7fec441cf799296fd0d8f66844cf2232d82cf36eb2ddff8fe", "impliedFormat": 99}, {"version": "7b86b536d3e8ca578f8fbc7e48500f89510925aeda67ed82d5b5a3213baf5685", "impliedFormat": 99}, {"version": "861596a3b58ade9e9733374bd6b45e5833b8b80fd2eb9fe504368fc8f73ae257", "impliedFormat": 99}, {"version": "a3da7cf20826f3344ad9a8a56da040186a1531cace94e2788a2db795f277df94", "impliedFormat": 99}, {"version": "900a9da363740d29e4df6298e09fad18ae01771d4639b4024aa73841c6a725da", "impliedFormat": 99}, {"version": "730cb342a128f5a8a036ffbd6dbc1135b623ce2100cefe1e1817bb8845bc7100", "impliedFormat": 99}, {"version": "4e979a85e80e332414f45089ff02f396683c0b5919598032a491eb7b981fedfd", "impliedFormat": 99}, {"version": "6d3496cac1c65b8a645ecbb3e45ec678dd4d39ce360eecbcb6806a33e3d9a7ae", "impliedFormat": 99}, {"version": "9909129eb7301f470e49bbf19f62a6e7dcdfe9c39fdc3f5030fd1578565c1d28", "impliedFormat": 99}, {"version": "442f6a9e83bb7d79ff61877dc5f221eea37f1d8609d8848dfbc6228ebc7a8e90", "impliedFormat": 99}, {"version": "10c21d52b988b30fcd2ee3ef277a15c7e5913e14da0641f8d50db18a3c4e6bef", "impliedFormat": 99}, {"version": "7e4fc245cc369ba9c1a39df427563e008b8bfe5bf73c6c3f5d3a928d926a8708", "impliedFormat": 99}, {"version": "2dd4989deea8669628ef01af137d9494c12bbfc5ff2bbe033369631932c558cb", "impliedFormat": 99}, {"version": "d39330cb139d83d5fa5071995bb615ea48aa093018646d4985acd3c04b4e443d", "impliedFormat": 99}, {"version": "663800dc36a836040573a5bb161d044da01e1eaf827ccc71a40721c532125a80", "impliedFormat": 99}, {"version": "f28691d933673efd0f69ac7eae66dea47f44d8aa29ec3f9e8b3210f3337d34df", "impliedFormat": 99}, {"version": "fa9c4f35c92322c61ec9a7f90dd2a290c35723348891f1459946186b189a129a", "impliedFormat": 99}, {"version": "687208233ae7a969baa2d0c565c9f24eb4cb1e64d6cfb30f71afec9e929e58c2", "impliedFormat": 99}, {"version": "f716500cce26a598e550ac0908723b9c452e0929738c55a3c7fe3c348416c3d0", "impliedFormat": 99}, {"version": "6b7c511d20403a5a1e3f5099056bc55973479960ceff56c066ff0dd14174c53c", "impliedFormat": 99}, {"version": "48b83bd0962dac0e99040e91a49f794d341c7271e1744d84e1077e43ecda9e04", "impliedFormat": 99}, {"version": "d7c98c7c260b3f68f766ec9bbd19d354db2254c190c5c6258ae6147283d308f0", "impliedFormat": 99}, {"version": "ffa53626a9de934a9447b4152579a54a61b2ea103dbbf02b0f65519bfef98cdd", "impliedFormat": 99}, {"version": "c427b591bfddecf5501efa905b408291a189ae579a06e4794407c8e94c8709fc", "impliedFormat": 99}, {"version": "b6e9b15869788861fff21ec7f371bda9a2e1a1b15040cc005db4d2e792ece5ca", "impliedFormat": 99}, {"version": "22c844fbe7c52ee4e27da1e33993c3bbb60f378fa27bb8348f32841baecb9086", "impliedFormat": 99}, {"version": "dee6934166088b55fe84eae24de63d2e7aae9bfe918dfe635b252f682ceca95a", "impliedFormat": 99}, {"version": "c39b9c4f5cc37a8ed51bef12075f5d023135e11a9b215739fd0dd87ee8da804a", "impliedFormat": 99}, {"version": "db027bc9edef650cff3cbe542959f0d4ef8532073308c04a5217af25fc4f5860", "impliedFormat": 99}, {"version": "a4e026fe4d88d36f577fbd38a390bd846a698206b6d0ca669a70c226e444af1b", "impliedFormat": 99}, {"version": "165f3c6426e7dfff8fb0c34952d11d3b8528cb18502a7350d2eeb967177b2eb7", "impliedFormat": 99}, {"version": "fa910f88f55844718a277ee9519206abce66629de2692676c3e2ad1c9278bdfd", "impliedFormat": 99}, {"version": "9a7914a6000dbd6feaea9bc51065664d0fef0b5c608b7f66a7b229213e4805ef", "impliedFormat": 99}, {"version": "9ae87bd743e93b6384efbfa306bde1fa70b6ff27533983e1e1fe08a4ef7037b8", "impliedFormat": 99}, {"version": "5f7c0a4aad7a3406db65d674a5de9e36e0d08773f638b0f49d70e441de7127c0", "impliedFormat": 99}, {"version": "4a43776782d9bce9cc167e176b4a4bb46e542619502ce1848d6f15946d54eaf7", "impliedFormat": 99}, {"version": "49fcfda71ea42a9475b530479a547f93d4e88c2deb0c713845243f5c08af8d76", "impliedFormat": 99}, {"version": "1921b8b1513bb282e741587ec802ef76a643a3a56b9ee07f549911eab532ee2e", "impliedFormat": 99}, {"version": "bbb02e695c037f84947e56da3485bb0d0da9493ed005fa59e4b3c5bc6d448529", "impliedFormat": 99}, {"version": "ba666b3ab51c8bc916c0cebc11a23f4afec6c504c767fd5f0228358f7d285322", "impliedFormat": 99}, {"version": "c10972922d1887fe48ed1722e04ab963e85e1ac12263a167edef9b804a2af097", "impliedFormat": 99}, {"version": "6efeacbd1759ea57a4c7264eb766c531ae0ab2c00385294be58bc5031ef43ad1", "impliedFormat": 99}, {"version": "1c261f5504d0175be4f1b6b99f101f4c3a129a5a29fc768e65c52d6861ca5784", "impliedFormat": 99}, {"version": "f0e69b5877b378d47cbac219992b851e2bbc0f7e3a3d3579d67496dabd341ec4", "impliedFormat": 99}, {"version": "b5ea27f19a54feca5621f5ba36a51026128ea98e7777e5d47f08b79637527cf5", "impliedFormat": 99}, {"version": "b54890769fa3c34ab3eb7e315b474f52d5237c86c35f17d59eb21541e7078f11", "impliedFormat": 99}, {"version": "c133db4b6c17a96db7fa36607c59151dec1e5364d9444cbe15e8c0ea4943861e", "impliedFormat": 99}, {"version": "3a0514f77606d399838431166a0da6dbd9f3c7914eae5bbfbd603e3b6a552959", "impliedFormat": 99}, {"version": "fa568f8d605595e1cffbfca3e8c8c492cf88ae2c6ed151f6c64acf0f9e8c25d8", "impliedFormat": 99}, {"version": "c76fb65cb2eb09a0ee91f02ff5b43a607b94a12c34d16d005b2c0afc62870766", "impliedFormat": 99}, {"version": "cf7af60a0d4308a150df0ab01985aabb1128638df2c22dd81a2f5b74495a3e45", "impliedFormat": 99}, {"version": "913bbf31f6b3a7388b0c92c39aec4e2b5dba6711bf3b04d065bd80c85b6da007", "impliedFormat": 99}, {"version": "42d8c168ca861f0a5b3c4c1a91ff299f07e07c2dd31532cd586fd1ee7b5e3ae6", "impliedFormat": 99}, {"version": "a29faa7cb35193109ec1777562ca52c72e7382ffe9916b26859b5874ad61ff29", "impliedFormat": 99}, {"version": "15bdf2eeef95500ba9f1602896e288cb425e50462b77a07fa4ca23f1068abb21", "impliedFormat": 99}, {"version": "452db58fd828ab87401f6cecc9a44e75fa40716cc4be80a6f66cf0a43c5a60cc", "impliedFormat": 99}, {"version": "54592d0215a3fd239a6aa773b1e1a448dc598b7be6ce9554629cd006ee63a9d6", "impliedFormat": 99}, {"version": "9ee28966bb038151e21e240234f81c6ba5be6fde90b07a9e57d4d84ae8bc030c", "impliedFormat": 99}, {"version": "ad639ad2ec93535c23cfa42fbd23d0d44be0fb50668dd57ee9b38b913e912430", "impliedFormat": 99}, {"version": "956e43b28b5244b27fdb431a1737a90f68c042e162673769330947a8d727d399", "impliedFormat": 99}, {"version": "92a2034da56c329a965c55fd7cffb31ccb293627c7295a114a2ccd19ab558d28", "impliedFormat": 99}, {"version": "c1b7957cd42a98ab392ef9027565404e5826d290a2b3239a81fbac51970b2e63", "impliedFormat": 99}, {"version": "4861ee34a633706bcbba4ea64216f52c82c0b972f3e790b14cf02202994d87c5", "impliedFormat": 99}, {"version": "7af4e33f8b95528de005282d6cca852c48d293655dd7118ad3ce3d4e2790146f", "impliedFormat": 99}, {"version": "df345b8d5bf736526fb45ae28992d043b2716838a128d73a47b18efffe90ffa7", "impliedFormat": 99}, {"version": "a6e18a521af3c12bb42bf2da73d0ef1a82420425726c662d068d8d4d813b16c5", "impliedFormat": 99}, {"version": "dcc38f415a89780b34d827b45493d6dbadb05447d194feb4498172e508c416ac", "impliedFormat": 99}, {"version": "7e917e3b599572a2dd9cfa58ff1f68fda9e659537c077a2c08380b2f2b14f523", "impliedFormat": 99}, {"version": "20b108e922abd1c1966c3f7eb79e530d9ac2140e5f51bfa90f299ad5a3180cf9", "impliedFormat": 99}, {"version": "2bc82315d4e4ed88dc470778e2351a11bc32d57e5141807e4cdb612727848740", "impliedFormat": 99}, {"version": "e2dd1e90801b6cd63705f8e641e41efd1e65abd5fce082ef66d472ba1e7b531b", "impliedFormat": 99}, {"version": "a3cb22545f99760ba147eec92816f8a96222fbb95d62e00706a4c0637176df28", "impliedFormat": 99}, {"version": "287671a0fe52f3e017a58a7395fd8e00f1d7cd9af974a8c4b2baf35cfda63cfa", "impliedFormat": 99}, {"version": "e2cdad7543a43a2fb6ed9b5928821558a03665d3632c95e3212094358ae5896b", "impliedFormat": 99}, {"version": "326a980e72f7b9426be0805774c04838e95195b467bea2072189cefe708e9be7", "impliedFormat": 99}, {"version": "e3588e9db86c6eaa572c313a23bf10f7f2f8370e62972996ac79b99da065acaa", "impliedFormat": 99}, {"version": "1f4700278d1383d6b53ef1f5aecd88e84d1b7e77578761838ffac8e305655c29", "impliedFormat": 99}, {"version": "6362a4854c52419f71f14d3fee88b3b434d1e89dcd58a970e9a82602c0fd707a", "impliedFormat": 99}, {"version": "fb1cc1e09d57dfeb315875453a228948b904cbe1450aaf8fda396ff58364a740", "impliedFormat": 99}, {"version": "50652ed03ea16011bb20e5fa5251301bb7e88c80a6bf0c2ea7ed469be353923b", "impliedFormat": 99}, {"version": "d388e0c1c9a42d59ce88412d3f6ce111f63ce2ff558e0a3f84510092431dfee0", "impliedFormat": 99}, {"version": "35ea0a1e995aef5ae19b1553548a793c76eb31bdf7fef30bc74656660c3a09c3", "impliedFormat": 99}, {"version": "56f4ae4e34cbff1e4158ccada4feea68a357bae86adb3bedaa65260d0af579df", "impliedFormat": 99}, {"version": "88ca3a19c8b99e409299e1173d2fe1b79c5960e966f2f3a7db6788969414f546", "impliedFormat": 99}, {"version": "a4f90a12cbfac13b45d256697ce70a6b4227790ca2bf3898ffd2359c19eab4eb", "impliedFormat": 99}, {"version": "4a6c2ac831cff2d8fa846dfb010ee5f7afce3f1b9bd294298ee54fdc555f1161", "impliedFormat": 99}, {"version": "8395cc6350a8233a4da1c471bdac6b63d5ed0a0605da9f1e0c50818212145b5b", "impliedFormat": 99}, {"version": "b58dda762d6bd8608d50e1a9cc4b4a1663a9d4aa50a9476d592a6ecdc6194af4", "impliedFormat": 99}, {"version": "bc14cb4f3868dab2a0293f54a8fe10aa23c0428f37aece586270e35631dd6b67", "impliedFormat": 99}, {"version": "2d4530d6228c27906cb4351f0b6af52ff761a7fab728622c5f67e946f55f7f00", "impliedFormat": 99}, {"version": "ec359d001e98bf56b0e06b4882bd1421fd088d4d181dff3138f52175c0582a51", "impliedFormat": 99}, {"version": "ed88c3365f1ed406cd592ab4c69c9e31aedbaabaf5450cc93e0f0bd576a48180", "impliedFormat": 99}, {"version": "73468feda625fe017c2904c4d753e8e4e2e292502af8bcd4db59ff56a762692a", "impliedFormat": 99}, {"version": "a85b5df75328fb3857cb558055d78d9aeb437214a766af0ad309ea1bfe943e6e", "impliedFormat": 99}, {"version": "f80561a76c0187c98313433339bb44818fd98dc10f31c0574b0e9e5ba2912700", "impliedFormat": 99}, {"version": "45c293919f535342cd0fcfe2da1a8d346014f7a368e4ec401ebdde80293eef96", "impliedFormat": 99}, {"version": "d8cf10c52fcfed3459ed885435124dfa75b7536c6dc7d56970e2a7c2015533a6", "impliedFormat": 99}, {"version": "77dabe31d44c48782c529d5c9acddc41f799bf9b424b259596131efc77355478", "impliedFormat": 99}, {"version": "6801ebe0b7ab3b24832bc352e939302f481496b5d90b3bc128c00823990d7c7d", "impliedFormat": 99}, {"version": "0abb1feddc76a0283c7e8e8910c28b366612a71f8bfdd5ca42271d7ad96e50b2", "impliedFormat": 99}, {"version": "ac56b2f316b70d6a727fdbbcfa8d124bcd1798c293487acb2b27a43b5c886bb0", "impliedFormat": 99}, {"version": "d849376baf73ec0b17ffd29de702a2fdbbe0c0390ec91bebf12b6732bf430d29", "impliedFormat": 99}, {"version": "70de5b72bc833ab9ee7430534435d10e8edb218d06fdf781e0cae39a7b96067b", "impliedFormat": 99}, {"version": "0f9c9f7d13a5cf1c63eb56318b6ae4dfa2accef1122b2e88b5ed1c22a4f24e3b", "impliedFormat": 99}, {"version": "c693f9c0fda89d41e7670429d30ddcda570f9ad63a7301379695916524eb6d2e", "impliedFormat": 99}, {"version": "66a83abc49216ddee4049056ee2b345c08c912529e93aa725d6cae384561de83", "impliedFormat": 99}, {"version": "6b514d5159d0d189675a1d5a707ba068a6da6bc097afb2828aae0c98d8b32f08", "impliedFormat": 99}, {"version": "39d7dbcfec85393fedc8c7cf62ee93f7e97c67605279492b085723b54ccaca8e", "impliedFormat": 99}, {"version": "81882f1fa8d1e43debb7fa1c71f50aa14b81de8c94a7a75db803bb714a9d4e27", "impliedFormat": 99}, {"version": "d3cfde44f8089768ebb08098c96d01ca260b88bccf238d55eee93f1c620ff5a5", "impliedFormat": 1}, {"version": "b542939a35357458e62f8229c2d7578ae888d63d3ab837395d7bb8a3064c205e", "impliedFormat": 1}, {"version": "3a5af4fba7b27b815bb40f52715aedebaa4b371da3e5a664e7e0798c9b638825", "impliedFormat": 1}, {"version": "8485b6da53ec35637d072e516631d25dae53984500de70a6989058f24354666f", "impliedFormat": 1}, {"version": "ebe80346928736532e4a822154eb77f57ef3389dbe2b3ba4e571366a15448ef2", "impliedFormat": 1}, {"version": "49c632082dc8a916353288d3d8b2dc82b3471794249a381d090d960c8ceac908", "impliedFormat": 1}, {"version": "f672c876c1a04a223cf2023b3d91e8a52bb1544c576b81bf64a8fec82be9969c", "impliedFormat": 1}, {"version": "71addb585c2db7b8e53dc1b0bcfa58c6c67c6e4fa2b968942046749d66f82e7e", "impliedFormat": 1}, {"version": "c76b0c5727302341d0bdfa2cc2cee4b19ff185b554edb6e8543f0661d8487116", "impliedFormat": 1}, {"version": "25b3f581e12ede11e5739f57a86e8668fbc0124f6649506def306cad2c59d262", "impliedFormat": 1}, {"version": "e703cfacb9965c4d4155346c65a0091ecded90ea98874ed6b3f36286577c4dde", "impliedFormat": 1}, {"version": "f5ef066942e4f0bd98200aa6a6694b831e73200c9b3ade77ad0aa2409e8fe1b1", "impliedFormat": 1}, {"version": "b9e99cd94f4166a245f5158f7286c05406e2a4c694619bceb7a4f3519d1d768e", "impliedFormat": 1}, {"version": "5568d7c32e5cf5f35e092649f4e5e168c3114c800b1d7545b7ae5e0415704802", "impliedFormat": 1}, {"version": "e237cd511b24b49085957dd93c29418306940a537e53231c62cc8ce06841a493", "impliedFormat": 99}, {"version": "17eb7d6994fa3706b7e5ea392f35350d581223df7706f0214677cf26e77863d8", "impliedFormat": 99}, {"version": "5121f4b075e5d441d8657292d58773fb55e9011489e697c038bff5e8ae06486a", "impliedFormat": 99}, {"version": "6aaafa9e14217999612042dd5ed572962c178a2c451cccc4b34ca3799ff310ce", "impliedFormat": 99}, {"version": "05364cfecbb8cfeaa60af77f4ec21a61d7dc4e4c6959d1051c66f9b96a6308d1", "impliedFormat": 99}, {"version": "973d9d1727e025f2d005419aae56fa2decd7dbd5d34d66b4c399c184a8a28e59", "impliedFormat": 99}, {"version": "dc9432c8a821a56442280551733b02e7cb5a84eefa8d1622951fd658683af2b7", "impliedFormat": 99}, "034af094803e7d5490f29ca672ba45493801831a31772115fd14878a4b9e6b66", "8077e84593105c26b92dafc7889042593eb96985a00f1a4ab131f57d644cdc9d", "db977d821af56ae3fb7952182d9c0a076a10c75c38bc2d2b000827e720423d32", "3957037558a0dbeac2db19506361a2f0896ec194527449cccace4ed61ee80fff", {"version": "caf4af98bf464ad3e10c46cf7d340556f89197aab0f87f032c7b84eb8ddb24d9", "impliedFormat": 99}, {"version": "7ec047b73f621c526468517fea779fec2007dd05baa880989def59126c98ef79", "impliedFormat": 99}, {"version": "8dd450de6d756cee0761f277c6dc58b0b5a66b8c274b980949318b8cad26d712", "impliedFormat": 99}, {"version": "904d6ad970b6bd825449480488a73d9b98432357ab38cf8d31ffd651ae376ff5", "impliedFormat": 99}, {"version": "dfcf16e716338e9fe8cf790ac7756f61c85b83b699861df970661e97bf482692", "impliedFormat": 99}, "7c5a69e3b69fbc0e7cafc9561cedff2885a5dc1ad1609c6f8fbd84e8164796c3", "18cb7304ae13848d68164976b1913eea8d58d29ae73ce8f68afbcfa9034fe60f", "58651d3d69f29e6a270dbaabaab45cd800902477ec035faa4e7d8b30a9269c76", "790dea6988ddd676560e50a0d5e774064cef9d098505e92241a07e3f69bfc51f", {"version": "cbfd5ef0c8fdb4983202252b5f5758a579f4500edc3b9ad413da60cffb5c3564", "impliedFormat": 99}, "8c036da4baed076de68d27719929dc98151a81b51456bfa6a2488d835303b1d7", {"version": "1cf55c2d6f6d0d1423df8b6bc0aa5fbd44f40fb8f5c75e57ab84a34ea2b9ab02", "affectsGlobalScope": true}, "5d0a2ae80ec99bd500c1501dfbf503df35806384fc83daf08d801d9294cc44a8", "9cfdc86602ae43672c2d1451dc2a4a0bed6eed74cc754da2e97fa8bec18f3693", "7aa6c347a9159b1d740f819a838e1614cfa387d4796576c83622729d65ee1e5a", "e368563ed6cb94471d6c55b427d4739c09acf6926498a851a6c4fa4ddef282cc", "66ac48d3f2f68347c718abc9df3b0e7a51157977f3fc677c3db3d445f55ed929", "dfa3510bb4a69f3a1a6eecb2c8b186c4b55ca356034d499107d2c4f23dbd16d1", "bb6a8eb316124a4b0a7cd74a038f41e6b87ec1389fe2bf1b635a08125bd58418", "7179da355b4a07fd4cdb11e59226f90c57213cd136fd62baf126d87d6661e621", "4d0702bbc433e83bba033245f15046fef816d786b52407d382d4dbaba50f4d0d", "3613fb81842b8538da55f8a52bed9c792c60aae04cad1e7fb2db945a5f07d178", {"version": "260f551168be7a50e7f1d4588574894683709dad712e60cd31282f5ee31c1fa2", "impliedFormat": 99}, "d7d02600effca55d0dcadce8c09c97ebddda3a19c5fa1d52dc9f6f727b26c6b1", "fc969031f2ec314b0b7b8a3bc744c9c3bdbc58200c89ff81afe8e06b6cec832c", "dd6a8db9e28bee784ac8fe7e95724ba6a30425fc91f77b388637379f2bcf7d96", "3ded606708fc76f5314eac3aed39f865f06e05dfe14b0c3de7fc7571669459d1", "39ad851b318c4e3dbe63fab0b0ab770c9fc7102ffeef28d378920a554c0ef691", {"version": "ec69ebd1c4850514ebb6724911ad56e71caa0d076891ed6b67cb10d3ebbf2586", "impliedFormat": 99}, {"version": "7a14bf21ae8a29d64c42173c08f026928daf418bed1b97b37ac4bb2aa197b89b", "impliedFormat": 99}, "d9958fd005db42bcca4f2c12ede63500e74f1a9bd82ddc3a6bf6fcb369706f07", {"version": "4d7d964609a07368d076ce943b07106c5ebee8138c307d3273ba1cf3a0c3c751", "impliedFormat": 99}, {"version": "0e48c1354203ba2ca366b62a0f22fec9e10c251d9d6420c6d435da1d079e6126", "impliedFormat": 99}, {"version": "0662a451f0584bb3026340c3661c3a89774182976cd373eca502a1d3b5c7b580", "impliedFormat": 99}, {"version": "68219da40672405b0632a0a544d1319b5bfe3fa0401f1283d4c9854b0cc114ce", "impliedFormat": 99}, {"version": "ee40ce45ec7c5888f0c1042abc595649d08f51e509af2c78c77403f1db75482a", "impliedFormat": 99}, {"version": "7841bca23a8296afd82fd036fc8d3b1fed3c1e0c82ee614254693ccd47e916fc", "impliedFormat": 99}, {"version": "b09c433ed46538d0dc7e40f49a9bf532712221219761a0f389e60349c59b3932", "impliedFormat": 99}, {"version": "06360da67958e51b36f6f2545214dca3f1bf61c9aef6e451294fcc9aca230690", "impliedFormat": 99}, {"version": "3672426a97d387a710aa2d0c3804024769c310ce9953771d471062cc71f47d51", "impliedFormat": 99}, "9f8cc2079f4816546db1787ac80f9d1f2e869b999c1a1017def769695da04d3c", "9427f74034bc5c3ab9df60684d8d9fbbbae045e9ee6c9481322c291449d2153c", "eb2c378df544263795f154bc0c38c1f4b0248d3308f0ad7241dffacf74d8bf61", "9ae70db74dadc8367c11150ae5ebef0771d16f8ba5b1e23c4e011b83ac614224", {"version": "297af734e0574ac79f40cb0f547c2ad11f8af06362fb8df125e41d5cd04e02b4", "affectsGlobalScope": true}, "571a4c638b3504e2e445b404b0d7ea97c4b067016763f81933fa31f21d6d449e", "b05df4d17a42539d6b5758cfa328e31c1aa590d04e109e52ccc6dcb24a4f65df", "0d0862d9e862ae8dff1174a737c578250bf556c6d40f43b0cebcd91d325a962f", {"version": "5e3795635c7f2de7d418598cd3d5fe37e72e3894a801865f887d9262d3d3aec7", "impliedFormat": 1}, {"version": "9e647e6bc7dbb345450dd4b3cb8ca21d3710f519d8cb5beb6f2bd3a88b41f4e8", "impliedFormat": 1}, {"version": "8661358baea374a2eadc47ce5fe735ce99e790bc90e67ba8706809cacfd2c695", "impliedFormat": 1}, {"version": "e6e2d470b5a6c537ec5651d087480345cb925132286fa4f68c055a14bc6948ea", "impliedFormat": 1}, {"version": "1bdbea3c8a60aa9ed5e85ef052498c7a786cb86a87b54987c82cdc51dedb1fa5", "impliedFormat": 1}, {"version": "e9e2e222500a5657542557400426af1568bca7dabe16024f39489fbb733f03da", "impliedFormat": 1}, {"version": "0d9f8b94f16f2f19544f2030a3c5a4d93fcaa2f62851bb08ec90d75cc79e6891", "impliedFormat": 1}, {"version": "56b20cc6232d69dc1fb6c3491d4b804584758983e02e2a7aa5d0dec0a452f096", "impliedFormat": 1}, {"version": "d0be516bcb9dbd1933dc1d1576bd214de5d2be73855f0fdd2695e116c9e1db45", "affectsGlobalScope": true, "impliedFormat": 1}, "b84e0829dafbd54c903e667079751a42d493cd5863152c00fa82485f490bd15d", "8f0a8a2adc4b24de37ef7cded5698a0ebcb273622aaff9aaea357cda4eab7b34", "c1aeae17bf4dd43db50929aba13b9567dc98b0e400ff464f85dde5bb106651c3", "4bea2ec68744ffda2fc1b59edef37eb16878d98df577c0954c94fa58ec8999fb", {"version": "4a5aa16151dbec524bb043a5cbce2c3fec75957d175475c115a953aca53999a9", "impliedFormat": 99}, "bd29ce723e952bc7f8fab367320347ca82fbb933f4da9872c7120d6765662ebe", "781fb9a532e9a6f91428ee14bf367e43de2c28956556ebb6422a3fe25537a7e5", "be57bd3377658925e1e27f5749fe1ff7f57690b7ff470479cffada5fe64b6315", "f973de4f3e637365f26c9ed50aab9cde65674cb558da7e2ccb76de3fdf6f75ec", "d38ad9a72c1858a2d234e886337082749f5d2ebe6d2025abf796785b09ec9db4", "0cc90b06b7dcc66216e89a92ed095378c735a2350806c9dc3ad284daff568cad", "f554a66ed7380bb56d74201751a14090e45e082ea6602c601fb4ee866a339789", "326b2b2d8087673f1e12e7efb46e23302046212e550dc5c6e97255dbe23de5c2", "b326e2af874b14aa2ac18096ddbec512c6258f3fafc20ba6c8262818d48e6a5b", "c98b6d4b486ef680f61f319f0c742243fe4316c77f90f77a03c51701f76abc6b", "59c2722c63624dc88663ceb49db40da1ddf6faaf5c649e85bad2322f2c9a4413", {"version": "71acd198e19fa38447a3cbc5c33f2f5a719d933fccf314aaff0e8b0593271324", "impliedFormat": 99}, "e69cfc27d78c9ef31b248ab8ea4fa54327c1038843f70efb5cd85d54c0bf1e0e", "aaf46918c590c2bf59e2afb7904dfd8e69317e12ee34ff93ed2746dd06cc994a", "60d5246653c714fc12a67743ee5951331ecd2548cdaef9a599922bcb14da26db", "051597ad3dfca93ded21285da77b8d90d70a968830b8c62a48f3077e593769bb", "6b7555a6999f7457dda58ad209f150ba6017c6c2a19af773f2bac70a213525af", "b14b5d46e253190596eaf4c424221da07776cabc506b3c65729124f69b213395", "c82369ce633b81caad729de4ac49b4a8b4ecef2719baa425ee0578d0f9e7c648", {"version": "9c580c6eae94f8c9a38373566e59d5c3282dc194aa266b23a50686fe10560159", "impliedFormat": 99}, "c956c4cca4b8442fa9314d6079b9f975e1ee39d5804aaf2966990f6258ac342a", "b4a1b8a036f86cbe2c9b6ea4348d35f1e69ff86d3f3ef9becadbe3ecec3af635", "b428550173fce7d51de3d5e6c74a8c1f89202beae165a728b33d7793080aae9f", "2e358c092bb59a77766a682407e343c46268ab1cf32799c74eeb43966e645145", "117b64f93265c08ede5fb4b05d9fae1e0b34ba36d218d9249cf194d262615864", "388d1805a9123336b8525b630f5880b28ec3cd28ec2e8a61013139b5df4899bc", "9a10e61a4cd5409fb3c1493832ad686d58d5e4641c579cce7e10df4351f3b0e9", "1950287f9979103b0851a88bcff43fc7890dd2650357c0bb9239101d13ee0dda", "8e30cd224f0712275fe831967e06adbd0f1ef55cced3c2b712788f80328312d1", "d6991d4b3de0852d81162fa0d8551d576fbfc34f2c0e26d75ece7ddfdbc0f265", "ef1ba22bb02995be05a68f6d6915a1a74c74f14a94c6e1bcdc78d70c164241fe", "c80c606159ffc28d475e783a768134801a4a2df66357920f23fb8900db8314ad", "5df418e50cb665c772b3acc607aff00d7562c7f26618d5a266977ee5f3294a39", "e01e4f6becf9edf5674951623d91339aa0e95595b82468df47f2f13b7c279702", "14de251b664e1cb75666695a8f00f824f5414004d29b03ab3e580677bdeae196", "dfb9dbd16360d306bc8fd4c5b70b2bc490683fa70363e12286bdd4098293658a", "6fc735596f0caf5e3348fb8e3186a5d4eb8e7435e7f9d198d9c48b647618e2be", "ce04a92347a9fc1b27416f99214dd3876ce0092c397684630851c18dae550af9", "dfe2aa408322ff60d70dd596bebefc4f199404de6a018d7840a3f9d04638207c", "da8aaff1e31b8be589a2f49c11e95e7f87f33844309005b03343c9b4f4158072", "f037f299838d60d40f3bd871ab82aad9144ebc4616ca1e8a62a9bcf39b2c3832", "2ac0e9172976a929091d68f429c5d8573888f204df5a01e16c609035d6ad1d70", "7bc2ef015d5074b79892058c840876b6d8e2a0adfaf06fa81db154cfacdf9dac", {"version": "05321b823dd3781d0b6aac8700bfdc0c9181d56479fe52ba6a40c9196fd661a8", "impliedFormat": 1}, "156ff05ace287d832ceb6d42ba4db0f8563d5519f2afd663330a2b5d4e83e3be", "fb71c2e8de5f1f7817d3e619a531eb9aebdcfbc8314524325dbace914a08856e", "e0d1b64f06d365177c58c9b711fab91c49d94083e021ecd52ec6ccf710455d1d", "8d1783856e4bbaf7bdb45c56a7d20ec42f4c05d4cca9c9242bfb302208c362dc", "4c9f45a5429b68094e4ac7976214ae8a2db11a6fccd46a1c66518476263ed0a1", "9815ef3891ffaf69c2d9f7d298f71308520cd5e406246661bf4d0d27dc200f8c", "d8fd12f0179cb096dd35df2b691dc0fb82bf9137cf822bb50260cbd6daad9965", "44b0bd52c9e7552ca87a20d17c8062d4b07fb0eaba01367e55dcd45b4ae436df", "0fef1d5b6a6f235f22c00c6b8bc30c1bd25ee6f49113255b3146f2288ef4bfae", "380552c7845673b7d8910db813a4bb95e38acaff873870ecbff0835b09ff4673", "e7ed011603eec3e5e8c1e1ee80969235a201c305e7db73cdfe0b8de1658371cc", {"version": "7e3373dde2bba74076250204bd2af3aa44225717435e46396ef076b1954d2729", "impliedFormat": 1}, {"version": "1c3dfad66ff0ba98b41c98c6f41af096fc56e959150bc3f44b2141fb278082fd", "impliedFormat": 1}, {"version": "432a61971738da04b67e04e08390ac124cc543479083709896b2071d0a790066", "impliedFormat": 1}, {"version": "1ba55e9efbea1dcf7a6563969ff406de1a9a865cbbdaea2714f090fff163e2b5", "impliedFormat": 1}, {"version": "eb9271b3c585ea9dc7b19b906a921bf93f30f22330408ffec6df6a22057f3296", "impliedFormat": 1}, {"version": "82b7bf38f1bc606dc662c35b8c80905e40956e4c2212d523402ae925bd75de63", "impliedFormat": 1}, {"version": "81be14ad77be99cea7343fdc92a0f4058bcdebaa789d944e04ce4f86f0ca5fbb", "impliedFormat": 1}, {"version": "9f1e00eab512de990ba27afa8634ca07362192063315be1f8166bc3dcc7f0e0f", "impliedFormat": 1}, {"version": "9674788d4c5fcbd55c938e6719177ac932c304c94e0906551cc57a7942d2b53b", "impliedFormat": 1}, {"version": "86dac6ce3fcd0a069b67a1ac9abdbce28588ea547fd2b42d73c1a2b7841cf182", "impliedFormat": 1}, {"version": "4d34fbeadba0009ed3a1a5e77c99a1feedec65d88c4d9640910ff905e4e679f7", "impliedFormat": 1}, {"version": "9d90361f495ed7057462bcaa9ae8d8dbad441147c27716d53b3dfeaea5bb7fc8", "impliedFormat": 1}, {"version": "8fcc5571404796a8fe56e5c4d05049acdeac9c7a72205ac15b35cb463916d614", "impliedFormat": 1}, {"version": "a3b3a1712610260c7ab96e270aad82bd7b28a53e5776f25a9a538831057ff44c", "impliedFormat": 1}, {"version": "33a2af54111b3888415e1d81a7a803d37fada1ed2f419c427413742de3948ff5", "impliedFormat": 1}, {"version": "d5a4fca3b69f2f740e447efb9565eecdbbe4e13f170b74dd4a829c5c9a5b8ebf", "impliedFormat": 1}, {"version": "56f1e1a0c56efce87b94501a354729d0a0898508197cb50ab3e18322eb822199", "impliedFormat": 1}, {"version": "8960e8c1730aa7efb87fcf1c02886865229fdbf3a8120dd08bb2305d2241bd7e", "impliedFormat": 1}, {"version": "27bf82d1d38ea76a590cbe56873846103958cae2b6f4023dc59dd8282b66a38a", "impliedFormat": 1}, {"version": "0daaab2afb95d5e1b75f87f59ee26f85a5f8d3005a799ac48b38976b9b521e69", "impliedFormat": 1}, {"version": "f94362be0203351e67499c41bd1f3c91f4dabf6872e5c880f269d5ad7ffda603", "impliedFormat": 1}, {"version": "75bc851da666e3e8ddfe0056f56ae55e4bd52e42590e35cbe55d89752a991006", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c60b14c297cc569c648ddaea70bc1540903b7f4da416edd46687e88a543515a1", "impliedFormat": 1}, {"version": "d03cf6cd011da250c9a67c35a3378de326f6136c4192a90dd11f3a84627b4ef6", "impliedFormat": 1}, {"version": "9c0217750253e3bf9c7e3821e51cff04551c00e63258d5e190cf8bd3181d5d4a", "impliedFormat": 1}, {"version": "5c2e7f800b757863f3ddf1a98d7521b8da892a95c1b2eafb48d652a782891677", "impliedFormat": 1}, {"version": "73ed3ff18ca862b9d7272de3b0d137d284a0c40e1c94cbf37acd5270ce9b7cd6", "impliedFormat": 1}, {"version": "c61d8275c35a76cb12c271b5fa8707bb46b1e5778a370fd6037c244c4df6a725", "impliedFormat": 1}, {"version": "c7793cb5cd2bef461059ca340fbcd19d7ddac7ab3dcc6cd1c90432fca260a6ae", "impliedFormat": 1}, {"version": "fd3bf6d545e796ebd31acc33c3b20255a5bc61d963787fc8473035ea1c09d870", "impliedFormat": 1}, {"version": "c7af51101b509721c540c86bb5fc952094404d22e8a18ced30c38a79619916fa", "impliedFormat": 1}, {"version": "59c8f7d68f79c6e3015f8aee218282d47d3f15b85e5defc2d9d1961b6ffed7a0", "impliedFormat": 1}, {"version": "93a2049cbc80c66aa33582ec2648e1df2df59d2b353d6b4a97c9afcbb111ccab", "impliedFormat": 1}, {"version": "d04d359e40db3ae8a8c23d0f096ad3f9f73a9ef980f7cb252a1fdc1e7b3a2fb9", "impliedFormat": 1}, {"version": "84aa4f0c33c729557185805aae6e0df3bd084e311da67a10972bbcf400321ff0", "impliedFormat": 1}, {"version": "cf6cbe50e3f87b2f4fd1f39c0dc746b452d7ce41b48aadfdb724f44da5b6f6ed", "impliedFormat": 1}, {"version": "3cf494506a50b60bf506175dead23f43716a088c031d3aa00f7220b3fbcd56c9", "impliedFormat": 1}, {"version": "f2d47126f1544c40f2b16fc82a66f97a97beac2085053cf89b49730a0e34d231", "impliedFormat": 1}, {"version": "724ac138ba41e752ae562072920ddee03ba69fe4de5dafb812e0a35ef7fb2c7e", "impliedFormat": 1}, {"version": "e4eb3f8a4e2728c3f2c3cb8e6b60cadeb9a189605ee53184d02d265e2820865c", "impliedFormat": 1}, {"version": "f16cb1b503f1a64b371d80a0018949135fbe06fb4c5f78d4f637b17921a49ee8", "impliedFormat": 1}, {"version": "f4808c828723e236a4b35a1415f8f550ff5dec621f81deea79bf3a051a84ffd0", "impliedFormat": 1}, {"version": "3b810aa3410a680b1850ab478d479c2f03ed4318d1e5bf7972b49c4d82bacd8d", "impliedFormat": 1}, {"version": "0ce7166bff5669fcb826bc6b54b246b1cf559837ea9cc87c3414cc70858e6097", "impliedFormat": 1}, {"version": "90ae889ba2396d54fe9c517fcb0d5a8923d3023c3e6cbd44676748045853d433", "impliedFormat": 1}, {"version": "3549400d56ee2625bb5cc51074d3237702f1f9ffa984d61d9a2db2a116786c22", "impliedFormat": 1}, {"version": "5ffe02488a8ffd06804b75084ecc66b512f85186508e7c9b57b5335283b1f487", "impliedFormat": 1}, {"version": "b60f6734309d20efb9b0e0c7e6e68282ee451592b9c079dd1a988bb7a5eeb5e7", "impliedFormat": 1}, {"version": "f4187a4e2973251fd9655598aa7e6e8bba879939a73188ee3290bb090cc46b15", "impliedFormat": 1}, {"version": "44c1a26f578277f8ccef3215a4bd642a0a4fbbaf187cf9ae3053591c891fdc9c", "impliedFormat": 1}, {"version": "a5989cd5e1e4ca9b327d2f93f43e7c981f25ee12a81c2ebde85ec7eb30f34213", "impliedFormat": 1}, {"version": "f65b8fa1532dfe0ef2c261d63e72c46fe5f089b28edcd35b3526328d42b412b8", "impliedFormat": 1}, {"version": "1060083aacfc46e7b7b766557bff5dafb99de3128e7bab772240877e5bfe849d", "impliedFormat": 1}, {"version": "d61a3fa4243c8795139e7352694102315f7a6d815ad0aeb29074cfea1eb67e93", "impliedFormat": 1}, {"version": "1f66b80bad5fa29d9597276821375ddf482c84cfb12e8adb718dc893ffce79e0", "impliedFormat": 1}, {"version": "1ed8606c7b3612e15ff2b6541e5a926985cbb4d028813e969c1976b7f4133d73", "impliedFormat": 1}, {"version": "c086ab778e9ba4b8dbb2829f42ef78e2b28204fc1a483e42f54e45d7a96e5737", "impliedFormat": 1}, {"version": "dd0b9b00a39436c1d9f7358be8b1f32571b327c05b5ed0e88cc91f9d6b6bc3c9", "impliedFormat": 1}, {"version": "a951a7b2224a4e48963762f155f5ad44ca1145f23655dde623ae312d8faeb2f2", "impliedFormat": 1}, {"version": "cd960c347c006ace9a821d0a3cffb1d3fbc2518a4630fb3d77fe95f7fd0758b8", "impliedFormat": 1}, {"version": "fe1f3b21a6cc1a6bc37276453bd2ac85910a8bdc16842dc49b711588e89b1b77", "impliedFormat": 1}, {"version": "1a6a21ff41d509ab631dbe1ea14397c518b8551f040e78819f9718ef80f13975", "impliedFormat": 1}, {"version": "0a55c554e9e858e243f714ce25caebb089e5cc7468d5fd022c1e8fa3d8e8173d", "impliedFormat": 1}, {"version": "3a5e0fe9dcd4b1a9af657c487519a3c39b92a67b1b21073ff20e37f7d7852e32", "impliedFormat": 1}, {"version": "977aeb024f773799d20985c6817a4c0db8fed3f601982a52d4093e0c60aba85f", "impliedFormat": 1}, {"version": "d59cf5116848e162c7d3d954694f215b276ad10047c2854ed2ee6d14a481411f", "impliedFormat": 1}, {"version": "50098be78e7cbfc324dfc04983571c80539e55e11a0428f83a090c13c41824a2", "impliedFormat": 1}, {"version": "08e767d9d3a7e704a9ea5f057b0f020fd5880bc63fbb4aa6ffee73be36690014", "impliedFormat": 1}, {"version": "dd6051c7b02af0d521857069c49897adb8595d1f0e94487d53ebc157294ef864", "impliedFormat": 1}, {"version": "79c6a11f75a62151848da39f6098549af0dd13b22206244961048326f451b2a8", "impliedFormat": 1}, "ee4bc77ff8021d99eda7701720a0316ca800c55234b47330555ab8d3f3882e6d", "1037d24ca405bbe8d3526e29b33760f0d057d94a6f35608a1feb3609610c9d83", "8af18123080ff62040ccae57caa4ec20e2d66cb6a53cb3cf739ac54088558a13", "ff64713d3a9a34c752552cf8d863b124dc1b63cc67b5c16f897b14fc9fb8cc45", "493c283ffa18510958d2012ccc1889c73788dad9e41bc9fdc34a7266d4387950", "b9736becfd161252220cb39b5dd750602f4222ac312c6f885367b5c63b7fec2d", "ad3b4f027b61c7c2814bf0a9bd9c790e1116919c8fb3b45cff63d16f03293eed", "e76ea405e7a418ac3f60b557a0d24cb262f1b760f772a3c2fb21f626b1844733", "162d620550f8dc39e8ce13ed035297b53757f51a2e427aff25b4b2fd70226462", "0997d31757d014a403918a79cd4f063b911f02b57b8152ee39194e3ad830a293", "8ca2f789dcace6f108dbe8e666ac137799375244b033daedbddb43ca55bcddab", "454d6ae83b8e1efec484fb1b6aa898347f1ebe8b881732207745fefc7f1721da", "020b6fe68b729c04fe3d075ba98dea6769cab9c32da49bd61f5a65873ebd0f23", "7144f0a25f2077fe90be7ce5764c08d049e5d856fe6ed5319062f98b355a93c8", "ad64f4765a8570ebe64cc06fcde460d24e492b493a34166554980109318fac6a", "36c925091772f74d109c3665f0d2ca6241e4151b6e4dd878d2b401ea9fad4857", "41e03073605a469808a60345e3072008d9e3756fd1987393e4f891f390dceded", "78196f06cbff6d9f6df351cababfaf8ed63ad3889d9807f84fad358fc89aeb0e", "ca19706dab210e0470c5d3334560045a47c0c295c78ff668d69c0e419959d97b", "2bd0e4cdc94ed08c80e8369f4ed8e504d5bfa26404cc3f5e7f8088766cb48738", "b2e4a88a221c95f9956d86aaff35768f527f44413fafa3364f7fbad1a4a368ee", "7716737c38d32d579b0c9543c416eba824ee4208e026ab36362c3445f92879e2", "d644dad51759a092bf639f9a812b383178a97d9c5c86d3f5835baf1582cdf471", "bf2662922e53b9236a9604582d0e7fe2d416e1855b15aaae5f90b5292c822cf2", "8a309d8253f94e4b2f64381b4ff6b532431983354b79d749e524d88cb1244b58", "97fc95cd597ff9213b4db74e2592d8bb32830ee7d239c5af33d9ebbe467f812d", "00051386689589f67b601ade37fb3813667429d49090ebe9376208b9c1f48f5f", "62455e902110a4c990d4a8cdc82f0d71eeb4ae6eb4b5708e70afee82f2d02962", "6f01989e25f29aed341b9060d4f927ddeb66ce693c6b86bb882c18e970c2150b", "cb50149762acec88a496716946824d3823e64cdfb65fa4d83ef8a8ad2ead5ca4", "0dd03185b39ad327a2790d1793e0bf34a1c1dd032cc06c9a9f71685e36ceabe3", "96055867aaa839a5d65315bb980f68c2bebc17cadbc1b4ddd5659b31d6b9e39a", "2a2d9f490e44c8a95ece28d1174255d88bc84e9df7b013702ab23edbe3d5112a", "2fc0d8571e78b5277baee2f91c254d05f3fa3f03030569b6d42b760656fdcd1e", "b90476e1e36c8cc1b36725b8875f7ba06402bc6b2c9f68ce38db9b77cc9e5a02", "cec1d6ce52885aa38d974458934025e4b33720f8581fbaf8cafde947101ab66c", "39bd4fcb9d9be4ec126cb85bfa6e823a408ca0632a408e86188acb5d5581486d", "edd7e439acdea07fa6d9d1317c0f552b95287c2763639cf0c955ab0065b4102f", "be57517088aadef157dd03d81808eb5e58571402c594a3d21cfa929549340b3d", "aeb71532601569707da969dbdca52f8a741be5739ce8bea7e62758b5eb4eccfc", "78714ff79e37f5e3e02f80b516ee00344ea2d241b4e3ae768d6e0fa6b7fce5f0", "71e141da43dd0f31f60804f75e5185a4b06a398cc39bfc76980c3430198a3280", "da748a8f9e0c400d40f0f45d63ba9c49537bc93fcc108dea1cb78b2d1c61bcd7", "47a7bfc16f05a3fdf4b964d74424e19571dd477d17e417b39929b81ac0195861", "47d8cfbe717698c1c28def638d8d57c726551ffa2b9deb65cda97920a1286325", "f7cbcd99331a2e45d47430354921ef0cdd100915f484f8dcb0f49457616943b3", "7fed79575f3d3d15e6de2f4e752ca6c5ee395a6f75d7957a60e68a23d5e1594a", "800ed7b08a472503df5a1f8e1465104f3b767d09ac59bb3b3bfb3502d52835bf", "e9ba500cc75e833f70701091dcfb6a0ddf753964db74da429c17a0b6324d5b28", "960ff343e6c4063886adafdd653464a16c1bdaaab3a323e479bd496cd1649eb4", "57b5ac61ed89e05eaeeb84bf0735fc8b728fbcddeb4508747cffb35d5f4143d4", "e06b9ef5bb93333bbbb042eec9cf9ba4a9bec3dd835f4dd4bd8d65a5e5b58978", {"version": "0943a6e4e026d0de8a4969ee975a7283e0627bf41aa4635d8502f6f24365ac9b", "impliedFormat": 99}, {"version": "1461efc4aefd3e999244f238f59c9b9753a7e3dfede923ebe2b4a11d6e13a0d0", "impliedFormat": 99}, "6b84cb28b5c8e2129d854cffb8c5ba0161758d577db6db30a380cf593a8c0705", {"version": "8dd450de6d756cee0761f277c6dc58b0b5a66b8c274b980949318b8cad26d712", "impliedFormat": 99}, {"version": "dfcf16e716338e9fe8cf790ac7756f61c85b83b699861df970661e97bf482692", "impliedFormat": 99}, {"version": "31c30cc54e8c3da37c8e2e40e5658471f65915df22d348990d1601901e8c9ff3", "impliedFormat": 99}, "5ab57317c19d0edd8e2d6d9c98992bcefa1e023a4f90072e40364451dc18456f", "ed78a053a9db783f48d5dfab7398b630f87c17ed2e237b6f30a3eb153675960c", {"version": "36d8011f1437aecf0e6e88677d933e4fb3403557f086f4ac00c5a4cb6d028ac2", "impliedFormat": 99}, "08b0aa0b05efc573c7d63363c03e83d4b101bfeb54140764e96ddea30659cfcc", {"version": "8085954ba165e611c6230596078063627f3656fed3fb68ad1e36a414c4d7599a", "impliedFormat": 99}, "e78b35ed76c67d8ff50603fbe0dfa4fe600097bd860d89e65beea3e16683dad8", {"version": "f4e8f4151c3490cf7b68c685aabe901cbab19f962aaa2f118a97550e22689a76", "impliedFormat": 1}, {"version": "799003c0ab928582fca04977f47b8d85b43a8de610f4eef0ad2d069fbb9f9399", "impliedFormat": 1}, {"version": "d998eea476c695d8e4ff9d007d5b46d49ca2ffa052f74dc20ca516425abd57b1", "impliedFormat": 1}, {"version": "a0bd46d587005aad4819980f6cf2dbcd80ebf584ed1a946202326a27158ba70e", "impliedFormat": 1}, {"version": "07fcbb61a71bd69a92a5bbde69e60654666cf966b5675c2010c3bf9f436f056a", "impliedFormat": 1}, {"version": "88b2eb23d36692162f2bf1e50577ebcde26de017260473e03ed9a0e61e2726a4", "impliedFormat": 1}, {"version": "23ffbd8c0e20a697d2ea5a0cf7513fb6e42c955a7648f021da12541728f62182", "impliedFormat": 1}, {"version": "43fba5fc019a4ce721a6f53ddb97fdc34c55049cfb793bc544d5c864ee5560b9", "impliedFormat": 1}, {"version": "f4e12292c9a7663a13d152195019711c427c552eb0fa02705e0f61370cd5547a", "impliedFormat": 1}, {"version": "c127ebf14d1b59d1604865008fb072865c5ca52277621f566092fe1f42ce0954", "impliedFormat": 1}, {"version": "def638da26d84825a312113a20649d3086861de7c06a18ea13121278702976fd", "impliedFormat": 1}, {"version": "fbaf86f8ba11298dea2727ce0da84b4ab6ae6c265e1919d44aff7d9b2bbc578a", "impliedFormat": 1}, {"version": "c1010caaeaca8e420c6e040c2e822dbe18702459c93a7d2d5de38597d477b8cd", "impliedFormat": 1}, {"version": "e1f0d8392efd9d71f2644eb97d3f33d90827e30ea8051d93b6f92bb11dff520a", "impliedFormat": 1}, {"version": "085211167559ca307d4053bb8d2298d5ad83cbc3d2ae9bb4c8435a4cabf59369", "impliedFormat": 1}, {"version": "55fc49198d8a85a73cdb79e596d9381cfdc9de93c32c77d42e661c1c1e7268ef", "impliedFormat": 1}, {"version": "6a53fb3df8dd32ed1a65502ca30aeae19cfe80990e78ba68162d6cb2a7fed129", "impliedFormat": 1}, {"version": "b5dcc18d7902597a5584a43c1146ca4fe0295ceb5125f724c1348f6a851dd6ed", "impliedFormat": 1}, {"version": "0c6b0f3fbe6eb6a3805170b3766a341118c92ed7b6d1f193b9f35aa82f594846", "impliedFormat": 1}, {"version": "60eaadb36cf157c5cae9c40e84fa367d04f52a150db3920dbe35139780739143", "impliedFormat": 1}, {"version": "4680a32b1098c49dc87881329af1e68af9af94e051e1b9e19fed555a786f6ce6", "impliedFormat": 1}, {"version": "89fcd129ec37f321cddcdb6b258ffe562de4281e90ec3ccbe7c1199ba39359ca", "impliedFormat": 1}, {"version": "4313011f692861c2c1f5205d7f9a473e763adab6444f9853b96937b187fb19f7", "impliedFormat": 1}, {"version": "caa57157e7bdb8d5f1efe56826fb84a6c8f22a1927bba7fa21fd54e2a44ccba2", "impliedFormat": 1}, {"version": "6b74700abfe4a9b88be957fd8e373cfd998efb1a5f6ad122da49a92997e183ad", "impliedFormat": 1}, {"version": "9ef1342f193bd8bae86c64e450c3ac468ef08652110355e1f3cdd45362eb95c4", "impliedFormat": 1}, {"version": "6853c91662c36a2bf4c8371a87177c819007c76a23c293ef3f686ce9157ae4c8", "impliedFormat": 1}, {"version": "9be1c5dabce43380d13fc621100676b03d420b5687b08d1288f479bee68ab7a8", "impliedFormat": 1}, {"version": "8996d218010896712678e6a0337d8ef8b81c1066ab76f637dd8253f0d6ff838d", "impliedFormat": 1}, {"version": "a15603bf387fc45defe28a68f405a6c29105e135c4e8538eeb6d0a1ef5b69a81", "impliedFormat": 1}, {"version": "84e2532e4d42949a2775cdd8bb7b2b97370dd6ddb683d0c199b21bf6978b152d", "impliedFormat": 1}, {"version": "22bf5f19f620db3b8392cfece44bdd587cdbed80ba39c88a53697d427135bf37", "impliedFormat": 1}, {"version": "23ebbd8d484d07e1c1d8783169c20570ed8409966b28f6be6cf8e970d76ef491", "impliedFormat": 1}, {"version": "18b6fa2c778cad6489f2febf76433453f5e2432ec3535f2d45ae7d803b93cc17", "impliedFormat": 1}, {"version": "609d0d7419999cf44529e6ba687e2944b2fc7ad2570d278fd4e6b1683c075149", "impliedFormat": 1}, {"version": "249cf421b8878a3fe948d9c02f6b0bae65491b3bb974c2ffc612341406fa78ff", "impliedFormat": 1}, {"version": "b4aa22522d653428c8148ddbf1dcc1fb3a3471e15eb1964429a67c390d8c7f38", "impliedFormat": 1}, {"version": "30b2cee905b1848b61c7d28082ebfa2675dd5545c0d25d1c093ce21a905cdccc", "impliedFormat": 1}, {"version": "0a2a2eed4137368735205de97c245f2a685af1a7f1bf8d636b918a0ee4ff4326", "impliedFormat": 1}, {"version": "69f342ce86706aa2835a62898e93ea7a1f21b1d89c70845da69371441bb6cd56", "impliedFormat": 1}, {"version": "b5ab4282affcfd860dd1cc3201653f591509a586d110f8e5b1b010508ba79b2c", "impliedFormat": 1}, {"version": "d396233f6cd3edf0d33c2fbfc84ded029c3ea4a05af3c94d09d31a367cced111", "impliedFormat": 1}, {"version": "bc41a726c817624a5136ae893d7aac7c4dc93c771e8d243a670324bccf39b02b", "impliedFormat": 1}, {"version": "710728600e4b3197f834c4dd1956443be787d2e647a72f190bf6519f235aaadd", "impliedFormat": 1}, {"version": "a45097e01ef30ba26640fed365376ab3ccd5faf97d03f20daff3355a7e60286a", "impliedFormat": 1}, {"version": "763cbb7c22199f43fd5c2b1566af5ba96bf7366f125dd31a038a2291cbc89254", "impliedFormat": 1}, {"version": "031933bf279b7563e11100b5e1746397caf3a278596796a87bc0db23cf68dc9e", "impliedFormat": 1}, {"version": "a4a54c1f58fc6e25a82e2c0f651bf680058bd7f72cfb2d43b85ee0ab5fe2e87e", "impliedFormat": 1}, {"version": "9613d789b6f1037f2523a8f70e1b736f1da4566b470593da062be5c9e13dac57", "impliedFormat": 1}, {"version": "0d2a320763a0c9c71493f8f1069971018c8720a6e7e5a8f10c26b6de79aa2f7d", "impliedFormat": 1}, {"version": "817e0df27a237a268dc16e5acffc19f9a74467093af7a0ba164ee927007a4d25", "impliedFormat": 1}, {"version": "43102521b5ca50ff1865188c3c60790feaed94dc9262b25d4adec4dbc76f9035", "impliedFormat": 1}, {"version": "f99947f8d873b960b0115e506ef9c43f4e40c2071b1d20375564538af4a6023b", "impliedFormat": 1}, {"version": "c1e5ad5ca89d18d2a36d25e8ec105623648cf35615825e202c7d8295a49d61ab", "impliedFormat": 1}, {"version": "2b6c9cb81da4e0a2e32a58230e8c0dec49fc5b345efb7f7a3648b98956be4b13", "impliedFormat": 1}, {"version": "99e34af3ede50062dcc826a1c3ce2d45562060dfd0f29f8066381a6ef548bf2a", "impliedFormat": 1}, {"version": "49f5c2a23ea5fc4b2cdb4426f09d1c8b83f8409fa2af13ef38845cc9b9d4bc3d", "impliedFormat": 1}, {"version": "e935227675144b64ecde3489e4a5e242eeb25fdd6b7464b8c21ad1f7a0faa88b", "impliedFormat": 1}, {"version": "b42e6bbe88dc79c2d6dc5605fb9c15184e70f64bdd7b8d4069b802b90ce86df6", "impliedFormat": 1}, {"version": "b9cd712399fdc00fdae07e96c9b39c3cb311e2a8a5425f1bd583f13cab35e44b", "impliedFormat": 1}, {"version": "5a978550ae131b7fef441d67372fd972abab98ea9fdb9fa266e8bdc89edcb8d6", "impliedFormat": 1}, {"version": "4f287919cfc1d26420db9f0457cd5c8780b1ef0a9f949570936abe48d3a43d91", "impliedFormat": 1}, {"version": "496b23b2fd07e614bc01d90dd4388996cb18cd5f3a612d98201e9f683e58ad2e", "impliedFormat": 1}, {"version": "dcfbe42824f37c5fb6dc7b9427ef2500791ec0d30825ecb614f15b8d5bf5a667", "impliedFormat": 1}, {"version": "390124ad2361b46bf01851d25e331cd7eed355d04451d8b2a4aa985c9de4f8ce", "impliedFormat": 1}, {"version": "14d94f17772c3a58eda01b6603490983d845ee2012cd643f7497b4e22566aacb", "impliedFormat": 1}, {"version": "03ef2386c683707ce741a1c30cb126e8c51a908aa0acc01c3471fafb9baaacd5", "impliedFormat": 1}, {"version": "66a372e03c41d2d5e920df5282dadcec2acae4c629cb51cab850825d2a144cea", "impliedFormat": 1}, {"version": "5b48ba9a30a93176a93c87f9e0abf26a9df457eeb808928009439ca578b56f27", "impliedFormat": 1}, {"version": "4707625392316d3c16edbd0716f4ac310e8ff5d346d58f4d01a2b7e0533a23df", "impliedFormat": 1}, {"version": "154d58a4b2d9c552dc864ea39c223d66efd0ed2dd8b55bd13db5225d14322915", "impliedFormat": 1}, {"version": "6a830433fa072931b4ea3eb9aa5fa7d283f470080586a27bfe69837a0f12de9a", "impliedFormat": 1}, {"version": "d25e930e181f4f69b2b128514538f2abb54ef1d48a046ad776ac6f1cda885a72", "impliedFormat": 1}, {"version": "0259b4c21bc93b52ca82c755f97fc90481072bcc44a8010131b2ea7326cf03fe", "impliedFormat": 1}, {"version": "bea43a13a1104a640da0cb049db85c6993f484a6cc03660496b97824719ecc91", "impliedFormat": 1}, {"version": "0224239d61fe66d4900544d912b2e11c2cca24b4707d53fdb94b874a01e29f48", "impliedFormat": 1}, {"version": "2bce8fd2d16a9432110bbe0ba1e663fd02f7d8b8968cd10178ea7bc306c4a5df", "impliedFormat": 1}, {"version": "9c4ad63738346873d685e5c086acbf41199e7022eff5b72bb668931e9ca42404", "impliedFormat": 1}, {"version": "cfb6329bf8ce324e83fe4bbdee537d866a0d5328246f149a0958b75d033de409", "impliedFormat": 1}, {"version": "efc3816f19ea87a7050c84271ea3d3aad9631a517c168013c4f4b6724c287ce0", "impliedFormat": 1}, {"version": "f99f6737336140047e8dd4ade3859f08331aa4b17bc2bd5f156a25c54e0febbc", "impliedFormat": 1}, {"version": "12a2b25c7c9c05c8994adf193e65749926acfcc076381f7166c2f709a97bdf0a", "impliedFormat": 1}, {"version": "0f93a3fdd517c1e45218cd0027c1d6b82237e379dc6b66d693aab1fe74c82e81", "impliedFormat": 1}, {"version": "03c753da0bee80ad0d0f1819b9b42dfe9bf9f436664caf15325aa426246fd891", "impliedFormat": 1}, {"version": "18f5bf1dae429c451f20171427c9e3223fade4346af4dfd817725cbeb247a09d", "impliedFormat": 1}, {"version": "a4eece5fab202e840dd84f7239e511017a8162edb8fc8b54ff2851c5c844125c", "impliedFormat": 1}, {"version": "c4a94af483a63bf947d89f97553a55df5107c605ec8a26f0b9b8bdcc14bd6d89", "impliedFormat": 1}, {"version": "19de2915ccebc0a1482c2337b34cb178d446def2493bf775c4018a4ea355adb8", "impliedFormat": 1}, {"version": "9be8fc03c8b5392cd17d40fd61063d73f08d0ee3457ecf075dcb3768ae1427bd", "impliedFormat": 1}, {"version": "3b568b63f0e8b3873629a4d7a918dce4266ad41461004ab979f8dcdfd13532bb", "impliedFormat": 1}, {"version": "a5e5223c775fe30d606b8aaa521953c925d5ad176a531c2b69437d2461aaabbd", "impliedFormat": 1}, {"version": "8cbf41d2d1ce8ac2066783ae00613c33feef07493796f638e30beaf892e4354a", "impliedFormat": 1}, {"version": "e22ad737718160df198cd428f18da707177d0467934cecdeed4be6e067b0c619", "impliedFormat": 1}, {"version": "15bf5ed8cb7c1a1e1db53fa9b45bc1a1c73c0497735343a8d0c59fdb596a3744", "impliedFormat": 1}, {"version": "791fce84bce8b6948e4f23422d9cbbd7d08c74b3f91cca12dcae83d96079798b", "impliedFormat": 1}, {"version": "8a2619c8e24305f6b9700b35af178394b995dcb28690a57a71cca87ee7e709ae", "impliedFormat": 1}, {"version": "f95fd2fc3cc164921a891f5d6c935fa0d014a576223dd098fc64677e696b0025", "impliedFormat": 1}, {"version": "8c9cecaaa9caba9a8caa47f46dcf24b524b27899b286d8edcc75a81b370d2ba3", "impliedFormat": 1}, {"version": "2b7a82692ecc877c5379df9653902e23f2d0d0bc9f210ec3cf9e47be54413c5c", "impliedFormat": 1}, {"version": "e2ad09c011cf9d7ee128875406bef787eeb504659495f42656a0098c15fe646c", "impliedFormat": 1}, {"version": "eb518567ea6b0b2623f9a6d37c364e1b1ac9d8b508d79e558f64ac05c17e2685", "impliedFormat": 1}, {"version": "630a48fb8f6b07161588e0aee3f9d301c59c97e1532c884118f89368baf4073b", "impliedFormat": 1}, {"version": "14736c608aa46120f8d6d0bc5e0721b46b927bc7eba20e479600571935f27062", "impliedFormat": 1}, {"version": "7574803692d2230db13205a7749b9c3587dccaccdf9e76f003f9e08078bb6d09", "impliedFormat": 1}, {"version": "f3cc1588e666651c51353b1728460bee8acbc6e0f36be8c025eaaf292dca525d", "impliedFormat": 1}, {"version": "0d4ea8a20527dcf3ad6cf1bd188b8ad4e449df174fad09b9e540ed81080af834", "impliedFormat": 1}, {"version": "aa82876d59912d25becff5a79ed7341af04c71bfeb2221cc0417bc34531125e2", "impliedFormat": 1}, {"version": "6f4b0389f439adc84cba35d45428668eabcfbdd351ba17e459d414ca51ab8eb8", "impliedFormat": 1}, {"version": "d5dd33d15fbb07668c264b38065ac542a07a7650af4917727bbc09b58570e862", "impliedFormat": 1}, {"version": "7d90202d0212e9cdc91a20bfddf04a539c89f09fe1d64db3343546fa2eb37e71", "impliedFormat": 1}, {"version": "1a5d073c95a3a4480b17d2fa7fd41862a9df0cb2afaee86834b13649e96bdb45", "impliedFormat": 1}, {"version": "2092495a5b3116c760527a690c4529748f2d8b126cdd5f56b2ce2230b48aba3f", "impliedFormat": 1}, {"version": "620b29d6adbd4061bc0a8fedf145fcc8e8fc9648fb6e0a39726e33babb4e07bc", "impliedFormat": 1}, {"version": "931eda51b5977f7f3fa7a0d9afde01cfd8b0cc1df0bb66dcf8c2cf6e7090384e", "impliedFormat": 1}, {"version": "b084a412374bdd124048c52c4e8a82d64f3adec6c0a9ad5ecbb7317636039b0f", "impliedFormat": 1}, {"version": "11199daa694c3ced3cc2a382a3fa7bd64e95eb40f9bbc3979fc8fb43f5ba38cc", "impliedFormat": 1}, {"version": "2c86f279d7db3c024de0f21cd9c8c2c972972f842357016bfbbd86955723b223", "impliedFormat": 1}, {"version": "dfb53b9d748df3e140b0fddb75f74d21d7623e800bb1f233817a1a2118d4bb24", "impliedFormat": 1}, {"version": "8cfc293b33082003cacbf7856b8b5e2d6dd3bde46abbd575b0c935dc83af4844", "impliedFormat": 1}, {"version": "7730c538d6d35efe95d2c0d246b1371565b13037e893178033360b4c9d2ac863", "impliedFormat": 1}, {"version": "b256694544b0d45495942720852d9597116979d52f2b53c559fda31f635c60df", "impliedFormat": 1}, {"version": "794e8831c68cc471671430ee0998397ea7a62c3b706b30304efdc3eaff77545a", "impliedFormat": 1}, {"version": "9cfc1b227477e31988e3fb18d26b6988618f4a5da9b7da6bc3df7fc12fb2602e", "impliedFormat": 1}, {"version": "264a292b6024567dd901fdabbf3239a8742bea426432cdbda4cf390b224188e1", "impliedFormat": 1}, {"version": "f1556a28bb8e33862dcfa9da7e6f1dca0b149faf433fe6a50153ae76f3362db1", "impliedFormat": 1}, {"version": "1d321aea1c6a77b2a44e02e5c2aeff290e3f1675ead1a86652b6d77f5fea2b32", "impliedFormat": 1}, {"version": "4910efc2ce1f96d6e71a9e7c9437812ffae5764b33ab3831c614663f62294124", "impliedFormat": 1}, {"version": "e3ceab51a36e8b34ab787af1a7cf02b9312b6651bac67c750579b3f05af646c1", "impliedFormat": 1}, {"version": "baf9f145bcee1b765bed6e79fd45e1ff0ca297a81315944de81eb5d6fff2d13d", "impliedFormat": 1}, {"version": "2afd62362b83db93cd20de22489fe4d46c6f51822069802620589a51ccad4b99", "impliedFormat": 1}, {"version": "9f0cd9bd4ab608123b88328c78814738cbdee620f29258b89ef8cd923f07ff9c", "impliedFormat": 1}, {"version": "801186c9e765583c825f28dab63a7ad12db5609e36dc6d9acbdc97d23888a463", "impliedFormat": 1}, {"version": "96c515141c6135ccd6fb655fb9e3500074a9216ba956fb685dc8edc33f689594", "impliedFormat": 1}, {"version": "416af6d65fc76c9ced6795f255cb1096c9d7947bede75b82289732b74d902784", "impliedFormat": 1}, {"version": "a280c68b128ebba35fb044965d67895201c2f83b6b28281bb8b023ade68bf665", "impliedFormat": 1}, {"version": "6fa118f15723b099a41d3beea98ed059bcd1b3eda708acf98c5eff0c7e88832f", "impliedFormat": 1}, {"version": "dcbf582243e20ea50d283f28f4f64e9990b4ed4a608757e996160c63cff6aa99", "impliedFormat": 1}, {"version": "efa432d8fd562529c4e9f859fd936676dd8fef5d3b4bedb06f754e4740056ea9", "impliedFormat": 1}, {"version": "a59b66720b2ccf2e0150fafb49e8da8dabdf4e1be36244a4ccd92f5bd18e1e9e", "impliedFormat": 1}, {"version": "c657fb1ec3b727d6a14a24c71ea20c41cb7d26a503e8e41b726bb919eb964534", "impliedFormat": 1}, {"version": "50d6d3174868f6e974355bf8e8db8c8b3fcf059315282a0c359ecf799d95514a", "impliedFormat": 1}, {"version": "86bf79091014a1424fc55122caa47f08622b721a4d614b97dd620e3037711541", "impliedFormat": 1}, {"version": "7a63313dff3a57f824a926e49a7262f7bd14e0e833cf45fa5af6da25286769c2", "impliedFormat": 1}, {"version": "36dcaeffe1a1aed1cb84d4feba32895bf442795170edccc874fa32232b2354e5", "impliedFormat": 1}, {"version": "686c6962d04d90edafc174aa5940acb9c9db8949c8d425131c01d796cf9a3aef", "impliedFormat": 1}, {"version": "2b1dbc3d5762d6865744b6e7be94b8b9004097698c37e93e06983e42dd8fe93b", "impliedFormat": 1}, {"version": "eb5e8f74826bdf3a6a0644d37a0f48133f8ad0b5298cc2c574102868542ba4eb", "impliedFormat": 1}, {"version": "c6a82a9673ba517cf04dd0803513257d0adf101aed2e3b162a54d840c9a1a3b2", "impliedFormat": 1}, {"version": "fc9f0f415abaa323efcecc4a4e0b6763bfe576e32043546d44f1de6541b6399b", "impliedFormat": 1}, {"version": "2c4d772ac7ac56a44deef82903364eb7c78dd7bc997701123df0ce4639fe39bb", "impliedFormat": 1}, {"version": "9369ef11eed17c1c223fdea9c0fa39e83f3722914ef390b1448db3d71620c93a", "impliedFormat": 1}, {"version": "aa84130dbc9049bba6095f87932138698f53259b642635f6c9e92dd0ddc7512c", "impliedFormat": 1}, {"version": "084ceadd21efabd4b58667dca00d4f644306099151d2ee18cd28a395855b8009", "impliedFormat": 1}, {"version": "b9503e29f06c99b352b7cae052da19e3599fa42899509d32b23a27c9bb5bebf6", "impliedFormat": 1}, {"version": "75188920fe6ccc14070fe9a65c036049f1141d968c627b623d4a897ec3587e15", "impliedFormat": 1}, {"version": "e2e1df7f45013d2b34f8d08e6ae5a9339724b0ea251b5445fcca3e170e640105", "impliedFormat": 1}, {"version": "af06feb5d18a6ea11c088b683bdb571800d1f76b98d848eecdf41e5ec8f317fd", "impliedFormat": 1}, {"version": "0596af52b95e0c8adc2c07f49f109d746b164739c5866fa8bb394dd6329a3725", "impliedFormat": 1}, {"version": "c3365d08fe7a1ccc3b8e8638edc30123007f3241b4604e2585b9f14422ab97d8", "impliedFormat": 1}, {"version": "a7a3d96b04bb0ec8cb7d2669767c4756f97dd70d08548f9e6522dde4de8e8a03", "impliedFormat": 1}, {"version": "745e960e885a4ba04c872225cbb44bd67a7490d169ceaefab7c0dfc444768676", "impliedFormat": 1}, {"version": "0b1ce1768cde3535493a9daf99e3bbb8c7dcc3a7f9d8cd358cb846af71ce5cdf", "impliedFormat": 1}, {"version": "48b9603f6e8a7c94b727277592a089f94261baa64e6c9d18165da0481663a69e", "impliedFormat": 1}, {"version": "3c20a3bb0c50c819419f44aa55acc58476dad4754a16884cef06012d02b0722f", "impliedFormat": 1}, {"version": "4dc64902cb86e677a928293593658fbf53388f9a30d2b934140c70a7267b07ec", "impliedFormat": 1}, {"version": "cb4fd56539a61d163ea9befe6b0292c32aa68a104c1f68f61416f1bc769bcfba", "impliedFormat": 1}, {"version": "0d852bdc2b72b22393a8eebe374ee3efe3e0d44e630037b5e1b6087985388e62", "impliedFormat": 1}, {"version": "b6c9a2deefb6a57ff68d2a38d33c34407b9939487fc9ee9f32ba3ecf2987a88a", "impliedFormat": 1}, {"version": "f6b371377bab3018dac2bca63e27502ecbd5d06f708ad7e312658d3b5315d948", "impliedFormat": 1}, {"version": "faa72893e85cb8ebb1dafde6b427e5204e60bb5f3ee6576bb64c01db1f255bc8", "impliedFormat": 1}, {"version": "95b7ed47b31a6eaddcdd853ee0871f2bb61e39ce36a01d03dfafb83766f6c10c", "impliedFormat": 1}, {"version": "19287d6b76288c2814f1633bdd68d2b76748757ffd355e73e41151644e4773d6", "impliedFormat": 1}, {"version": "fc4e6ec7dade5f9d422b153c5d8f6ad074bd9cc4e280415b7dc58fb5c52b5df1", "impliedFormat": 1}, {"version": "3aea973106e1184db82d8880f0ca134388b6cbc420f7309d1c8947b842886349", "impliedFormat": 1}, {"version": "765e278c464923da94dda7c2b281ece92f58981642421ae097862effe2bd30fa", "impliedFormat": 1}, {"version": "de260bed7f7d25593f59e859bd7c7f8c6e6bb87e8686a0fcafa3774cb5ca02d8", "impliedFormat": 1}, {"version": "d95c4eaad4df9e564859f0c74a177fa0b2e5f8a155939b52580566ab6b311c3f", "impliedFormat": 1}, {"version": "7192a6d17bfa06e83ba14287907b7c671bef9b7111c146f59c6ea753cfc736b9", "impliedFormat": 1}, {"version": "5156d3d392db5d77e1e2f3ea723c0a8bd3ca8acffe3b754b10c84b12f55a6e10", "impliedFormat": 1}, {"version": "a6494e7833ee04386a9f0c686726f7cb05f52f6e069d9293475ccb1e791ee0da", "impliedFormat": 1}, {"version": "d9af0c89a310256851238f509a22aa1071a464d35dc22ea8c2a0bae42dd81bc5", "impliedFormat": 1}, {"version": "291642a66e55e6ca38b029bc6921c7301f5c7b7acf21ae588a5f352e6c1f6d58", "impliedFormat": 1}, {"version": "43cd7c37298b051d1ce0307d94105bcd792c6c7e017282c9d13f1097c27408e8", "impliedFormat": 1}, {"version": "e00d8cce6e2e627654e49c543b582568ad0bf27c1d4ad1018d26aff78d7599df", "impliedFormat": 1}, {"version": "ed13354f0d96fb6d5878655b1fead51722b54875e91d5e53ef16de5b71a0e278", "impliedFormat": 1}, {"version": "fcb934d0fcdee06a8571bd90aa3a63aa288c784b3ebcecfe7ae90d3104d321f4", "impliedFormat": 1}, {"version": "af682dfabe85688289b420d939020a10eb61f0120e393d53c127f1968b3e9f66", "impliedFormat": 1}, {"version": "0dca04006bf13f72240c6a6a502df9c0b49c41c3cab2be75e81e9b592dcd4ea8", "impliedFormat": 1}, {"version": "7dc0b5e3d7be8e1f451f0545448c2eaa02683f230797d24434b36f9820d5a641", "impliedFormat": 1}, {"version": "247af61cdc3f4ec7876b9e993a2ecdd069e10934ff790c9cee5811842bff49eb", "impliedFormat": 1}, {"version": "4be8c2c63d5cd1381081d90021ddfaef106881df4129eddeeaba906f2d0f75d0", "impliedFormat": 1}, {"version": "012f621d6eb28172afb1b2dc23898d8bc74cf35a6d76b63e5581aa8e50fa71b3", "impliedFormat": 1}, {"version": "3a561fa91097e4580c5349ce72e69d247c31c11d29f39e1d0bd3716042ff2c0b", "impliedFormat": 1}, {"version": "bc9981a79dda3badea61d716d368a280c370267e900f43321f828495f4fef23c", "impliedFormat": 1}, {"version": "2ed3b93d55aea416d7be8d49fe25016430caab0fe64c87d641e4c2c551130d17", "impliedFormat": 1}, {"version": "3d66dfc31dd26092c3663d9623b6fc5cec90878606941a19e2b884c4eacd1a24", "impliedFormat": 1}, {"version": "6916c678060af14a8ce8d78a1929d84184e9507fba7ab75142c1bcb646e1c789", "impliedFormat": 1}, {"version": "3eea74afae095028597b3954bde69390f568afc66d457f64fff56e416ea47811", "impliedFormat": 1}, {"version": "549fb2d19deb7d7cae64922918ddddf190109508cc6c7c47033478f7359556d2", "impliedFormat": 1}, {"version": "e7023afc677a74f03f8ccb567532fe9eedd1f5241ee74be7b75ac2336514f6f6", "impliedFormat": 1}, {"version": "ff55505622eac7d104b9ab9570f4cc67166ba47dd8f3badfb85605d55dd6bdc9", "impliedFormat": 1}, {"version": "102fac015b1eebfa13305cb90fd91a4f0bbcabb10f2343556b3483bbb0a04b62", "impliedFormat": 1}, {"version": "18a1f4493f2dbad5fd4f7d9bfba683c98cf5ed5a4fa704fa0d9884e3876e2446", "impliedFormat": 1}, {"version": "f57e6707d035ab89a03797d34faef37deefd3dd90aa17d90de2f33dce46a2c56", "impliedFormat": 1}, {"version": "cc8b559b2cf9380ca72922c64576a43f000275c72042b2af2415ce0fb88d7077", "impliedFormat": 1}, {"version": "1a337ca294c428ba8f2eb01e887b28d080ee4a4307ae87e02e468b1d26af4a74", "impliedFormat": 1}, {"version": "310fe80ff40a158c2de408efbe9de11e249c53d2de5e33ca32798e6f3fbc8822", "impliedFormat": 1}, {"version": "d6ce96c7bb34945c1d444101f44e0f8ba0bba8ab7587a6cc009a9934b538c335", "impliedFormat": 1}, {"version": "1b10a2715917601939a9288d49beccd45b591723256495b229569cd67bbe48a8", "impliedFormat": 1}, {"version": "7498dfdeed2e003ec49cdf726ff6c293002d1d7fdadbc398ce8aafe6d0688de7", "impliedFormat": 1}, {"version": "8492306a4864a1dc6fc7e0cc0de0ae9279cbd37f3aae3e9dc1065afcdc83dddc", "impliedFormat": 1}, {"version": "9c86abbc4fd0248f56abc12aaecd76854517389af405d5ec2eb187fdb00a606f", "impliedFormat": 1}, {"version": "9ffd906f14f8b059d6b95d6640920f530507e596e548f7a595da58ab66e3ce76", "impliedFormat": 1}, {"version": "1884bccc10ce40adca470c2c371c1c938b36824f169c56f7f43d860416ca0a4c", "impliedFormat": 1}, {"version": "986b55b4f920c99d77c1845f2542df6f746cb5adc9ab93eb1545a7e6ef37590d", "impliedFormat": 1}, {"version": "cd00906068b81fbd8a22d021580ac505e272844408174520fafed0ae00627a5d", "impliedFormat": 1}, {"version": "69fab68a769c17a52a24b868aeb644f3ee14abaa5064115f575ddd59231105ce", "impliedFormat": 1}, {"version": "e181eb86b2caf80fe18c72efce6b913bc226e4a69a5456eaf4f859f1c29c6fd6", "impliedFormat": 1}, {"version": "93f7871380478bc6acf02ad9f3dc7da0c21997caebbe782eb93a11b7bd06a46d", "impliedFormat": 1}, {"version": "d00279ab020713264f570d5181c89ca362b7de8abddf96733de86bce0eca082c", "impliedFormat": 1}, {"version": "f7db473f1d5d2a124f14886ac9dbfeccfbb94a98bbe1610a47c30c2933afa279", "impliedFormat": 1}, {"version": "f44cf6c6d608ef925831e550b19841b5d71bd87195bd346604ff05644fb0d29c", "impliedFormat": 1}, {"version": "154f23902d7a3fcdace4c20b654da7355fee4b7f807d1f77d6c9a24a8756013a", "impliedFormat": 1}, {"version": "562f4f3c75a497d3ad7709381f850bb8c7646a9c6e94fdf8e91928e23d155411", "impliedFormat": 1}, {"version": "4583380b676ee59b70a9696b42acfa986cd5f32430f37672e04f31f40b05df74", "impliedFormat": 1}, {"version": "ad0a13f35a0d88803979f8ea9050ad7441e09d21a509abf2f303e18c1267af17", "impliedFormat": 1}, {"version": "ba9781c718ab3d09cbde1216029072698d2da6135f0d2f856ba387d6caceb13e", "impliedFormat": 1}, {"version": "d7c597c14698ba5fc8010076afa426f029b2d8edabb5073270c070cc645ba638", "impliedFormat": 1}, {"version": "bd2afc69cf1d85cd950a99813bc7eff007d8afa496e7c2142a845cd1181d0474", "impliedFormat": 1}, {"version": "558b462b23ea186d094dbff158d652acd58c0988c9fd53af81a8903412aa5901", "impliedFormat": 1}, {"version": "0e984ae642a15973d652fd7b0d2712a284787d0d7a1db99aa49af0121e47f1df", "impliedFormat": 1}, {"version": "0ad53ee208a23eef2a5cb3d85f2a9dc1019fd5e69179c4b0c02dc56c40d611c4", "impliedFormat": 1}, {"version": "7a6898b26947bd356f33f4efef3eb23e61174d85dca19f41a8780d6bb4bfb405", "impliedFormat": 1}, {"version": "9fe30349d26f34e85209fb06340bac34177f7eae3d6bb69dc12cd179d2c13ddf", "impliedFormat": 1}, {"version": "d568c51d2c4360fd407445e39f4d86891dba04083402602bf5f24fd3969cacbb", "impliedFormat": 1}, {"version": "b2483a924349ec835f4d778dd6787447a2f8bfbb651164851bff29d5b3d990a6", "impliedFormat": 1}, {"version": "aae66889332cff4b2f7586c5c8758abc394d8d1c48f9b04b0c257e58f629d285", "impliedFormat": 1}, {"version": "0f86c85130c64d6dbe6a9090bb3df71c4b0987bce4a08afe1ac4ece597655b9c", "impliedFormat": 1}, {"version": "0ce28ad2671baed24517e1c1f4f2a986029137635bce788ee8fb542f002ac5b8", "impliedFormat": 1}, {"version": "cd12e4fe77d24db98d66049360a4269299bcfb9dc3a1b47078ab1b4afac394cb", "impliedFormat": 1}, {"version": "1589e5ac394b2b2e64264da3e1798d0e103b4f408f5bae1527d9e706f98269c7", "impliedFormat": 1}, {"version": "ff8181aa0fde5ec2d737aecc5ebaa9e881379041f13e5ce1745620e17f78dcf9", "impliedFormat": 1}, {"version": "0b2e54504b568c08df1e7db11c105786742866ba51e20486ab9b2286637d268f", "impliedFormat": 1}, {"version": "bc1ffc3a2dca8ee715571739be3ec74d079e60505e1d0d2446e4978f6c75ba5c", "impliedFormat": 1}, {"version": "770a40373470dff27b3f7022937ea2668a0854d7977c9d22073e1c62af537727", "impliedFormat": 1}, {"version": "a0f8ce72cb02247a112ce4a2fa0f122478a8e99c90a5e6b676b41a68b1891ad2", "impliedFormat": 1}, {"version": "6e957ea18b2bf951cf3995d115ad9bfa439e8d891aeb1afc901d793202c0b90d", "impliedFormat": 1}, {"version": "a1c65bd78725f9172b5846c3c58ddf4bcbb43a30ab19e951f0102552fbfd3d5d", "impliedFormat": 1}, {"version": "04718c7325e7df4bac9a6d026a0a2bd5a8b54501f274aaf93a03b5d1d0635bd1", "impliedFormat": 1}, {"version": "405205f932d4e0ce688a380fa3150b1c7ff60e7fc89909e11a33eab7af240edb", "impliedFormat": 1}, {"version": "566fc1a6616a522f8b45082032a33e6d37ff7df3f7d4d63c3cce9017d0345178", "impliedFormat": 1}, {"version": "3b699b08db04559803b85aa0809748e61427b3d831f77834b8206e9f2ed20c93", "impliedFormat": 1}, {"version": "b27242dd3af2a5548d0c7231db7da63d6373636d6c4e72d9b616adaa2acef7e1", "impliedFormat": 1}, {"version": "e0ee7ba0571b83c53a3d6ec761cf391e7128d8f8f590f8832c28661b73c21b68", "impliedFormat": 1}, {"version": "072bfd97fc61c894ef260723f43a416d49ebd8b703696f647c8322671c598873", "impliedFormat": 1}, {"version": "e70875232f5d5528f1650dd6f5c94a5bed344ecf04bdbb998f7f78a3c1317d02", "impliedFormat": 1}, {"version": "8e495129cb6cd8008de6f4ff8ce34fe1302a9e0dcff8d13714bd5593be3f7898", "impliedFormat": 99}, {"version": "8079d851ffa1dbe193fe36643b3d67f4fdf8d360df4c900065788eff44bc15a7", "impliedFormat": 1}, "a7873d2b249506009a6f018b10a58c7cb38cc583d0d9391e2d90ed288cf6d013", {"version": "e516240bc1e5e9faef055432b900bc0d3c9ca7edce177fdabbc6c53d728cced8", "impliedFormat": 99}, {"version": "5402765feacf44e052068ccb4535a346716fa1318713e3dae1af46e1e85f29a9", "impliedFormat": 99}, {"version": "e16ec5d4796e7a765810efee80373675cedc4aa4814cf7272025a88addf5f0be", "impliedFormat": 99}, {"version": "1f57157fcd45f9300c6efcfc53e2071fbe43396b0a7ed2701fbd1efb5599f07f", "impliedFormat": 99}, {"version": "9f1886f3efddfac35babcada2d454acd4e23164345d11c979966c594af63468b", "impliedFormat": 99}, {"version": "a3541c308f223863526df064933e408eba640c0208c7345769d7dc330ad90407", "impliedFormat": 99}, {"version": "59af208befeb7b3c9ab0cb6c511e4fec54ede11922f2ffb7b497351deaf8aa2e", "impliedFormat": 99}, {"version": "928b16f344f6cddaba565da8238f4cf2ddf12fe03eb426ab46a7560e9b3078fa", "impliedFormat": 99}, {"version": "120bdf62bccef4ea96562a3d30dd60c9d55481662f5cf31c19725f56c0056b34", "impliedFormat": 99}, {"version": "39e0da933908de42ba76ea1a92e4657305ae195804cfaa8760664e80baac2d6a", "impliedFormat": 99}, {"version": "55ce6ca8df9d774d60cef58dd5d716807d5cc8410b8b065c06d3edac13f2e726", "impliedFormat": 99}, {"version": "788a0faf3f28d43ce3793b4147b7539418a887b4a15a00ffb037214ed8f0b7f6", "impliedFormat": 99}, {"version": "a3e66e7b8ccdab967cd4ada0f178151f1c42746eabb589a06958482fd4ed354e", "impliedFormat": 99}, {"version": "bf45a2964a872c9966d06b971d0823daecbd707f97e927f2368ba54bb1b13a90", "impliedFormat": 99}, {"version": "39973a12c57e06face646fb79462aabe8002e5523eec4e86e399228eb34b32c9", "impliedFormat": 99}, {"version": "f01091e9b5028acfb38208113ae051fad8a0b4b8ec1f7137a2a5cf903c47eefc", "impliedFormat": 99}, {"version": "b3e87824c9e7e3a3be7f76246e45c8d603ce83d116733047200b3aa95875445b", "impliedFormat": 99}, {"version": "7e1f7f9ae14e362d41167dc861be6a8d76eca30dde3a9893c42946dc5a5fc686", "impliedFormat": 99}, {"version": "9308ef3b9433063ac753a55c3f36d6d89fa38a8e6c51e05d9d8329c7f1174f24", "impliedFormat": 99}, {"version": "cd3bb1aa24726a0abd67558fde5759fe968c3c6aa3ec7bad272e718851502894", "impliedFormat": 99}, {"version": "1ae0f22c3b8420b5c2fec118f07b7ebd5ae9716339ab3477f63c603fe7a151c8", "impliedFormat": 99}, {"version": "919ff537fff349930acc8ad8b875fd985a17582fb1beb43e2f558c541fd6ecd9", "impliedFormat": 99}, {"version": "4e67811e45bae6c44bd6f13a160e4188d72fd643665f40c2ac3e8a27552d3fd9", "impliedFormat": 99}, {"version": "3d1450fd1576c1073f6f4db9ebae5104e52e2c4599afb68d7d6c3d283bdbaf4f", "impliedFormat": 99}, {"version": "c072af873c33ff11af126c56a846dfada32461b393983a72b6da7bff373e0002", "impliedFormat": 99}, {"version": "de66e997ea5376d4aeb16d77b86f01c7b7d6d72fbb738241966459d42a4089e0", "impliedFormat": 99}, {"version": "d77ea3b91e4bc44d710b7c9487c2c6158e8e5a3439d25fc578befeb27b03efd7", "impliedFormat": 99}, {"version": "a3d5c695c3d1ebc9b0bd55804afaf2ac7c97328667cbeedf2c0861b933c45d3e", "impliedFormat": 99}, {"version": "270724545d446036f42ddea422ee4d06963db1563ccc5e18b01c76f6e67968ae", "impliedFormat": 99}, {"version": "85441c4f6883f7cfd1c5a211c26e702d33695acbabec8044e7fa6831ed501b45", "impliedFormat": 99}, {"version": "0f268017a6b1891fdeea69c2a11d576646d7fd9cdfc8aac74d003cd7e87e9c5a", "impliedFormat": 99}, {"version": "9ece188c336c80358742a5a0279f2f550175f5a07264349d8e0ce64db9701c0b", "impliedFormat": 99}, {"version": "cf41b0fc7d57643d1a8d21af07b0247db2f2d7e2391c2e55929e9c00fbe6ab9a", "impliedFormat": 99}, {"version": "11e7ddddd9eddaac56a6f23d8699ae7a94c2a55ae8c986fdabc719d3c3e875a1", "impliedFormat": 99}, {"version": "dd129c2d348be7dbf9f15d34661defdfc11ee00628ca6f7161bead46095c6bc3", "impliedFormat": 99}, {"version": "c38d8e7cfc64bbfc14a63346388249c1cfa2cc02166c5f37e5a57da4790ce27f", "impliedFormat": 99}, "5a4ff73c804e86c873382da80c453f1399006326ef042fb984c24162ec86666e", "e30219cedb35c55c2f9069f6470d60514c54c43fe0a3b641615275a2acd25f12", {"version": "ecd224c9132c9f44b2a93b9c4b6b29d839bef41df565fe2bc6f46a6f4f20d109", "impliedFormat": 99}, "60a0084cbdfcb96e54c9f6438e9066dfe7ed0a7e421b33985d5eb57975d110a4", "f4cdd104de29928bfcd40b865c7d08eed9157a537fbb8b5e6d0921f02b63cc04", {"version": "bb703864a1bc9ca5ac3589ffd83785f6dc86f7f6c485c97d7ffd53438777cb9e", "impliedFormat": 1}, "924fef47d5e04a133ecc700e5881175cef1f71a338eee7c4db5cf5fc46b9c5e1", {"version": "89783bd45ab35df55203b522f8271500189c3526976af533a599a86caaf31362", "impliedFormat": 99}, {"version": "6da2e0928bdab05861abc4e4abebea0c7cf0b67e25374ba35a94df2269563dd8", "impliedFormat": 99}, "b3abf3bb92fc028dd9a4c5dc6a10093b7f1346be8841fabb214fa08e5f40b181", {"version": "e7441be68f390975c6155c805cea8f54cc1b7f3656b6b9440ecbbbd7753499e6", "impliedFormat": 99}, "774316527ddc577fc54012a0c898ebcf7cf8f11152126e550828b53004a5b70c", {"version": "26e6c521a290630ea31f0205a46a87cab35faac96e2b30606f37bae7bcda4f9d", "impliedFormat": 99}, "aca10633792d03bb541e09ea106b5e1f3de429b69b3cade5ccfb7dca20289424", {"version": "91b4ce96f6ad631a0a6920eb0ab928159ff01a439ae0e266ecdc9ea83126a195", "impliedFormat": 1}, {"version": "e3448881d526bfca052d5f9224cc772f61d9fc84d0c52eb7154b13bd4db9d8b2", "impliedFormat": 1}, {"version": "e348f128032c4807ad9359a1fff29fcbc5f551c81be807bfa86db5a45649b7ba", "impliedFormat": 1}, {"version": "42f4d7040a48e5b9c9b20b5f17a04c381676211bdb0b5a580a183cf5908664be", "impliedFormat": 1}, {"version": "d4e4fbb20d20cc5b9f4c85f2357f27cb233cd01f8ca6d85dcca905ec15143e06", "impliedFormat": 1}, {"version": "c2fc483dea0580d1266c1500f17e49a739ca6cfe408691da638ddc211dfffad0", "impliedFormat": 1}, {"version": "dfc8ab0e4a452b8361ccf895ab998bbf27d1f7608fae372ac6aa7f089ef7f68d", "impliedFormat": 1}, {"version": "cca630c92b5382a0677d2dedca95e4e08a0cae660181d6d0dd8fd8bdb104d745", "impliedFormat": 1}, {"version": "801cdc85f84235c3c9d4252b87db1ed3d5b2950898fa0b913fcc54d489bc15b5", "impliedFormat": 1}, {"version": "c16c3b97930e8fbf05022024f049d51c998dd5eb6509047e1f841777968e85c1", "impliedFormat": 1}, {"version": "cce15e7530c8062dea0666a174f31c1fe445a97357885480748b072778fc6f36", "impliedFormat": 1}, {"version": "535b2fc8c89091c20124fe144699bb4a96d5db4418a1594a9a0a6a863b2195ae", "impliedFormat": 1}, {"version": "dd5165bf834f6e784b4aad9fae6d84307c19f140829e4c6c4123b2d1a707d8bd", "impliedFormat": 1}, {"version": "7ccf260729e19eed74c34046b38b6957bcfe4784d94f76eb830a70fc5d59cb43", "impliedFormat": 1}, {"version": "21575cdeaca6a2c2a0beb8c2ecbc981d9deb95f879f82dc7d6e325fe8737b5ba", "impliedFormat": 1}, {"version": "00343c2c578a0e32ecc384ed779ff39bc7ec6778ef84dc48106b602eb5598a6c", "impliedFormat": 1}, {"version": "c333b496e7676a8b84c720bdece6c34621e3945b7d1710d6ed85d8b742852825", "impliedFormat": 1}, {"version": "3eb7d541136cd8b66020417086e4f481fb1ae0e2b916846d43cbf0b540371954", "impliedFormat": 1}, {"version": "b6fed756be83482969cd037fb707285d46cbb03a19dc576cff8179dc55540727", "impliedFormat": 1}, {"version": "26602933b613e4df3868a6c82e14fffa2393a08531cb333ed27b151923462981", "impliedFormat": 1}, {"version": "8fc19c7114cfd352ff9fb615028e6062cb9fa3cd59c4850bc6c5634b9f57ea27", "impliedFormat": 1}, {"version": "05942150b4d7e0eb991776b1905487ecd94e7299847bb251419c99658363ff84", "impliedFormat": 1}, {"version": "073c43eff28f369a05973364a5c466859867661670eb28e1b6f3dd0654dd0f0e", "impliedFormat": 1}, {"version": "4a7c3274af9c78f7b4328f1e673dec81f48dd75da3bc159780fb4a13238b6684", "impliedFormat": 1}, {"version": "3c863779b6de32f36d366465de6abe3a4066813074c6f8cad066a2e0db749525", "impliedFormat": 1}, {"version": "259a3d89235d858b3d495dc2d1d610d6ce4ac0e91da1ae6a293f250d895d45dd", "impliedFormat": 1}, {"version": "369b7270eeeb37982203b2cb18c7302947b89bf5818c1d3d2e95a0418f02b74e", "impliedFormat": 1}, {"version": "f4c772371ce8ceaab394e1f8af9a6e502f0c02cbf184632dd6e64a00b8aeaf74", "impliedFormat": 1}, {"version": "039bd8d1e0d151570b66e75ee152877fb0e2f42eca43718632ac195e6884be34", "impliedFormat": 1}, {"version": "89fb1e22c3c98cbb86dc3e5949012bdae217f2b5d768a2cc74e1c4b413c25ad2", "impliedFormat": 1}, "0d812c11a1afa799fb2bfa8f1e11052eacb64252c4b52be969a5f89aae1f52fb", {"version": "89ad9a4e8044299f356f38879a1c2176bc60c997519b442c92cc5a70b731a360", "impliedFormat": 99}, "dcb793b8b1202b1634d791a993acadca0cfc3043a93b98c91a627fbff794f384", {"version": "b843496b17a2bbd79c83809c73fd9c59fab53d3e361e04e52e2d489524eea764", "impliedFormat": 1}, "b06b6294edee39d817586b3cf4766de1720b06e184624d45d2bfd0f38d2a5e72", {"version": "fd4f58cd6b5fc8ce8af0d04bfef5142f15c4bafaac9a9899c6daa056f10bb517", "impliedFormat": 99}, "da0c7ace239ead75f7fbc46ffbae8ddf177477445354500d7304a276065ea7b3", {"version": "6fb3298e948c919dddff11c2b835e0d78e0b7f8597aff359c359a0e5a64cffe0", "impliedFormat": 99}, {"version": "f876363b4931492eccba17cf0416f4aca9b777d67831aaf7567d41a09c72fbc6", "impliedFormat": 99}, "74f3b1b937e42582093a1671cf7f5c7250cd884922d51f201632317af9c24209", "e3eebfdc82ab2624c3510e0e74d587ab107554afbe557ce56cb47d243be4869a", "6d66283fc04f3901772f15f3108bda732efc75ce878e0d8ecf8f9fc3b0c63c26", {"version": "2535fc1a5fe64892783ff8f61321b181c24f824e688a4a05ae738da33466605b", "impliedFormat": 99}, "d853c561833e545879a56e2c4de18a92653de1e55e9e7fae463540679924ac0c", {"version": "76595c0e5a532556431fbda63e041df8a34902f4ed3404064d0f846bc19fa98d", "impliedFormat": 99}, "74f87531da1fde8d3c07dbd7b380ee6508a70d5c0af1c22e2d2c33d66cc79f10", {"version": "ad37aec058ed443d7460433df152313718febcae11564e2c7940f4535ce02233", "impliedFormat": 1}, {"version": "3509a51f87227ae65de0e428750e4b8c4148681b5cb56385b6526c79bfb51275", "impliedFormat": 1}, {"version": "348c13a1c9160681e41bc5cd3cc519dd8170d38a36a30480b41849f60f5bf8a0", "impliedFormat": 1}, {"version": "39d3b53ba8a622ae29df44e9429e8c0b632f302623b2246a5fcafdff14a93908", "impliedFormat": 1}, {"version": "279248c34ecd223fc46224f86384ebf49c775eb69329ad644d3d99f1205f3e7d", "impliedFormat": 1}, {"version": "284a96a6ad160f5982dcc1d6fa36350e042f94d84d49f46db454b356dcb824a8", "impliedFormat": 1}, {"version": "3aa3513d5e13d028202e788d763f021d2d113bd673087b42a2606ab50345492d", "impliedFormat": 1}, {"version": "f012173d64d0579875aa60405de21ad379af7971b93bf46bee23acc5fa2b76a4", "impliedFormat": 1}, {"version": "dcf5dc3ce399d472929c170de58422b549130dd540531623c830aaaaf3dd5f93", "impliedFormat": 1}, {"version": "ec35f1490510239b89c745c948007c5dd00a8dca0861a836dcf0db5360679a2d", "impliedFormat": 1}, {"version": "32868e4ec9b6bd4b1d96d24611343404b3a0a37064a7ac514b1d66b48325a911", "impliedFormat": 1}, {"version": "4bbea07f21ff84bf3ceeb218b5a8c367c6e0f08014d3fd09e457d2ffb2826b9c", "impliedFormat": 1}, {"version": "873a07dbeb0f8a3018791d245c0cf10c3289c8f7162cdbbb4a5b9cf723136185", "impliedFormat": 1}, {"version": "43839af7f24edbd4b4e42e861eb7c0d85d80ec497095bb5002c93b451e9fcf88", "impliedFormat": 1}, {"version": "54a7ee56aadecbe8126744f7787f54f79d1e110adab8fe7026ad83a9681f136a", "impliedFormat": 1}, {"version": "6333c727ee2b79cdab55e9e10971e59cbfee26c73dfb350972cfd97712fc2162", "impliedFormat": 1}, {"version": "8743b4356e522c26dc37f20cde4bcdb5ebd0a71a3afe156e81c099db7f34621d", "impliedFormat": 1}, {"version": "af3d97c3a0da9491841efc4e25585247aa76772b840dd279dbff714c69d3a1ec", "impliedFormat": 1}, {"version": "d9ac50fe802967929467413a79631698b8d8f4f2dc692b207e509b6bb3a92524", "impliedFormat": 1}, {"version": "3cb4960dce78abf548af390b5849e0eec1a0ce34fb16e3fab2bbd95ed434c026", "impliedFormat": 1}, {"version": "b75d56703daaffcb31a7cdebf190856e07739a9481f01c2919f95bde99be9424", "impliedFormat": 99}, "70d1e35a5fb0897af7063cdd841d8ed636e1c332ef7ea6469f0f175a5a93dddf", {"version": "f014493efd0ebbeb7208c9c1287a888d1af91e3cfec0cb923bd6fa9edd03fd2b", "impliedFormat": 99}, "79888d8afa95a5eb89cf0395e3e5a64d9c30264ea802d2bf3f6e52d8850d6304", "ad0936f84f1df79d3697bfbff9c18f8ad58431c1cbaf2359c6a853b0fcc9f28b", "a72a9d8fc1c1999b5411a33391c5e70048863c5865629077280e943ca85689a8", "fcf9c1332e49671509ed9aeb2c334939d0765014d17aec3156d18ecb3b1be0f4", {"version": "cc3738ba01d9af5ba1206a313896837ff8779791afcd9869e582783550f17f38", "impliedFormat": 99}, "61e959d046f72e295d73a822b9afc7cc87ec6d1007905f2572e6117d7b2067b6", "1da243956282c040db49d5718d2f0093a705fbbdfa3a25f51c1a31a069c45093", {"version": "69ec8d900cfec3d40e50490fedbbea5c1b49d32c38adbc236e73a3b8978c0b11", "impliedFormat": 99}, {"version": "7fd629484ba6772b686885b443914655089246f75a13dd685845d0abae337671", "impliedFormat": 99}, "c9a401b2e134f28d870f3f44330b9d91d66bd2ac7ced3ad67039a2bb3d52e8f0", "dba95ead40d163af6959198ded9853a2cc9282b2cb534980f99937a65edf4e2d", "4c1326fd4f09530e1088cc0a1331ca09f02e12a665abba3df2a324cfcbc5dcc5", "1fc7be646c18b1c9b858dfdca1b5d1b176b7cd11b39b4141c3352ea501068671", "6a66e35a0e0746a3e4a9c0c3f9d8396ebe850b7d5ef7c00d0665823c7bd278e4", "ce178ff078b9297e3f138f4e9cf431c0077a039c7a3a0f108ddc156a409b8d99", "c94040d4a3fd8c23098b091526b7984490d405516a5b39f7c1bd5e5f91580b86", "c177e289060cb2aa51ec338ca7368a19a44576501498d7372952400d98348dbb", "5478db85c25b47a8983311844424b5fd7fc04d58336e6aa8544bf8420140c57c", "cd8f9d34cf5eb95368cf7086a3abcf8caf0a89f9310dee90536aa45df16597af", "b822235ae75546ad72e30b587a86f82fa3cd97d3b23508b387a234b10b000e50", "859e9b66fefa0fbc5a5f3517f34627245917ffa213588d903b60127d407907ac", "6fe7fa68ab6bdceb109e49755079fbaed629d4d4152644ac3238d8396ee18edb", "2e808b1fa24408d1fc40f3ab5faad2a72c8bf0a28fa5ed081b59a38d224076ca", "f95af7ace9197576ff9327a9ab488378e5d2aba69f80bf17b711101a0aa67ac3", "fd6da76bd4ade17bf91cee58dbc3c58d4c519aeee90044d550bc3757b111cab6", "00d3bd1364d4d7d5727a41efcc657e278af5cd35a504a162e28648df294c4037", "53cb65ee6cb4dd93729a48e1b1bb78b0ed80ec789b01bebcf04d73b4469450e4", "b86708fb539578693038ad2cba26fc98097c2e0943c06b0249771a0c7f85738f", "6d9749ce6adf7225ef724295f2e359f082bdefd457657e03c06873876ac69373", "1abed99dd529cea37071b4f8abca8a619b361966ecd314a5859d0d259c042139", "efbd54406b2c80c98b43c9107657f2b044cfd932f1c43b502432b403bd61dd56", "b5f544f9adb3388e826c3291a57d3bee90fd814793f36f6ef04deabdb9e7f8e4", "e34f3ad1e6a22da0a16a437e353f64391368dd57705d84d0182d6ab3e726a586", "b8284a9c6d1416b607a93ae81edc35976f2735fa20e058d01a418d9012a4002a", "a21260c86884234fc69472de15c6367d0adbf1a26c651ffafab1054d9b90e0b9", "75d0bec33ec6b7ca2f6bcafc69b38abea374c713cc987e088b8af30704588ab2", "02c1f0b07de4d88f595ac56edd4be0884be43d24f02a96aa7fd579eacdedd20b", "bbc0b275347f901adec7911dd6bf7f11eb68347d79b930ced1740e4ac85dcb82", "7a4ecb49b7e360d0cd1b724476bc370eb4830c9f0a92527a0fd7fce3b692049b", "e620f10d5292e861994d64f337fa8bd00294130df7cc3897d922c9b980d36328", "f6bba27cb66541e3aeabf8e2ea33a82dcea190f52d21154f0c72916c40c4c413", "f76102759929d7e90c165994839fcefd76064559062d248001c9c14e78cfccef", "c652285e31db88babc679cc3e8db71421ec8281d9242a820535c05875317c765", "71bc2083cc636d52b3741f7df22d449b25f219835c366ca16651bb7ebd213f86", "83860539ca2acb6a0791a1350b1e28470f905953c2c11562df74d98dfa8f6a95", "b169e1847dd42e6dfe08e4ccef3fd633e54ae74b11a4fc172ad87f76568bc70e", "7fc995349585ab46915680b2fab79a3c99bbef94fe341d3046f8ac4e1b5f1e4b", {"version": "a79e62f1e20467e11a904399b8b18b18c0c6eea6b50c1168bf215356d5bebfaf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0fd06258805d26c72f5997e07a23155d322d5f05387adb3744a791fe6a0b042d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2db0dd3aaa2ed285950273ce96ae8a450b45423aa9da2d10e194570f1233fa6b", "impliedFormat": 1}, {"version": "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "impliedFormat": 1}, {"version": "8e9c23ba78aabc2e0a27033f18737a6df754067731e69dc5f52823957d60a4b6", "impliedFormat": 1}, {"version": "5929864ce17fba74232584d90cb721a89b7ad277220627cc97054ba15a98ea8f", "impliedFormat": 1}, {"version": "24bd580b5743dc56402c440dc7f9a4f5d592ad7a419f25414d37a7bfe11e342b", "impliedFormat": 1}, {"version": "25c8056edf4314820382a5fdb4bb7816999acdcb929c8f75e3f39473b87e85bc", "impliedFormat": 1}, {"version": "c464d66b20788266e5353b48dc4aa6bc0dc4a707276df1e7152ab0c9ae21fad8", "impliedFormat": 1}, {"version": "78d0d27c130d35c60b5e5566c9f1e5be77caf39804636bc1a40133919a949f21", "impliedFormat": 1}, {"version": "c6fd2c5a395f2432786c9cb8deb870b9b0e8ff7e22c029954fabdd692bff6195", "impliedFormat": 1}, {"version": "1d6e127068ea8e104a912e42fc0a110e2aa5a66a356a917a163e8cf9a65e4a75", "impliedFormat": 1}, {"version": "5ded6427296cdf3b9542de4471d2aa8d3983671d4cac0f4bf9c637208d1ced43", "impliedFormat": 1}, {"version": "6bdc71028db658243775263e93a7db2fd2abfce3ca569c3cca5aee6ed5eb186d", "impliedFormat": 1}, {"version": "cadc8aced301244057c4e7e73fbcae534b0f5b12a37b150d80e5a45aa4bebcbd", "impliedFormat": 1}, {"version": "385aab901643aa54e1c36f5ef3107913b10d1b5bb8cbcd933d4263b80a0d7f20", "impliedFormat": 1}, {"version": "9670d44354bab9d9982eca21945686b5c24a3f893db73c0dae0fd74217a4c219", "impliedFormat": 1}, {"version": "0b8a9268adaf4da35e7fa830c8981cfa22adbbe5b3f6f5ab91f6658899e657a7", "impliedFormat": 1}, {"version": "11396ed8a44c02ab9798b7dca436009f866e8dae3c9c25e8c1fbc396880bf1bb", "impliedFormat": 1}, {"version": "ba7bc87d01492633cb5a0e5da8a4a42a1c86270e7b3d2dea5d156828a84e4882", "impliedFormat": 1}, {"version": "4893a895ea92c85345017a04ed427cbd6a1710453338df26881a6019432febdd", "impliedFormat": 1}, {"version": "c21dc52e277bcfc75fac0436ccb75c204f9e1b3fa5e12729670910639f27343e", "impliedFormat": 1}, {"version": "13f6f39e12b1518c6650bbb220c8985999020fe0f21d818e28f512b7771d00f9", "impliedFormat": 1}, {"version": "9b5369969f6e7175740bf51223112ff209f94ba43ecd3bb09eefff9fd675624a", "impliedFormat": 1}, {"version": "4fe9e626e7164748e8769bbf74b538e09607f07ed17c2f20af8d680ee49fc1da", "impliedFormat": 1}, {"version": "24515859bc0b836719105bb6cc3d68255042a9f02a6022b3187948b204946bd2", "impliedFormat": 1}, {"version": "ea0148f897b45a76544ae179784c95af1bd6721b8610af9ffa467a518a086a43", "impliedFormat": 1}, {"version": "24c6a117721e606c9984335f71711877293a9651e44f59f3d21c1ea0856f9cc9", "impliedFormat": 1}, {"version": "dd3273ead9fbde62a72949c97dbec2247ea08e0c6952e701a483d74ef92d6a17", "impliedFormat": 1}, {"version": "405822be75ad3e4d162e07439bac80c6bcc6dbae1929e179cf467ec0b9ee4e2e", "impliedFormat": 1}, {"version": "0db18c6e78ea846316c012478888f33c11ffadab9efd1cc8bcc12daded7a60b6", "impliedFormat": 1}, {"version": "4d2b0eb911816f66abe4970898f97a2cfc902bcd743cbfa5017fad79f7ef90d8", "impliedFormat": 1}, {"version": "bd0532fd6556073727d28da0edfd1736417a3f9f394877b6d5ef6ad88fba1d1a", "impliedFormat": 1}, {"version": "89167d696a849fce5ca508032aabfe901c0868f833a8625d5a9c6e861ef935d2", "impliedFormat": 1}, {"version": "e53a3c2a9f624d90f24bf4588aacd223e7bec1b9d0d479b68d2f4a9e6011147f", "impliedFormat": 1}, {"version": "24b8685c62562f5d98615c5a0c1d05f297cf5065f15246edfe99e81ec4c0e011", "impliedFormat": 1}, {"version": "93507c745e8f29090efb99399c3f77bec07db17acd75634249dc92f961573387", "impliedFormat": 1}, {"version": "339dc5265ee5ed92e536a93a04c4ebbc2128f45eeec6ed29f379e0085283542c", "impliedFormat": 1}, {"version": "4732aec92b20fb28c5fe9ad99521fb59974289ed1e45aecb282616202184064f", "impliedFormat": 1}, {"version": "2e85db9e6fd73cfa3d7f28e0ab6b55417ea18931423bd47b409a96e4a169e8e6", "impliedFormat": 1}, {"version": "c46e079fe54c76f95c67fb89081b3e399da2c7d109e7dca8e4b58d83e332e605", "impliedFormat": 1}, {"version": "bf67d53d168abc1298888693338cb82854bdb2e69ef83f8a0092093c2d562107", "impliedFormat": 1}, {"version": "4eedea23b8a843ec0fd51a384fb6b9fe1bc89198f713d0465c2c8392a9d51271", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3d77c73be94570813f8cadd1f05ebc3dc5e2e4fdefe4d340ca20cd018724ee36", "impliedFormat": 1}, {"version": "d674383111e06b6741c4ad2db962131b5b0fa4d0294b998566c635e86195a453", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3e58c4c18a031cbb17abec7a4ad0bd5ae9fc70c1f4ba1e7fb921ad87c504aca", "impliedFormat": 1}, {"version": "a3e8bafb2af8e850c644f4be7f5156cf7d23b7bfdc3b786bd4d10ed40329649c", "impliedFormat": 1}, {"version": "35ec8b6760fd7138bbf5809b84551e31028fb2ba7b6dc91d95d098bf212ca8b4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a40826e8476694e90da94aa008283a7de50d1dafd37beada623863f1901cb7fb", "impliedFormat": 1}, {"version": "c521f961c1606c94dc831992e659f426b6def6e2e6e327ee25d3c642eb393f95", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b0c0d1d13be149f790a75b381b413490f98558649428bb916fd2d71a3f47a134", "impliedFormat": 1}, {"version": "3c884d9d9ec454bdf0d5a0b8465bf8297d2caa4d853851d92cc417ac6f30b969", "impliedFormat": 1}, {"version": "5a369483ac4cfbdf0331c248deeb36140e6907db5e1daed241546b4a2055f82c", "impliedFormat": 1}, {"version": "e8f5b5cc36615c17d330eaf8eebbc0d6bdd942c25991f96ef122f246f4ff722f", "impliedFormat": 1}, {"version": "f0bd7e6d931657b59605c44112eaf8b980ba7f957a5051ed21cb93d978cf2f45", "impliedFormat": 1}, {"version": "ee1ee365d88c4c6c0c0a5a5701d66ebc27ccd0bcfcfaa482c6e2e7fe7b98edf7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f0de0233242682db9ac13efa0ddbb456a41abe6f291211b40ba3aa766b03e9b3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "596572d40c1f13d8b57920d6d1a77c5ec6fe4952dc157f416f04a801cd3e2678", "impliedFormat": 1}, {"version": "ad23fd126ff06e72728dd7bfc84326a8ca8cec2b9d2dac0193d42a777df0e7d8", "impliedFormat": 1}, {"version": "a55fd4d49da86d2cc19de575beb1515184e55f5f3165e1482ff02fd99f900b5c", "impliedFormat": 1}, {"version": "93bd413918fa921c8729cef45302b24d8b6c7855d72d5bf82d3972595ae8dcbf", "impliedFormat": 1}, {"version": "4ff41188773cbf465807dd2f7059c7494cbee5115608efc297383832a1150c43", "impliedFormat": 1}, {"version": "dccdf1677e531e33f8ac961a68bc537418c9a414797c1ea7e91307501cdc3f5e", "impliedFormat": 1}, {"version": "7edec695cdb707c7146ac34c44ca364469c7ea504344b3206c686e79f61b61a2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d206b4baf4ddcc15d9d69a9a2f4999a72a2c6adeaa8af20fa7a9960816287555", "impliedFormat": 1}, {"version": "93f437e1398a4f06a984f441f7fa7a9f0535c04399619b5c22e0b87bdee182cb", "impliedFormat": 1}, {"version": "d1b1295af3667779be43eb6d4fbaa342e656aa2c4b77a4ad3cf42ec55baeea00", "impliedFormat": 1}, {"version": "70731d10d5311bd4cf710ef7f6539b62660f4b0bfdbb3f9fbe1d25fe6366a7fa", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a20f1e119615bf7632729fd89b6c0b5ffdc2df3b512d6304146294528e3ebe19", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e043a1bc8fbf2a255bccf9bf27e0f1caf916c3b0518ea34aa72357c0afd42ec", "impliedFormat": 1}, {"version": "137c2894e8f3e9672d401cc0a305dc7b1db7c69511cf6d3970fb53302f9eae09", "impliedFormat": 1}, {"version": "dd9492e12a57068f08d70cb5eb5ceb39fa5bcf23be01af574270aeee95b982af", "impliedFormat": 1}, {"version": "e432b0e3761ca9ba734bdd41e19a75fec1454ca8e9769bfdf8b31011854cf06a", "impliedFormat": 1}, {"version": "f9d9d753d430ed050dc1bf2667a1bab711ccbb1c1507183d794cc195a5b085cc", "impliedFormat": 1}, {"version": "9eece5e586312581ccd106d4853e861aaaa1a39f8e3ea672b8c3847eedd12f6e", "impliedFormat": 1}, {"version": "78955c9259da94920609be3e589fc9253268b3fffa822e1e31d28ee2ce0b8a74", "impliedFormat": 1}, {"version": "269929a24b2816343a178008ac9ae9248304d92a8ba8e233055e0ed6dbe6ef71", "impliedFormat": 1}, {"version": "93452d394fdd1dc551ec62f5042366f011a00d342d36d50793b3529bfc9bd633", "impliedFormat": 1}, {"version": "73aa178e8fb1449ef3666093d8dca25f96302a80ee45f8ff027df8e4792bf9fd", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1851a3b4db78664f83901bb9cac9e45e03a37bb5933cc5bf37e10bb7e91ab4eb", "impliedFormat": 1}, {"version": "fdedf82878e4c744bc2a1c1e802ae407d63474da51f14a54babe039018e53d8f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "08353b04a3501d84fc8d7b49de99f6c1cc26026e6d9d697a18315f3bfe92ed03", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "578d8bb6dcb2a1c03c4c3f8eb71abc9677e1a5c788b7f24848e3138ce17f3400", "impliedFormat": 1}, {"version": "4f029899f9bae07e225c43aef893590541b2b43267383bf5e32e3a884d219ed5", "impliedFormat": 1}, {"version": "ae56f65caf3be91108707bd8dfbccc2a57a91feb5daabf7165a06a945545ed26", "impliedFormat": 1}, {"version": "a136d5de521da20f31631a0a96bf712370779d1c05b7015d7019a9b2a0446ca9", "impliedFormat": 1}, {"version": "710ad93f8de29dc15e5892aa735e72348b62f40a6d1220f2849837d332f92885", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1f1da5d682cdb628890e4a8578fb9e8ab332e6a1a4b3a13fce08b7b4d45d192a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "efeedd8bbc5c0d53e760d8b120a010470722982e6ae14de8d1bcff66ebc2ae71", "impliedFormat": 1}, {"version": "b718a94332858862943630649a310d6f8e9a09f86ae7215d8554e75bbbfd7817", "impliedFormat": 1}, {"version": "d91a7d8b5655c42986f1bdfe2105c4408f472831c8f20cf11a8c3345b6b56c8c", "impliedFormat": 1}, {"version": "616075a6ac578cf5a013ee12964188b4412823796ce0b202c6f1d2e4ca8480d7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e8a979b8af001c9fc2e774e7809d233c8ca955a28756f52ee5dee88ccb0611d2", "impliedFormat": 1}, {"version": "ab9b9a36e5284fd8d3bf2f7d5fcbc60052f25f27e4d20954782099282c60d23e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "95689cb423a99816e9ff2fbadac7e257777c4ff801e7a2fb4f9af16e17c0f288", "impliedFormat": 1}, {"version": "d3f2d715f57df3f04bf7b16dde01dec10366f64fce44503c92b8f78f614c1769", "impliedFormat": 1}, {"version": "b78cd10245a90e27e62d0558564f5d9a16576294eee724a59ae21b91f9269e4a", "impliedFormat": 1}, {"version": "630ff11de47d75175f2d1d43cc447068818cb9377324752e01fe0e5fc3f77757", "impliedFormat": 1}, {"version": "2f5747b1508ccf83fad0c251ba1e5da2f5a30b78b09ffa1cfaf633045160afed", "impliedFormat": 1}, {"version": "a45c25e77c911c1f2a04cade78f6f42b4d7d896a3882d4e226efd3a3fcd5f2c4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b71c603a539078a5e3a039b20f2b0a0d1708967530cf97dec8850a9ca45baa2b", "impliedFormat": 1}, {"version": "0e13570a7e86c6d83dd92e81758a930f63747483e2cd34ef36fcdb47d1f9726a", "impliedFormat": 1}, {"version": "104c67f0da1bdf0d94865419247e20eded83ce7f9911a1aa75fc675c077ca66e", "impliedFormat": 1}, {"version": "cc0d0b339f31ce0ab3b7a5b714d8e578ce698f1e13d7f8c60bfb766baeb1d35c", "impliedFormat": 1}, {"version": "5c45abf1e13e4463eacfd5dedda06855da8748a6a6cb3334f582b52e219acc04", "impliedFormat": 1}, {"version": "12d19496f25ecd6afef2094be494b3b0ae12c02bd631901f6da760c7540a5ec1", "impliedFormat": 1}, {"version": "99b6b07b5b54123e22e01e721a4d27eabecb7714060ec8ab60b79db5224cfcc0", "impliedFormat": 99}, {"version": "b478cef88033c3b939a6b8a9076af57fc7030e7fd957557f82f2f57eddfc2b51", "impliedFormat": 99}, {"version": "2fac70f99da22181acfda399eed248b47395a8eeb33c9c82d75ca966aee58912", "impliedFormat": 99}, "dc37b016de1ac055011d65e21df46d3dee2c20df34306318e0578f3c569984d5", "00e8e8bfe68aada4b36d051cf18fb5683d619a02ac3f7d715c63af61cd7e8489", "7843b6d99775383c70ecbf7658daa487e2fd7535d3aab39a0c7ed310582bcf66", "a6b97e2b959e2ca8fc6bedab91e0fa2d6ecb04a6d9a6ee4ff30f55a8ac263678", "d1834b72e11f374c634eee4eb1ae1d51b58b52319bbe807103efd46366f03a43", {"version": "fb893a0dfc3c9fb0f9ca93d0648694dd95f33cbad2c0f2c629f842981dfd4e2e", "impliedFormat": 1}, {"version": "95da3c365e3d45709ad6e0b4daa5cdaf05e9076ba3c201e8f8081dd282c02f57", "impliedFormat": 1}, {"version": "160b24efb5a868df9c54f337656b4ef55fcbe0548fe15408e1c0630ec559c559", "impliedFormat": 1}, {"version": "fa4f7a50d9bf0f0848a6606ded81436678f64b3b7977a3a806ac5386573c7c88", "impliedFormat": 99}, "1fa3b160e06d35276a7c1c9b7ef698d27400340f159a0e0567f7394508126e52", "7f192f7c098dd30b67cf85449405eed81d886ab189834c57a98eac6c991bdb88", {"version": "316f1486e15cbf7896425f0a16dfe12d447dd57cfb3244b8b119c77df870858f", "impliedFormat": 99}, "75e850dcd3d1a303bf93ae2f52934a22b2f6e2fa669233ff45ac3d94e708770f", "f74b0f8cf5bcd66f9faec1309e99c0b026542f2e068d8b6a5cedb4ab493bd4f1", "d941e57002f8b020e36bfee71567dc835f1c6e3e6483a3823abac61409348a80", "aeed407efaf239c95ada087f0250ca15774044cf570a29af98f755b4e06c633e", "7f7d18c7d7172d676fbcc5f31b3e113bdc5ab176768fc9ba15d5a5c95e000306", {"version": "d54b8bf170290a4b87701ab6d1143ef0a55415b36b42b57f019fb62b1c435bed", "impliedFormat": 1}, {"version": "4b2ebe59f23314851c4cf4d65b194e3a65b68bdf0cdc1be2ffe19406605c9694", "impliedFormat": 1}, {"version": "ddbffcbd0335b4e43f2e1771861b6e85f34fa12375dd0565caab4cbd83708985", "impliedFormat": 1}, {"version": "78405540cf6aa0ee0b65a85a0ff17a3eebfd74c565d1bdcb0815c9327ccf2554", "impliedFormat": 1}, {"version": "dbd416a31ba77a6d90ea4163c2fff64c675d7377bf4c637776e0b08fc225db48", "impliedFormat": 1}, {"version": "5b54a276dde01f6f7120b4ead0bce82dfdd6a52e0e001a2fd5f0ccc6f470fa6b", "impliedFormat": 1}, {"version": "360de32ec39b1699ade98153c3f8351988e85b878bdf12907fd39398aa8a8fe0", "impliedFormat": 1}, {"version": "0a9584867a25e2f2db31244e887b0afaad520da2a8bae39dc3b595e2338f0c29", "impliedFormat": 1}, {"version": "404d0cb13e779108e74a5b1e02ec10a8e9933b825f300c797ba4558451784de5", "impliedFormat": 1}, {"version": "4ffee86e26af85318716d9a053a33b2659c987a9492015435b213f0ccf04a491", "impliedFormat": 1}, {"version": "e8747cb2f6770567961b051c13a41e2c7cb748c42762f904d16b19bba55e8432", "impliedFormat": 1}, {"version": "c9d70252b35da8cb8137d70b02a383e7edc3c2e1e6c37ea87ffef982a7eaf644", "impliedFormat": 1}, {"version": "46c59c3569d97822d42a003eb02cd5f0eb88676e12abcc8ce249675ea8d5c4ef", "impliedFormat": 1}, {"version": "c4aa043e77a847629a298e6182d4bae576f6c6e2f309f3ae11c58ed79b0dab64", "impliedFormat": 1}, "457e4cc1ad96228fff0bb950d34cd069f724dc67a328f6c3f955dba80a57b258", "747a81c5e497c50afbb9c5ffe7395515c0fd682c2c4d775387f38a3cda0b3edf", "5a573ec41e293ae0fd590f96e7cb56d292cb1f68f99834685a39c0edd3a198e9", "2198a00fd67f1474a66f0d7d0104181d2438d03d410ec95fcf8f790ba623cf40", "a1b1c204828cdd64af1023458b24e782be1ac2e2dee5d908f20194f423ff159a", "d0f51b6ad370fedeb619acee71b0af069f5e65224c7a3ba4fcf20b8d4309a0f5", "e245c800b93dce2de8d7d3c1a25d30414922040aff0e72f9bff448b41e5be5ed", "c3e8ea6a6f8454fb9d2515ce559974553987c1dd5aeb4a8faf6f166a4dcb6eee", "af38ff13fcbf6a9cb640f585be60ce72d38b3a0c6d8d83c2fe15ae9ff6c524fb", "94d785c0d6d578f57fff10d14edd93891b9dd506db23c79874df2a251a303a84", "68c73aa98030bafff687e830b97f941c76d1c8207e765c98a50be44830c1ee02", "e3a62c11d5c85ed9de4336a15df55ddd8e05504d89deae702596890762041d85", "87dd80f74fa0dd83617bf919f5bdfd6a4d3fffb6f92154a41b09dcb258cadca8", "3a9296699c3616f1c7dfbabe938a397b1c65d285b7c930f3fb8f8ac9e7bdd00c", "3d29a94e213216a947753eae3d963ddfeea94b16d632c5d09d5ece0b6c9f0cd3", "776a30e01f08598f1c31a6fb98624b1e96fc2d9989cc5003dddbd2fc37639464", "73db8d054d41ff1e8962c353b01dcd4bd8817a2166b0f674f0898db0be6e8d01", "006c59498c888edec24e29a3bba129a69f94e7f260ee490fa07a5514622a8e76", "9821d334e5c9ba8c8cd4afb27c402ce13255841eb904c1113326c93ae02b3811", "cd58f65d914a24869512b1fde385524e7b9852c91381024422706c818b2c748e", "b3d759daf019acfd00c3bf736ce974c4855ce31c9817a8a490a6edab9d71f690", "66192f1064228da98de00797cf8a6cb90510a1c265aca4cf2b79b6db70303801", "4edd9941fe20661f8498f874a8fe356d9351ba447dc05cbbe41c8461752e67ed", "39afe10c2cf0adc6a9b0f05e5dee31a98bf83487372ce6d6e411e33af72ccb57", "e8b1dd4ea74c4d0f594e4c79ae5013c0f5ebda125c11646d44a6f9cbdff4a9b0", "3fca564cdbe97870aebd61dec77a1c2c0179a358b798305d2a6c9e8f111dd209", "dde72c1527eb251cdbe85ea127799adf3911aa9df2ae2e5d21179cfd75b814a5", "17987ba6bc5b0dc6e46881446ec9307afa06937d9eaadd359003ba5892567f88", "1043a310dc625874ccf841c7c19fa88e578aa6495a1ee8da8d1589f6fddc677e", {"version": "785b9d575b49124ce01b46f5b9402157c7611e6532effa562ac6aebec0074dfc", "impliedFormat": 1}, {"version": "45d93a5f9646a6a8f93f0a59289bef4a8fac44cabcf475f290a12cb6c17eaa44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a660aa95476042d3fdcc1343cf6bb8fdf24772d31712b1db321c5a4dcc325434", "impliedFormat": 1}, {"version": "282f98006ed7fa9bb2cd9bdbe2524595cfc4bcd58a0bb3232e4519f2138df811", "impliedFormat": 1}, {"version": "6222e987b58abfe92597e1273ad7233626285bc2d78409d4a7b113d81a83496b", "impliedFormat": 1}, {"version": "cbe726263ae9a7bf32352380f7e8ab66ee25b3457137e316929269c19e18a2be", "impliedFormat": 1}, {"version": "8b96046bf5fb0a815cba6b0880d9f97b7f3a93cf187e8dcfe8e2792e97f38f87", "impliedFormat": 99}, {"version": "bacf2c84cf448b2cd02c717ad46c3d7fd530e0c91282888c923ad64810a4d511", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "402e5c534fb2b85fa771170595db3ac0dd532112c8fa44fc23f233bc6967488b", "impliedFormat": 1}, {"version": "8885cf05f3e2abf117590bbb951dcf6359e3e5ac462af1c901cfd24c6a6472e2", "impliedFormat": 1}, {"version": "4d979e3c12ffb6497d2b1dc5613130196d986fff764c4526360c0716a162e7e7", "impliedFormat": 1}, {"version": "e61df3640a38d535fd4bc9f4a53aef17c296b58dc4b6394fd576b808dd2fe5e6", "impliedFormat": 1}, {"version": "80781460eca408fe8d2937d9fdbbb780d6aac35f549621e6200c9bee1da5b8fe", "impliedFormat": 1}, {"version": "4719c209b9c00b579553859407a7e5dcfaa1c472994bd62aa5dd3cc0757eb077", "impliedFormat": 1}, {"version": "7ec359bbc29b69d4063fe7dad0baaf35f1856f914db16b3f4f6e3e1bca4099fa", "impliedFormat": 1}, {"version": "b9261ac3e9944d3d72c5ee4cf888ad35d9743a5563405c6963c4e43ee3708ca4", "impliedFormat": 1}, {"version": "c84fd54e8400def0d1ef1569cafd02e9f39a622df9fa69b57ccc82128856b916", "impliedFormat": 1}, {"version": "a022503e75d6953d0e82c2c564508a5c7f8556fad5d7f971372d2d40479e4034", "impliedFormat": 1}, {"version": "2ed6489ef46eb61442d067c08e87e3db501c0bfb2837eee4041a27bf3e792bb0", "impliedFormat": 1}, {"version": "644491cde678bd462bb922c1d0cfab8f17d626b195ccb7f008612dc31f445d2d", "impliedFormat": 1}, {"version": "d60fe6d59d4e19ecc65359490b8535e359ca4b760d2cdb56897ca75d09d41ba3", "impliedFormat": 1}, {"version": "f45a2a8b1777ecb50ed65e1a04bb899d4b676529b7921bd5d69b08573a00c832", "impliedFormat": 1}, {"version": "774b783046ba3d473948132d28a69f52a295b2f378f2939304118ba571b1355e", "impliedFormat": 1}, {"version": "b5734e05c787a40e4f9efe71f16683c5f7dc3bdb0de7c04440c855bd000f8fa7", "impliedFormat": 1}, {"version": "14ba97f0907144771331e1349fdccb5a13526eba0647e6b447e572376d811b6f", "impliedFormat": 1}, {"version": "2a771d907aebf9391ac1f50e4ad37952943515eeea0dcc7e78aa08f508294668", "impliedFormat": 1}, {"version": "7165050eddaed878c2d2cd3cafcaf171072ac39e586a048c0603712b5555f536", "impliedFormat": 1}, {"version": "26e629be9bbd94ea1d465af83ce5a3306890520695f07be6eb016f8d734d02be", "impliedFormat": 99}, {"version": "82e687ebd99518bc63ea04b0c3810fb6e50aa6942decd0ca6f7a56d9b9a212a6", "impliedFormat": 99}, {"version": "7f698624bbbb060ece7c0e51b7236520ebada74b747d7523c7df376453ed6fea", "impliedFormat": 1}, {"version": "8f07f2b6514744ac96e51d7cb8518c0f4de319471237ea10cf688b8d0e9d0225", "impliedFormat": 1}, {"version": "0a95d25ef86ecf0f3f04d23eeef241b7adf2be8c541d8b567564306d3b9248cf", "impliedFormat": 99}, {"version": "8d27e5f73b75340198b2df36f39326f693743e64006bd7b88a925a5f285df628", "impliedFormat": 1}, {"version": "2c8e55457aaf4902941dfdba4061935922e8ee6e120539c9801cd7b400fae050", "impliedFormat": 1}, {"version": "1c2cd862994b1fbed3cde0d1e8de47835ff112d197a3debfddf7b2ee3b2c52bc", "impliedFormat": 1}, {"version": "670a76db379b27c8ff42f1ba927828a22862e2ab0b0908e38b671f0e912cc5ed", "impliedFormat": 1}, {"version": "9e0cf651e8e2c5b9bebbabdff2f7c6f8cedd91b1d9afcc0a854cdff053a88f1b", "impliedFormat": 1}, {"version": "069bebfee29864e3955378107e243508b163e77ab10de6a5ee03ae06939f0bb9", "impliedFormat": 1}, {"version": "f5e8546cfe500116aba8a6cb7ee171774b14a6db30d4bcd6e0aa5073e919e739", "impliedFormat": 99}, {"version": "f97458587581043b81054ae11e95c18433cfa442510c63a96bdaac1003254550", "impliedFormat": 99}, {"version": "0d59368cbb291cb7846bccd745369846c3e8aeb57ee22a3a8d51a5e088e82d50", "impliedFormat": 99}, "425a1c807cd6fff14343a90643583d1fb8644c823fd7eff8d0cddb65a49d2791", "1ac9150e29d1975b2944cbe8796549cb8805e097cdaf3edda4889ea28b9e7c0d", "3e88faf07234a1b8a3aceae58253f1b544cce5ddafe123cdfcb70c53c853edb7", "3b1da50e38e301f55a99ee3f64c9d07d19095ba83033b183dfbf1a1711b8c0d9", "eff5cf5baa335f0130b1ffd7a299d53ceaf7cac2312e3d7b9d82547230205b38", "5d519225527f29ec1e333b45264362a180f317ac5429a44bcc1021612aa396d4", "37948d6a50b3366bf07373d95aba29dfcf5d22d319f1b172adba14dca55ddb2d", "d47a2dc774efac69128be66ce60feb7b5664c15d50440c2c2fe5d6eb581d3aca", "81a4d4a0651a78b2bf268d3edf017d3703daed0a1c1518cab4297440ea3858df", "0d5f143f0c7577103dce5cb54bddacf0818a35e59c0a8e6146055ad12eb009c4", "d7b4b7099094ffe3e9c4dcb9cdce6ee2689a44da9040268b1da5e8e86dd28957", "27c65f8aa49f0c199b435bf1bd1f10526a33d5c0e0e618b939b253eb85b4ccc2", "d2bdb6e645ce71b8eec68f03b54a2325ab3610b2dcb4cb8a10b589c15702acb8", {"version": "4025a454b1ca489b179ee8c684bdd70ff8c1967e382076ade53e7e4653e1daec", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "984c09345059b76fc4221c2c54e53511f4c27a0794dfd6e9f81dc60f0b564e05", "affectsGlobalScope": true, "impliedFormat": 99}], "root": [88, [98, 101], 107, 108, 110, 111, [372, 375], [381, 384], [386, 397], [399, 403], 406, [416, 423], [433, 436], [438, 448], [450, 456], [458, 480], [482, 492], [563, 614], 617, 621, 622, 624, 626, 885, 922, 923, 925, 926, 928, 931, 933, 935, 966, 968, 970, 972, [975, 977], 979, 981, 1003, [1005, 1008], 1010, 1011, [1014, 1051], [1161, 1165], 1170, 1171, [1173, 1177], [1192, 1220], [1263, 1274]], "options": {"allowImportingTsExtensions": true, "esModuleInterop": true, "jsx": 4, "module": 99, "skipLibCheck": true, "strict": true, "target": 7, "tsBuildInfoFile": "./tsbuildinfo"}, "referencedMap": [[47, 1], [48, 1], [8, 1], [9, 1], [11, 1], [10, 1], [2, 1], [12, 1], [13, 1], [14, 1], [15, 1], [16, 1], [17, 1], [18, 1], [19, 1], [3, 1], [20, 1], [21, 1], [4, 1], [22, 1], [26, 1], [23, 1], [24, 1], [25, 1], [27, 1], [28, 1], [29, 1], [5, 1], [30, 1], [31, 1], [32, 1], [33, 1], [6, 1], [37, 1], [34, 1], [35, 1], [36, 1], [38, 1], [7, 1], [39, 1], [44, 1], [45, 1], [40, 1], [41, 1], [42, 1], [43, 1], [1, 1], [46, 1], [480, 2], [487, 3], [476, 4], [484, 5], [485, 6], [488, 7], [490, 8], [491, 9], [492, 10], [563, 11], [564, 12], [565, 13], [566, 14], [436, 15], [419, 16], [567, 17], [111, 18], [568, 4], [569, 13], [574, 19], [439, 20], [401, 21], [575, 22], [576, 23], [577, 24], [578, 4], [580, 25], [468, 26], [581, 13], [472, 27], [467, 28], [582, 29], [583, 24], [584, 30], [593, 31], [420, 23], [384, 32], [594, 33], [598, 34], [471, 35], [477, 4], [599, 13], [602, 36], [418, 37], [603, 38], [604, 9], [605, 29], [573, 39], [606, 13], [607, 4], [608, 40], [402, 41], [609, 42], [617, 43], [621, 44], [622, 45], [624, 46], [626, 47], [374, 45], [383, 48], [110, 49], [885, 50], [108, 51], [922, 52], [923, 53], [925, 54], [926, 55], [928, 56], [931, 57], [452, 58], [933, 59], [935, 60], [966, 61], [968, 62], [970, 63], [446, 51], [450, 64], [972, 65], [975, 66], [976, 67], [977, 68], [979, 69], [386, 70], [981, 71], [1003, 72], [399, 73], [1005, 74], [458, 75], [381, 76], [1008, 77], [1007, 78], [1010, 79], [438, 80], [1011, 51], [406, 81], [451, 51], [99, 82], [101, 83], [1015, 84], [1014, 85], [107, 86], [610, 87], [417, 87], [382, 28], [397, 88], [464, 89], [611, 90], [613, 91], [614, 92], [1006, 93], [100, 94], [373, 95], [390, 96], [1016, 97], [486, 93], [597, 98], [470, 99], [396, 93], [1018, 100], [395, 101], [1019, 102], [400, 103], [1020, 103], [1021, 103], [389, 104], [391, 103], [392, 103], [1023, 103], [1024, 103], [1022, 103], [572, 103], [570, 103], [1025, 103], [586, 103], [1026, 103], [393, 105], [1027, 103], [433, 103], [1028, 103], [588, 106], [1029, 103], [600, 107], [601, 93], [453, 103], [589, 108], [571, 103], [590, 103], [1030, 103], [596, 103], [591, 108], [469, 103], [388, 103], [585, 103], [1031, 103], [587, 103], [1033, 109], [482, 103], [1032, 103], [455, 103], [1034, 103], [88, 110], [387, 103], [1035, 111], [1036, 103], [394, 103], [1017, 103], [1037, 103], [1038, 103], [592, 108], [434, 103], [489, 112], [1039, 112], [579, 103], [435, 113], [1040, 103], [1041, 114], [1042, 103], [1043, 103], [454, 103], [1044, 108], [479, 103], [1045, 108], [1046, 115], [595, 103], [440, 116], [416, 116], [1048, 117], [465, 103], [98, 118], [1047, 103], [612, 103], [483, 119], [463, 120], [403, 121], [422, 122], [462, 123], [443, 124], [1049, 125], [421, 126], [441, 127], [442, 128], [1050, 129], [466, 130], [460, 123], [459, 131], [375, 132], [461, 122], [448, 133], [473, 134], [478, 135], [474, 123], [423, 123], [475, 136], [456, 137], [447, 133], [444, 122], [445, 138], [1255, 139], [1253, 1], [616, 140], [620, 141], [619, 142], [102, 143], [623, 143], [625, 144], [924, 144], [615, 144], [930, 145], [89, 146], [380, 147], [377, 148], [378, 148], [379, 148], [376, 146], [91, 143], [934, 145], [618, 143], [967, 149], [449, 143], [929, 150], [971, 151], [974, 152], [978, 153], [104, 154], [105, 143], [90, 146], [385, 144], [980, 155], [404, 144], [398, 144], [1004, 153], [457, 143], [1009, 144], [109, 146], [437, 144], [405, 155], [92, 156], [1013, 157], [1012, 143], [106, 149], [973, 143], [103, 1], [1261, 158], [1260, 158], [1189, 159], [1188, 160], [1186, 161], [1187, 162], [1178, 1], [1179, 1], [1184, 163], [1181, 164], [1180, 165], [1185, 166], [1183, 1], [1182, 1], [1191, 167], [1190, 168], [65, 169], [61, 170], [68, 171], [63, 172], [64, 1], [66, 169], [62, 172], [59, 1], [67, 172], [60, 1], [81, 173], [87, 174], [78, 175], [86, 146], [79, 173], [80, 93], [71, 175], [69, 176], [85, 177], [82, 176], [84, 175], [83, 176], [77, 176], [76, 176], [70, 175], [72, 178], [74, 175], [75, 175], [73, 175], [1258, 179], [1254, 139], [1256, 180], [1257, 139], [1168, 181], [1155, 182], [1154, 183], [513, 1], [496, 184], [514, 185], [495, 1], [1221, 1], [1151, 186], [1156, 187], [1152, 1], [1167, 188], [1147, 1], [1166, 1], [1054, 189], [1055, 189], [1095, 190], [1053, 191], [1096, 192], [1097, 193], [1098, 194], [1099, 195], [1100, 196], [1101, 197], [1102, 198], [1103, 199], [1104, 200], [1105, 200], [1107, 201], [1106, 202], [1108, 203], [1109, 204], [1110, 205], [1094, 206], [1145, 1], [1052, 1], [1111, 207], [1112, 208], [1113, 209], [1146, 210], [1114, 211], [1115, 212], [1116, 213], [1117, 214], [1118, 215], [1119, 216], [1120, 217], [1121, 218], [1122, 219], [1123, 220], [1124, 220], [1125, 221], [1126, 1], [1127, 222], [1129, 223], [1128, 224], [1130, 225], [1131, 226], [1132, 227], [1133, 228], [1134, 229], [1135, 230], [1136, 231], [1137, 232], [1138, 233], [1139, 234], [1140, 235], [1141, 236], [1142, 237], [1143, 238], [1144, 239], [51, 1], [1149, 1], [1150, 1], [481, 146], [49, 1], [52, 240], [53, 146], [1148, 241], [1153, 242], [1172, 243], [1259, 244], [1056, 1], [95, 245], [94, 246], [93, 1], [927, 247], [50, 1], [714, 248], [693, 249], [790, 1], [694, 250], [630, 248], [631, 1], [632, 1], [633, 1], [634, 1], [635, 1], [636, 1], [637, 1], [638, 1], [639, 1], [640, 1], [641, 1], [642, 248], [643, 248], [644, 1], [645, 1], [646, 1], [647, 1], [648, 1], [649, 1], [650, 1], [651, 1], [652, 1], [654, 1], [653, 1], [655, 1], [656, 1], [657, 248], [658, 1], [659, 1], [660, 248], [661, 1], [662, 1], [663, 248], [664, 1], [665, 248], [666, 248], [667, 248], [668, 1], [669, 248], [670, 248], [671, 248], [672, 248], [673, 248], [675, 248], [676, 1], [677, 1], [674, 248], [678, 248], [679, 1], [680, 1], [681, 1], [682, 1], [683, 1], [684, 1], [685, 1], [686, 1], [687, 1], [688, 1], [689, 1], [690, 248], [691, 1], [692, 1], [695, 251], [696, 248], [697, 248], [698, 252], [699, 253], [700, 248], [701, 248], [702, 248], [703, 248], [706, 248], [704, 1], [705, 1], [628, 1], [707, 1], [708, 1], [709, 1], [710, 1], [711, 1], [712, 1], [713, 1], [715, 254], [716, 1], [717, 1], [718, 1], [720, 1], [719, 1], [721, 1], [722, 1], [723, 1], [724, 248], [725, 1], [726, 1], [727, 1], [728, 1], [729, 248], [730, 248], [732, 248], [731, 248], [733, 1], [734, 1], [735, 1], [736, 1], [883, 255], [737, 248], [738, 248], [739, 1], [740, 1], [741, 1], [742, 1], [743, 1], [744, 1], [745, 1], [746, 1], [747, 1], [748, 1], [749, 1], [750, 1], [751, 248], [752, 1], [753, 1], [754, 1], [755, 1], [756, 1], [757, 1], [758, 1], [759, 1], [760, 1], [761, 1], [762, 248], [763, 1], [764, 1], [765, 1], [766, 1], [767, 1], [768, 1], [769, 1], [770, 1], [771, 1], [772, 248], [773, 1], [774, 1], [775, 1], [776, 1], [777, 1], [778, 1], [779, 1], [780, 1], [781, 248], [782, 1], [783, 1], [784, 1], [785, 1], [786, 1], [787, 1], [788, 248], [789, 1], [791, 256], [627, 248], [792, 1], [793, 248], [794, 1], [795, 1], [796, 1], [797, 1], [798, 1], [799, 1], [800, 1], [801, 1], [802, 1], [803, 248], [804, 1], [805, 1], [806, 1], [807, 1], [808, 1], [809, 1], [810, 1], [815, 257], [813, 258], [814, 259], [812, 260], [811, 248], [816, 1], [817, 1], [818, 248], [819, 1], [820, 1], [821, 1], [822, 1], [823, 1], [824, 1], [825, 1], [826, 1], [827, 1], [828, 248], [829, 248], [830, 1], [831, 1], [832, 1], [833, 248], [834, 1], [835, 248], [836, 1], [837, 254], [838, 1], [839, 1], [840, 1], [841, 1], [842, 1], [843, 1], [844, 1], [845, 1], [846, 1], [847, 248], [848, 248], [849, 1], [850, 1], [851, 1], [852, 1], [853, 1], [854, 1], [855, 1], [856, 1], [857, 1], [858, 1], [859, 1], [860, 1], [861, 248], [862, 248], [863, 1], [864, 1], [865, 248], [866, 1], [867, 1], [868, 1], [869, 1], [870, 1], [871, 1], [872, 1], [873, 1], [874, 1], [875, 1], [876, 1], [877, 1], [878, 248], [629, 261], [879, 1], [880, 1], [881, 1], [882, 1], [185, 262], [115, 263], [277, 264], [278, 265], [112, 1], [186, 266], [162, 267], [188, 268], [113, 266], [164, 1], [183, 269], [120, 270], [145, 271], [152, 272], [121, 272], [122, 272], [123, 273], [151, 274], [124, 275], [139, 272], [125, 276], [126, 276], [127, 272], [128, 272], [129, 273], [130, 272], [153, 277], [131, 272], [132, 272], [133, 278], [134, 272], [135, 272], [136, 278], [137, 273], [138, 272], [140, 279], [141, 278], [142, 272], [143, 273], [144, 272], [178, 280], [174, 281], [150, 282], [190, 283], [146, 284], [147, 282], [175, 285], [166, 286], [176, 287], [173, 288], [171, 289], [177, 290], [170, 291], [182, 292], [172, 293], [184, 294], [179, 295], [168, 296], [149, 297], [148, 282], [189, 298], [169, 299], [180, 1], [181, 300], [279, 301], [345, 302], [280, 303], [315, 304], [324, 305], [281, 306], [282, 306], [283, 307], [284, 306], [323, 308], [285, 309], [286, 310], [287, 311], [288, 306], [325, 312], [326, 313], [289, 306], [291, 314], [292, 305], [294, 315], [295, 316], [296, 316], [297, 307], [298, 306], [299, 306], [300, 316], [301, 307], [302, 307], [303, 316], [304, 306], [305, 305], [306, 306], [307, 307], [308, 317], [293, 318], [309, 306], [310, 307], [311, 306], [312, 306], [313, 306], [314, 306], [333, 319], [340, 320], [322, 321], [350, 322], [316, 323], [318, 324], [319, 321], [328, 325], [335, 326], [339, 327], [337, 328], [341, 329], [329, 330], [330, 331], [331, 332], [338, 333], [344, 334], [336, 335], [317, 266], [346, 336], [290, 266], [334, 337], [332, 338], [321, 339], [320, 321], [347, 340], [348, 1], [349, 341], [327, 299], [342, 1], [343, 342], [1159, 343], [1160, 344], [1158, 345], [161, 346], [117, 347], [165, 266], [163, 348], [167, 349], [247, 350], [238, 351], [216, 352], [222, 353], [191, 353], [192, 353], [193, 354], [221, 355], [194, 356], [209, 353], [195, 357], [196, 357], [197, 353], [198, 353], [199, 358], [200, 353], [223, 359], [201, 353], [202, 353], [203, 360], [204, 353], [205, 353], [206, 360], [207, 354], [208, 353], [210, 361], [211, 360], [212, 353], [213, 354], [214, 353], [215, 353], [235, 362], [227, 363], [241, 364], [217, 365], [218, 366], [230, 367], [224, 368], [234, 369], [226, 370], [233, 371], [232, 372], [237, 373], [225, 374], [239, 375], [236, 376], [231, 377], [220, 378], [219, 366], [240, 379], [229, 380], [228, 381], [154, 382], [156, 383], [155, 382], [157, 382], [159, 384], [158, 385], [160, 386], [118, 387], [275, 388], [242, 389], [268, 390], [272, 391], [271, 392], [243, 393], [273, 394], [264, 395], [265, 396], [266, 396], [267, 397], [252, 398], [260, 399], [270, 400], [276, 401], [244, 402], [245, 400], [248, 403], [255, 404], [259, 405], [257, 406], [261, 407], [249, 408], [253, 409], [258, 410], [274, 411], [256, 412], [254, 413], [250, 414], [269, 415], [246, 416], [263, 417], [251, 299], [262, 418], [116, 299], [119, 419], [114, 420], [187, 1], [365, 421], [367, 422], [371, 423], [370, 424], [369, 425], [368, 426], [366, 427], [920, 428], [921, 429], [886, 1], [894, 430], [888, 431], [895, 1], [917, 432], [892, 433], [916, 434], [913, 435], [896, 436], [897, 1], [890, 1], [887, 1], [918, 437], [914, 438], [898, 1], [915, 439], [899, 440], [901, 441], [902, 442], [891, 443], [903, 444], [904, 443], [906, 444], [907, 445], [908, 446], [910, 447], [905, 448], [911, 449], [912, 450], [889, 451], [909, 452], [893, 453], [900, 1], [919, 454], [432, 455], [969, 146], [96, 146], [429, 1], [430, 1], [428, 456], [431, 457], [427, 1], [424, 1], [426, 458], [425, 1], [1169, 1], [1245, 459], [1243, 460], [1244, 461], [1232, 462], [1233, 460], [1240, 463], [1231, 464], [1236, 465], [1246, 1], [1237, 466], [1242, 467], [1248, 468], [1247, 469], [1230, 470], [1238, 471], [1239, 472], [1234, 473], [1241, 459], [1235, 474], [1157, 475], [884, 476], [936, 1], [951, 477], [952, 477], [965, 478], [953, 479], [954, 479], [955, 480], [949, 481], [947, 482], [938, 1], [942, 483], [946, 484], [944, 485], [950, 486], [939, 487], [940, 488], [941, 489], [943, 490], [945, 491], [948, 492], [956, 479], [957, 479], [958, 479], [959, 477], [960, 479], [961, 479], [937, 479], [962, 1], [964, 493], [963, 479], [1001, 494], [983, 495], [985, 496], [987, 497], [986, 498], [984, 1], [988, 1], [989, 1], [990, 1], [991, 1], [992, 1], [993, 1], [994, 1], [995, 1], [996, 1], [997, 499], [999, 500], [1000, 500], [998, 1], [982, 146], [1002, 501], [536, 502], [538, 503], [528, 504], [533, 505], [534, 506], [540, 507], [535, 508], [532, 509], [531, 510], [530, 511], [541, 512], [498, 505], [499, 505], [539, 505], [544, 513], [554, 514], [548, 514], [556, 514], [560, 514], [546, 515], [547, 514], [549, 514], [552, 514], [555, 514], [551, 516], [553, 514], [557, 146], [550, 505], [545, 517], [507, 146], [511, 146], [501, 505], [504, 146], [509, 505], [510, 518], [503, 519], [506, 146], [508, 146], [505, 520], [494, 146], [493, 146], [562, 521], [559, 522], [525, 523], [524, 505], [522, 146], [523, 505], [526, 524], [527, 525], [520, 146], [516, 526], [519, 505], [518, 505], [517, 505], [512, 505], [521, 526], [558, 505], [537, 527], [543, 528], [542, 529], [561, 1], [529, 1], [502, 1], [500, 530], [57, 1], [1223, 531], [1222, 532], [1229, 1], [97, 1], [1072, 533], [1082, 534], [1071, 533], [1092, 535], [1063, 536], [1062, 537], [1091, 181], [1085, 538], [1090, 539], [1065, 540], [1079, 541], [1064, 542], [1088, 543], [1060, 544], [1059, 181], [1089, 545], [1061, 546], [1066, 547], [1067, 1], [1070, 547], [1057, 1], [1093, 548], [1083, 549], [1074, 550], [1075, 551], [1077, 552], [1073, 553], [1076, 554], [1086, 181], [1068, 555], [1069, 556], [1078, 557], [1058, 475], [1081, 549], [1080, 547], [1084, 1], [1087, 558], [932, 247], [497, 559], [515, 560], [1276, 561], [1252, 562], [1249, 563], [1227, 564], [1228, 1], [1225, 565], [1224, 1], [1226, 566], [1250, 1], [1275, 567], [1251, 568], [58, 569], [54, 1], [56, 570], [55, 570], [364, 571], [355, 572], [362, 573], [357, 1], [358, 1], [356, 574], [359, 571], [351, 1], [352, 1], [363, 575], [354, 576], [360, 1], [361, 577], [353, 578], [409, 579], [415, 580], [413, 581], [411, 581], [414, 581], [410, 581], [412, 581], [408, 581], [407, 1], [1162, 582], [1163, 112], [1165, 583], [1171, 584], [1174, 585], [1175, 586], [1161, 587], [1176, 588], [1177, 589], [1192, 590], [1195, 591], [1196, 592], [1198, 593], [1199, 594], [1200, 595], [1264, 596], [1213, 597], [1215, 598], [1265, 103], [1266, 103], [1267, 599], [1210, 594], [1203, 600], [1268, 601], [1269, 103], [1212, 582], [1207, 602], [1206, 603], [1205, 604], [1204, 605], [1270, 103], [1197, 606], [1202, 607], [1220, 608], [1194, 103], [1214, 595], [1271, 609], [1164, 595], [1217, 610], [1219, 594], [1216, 611], [1218, 612], [1272, 613], [1211, 582], [1273, 595], [1201, 614], [1193, 615], [1170, 616], [1274, 617], [1209, 618], [1263, 619], [1208, 620], [1173, 621], [1051, 622], [372, 623], [1262, 624]], "semanticDiagnosticsPerFile": [[88, [{"start": 9, "length": 11, "messageText": "Duplicate identifier 'QueryClient'.", "category": 1, "code": 2300}, {"start": 1086, "length": 11, "messageText": "Duplicate identifier 'QueryClient'.", "category": 1, "code": 2300}]], [395, [{"start": 255, "length": 20, "messageText": "Module '\"@/lib/energy-sync-controller\"' has no exported member 'energySyncController'.", "category": 1, "code": 2305}, {"start": 3574, "length": 311, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '(prev: RealTimeData) => { networkSpeed: number; memoryUsage: number; cpuCores: number; isRealHardware: true; batteryLevel: number; isCharging: boolean; umatterTotal: number; umatterRate: number; timestamp: number; }' is not assignable to parameter of type 'SetStateAction<RealTimeData>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(prev: RealTimeData) => { networkSpeed: number; memoryUsage: number; cpuCores: number; isRealHardware: true; batteryLevel: number; isCharging: boolean; umatterTotal: number; umatterRate: number; timestamp: number; }' is not assignable to type '(prevState: RealTimeData) => RealTimeData'.", "category": 1, "code": 2322, "next": [{"messageText": "Call signature return types '{ networkSpeed: number; memoryUsage: number; cpuCores: number; isRealHardware: true; batteryLevel: number; isCharging: boolean; umatterTotal: number; umatterRate: number; timestamp: number; }' and 'RealTimeData' are incompatible.", "category": 1, "code": 2202, "next": [{"messageText": "The types of 'networkSpeed' are incompatible between these types.", "category": 1, "code": 2200, "next": [{"messageText": "Type 'number' is not assignable to type 'string'.", "category": 1, "code": 2322, "canonicalHead": {"code": 2322, "messageText": "Type '(prev: RealTimeData) => { networkSpeed: number; memoryUsage: number; cpuCores: number; isRealHardware: true; batteryLevel: number; isCharging: boolean; umatterTotal: number; umatterRate: number; timestamp: number; }' is not assignable to type '(prevState: RealTimeData) => RealTimeData'."}}]}]}]}]}}]], [397, [{"start": 511, "length": 4, "messageText": "Property 'data' does not exist on type '{ umatterTotal: number; umatterRate: number; batteryLevel: number; isCharging: boolean; timestamp: number; networkSpeed: string; memoryUsage: string; cpuCores: number; isRealHardware: boolean; }'.", "category": 1, "code": 2339}]], [400, [{"start": 3604, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 3736, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}]], [401, [{"start": 4005, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"system_repair\"' is not assignable to type '\"function_call\" | \"system_analysis\" | \"code_generation\" | \"file_edit\" | \"repair\" | \"optimization\" | \"learning\" | \"diagnosis\"'.", "relatedInformation": [{"file": "../../client/src/lib/activity-dispatcher.ts", "start": 174, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Omit<LiveAction, \"id\" | \"timestamp\">'", "category": 3, "code": 6500}]}, {"start": 4876, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"system_repair\"' is not assignable to type '\"function_call\" | \"system_analysis\" | \"code_generation\" | \"file_edit\" | \"repair\" | \"optimization\" | \"learning\" | \"diagnosis\"'.", "relatedInformation": [{"file": "../../client/src/lib/activity-dispatcher.ts", "start": 174, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Omit<LiveAction, \"id\" | \"timestamp\">'", "category": 3, "code": 6500}]}, {"start": 5650, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"system_repair\"' is not assignable to type '\"function_call\" | \"system_analysis\" | \"code_generation\" | \"file_edit\" | \"repair\" | \"optimization\" | \"learning\" | \"diagnosis\"'.", "relatedInformation": [{"file": "../../client/src/lib/activity-dispatcher.ts", "start": 174, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Omit<LiveAction, \"id\" | \"timestamp\">'", "category": 3, "code": 6500}]}, {"start": 6007, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"analysis\"' is not assignable to type '\"function_call\" | \"system_analysis\" | \"code_generation\" | \"file_edit\" | \"repair\" | \"optimization\" | \"learning\" | \"diagnosis\"'.", "relatedInformation": [{"file": "../../client/src/lib/activity-dispatcher.ts", "start": 174, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Omit<LiveAction, \"id\" | \"timestamp\">'", "category": 3, "code": 6500}]}, {"start": 6371, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"task_execution\"' is not assignable to type '\"function_call\" | \"system_analysis\" | \"code_generation\" | \"file_edit\" | \"repair\" | \"optimization\" | \"learning\" | \"diagnosis\"'.", "relatedInformation": [{"file": "../../client/src/lib/activity-dispatcher.ts", "start": 174, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Omit<LiveAction, \"id\" | \"timestamp\">'", "category": 3, "code": 6500}]}, {"start": 6769, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"system_repair\"' is not assignable to type '\"function_call\" | \"system_analysis\" | \"code_generation\" | \"file_edit\" | \"repair\" | \"optimization\" | \"learning\" | \"diagnosis\"'.", "relatedInformation": [{"file": "../../client/src/lib/activity-dispatcher.ts", "start": 174, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Omit<LiveAction, \"id\" | \"timestamp\">'", "category": 3, "code": 6500}]}, {"start": 7126, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"system_repair\"' is not assignable to type '\"function_call\" | \"system_analysis\" | \"code_generation\" | \"file_edit\" | \"repair\" | \"optimization\" | \"learning\" | \"diagnosis\"'.", "relatedInformation": [{"file": "../../client/src/lib/activity-dispatcher.ts", "start": 174, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'Omit<LiveAction, \"id\" | \"timestamp\">'", "category": 3, "code": 6500}]}]], [403, [{"start": 1023, "length": 9, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 3, '(options: DefinedInitialDataOptions<unknown, Error, unknown, string[]>, queryClient?: QueryClient | undefined): DefinedUseQueryResult<unknown, Error>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Object literal may only specify known properties, and 'onSuccess' does not exist in type 'DefinedInitialDataOptions<unknown, Error, unknown, string[]>'.", "category": 1, "code": 2353}]}, {"messageText": "Overload 2 of 3, '(options: UndefinedInitialDataOptions<unknown, Error, unknown, string[]>, queryClient?: QueryClient | undefined): UseQueryResult<unknown, Error>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Object literal may only specify known properties, and 'onSuccess' does not exist in type 'UndefinedInitialDataOptions<unknown, Error, unknown, string[]>'.", "category": 1, "code": 2353}]}, {"messageText": "Overload 3 of 3, '(options: UseQueryOptions<unknown, Error, unknown, string[]>, queryClient?: QueryClient | undefined): UseQueryResult<unknown, Error>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Object literal may only specify known properties, and 'onSuccess' does not exist in type 'UseQueryOptions<unknown, Error, unknown, string[]>'.", "category": 1, "code": 2353}]}]}, "relatedInformation": []}, {"start": 2570, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'umatter' does not exist on type '{}'."}, {"start": 2597, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'umatter' does not exist on type '{}'."}, {"start": 2651, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'tru' does not exist on type '{}'."}, {"start": 2674, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'tru' does not exist on type '{}'."}, {"start": 2725, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'nuva' does not exist on type '{}'."}, {"start": 2749, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'nuva' does not exist on type '{}'."}, {"start": 2869, "length": 5, "code": 2322, "category": 1, "messageText": {"messageText": "Type '{ children: Element; title: string; subtitle: string; }' is not assignable to type 'IntrinsicAttributes & PageLayoutProps'.", "category": 1, "code": 2322, "next": [{"messageText": "Property 'title' does not exist on type 'IntrinsicAttributes & PageLayoutProps'.", "category": 1, "code": 2339}]}}, {"start": 7048, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'authenticEnergy' does not exist on type '{}'."}, {"start": 9838, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'length' does not exist on type '{}'."}, {"start": 10111, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'length' does not exist on type '{}'."}, {"start": 10376, "length": 15, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type '0' can't be used to index type '{}'.", "category": 1, "code": 7053, "next": [{"messageText": "Property '0' does not exist on type '{}'.", "category": 1, "code": 2339}]}}, {"start": 10699, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'reduce' does not exist on type '{}'."}, {"start": 10707, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 10712, "length": 2, "messageText": "Parameter 'tx' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [416, [{"start": 2234, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'SimpleNuvaState'."}, {"start": 2713, "length": 20, "messageText": "Cannot invoke an object which is possibly 'undefined'.", "category": 1, "code": 2722}, {"start": 2770, "length": 2, "messageText": "'ws' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 3202, "length": 2, "messageText": "'ws' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 3614, "length": 2, "messageText": "'ws' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 4718, "length": 5, "messageText": "Cannot find name 'state'.", "category": 1, "code": 2304}]], [421, [{"start": 585, "length": 10, "messageText": "Module '\"/Users/<USER>/Desktop/SPUNDERSV2 2/client/src/components/PageLayout\"' has no default export. Did you mean to use 'import { PageLayout } from \"/Users/<USER>/Desktop/SPUNDERSV2 2/client/src/components/PageLayout\"' instead?", "category": 1, "code": 2613}, {"start": 1970, "length": 9, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 3, '(options: DefinedInitialDataOptions<BankingBalance, Error, BankingBalance, readonly unknown[]>, queryClient?: QueryClient | undefined): DefinedUseQueryResult<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Object literal may only specify known properties, and 'cacheTime' does not exist in type 'DefinedInitialDataOptions<BankingBalance, Error, BankingBalance, readonly unknown[]>'.", "category": 1, "code": 2353}]}, {"messageText": "Overload 2 of 3, '(options: UndefinedInitialDataOptions<BankingBalance, Error, BankingBalance, readonly unknown[]>, queryClient?: QueryClient | undefined): UseQueryResult<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Object literal may only specify known properties, and 'cacheTime' does not exist in type 'UndefinedInitialDataOptions<BankingBalance, Error, BankingBalance, readonly unknown[]>'.", "category": 1, "code": 2353}]}, {"messageText": "Overload 3 of 3, '(options: UseQueryOptions<BankingBalance, Error, BankingBalance, readonly unknown[]>, queryClient?: QueryClient | undefined): UseQueryResult<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Object literal may only specify known properties, and 'cacheTime' does not exist in type 'UseQueryOptions<BankingBalance, Error, BankingBalance, readonly unknown[]>'.", "category": 1, "code": 2353}]}]}, "relatedInformation": []}, {"start": 2481, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'umatter' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 2531, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'tru' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 2579, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'nuva' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}]], [434, [{"start": 3833, "length": 19, "code": 2322, "category": 1, "messageText": "Type 'Timeout' is not assignable to type 'number'."}]], [435, [{"start": 3430, "length": 13, "code": 2551, "category": 1, "messageText": "Property 'currentDevice' does not exist on type 'RealTimeDeviceManager'. Did you mean 'addCurrentDevice'?", "relatedInformation": [{"start": 7050, "length": 16, "messageText": "'addCurrentDevice' is declared here.", "category": 3, "code": 2728}]}, {"start": 3462, "length": 13, "code": 2551, "category": 1, "messageText": "Property 'currentDevice' does not exist on type 'RealTimeDeviceManager'. Did you mean 'addCurrentDevice'?", "relatedInformation": [{"start": 7050, "length": 16, "messageText": "'addCurrentDevice' is declared here.", "category": 3, "code": 2728}]}, {"start": 3503, "length": 13, "code": 2551, "category": 1, "messageText": "Property 'currentDevice' does not exist on type 'RealTimeDeviceManager'. Did you mean 'addCurrentDevice'?", "relatedInformation": [{"start": 7050, "length": 16, "messageText": "'addCurrentDevice' is declared here.", "category": 3, "code": 2728}]}, {"start": 3691, "length": 13, "code": 2551, "category": 1, "messageText": "Property 'currentDevice' does not exist on type 'RealTimeDeviceManager'. Did you mean 'addCurrentDevice'?", "relatedInformation": [{"start": 7050, "length": 16, "messageText": "'addCurrentDevice' is declared here.", "category": 3, "code": 2728}]}, {"start": 3708, "length": 139, "messageText": "Expected 1 arguments, but got 2.", "category": 1, "code": 2554}, {"start": 11633, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'navigationStart' does not exist on type 'PerformanceNavigationTiming'."}, {"start": 17642, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}]], [439, [{"start": 10481, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}]], [440, [{"start": 7442, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'umatter' does not exist on type 'NuvaEnergyState'."}, {"start": 7498, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'umatter' does not exist in type 'NuvaEnergyState | Partial<NuvaEnergyState> | ((state: NuvaEnergyState) => NuvaEnergyState | Partial<...>)'."}, {"start": 7513, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'umatter' does not exist on type 'NuvaEnergyState'."}, {"start": 7561, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'tru' does not exist on type 'NuvaEnergyState'."}, {"start": 7718, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'umatter' does not exist on type 'NuvaEnergyState'."}, {"start": 7774, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'umatter' does not exist in type 'NuvaEnergyState | Partial<NuvaEnergyState> | ((state: NuvaEnergyState) => NuvaEnergyState | Partial<...>)'."}, {"start": 7789, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'umatter' does not exist on type 'NuvaEnergyState'."}, {"start": 7838, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'nuva' does not exist on type 'NuvaEnergyState'."}, {"start": 8256, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'NuvaEnergyState'."}, {"start": 9125, "length": 2, "messageText": "'ws' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 9551, "length": 18, "code": 2339, "category": 1, "messageText": "Property 'numentumMultiplier' does not exist on type 'NuvaEnergyState'."}, {"start": 9963, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'umatter' does not exist on type 'NuvaEnergyState'."}, {"start": 10065, "length": 2, "messageText": "'ws' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 10304, "length": 2, "messageText": "'ws' is possibly 'null'.", "category": 1, "code": 18047}]], [441, [{"start": 6039, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'ubitConversion' does not exist on type '{}'."}, {"start": 6117, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'energyRequired' does not exist on type 'EnergyPackage'."}, {"start": 6697, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'energyRequired' does not exist on type 'EnergyPackage'."}, {"start": 6761, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'truReward' does not exist on type 'EnergyPackage'."}, {"start": 6830, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'energyRequired' does not exist on type 'EnergyPackage'."}, {"start": 7747, "length": 14, "messageText": "Cannot find name 'useRealBalance'.", "category": 1, "code": 2304}, {"start": 10339, "length": 6, "code": 2322, "category": 1, "messageText": "Type '\"active\"' is not assignable to type '\"charging\" | \"discharging\" | \"idle\"'.", "relatedInformation": [{"start": 3752, "length": 6, "messageText": "The expected type comes from property 'status' which is declared here on type 'EnergyStorage'", "category": 3, "code": 6500}]}, {"start": 12038, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'globalScale' does not exist on type '{}'."}, {"start": 14509, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'fabricNodes' does not exist on type '{}'."}, {"start": 15054, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'ubitConversion' does not exist on type '{}'."}, {"start": 15136, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'ubitConversion' does not exist on type '{}'."}, {"start": 15730, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'globalScale' does not exist on type '{}'."}]], [442, [{"start": 919, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'umatterBalance' does not exist on type '{}'."}, {"start": 1187, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'truBalance' does not exist on type '{}'."}, {"start": 1453, "length": 20, "code": 2339, "category": 1, "messageText": "Property 'totalEnergyGenerated' does not exist on type '{}'."}]], [443, [{"start": 2180, "length": 9, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 3, '(options: DefinedInitialDataOptions<EnergyBalance, Error, EnergyBalance, readonly unknown[]>, queryClient?: QueryClient | undefined): DefinedUseQueryResult<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Object literal may only specify known properties, and 'cacheTime' does not exist in type 'DefinedInitialDataOptions<EnergyBalance, Error, EnergyBalance, readonly unknown[]>'.", "category": 1, "code": 2353}]}, {"messageText": "Overload 2 of 3, '(options: UndefinedInitialDataOptions<EnergyBalance, Error, EnergyBalance, readonly unknown[]>, queryClient?: QueryClient | undefined): UseQueryResult<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Object literal may only specify known properties, and 'cacheTime' does not exist in type 'UndefinedInitialDataOptions<EnergyBalance, Error, EnergyBalance, readonly unknown[]>'.", "category": 1, "code": 2353}]}, {"messageText": "Overload 3 of 3, '(options: UseQueryOptions<EnergyBalance, Error, EnergyBalance, readonly unknown[]>, queryClient?: QueryClient | undefined): UseQueryResult<...>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Object literal may only specify known properties, and 'cacheTime' does not exist in type 'UseQueryOptions<EnergyBalance, Error, EnergyBalance, readonly unknown[]>'.", "category": 1, "code": 2353}]}]}, "relatedInformation": []}, {"start": 2228, "length": 4, "messageText": "Parameter 'data' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2831, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'transactions' does not exist on type '{}'."}, {"start": 3126, "length": 18, "code": 2345, "category": 1, "messageText": "Argument of type '{ method: string; }' is not assignable to parameter of type 'string'."}, {"start": 3339, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'did' does not exist on type 'Response'."}, {"start": 3499, "length": 18, "code": 2345, "category": 1, "messageText": "Argument of type '{ method: string; }' is not assignable to parameter of type 'string'."}, {"start": 3709, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'walletAddress' does not exist on type 'Response'."}, {"start": 5733, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'totalValueUSD' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 5877, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'umatter' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 5928, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'tru' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 6059, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'transactionCount' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 6133, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'totalValueUSD' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 6308, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'authentic' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 6428, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'authentic' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 6507, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'authentic' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 6602, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'source' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 6681, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'source' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 7533, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'source' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 9086, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'umatter' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 9231, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'authentic' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 9348, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'tru' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 9489, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'authentic' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 9607, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'nuva' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 9751, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'authentic' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 9872, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'inurtia' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 10018, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'authentic' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 10137, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'ubits' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 10282, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'authentic' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 11976, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'transactionCount' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 12038, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'transactionCount' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 15488, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'did' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 15969, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'walletAddress' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 16683, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'lastEnergyUpdate' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 16740, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'lastEnergyUpdate' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 16862, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'umatter' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 17140, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'did' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 17313, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'did' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 17521, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'walletAddress' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}, {"start": 17705, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'walletAddress' does not exist on type 'NonNullable<NoInfer<TQueryFnData>>'."}]], [444, [{"start": 989, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'umatterBalance' does not exist on type '{}'."}, {"start": 1051, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'truBalance' does not exist on type '{}'."}, {"start": 1110, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'nuvaBalance' does not exist on type '{}'."}, {"start": 12010, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'slice' does not exist on type '{}'."}, {"start": 12027, "length": 2, "messageText": "Parameter 'tx' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 12031, "length": 1, "messageText": "Parameter 'i' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [455, [{"start": 1746, "length": 15, "messageText": "Cannot find name 'BluetoothDevice'.", "category": 1, "code": 2304}, {"start": 21767, "length": 24, "messageText": "This comparison appears to be unintentional because the types '\"unknown\" | \"phone\" | \"laptop\" | \"tablet\" | \"desktop\"' and '\"iPhone\"' have no overlap.", "category": 1, "code": 2367}, {"start": 21801, "length": 25, "messageText": "This comparison appears to be unintentional because the types '\"unknown\" | \"phone\" | \"laptop\" | \"tablet\" | \"desktop\"' and '\"Android\"' have no overlap.", "category": 1, "code": 2367}]], [456, [{"start": 11201, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'ipAddress' does not exist on type 'DiscoveredDevice'."}]], [461, [{"start": 2891, "length": 11, "messageText": "'connections' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 2952, "length": 11, "messageText": "'connections' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 3026, "length": 11, "messageText": "'connections' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 3740, "length": 11, "messageText": "'connections' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 4423, "length": 11, "messageText": "'connections' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 5167, "length": 10, "messageText": "'vibeGroups' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 5387, "length": 10, "messageText": "'vibeGroups' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 7043, "length": 11, "messageText": "'connections' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 7060, "length": 10, "messageText": "Parameter 'connection' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 10233, "length": 10, "messageText": "'vibeGroups' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 10249, "length": 5, "messageText": "Parameter 'group' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 12800, "length": 14, "messageText": "'vibeActivities' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 12820, "length": 8, "messageText": "Parameter 'activity' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 12830, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [466, [{"start": 1131, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'connected' does not exist on type '{}'."}, {"start": 1191, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'totalUMatter' does not exist on type '{}'."}, {"start": 1252, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'adsIntercepted' does not exist on type '{}'."}, {"start": 1379, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'version' does not exist on type '{}'."}, {"start": 1434, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'realTimeSync' does not exist on type '{}'."}, {"start": 1494, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'quantumFidelity' does not exist on type '{}'."}, {"start": 1582, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'extensionMetrics' does not exist on type '{}'."}, {"start": 1660, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'extensionMetrics' does not exist on type '{}'."}, {"start": 1741, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'networkSpeed' does not exist on type '{}'."}, {"start": 1791, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'earnings' does not exist on type '{}'."}]], [470, [{"start": 121, "length": 20, "messageText": "Module '\"@/lib/energy-sync-controller\"' has no exported member 'energySyncController'.", "category": 1, "code": 2305}]], [472, [{"start": 3269, "length": 13, "messageText": "Cannot find name 'setSyncStatus'. Did you mean 'syncStatus'?", "category": 1, "code": 2552, "canonicalHead": {"code": 2304, "messageText": "Cannot find name 'setSyncStatus'."}, "relatedInformation": [{"start": 1086, "length": 10, "messageText": "'syncStatus' is declared here.", "category": 3, "code": 2728}]}, {"start": 3751, "length": 19, "messageText": "Cannot find name 'setUmatterGenerated'.", "category": 1, "code": 2304}, {"start": 3771, "length": 4, "messageText": "Parameter 'prev' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [477, [{"start": 2539, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'memory' does not exist on type 'Performance'."}, {"start": 2692, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'memory' does not exist on type 'Performance'."}]], [480, [{"start": 3821, "length": 9, "code": 2322, "category": 1, "messageText": {"messageText": "Type '({ className }: AdvancedSpUnderDashboardProps) => Element' is not assignable to type 'ComponentType<RouteComponentProps<{ [param: number]: string | undefined; }>> | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '({ className }: AdvancedSpUnderDashboardProps) => Element' is not assignable to type 'FunctionComponent<RouteComponentProps<{ [param: number]: string | undefined; }>>'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters '__0' and 'props' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'RouteComponentProps<{ [param: number]: string | undefined; }>' has no properties in common with type 'AdvancedSpUnderDashboardProps'.", "category": 1, "code": 2559}]}]}]}, "relatedInformation": [{"file": "../wouter/types/index.d.ts", "start": 1897, "length": 9, "messageText": "The expected type comes from property 'component' which is declared here on type 'IntrinsicAttributes & RouteProps<undefined, \"/spunder-butler\">'", "category": 3, "code": 6500}]}]], [486, [{"start": 408, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'chrome' does not exist on type 'Window & typeof globalThis'."}, {"start": 425, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'chrome' does not exist on type 'Window & typeof globalThis'."}, {"start": 537, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'chrome' does not exist on type 'Window & typeof globalThis'."}, {"start": 637, "length": 8, "messageText": "Parameter 'response' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 688, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'chrome' does not exist on type 'Window & typeof globalThis'."}]], [489, [{"start": 3170, "length": 17, "messageText": "Expected 3 arguments, but got 1.", "category": 1, "code": 2554, "relatedInformation": [{"start": 14192, "length": 9, "messageText": "An argument for 'y' was not provided.", "category": 3, "code": 6210}]}, {"start": 14651, "length": 78, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ timestamp: number; motionIntensity: number; }' is not assignable to parameter of type 'BiometricReading'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '{ timestamp: number; motionIntensity: number; }' is missing the following properties from type 'BiometricReading': energyLevel, stressLevel, focusScore", "category": 1, "code": 2739}]}}, {"start": 14937, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'requestPermission' does not exist on type '{ new (type: string, eventInitDict?: DeviceMotionEventInit | undefined): DeviceMotionEvent; prototype: DeviceMotionEvent; }'."}, {"start": 15734, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'readings' does not exist on type 'RealBiometricTracker'."}, {"start": 15807, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'readings' does not exist on type 'RealBiometricTracker'."}, {"start": 15843, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'readings' does not exist on type 'RealBiometricTracker'."}, {"start": 15922, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'readings' does not exist on type 'RealBiometricTracker'."}, {"start": 16308, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'readings' does not exist on type 'RealBiometricTracker'."}, {"start": 18011, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'readings' does not exist on type 'RealBiometricTracker'."}, {"start": 18034, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 18222, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 18227, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 18673, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'isActive' does not exist on type 'RealBiometricTracker'."}, {"start": 18701, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'isActive' does not exist on type 'RealBiometricTracker'."}, {"start": 19366, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'isActive' does not exist on type 'RealBiometricTracker'."}, {"start": 19740, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'isActive' does not exist on type 'RealBiometricTracker'."}, {"start": 19825, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'readings' does not exist on type 'RealBiometricTracker'."}, {"start": 19868, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'readings' does not exist on type 'RealBiometricTracker'."}, {"start": 19882, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'readings' does not exist on type 'RealBiometricTracker'."}, {"start": 20205, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'readings' does not exist on type 'RealBiometricTracker'."}, {"start": 20530, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'readings' does not exist on type 'RealBiometricTracker'."}, {"start": 20587, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 20592, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 20675, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 20680, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 20763, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 20768, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 20839, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'readings' does not exist on type 'RealBiometricTracker'."}, {"start": 20853, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'readings' does not exist on type 'RealBiometricTracker'."}, {"start": 21264, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'readings' does not exist on type 'RealBiometricTracker'."}, {"start": 21302, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'isActive' does not exist on type 'RealBiometricTracker'."}]], [567, [{"start": 7602, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'toFixed' does not exist on type 'never'."}, {"start": 8425, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'toFixed' does not exist on type 'never'."}]], [570, [{"start": 1299, "length": 31, "messageText": "Duplicate function implementation.", "category": 1, "code": 2393}, {"start": 1399, "length": 16, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(type: keyof WindowEventMap, listener: (this: Window, ev: ClipboardEvent | Event | CompositionEvent | UIEvent | FocusEvent | ... 24 more ... | StorageEvent) => any, options?: boolean | ... 1 more ... | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '\"spunderInteraction\"' is not assignable to parameter of type 'keyof WindowEventMap'.", "category": 1, "code": 2345}]}, {"messageText": "Overload 2 of 2, '(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '(event: CustomEvent<any>) => void' is not assignable to parameter of type 'EventListenerOrEventListenerObject'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(event: CustomEvent<any>) => void' is not assignable to type 'EventListener'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'event' and 'evt' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'Event' is missing the following properties from type 'CustomEvent<any>': detail, initCustomEvent", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type 'Event' is not assignable to type 'CustomEvent<any>'."}}]}]}]}]}]}, "relatedInformation": []}, {"start": 1555, "length": 16, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(type: keyof WindowEventMap, listener: (this: Window, ev: ClipboardEvent | Event | CompositionEvent | UIEvent | FocusEvent | ... 24 more ... | StorageEvent) => any, options?: boolean | ... 1 more ... | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '\"messageReceived\"' is not assignable to parameter of type 'keyof WindowEventMap'.", "category": 1, "code": 2345}]}, {"messageText": "Overload 2 of 2, '(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '(event: CustomEvent<any>) => void' is not assignable to parameter of type 'EventListenerOrEventListenerObject'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(event: CustomEvent<any>) => void' is not assignable to type 'EventListener'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'event' and 'evt' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'Event' is missing the following properties from type 'CustomEvent<any>': detail, initCustomEvent", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type 'Event' is not assignable to type 'CustomEvent<any>'."}}]}]}]}]}]}, "relatedInformation": []}, {"start": 1642, "length": 16, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(type: keyof WindowEventMap, listener: (this: Window, ev: ClipboardEvent | Event | CompositionEvent | UIEvent | FocusEvent | ... 24 more ... | StorageEvent) => any, options?: boolean | ... 1 more ... | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '\"messageSent\"' is not assignable to parameter of type 'keyof WindowEventMap'.", "category": 1, "code": 2345}]}, {"messageText": "Overload 2 of 2, '(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '(event: CustomEvent<any>) => void' is not assignable to parameter of type 'EventListenerOrEventListenerObject'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(event: CustomEvent<any>) => void' is not assignable to type 'EventListener'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'event' and 'evt' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'Event' is missing the following properties from type 'CustomEvent<any>': detail, initCustomEvent", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type 'Event' is not assignable to type 'CustomEvent<any>'."}}]}]}]}]}]}, "relatedInformation": []}, {"start": 1761, "length": 16, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(type: keyof WindowEventMap, listener: (this: Window, ev: ClipboardEvent | Event | CompositionEvent | UIEvent | FocusEvent | ... 24 more ... | StorageEvent) => any, options?: boolean | ... 1 more ... | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '\"energyShared\"' is not assignable to parameter of type 'keyof WindowEventMap'.", "category": 1, "code": 2345}]}, {"messageText": "Overload 2 of 2, '(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '(event: CustomEvent<any>) => void' is not assignable to parameter of type 'EventListenerOrEventListenerObject'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(event: CustomEvent<any>) => void' is not assignable to type 'EventListener'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'event' and 'evt' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'Event' is missing the following properties from type 'CustomEvent<any>': detail, initCustomEvent", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type 'Event' is not assignable to type 'CustomEvent<any>'."}}]}]}]}]}]}, "relatedInformation": []}, {"start": 1842, "length": 16, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(type: keyof WindowEventMap, listener: (this: Window, ev: ClipboardEvent | Event | CompositionEvent | UIEvent | FocusEvent | ... 24 more ... | StorageEvent) => any, options?: boolean | ... 1 more ... | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '\"energyReceived\"' is not assignable to parameter of type 'keyof WindowEventMap'.", "category": 1, "code": 2345}]}, {"messageText": "Overload 2 of 2, '(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions | undefined): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '(event: CustomEvent<any>) => void' is not assignable to parameter of type 'EventListenerOrEventListenerObject'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(event: CustomEvent<any>) => void' is not assignable to type 'EventListener'.", "category": 1, "code": 2322, "next": [{"messageText": "Types of parameters 'event' and 'evt' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'Event' is missing the following properties from type 'CustomEvent<any>': detail, initCustomEvent", "category": 1, "code": 2739, "canonicalHead": {"code": 2322, "messageText": "Type 'Event' is not assignable to type 'CustomEvent<any>'."}}]}]}]}]}]}, "relatedInformation": []}, {"start": 2607, "length": 31, "messageText": "Duplicate function implementation.", "category": 1, "code": 2393}, {"start": 5250, "length": 4, "code": 2820, "category": 1, "messageText": "Type '\"celebration\"' is not assignable to type '\"message\" | \"help\" | \"share\" | \"reaction\" | \"collaboration\" | \"celebrate\"'. Did you mean '\"celebrate\"'?", "relatedInformation": [{"start": 264, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'CommunityInteraction'", "category": 3, "code": 6500}]}, {"start": 17226, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'currentMultiplier' does not exist on type 'CommunityNumentumMultiplier'."}, {"start": 17354, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'currentMultiplier' does not exist on type 'CommunityNumentumMultiplier'."}, {"start": 17423, "length": 25, "code": 2339, "category": 1, "messageText": "Property 'broadcastMultiplierUpdate' does not exist on type 'CommunityNumentumMultiplier'."}]], [574, [{"start": 659, "length": 30, "messageText": "Cannot find module '@/lib/realExtensionDashboard' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 2793, "length": 8, "messageText": "Parameter 'activity' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2934, "length": 18, "messageText": "Cannot find name 'setCurrentNumentum'.", "category": 1, "code": 2304}, {"start": 2953, "length": 4, "messageText": "Parameter 'prev' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3694, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'getEnergyMetrics' does not exist on type 'IoTEnergyIntegration'."}, {"start": 3760, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'getDevices' does not exist on type 'IoTEnergyIntegration'."}, {"start": 5882, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'currentBalance' does not exist on type '{}'."}, {"start": 6273, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'compoundRate' does not exist on type '{}'."}, {"start": 7520, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'currentBalance' does not exist on type '{}'."}, {"start": 7572, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'totalEarned' does not exist on type '{}'."}, {"start": 7626, "length": 18, "code": 2339, "category": 1, "messageText": "Property 'dailyNumentumScore' does not exist on type '{}'."}, {"start": 10771, "length": 50, "code": 2322, "category": 1, "messageText": "Type 'unknown' is not assignable to type 'ReactNode'."}, {"start": 10829, "length": 27, "code": 2322, "category": 1, "messageText": "Type 'unknown' is not assignable to type 'ReactNode'."}, {"start": 15904, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'compoundRate' does not exist on type '{}'."}, {"start": 19412, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'length' does not exist on type '{}'."}, {"start": 19807, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'slice' does not exist on type '{}'."}, {"start": 19850, "length": 5, "messageText": "Parameter 'index' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 19956, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'map' does not exist on type '{}'."}, {"start": 20867, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'length' does not exist on type '{}'."}, {"start": 21217, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'slice' does not exist on type '{}'."}]], [578, [{"start": 2578, "length": 11, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | undefined' is not assignable to parameter of type 'SetStateAction<string | null>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'undefined' is not assignable to type 'SetStateAction<string | null>'.", "category": 1, "code": 2322}]}}]], [580, [{"start": 495, "length": 12, "messageText": "'\"lucide-react\"' has no exported member named 'TouchPadIcon'. Did you mean 'TouchpadIcon'?", "category": 1, "code": 2724}]], [582, [{"start": 2676, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'shareableLink' does not exist on type 'Response'."}]], [584, [{"start": 73, "length": 4, "code": 7016, "category": 1, "messageText": {"messageText": "Could not find a declaration file for module 'd3'. '/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/d3/src/index.js' implicitly has an 'any' type.", "category": 1, "code": 7016, "next": [{"info": {"moduleReference": "d3", "mode": 99}}]}}, {"start": 1746, "length": 5, "messageText": "'nodes' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 1806, "length": 11, "messageText": "'connections' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 1898, "length": 12, "messageText": "'dynamicNodes' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 2227, "length": 18, "messageText": "'dynamicConnections' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 5377, "length": 5, "messageText": "Parameter 'event' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5384, "length": 1, "messageText": "Parameter 'd' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5823, "length": 5, "messageText": "Parameter 'event' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5830, "length": 1, "messageText": "Parameter 'd' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5982, "length": 5, "messageText": "Parameter 'event' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5989, "length": 1, "messageText": "Parameter 'd' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6080, "length": 5, "messageText": "Parameter 'event' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6087, "length": 1, "messageText": "Parameter 'd' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7151, "length": 5, "messageText": "Parameter 'event' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7158, "length": 1, "messageText": "Parameter 'd' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7290, "length": 5, "messageText": "Parameter 'event' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [586, [{"start": 393, "length": 8, "messageText": "Duplicate identifier '<PERSON><PERSON><PERSON>ot'.", "category": 1, "code": 2300}, {"start": 10866, "length": 8, "messageText": "Duplicate identifier '<PERSON><PERSON><PERSON>ot'.", "category": 1, "code": 2300}, {"start": 10220, "length": 16, "messageText": "Cannot redeclare block-scoped variable 'drainBotInstance'.", "category": 1, "code": 2451}, {"start": 17226, "length": 16, "messageText": "Cannot redeclare block-scoped variable 'drainBotInstance'.", "category": 1, "code": 2451}, {"start": 10414, "length": 8, "messageText": "Duplicate identifier '<PERSON><PERSON><PERSON>ot'.", "category": 1, "code": 2300}, {"start": 17271, "length": 8, "messageText": "Duplicate identifier '<PERSON><PERSON><PERSON>ot'.", "category": 1, "code": 2300}, {"start": 132, "length": 11, "messageText": "Individual declarations in merged declaration 'BatteryInfo' must be all exported or all local.", "category": 1, "code": 2395}, {"start": 273, "length": 11, "messageText": "Individual declarations in merged declaration 'DrainResult' must be all exported or all local.", "category": 1, "code": 2395}, {"start": 10279, "length": 11, "messageText": "Cannot redeclare exported variable 'getDrainBot'.", "category": 1, "code": 2323}, {"start": 10279, "length": 11, "messageText": "Duplicate function implementation.", "category": 1, "code": 2393}, {"start": 10440, "length": 11, "messageText": "Export declaration conflicts with exported declaration of 'BatteryInfo'.", "category": 1, "code": 2484}, {"start": 10453, "length": 11, "messageText": "Export declaration conflicts with exported declaration of 'DrainResult'.", "category": 1, "code": 2484}, {"start": 10623, "length": 11, "messageText": "Individual declarations in merged declaration 'DrainResult' must be all exported or all local.", "category": 1, "code": 2395}, {"start": 10752, "length": 11, "messageText": "Individual declarations in merged declaration 'BatteryInfo' must be all exported or all local.", "category": 1, "code": 2395}, {"start": 11648, "length": 6, "code": 2741, "category": 1, "messageText": "Property 'lastUpdated' is missing in type '{ level: number; charging: any; chargingTime: any; dischargingTime: any; }' but required in type 'BatteryInfo'.", "relatedInformation": [{"start": 239, "length": 11, "messageText": "'lastUpdated' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ level: number; charging: any; chargingTime: any; dischargingTime: any; }' is not assignable to type 'BatteryInfo'."}}, {"start": 12016, "length": 6, "code": 2741, "category": 1, "messageText": "Property 'lastUpdated' is missing in type '{ level: number; charging: true; }' but required in type 'BatteryInfo'.", "relatedInformation": [{"start": 239, "length": 11, "messageText": "'lastUpdated' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2322, "messageText": "Type '{ level: number; charging: true; }' is not assignable to type 'BatteryInfo'."}}, {"start": 13323, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'success' does not exist in type 'DrainResult'."}, {"start": 17333, "length": 11, "messageText": "Cannot redeclare exported variable 'getDrainBot'.", "category": 1, "code": 2323}, {"start": 17333, "length": 11, "messageText": "Duplicate function implementation.", "category": 1, "code": 2393}, {"start": 17530, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'DrainBot | null' is not assignable to type 'DrainBot'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'DrainBot'.", "category": 1, "code": 2322}]}}]], [587, [{"start": 338, "length": 15, "messageText": "Duplicate identifier 'NUTShellNetwork'.", "category": 1, "code": 2300}, {"start": 3601, "length": 15, "messageText": "Duplicate identifier 'NUTShellNetwork'.", "category": 1, "code": 2300}, {"start": 3144, "length": 15, "messageText": "Duplicate identifier 'NUTShellNetwork'.", "category": 1, "code": 2300}, {"start": 9171, "length": 15, "messageText": "Duplicate identifier 'NUTShellNetwork'.", "category": 1, "code": 2300}, {"start": 141, "length": 11, "messageText": "Individual declarations in merged declaration 'NetworkPeer' must be all exported or all local.", "category": 1, "code": 2395}, {"start": 249, "length": 13, "messageText": "Individual declarations in merged declaration 'NetworkStatus' must be all exported or all local.", "category": 1, "code": 2395}, {"start": 2967, "length": 18, "messageText": "Cannot redeclare exported variable 'getNUTShellNetwork'.", "category": 1, "code": 2323}, {"start": 2967, "length": 18, "messageText": "Duplicate function implementation.", "category": 1, "code": 2393}, {"start": 3177, "length": 11, "messageText": "Export declaration conflicts with exported declaration of 'NetworkPeer'.", "category": 1, "code": 2484}, {"start": 3190, "length": 13, "messageText": "Export declaration conflicts with exported declaration of 'NetworkStatus'.", "category": 1, "code": 2484}, {"start": 3352, "length": 11, "messageText": "Individual declarations in merged declaration 'NetworkPeer' must be all exported or all local.", "category": 1, "code": 2395}, {"start": 3488, "length": 13, "messageText": "Individual declarations in merged declaration 'NetworkStatus' must be all exported or all local.", "category": 1, "code": 2395}, {"start": 4988, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'address' does not exist in type 'NetworkPeer'."}, {"start": 5754, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'reliability' does not exist on type 'NetworkPeer'."}, {"start": 6129, "length": 9, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'connected' does not exist in type 'NetworkStatus'."}, {"start": 6677, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'address' does not exist in type 'NetworkPeer'."}, {"start": 7341, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'reliability' does not exist on type 'NetworkPeer'."}, {"start": 8409, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'reliability' does not exist on type 'NetworkPeer'."}, {"start": 9239, "length": 18, "messageText": "Cannot redeclare exported variable 'getNUTShellNetwork'.", "category": 1, "code": 2323}, {"start": 9239, "length": 18, "messageText": "Duplicate function implementation.", "category": 1, "code": 2393}]], [588, [{"start": 11151, "length": 15, "messageText": "Variable 'recommendations' implicitly has type 'any[]' in some locations where its type cannot be determined.", "category": 1, "code": 7034}, {"start": 11501, "length": 15, "messageText": "Variable 'recommendations' implicitly has an 'any[]' type.", "category": 1, "code": 7005}, {"start": 16155, "length": 5, "messageText": "'count' is of type 'unknown'.", "category": 1, "code": 18046}]], [589, [{"start": 9294, "length": 13, "messageText": "'navigator.usb' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 9337, "length": 5, "messageText": "Parameter 'event' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 9426, "length": 13, "messageText": "'navigator.usb' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 9472, "length": 5, "messageText": "Parameter 'event' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 9586, "length": 13, "messageText": "'navigator.usb' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 9638, "length": 6, "messageText": "Parameter 'device' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 15199, "length": 20, "messageText": "Property 'energySyncController' does not exist on type 'typeof import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/client/src/lib/energy-sync-controller\")'.", "category": 1, "code": 2339}]], [591, [{"start": 17511, "length": 20, "messageText": "Property 'energySyncController' does not exist on type 'typeof import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/client/src/lib/energy-sync-controller\")'.", "category": 1, "code": 2339}]], [592, [{"start": 15838, "length": 35, "code": 2339, "category": 1, "messageText": "Property 'createEnhancedSmartDeviceSimulation' does not exist on type 'RealWorldEnergyAPI'."}, {"start": 16004, "length": 35, "code": 2339, "category": 1, "messageText": "Property 'createEnhancedSmartDeviceSimulation' does not exist on type 'RealWorldEnergyAPI'."}, {"start": 22435, "length": 20, "messageText": "Property 'energySyncController' does not exist on type 'typeof import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/client/src/lib/energy-sync-controller\")'.", "category": 1, "code": 2339}]], [593, [{"start": 3231, "length": 8, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../../client/src/lib/ghost-bot.ts", "start": 680, "length": 8, "messageText": "The expected type comes from property 'priority' which is declared here on type 'BotTask'", "category": 3, "code": 6500}]}, {"start": 3520, "length": 8, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../../client/src/lib/ghost-bot.ts", "start": 680, "length": 8, "messageText": "The expected type comes from property 'priority' which is declared here on type 'BotTask'", "category": 3, "code": 6500}]}, {"start": 3826, "length": 8, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../../client/src/lib/ghost-bot.ts", "start": 680, "length": 8, "messageText": "The expected type comes from property 'priority' which is declared here on type 'BotTask'", "category": 3, "code": 6500}]}, {"start": 7520, "length": 8, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../../client/src/lib/ghost-bot.ts", "start": 680, "length": 8, "messageText": "The expected type comes from property 'priority' which is declared here on type 'BotTask'", "category": 3, "code": 6500}]}]], [596, [{"start": 9711, "length": 5, "messageText": "Parameter 'match' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [598, [{"start": 4128, "length": 13, "messageText": "'privacyAlerts' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 4183, "length": 13, "messageText": "'privacyAlerts' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 4693, "length": 34, "code": 2322, "category": 1, "messageText": "Type 'unknown' is not assignable to type 'ReactNode'."}, {"start": 4736, "length": 3618, "code": 2322, "category": 1, "messageText": "Type 'unknown' is not assignable to type 'ReactNode'."}, {"start": 4768, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'length' does not exist on type '{}'."}, {"start": 5066, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'length' does not exist on type '{}'."}, {"start": 5227, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'slice' does not exist on type '{}'."}, {"start": 7999, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'length' does not exist on type '{}'."}, {"start": 8189, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'length' does not exist on type '{}'."}, {"start": 8965, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'overallScore' does not exist on type '{}'."}, {"start": 9256, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'encryptionLevel' does not exist on type '{}'."}, {"start": 9561, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'dataShares' does not exist on type '{}'."}, {"start": 9856, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'exposureRisk' does not exist on type '{}'."}]], [599, [{"start": 1664, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}]], [603, [{"start": 1134, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'connected' does not exist on type '{}'."}, {"start": 1155, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'totalCount' does not exist on type '{}'."}, {"start": 1188, "length": 20, "code": 2339, "category": 1, "messageText": "Property 'totalEnergyGenerated' does not exist on type '{}'."}, {"start": 1900, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'totalUMatter' does not exist on type '{}'."}, {"start": 1924, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'totalUMatter' does not exist on type '{}'."}, {"start": 1984, "length": 20, "code": 2339, "category": 1, "messageText": "Property 'totalEnergyGenerated' does not exist on type '{}'."}, {"start": 3971, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'totalAdsBlocked' does not exist on type '{}'."}, {"start": 3998, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'totalCount' does not exist on type '{}'."}, {"start": 4260, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'fabricNodes' does not exist on type '{}'."}]], [605, [{"start": 4820, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'conversionRate' does not exist on type 'Response'."}]], [609, [{"start": 2861, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'usdAmount' does not exist on type 'Response'."}, {"start": 4014, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'totalEarnings' does not exist on type '{}'."}, {"start": 7258, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'truToUsd' does not exist on type '{}'."}, {"start": 7403, "length": 526, "code": 2322, "category": 1, "messageText": "Type 'unknown' is not assignable to type 'ReactNode'."}, {"start": 7676, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'totalEarnings' does not exist on type '{}'."}, {"start": 7819, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'totalEarnings' does not exist on type '{}'."}, {"start": 7848, "length": 21, "messageText": "The right-hand side of an arithmetic operation must be of type 'any', 'number', 'bigint' or an enum type.", "category": 1, "code": 2363}, {"start": 8115, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'lastUpdated' does not exist on type '{}'."}, {"start": 9153, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'truToUsd' does not exist on type '{}'."}, {"start": 9304, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'change24h' does not exist on type '{}'."}, {"start": 9412, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'change24h' does not exist on type '{}'."}, {"start": 9496, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'change24h' does not exist on type '{}'."}, {"start": 9553, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'change24h' does not exist on type '{}'."}, {"start": 9595, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'change24h' does not exist on type '{}'."}, {"start": 9690, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'lastUpdated' does not exist on type '{}'."}, {"start": 9828, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'lastUpdated' does not exist on type '{}'."}, {"start": 9911, "length": 29, "code": 2322, "category": 1, "messageText": "Type 'unknown' is not assignable to type 'ReactNode'."}, {"start": 10300, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'totalEarnings' does not exist on type '{}'."}, {"start": 10462, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'totalEarnings' does not exist on type '{}'."}, {"start": 10697, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'totalEarnings' does not exist on type '{}'."}, {"start": 10743, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'truToUsd' does not exist on type '{}'."}]], [611, [{"start": 2286, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'connected' does not exist on type '{}'."}, {"start": 5606, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'connected' does not exist on type '{}'."}, {"start": 5902, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'connected' does not exist on type '{}'."}, {"start": 6037, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'connected' does not exist on type '{}'."}, {"start": 6189, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'connected' does not exist on type '{}'."}]], [976, [{"start": 93, "length": 4, "messageText": "Module '\"lucide-react\"' has no exported member 'Sync'.", "category": 1, "code": 2305}]], [1016, [{"start": 6255, "length": 19, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ 'e-commerce': string; entertainment: string; food: string; automotive: string; general: string; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ 'e-commerce': string; entertainment: string; food: string; automotive: string; general: string; }'.", "category": 1, "code": 7054}]}}, {"start": 6535, "length": 15, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ 'e-commerce': string; entertainment: string; food: string; automotive: string; general: string; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ 'e-commerce': string; entertainment: string; food: string; automotive: string; general: string; }'.", "category": 1, "code": 7054}]}}]], [1017, [{"start": 2509, "length": 23, "messageText": "Operator '+' cannot be applied to types 'number' and 'Promise<number>'.", "category": 1, "code": 2365, "relatedInformation": [{"start": 2509, "length": 23, "messageText": "Did you forget to use 'await'?", "category": 1, "code": 2773}]}, {"start": 2670, "length": 8, "code": 2322, "category": 1, "messageText": "Type 'Promise<number>' is not assignable to type 'number'.", "relatedInformation": [{"start": 246, "length": 8, "messageText": "The expected type comes from property 'cpuWatts' which is declared here on type 'RealPowerMeasurement'", "category": 3, "code": 6500}]}]], [1020, [{"start": 10284, "length": 16, "messageText": "'dependencies' is specified more than once, so this usage will be overwritten.", "category": 1, "code": 2783, "relatedInformation": [{"start": 10308, "length": 11, "messageText": "This spread always overwrites this property.", "category": 1, "code": 2785}]}, {"start": 13326, "length": 24, "messageText": "This comparison appears to be unintentional because the types '\"create\"' and '\"update\"' have no overlap.", "category": 1, "code": 2367}]], [1025, [{"start": 892, "length": 6, "messageText": "Parameter 'device' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [1032, [{"start": 1510, "length": 2, "code": 2345, "category": 1, "messageText": "Argument of type 'Timeout' is not assignable to parameter of type 'number'."}]], [1033, [{"start": 1408, "length": 8, "code": 2322, "category": 1, "messageText": {"messageText": "Type '\"high\" | \"low\" | \"normal\"' is not assignable to type '\"high\" | \"low\" | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"normal\"' is not assignable to type '\"high\" | \"low\" | undefined'.", "category": 1, "code": 2322}]}}, {"start": 1937, "length": 8, "code": 2322, "category": 1, "messageText": {"messageText": "Type '\"high\" | \"low\" | \"normal\"' is not assignable to type '\"high\" | \"low\" | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '\"normal\"' is not assignable to type '\"high\" | \"low\" | undefined'.", "category": 1, "code": 2322}]}}]], [1034, [{"start": 2695, "length": 7, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'actions' does not exist in type 'NotificationOptions'."}, {"start": 3297, "length": 19, "messageText": "'navigator.bluetooth' is possibly 'undefined'.", "category": 1, "code": 18048}]], [1036, [{"start": 134, "length": 21, "messageText": "Individual declarations in merged declaration 'RealEnergyMeasurement' must be all exported or all local.", "category": 1, "code": 2395}, {"start": 457, "length": 21, "messageText": "Individual declarations in merged declaration 'RealEnergyMeasurement' must be all exported or all local.", "category": 1, "code": 2395}]], [1037, [{"start": 2674, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 3329, "length": 7, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(input: string | URL | Request, init?: RequestInit | undefined): Promise<Response>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Object literal may only specify known properties, and 'timeout' does not exist in type 'RequestInit'.", "category": 1, "code": 2353}]}, {"messageText": "Overload 2 of 2, '(input: URL | RequestInfo, init?: RequestInit | undefined): Promise<Response>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Object literal may only specify known properties, and 'timeout' does not exist in type 'RequestInit'.", "category": 1, "code": 2353}]}]}, "relatedInformation": []}, {"start": 3585, "length": 4, "code": 2322, "category": 1, "messageText": "Type '\"Unknown\"' is not assignable to type '\"iPad\" | \"Android\" | \"iPhone\" | \"Laptop\"'.", "relatedInformation": [{"start": 182, "length": 4, "messageText": "The expected type comes from property 'type' which is declared here on type 'DiscoveredDevice'", "category": 3, "code": 6500}]}]], [1038, [{"start": 7035, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'navigationStart' does not exist on type 'PerformanceNavigationTiming'."}, {"start": 7213, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'navigationStart' does not exist on type 'PerformanceNavigationTiming'."}]], [1039, [{"start": 15927, "length": 223, "code": 2345, "category": 1, "messageText": "Argument of type '{ method: string; body: string; }' is not assignable to parameter of type 'string'."}]], [1040, [{"start": 609, "length": 8, "messageText": "Duplicate identifier 'isActive'.", "category": 1, "code": 2300}, {"start": 3298, "length": 8, "messageText": "Duplicate identifier 'isActive'.", "category": 1, "code": 2300}, {"start": 3298, "length": 8, "messageText": "Duplicate identifier 'isActive'.", "category": 1, "code": 2300}]], [1041, [{"start": 5641, "length": 21, "code": 2339, "category": 1, "messageText": "Property 'getNumentumMultiplier' does not exist on type 'BiometricEnergyTracker'."}]], [1043, [{"start": 2944, "length": 4, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'json' does not exist on type 'Response | { ok: boolean; error: any; }'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'json' does not exist on type '{ ok: boolean; error: any; }'.", "category": 1, "code": 2339}]}}, {"start": 3212, "length": 4, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'json' does not exist on type 'Response | { ok: boolean; error: any; }'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'json' does not exist on type '{ ok: boolean; error: any; }'.", "category": 1, "code": 2339}]}}, {"start": 3388, "length": 4, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'json' does not exist on type 'Response | { ok: boolean; error: any; }'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'json' does not exist on type '{ ok: boolean; error: any; }'.", "category": 1, "code": 2339}]}}, {"start": 3572, "length": 4, "code": 2339, "category": 1, "messageText": {"messageText": "Property 'json' does not exist on type 'Response | { ok: boolean; error: any; }'.", "category": 1, "code": 2339, "next": [{"messageText": "Property 'json' does not exist on type '{ ok: boolean; error: any; }'.", "category": 1, "code": 2339}]}}, {"start": 3993, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'memory' does not exist on type 'Performance'."}, {"start": 4025, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'memory' does not exist on type 'Performance'."}, {"start": 9148, "length": 5, "messageText": "'React' refers to a UMD global, but the current file is a module. Consider adding an import instead.", "category": 1, "code": 2686}, {"start": 9196, "length": 5, "messageText": "'React' refers to a UMD global, but the current file is a module. Consider adding an import instead.", "category": 1, "code": 2686}]], [1044, [{"start": 5708, "length": 20, "messageText": "Property 'energySyncController' does not exist on type 'typeof import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/client/src/lib/energy-sync-controller\")'.", "category": 1, "code": 2339}]], [1045, [{"start": 17173, "length": 20, "messageText": "Property 'energySyncController' does not exist on type 'typeof import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/client/src/lib/energy-sync-controller\")'.", "category": 1, "code": 2339}, {"start": 19163, "length": 20, "messageText": "Property 'energySyncController' does not exist on type 'typeof import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/client/src/lib/energy-sync-controller\")'.", "category": 1, "code": 2339}]], [1048, [{"start": 4469, "length": 8, "code": 2322, "category": 1, "messageText": "Type 'string' is not assignable to type 'number'.", "relatedInformation": [{"file": "../../client/src/lib/ghost-bot.ts", "start": 680, "length": 8, "messageText": "The expected type comes from property 'priority' which is declared here on type 'BotTask'", "category": 3, "code": 6500}]}, {"start": 9293, "length": 20, "messageText": "Property 'energySyncController' does not exist on type 'typeof import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/client/src/lib/energy-sync-controller\")'.", "category": 1, "code": 2339}, {"start": 9798, "length": 20, "messageText": "Property 'energySyncController' does not exist on type 'typeof import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/client/src/lib/energy-sync-controller\")'.", "category": 1, "code": 2339}]], [1050, [{"start": 1175, "length": 11, "messageText": "Cannot find name 'CheckCircle'.", "category": 1, "code": 2304}]], [1162, [{"start": 261, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 570, "length": 18, "code": 2345, "category": 1, "messageText": "Argument of type 'unknown' is not assignable to parameter of type 'string'."}, {"start": 634, "length": 25, "code": 2345, "category": 1, "messageText": "Argument of type 'unknown' is not assignable to parameter of type 'string'."}]], [1163, [{"start": 3431, "length": 7, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 2, '(input: string | URL | Request, init?: RequestInit | undefined): Promise<Response>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type '{ 'x-api-key': string | undefined; 'anthropic-version': string; }' is not assignable to type 'HeadersInit | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ 'x-api-key': string | undefined; 'anthropic-version': string; }' is not assignable to type 'undefined'.", "category": 1, "code": 2322}]}]}, {"messageText": "Overload 2 of 2, '(input: URL | RequestInfo, init?: RequestInit | undefined): Promise<Response>', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type '{ 'x-api-key': string | undefined; 'anthropic-version': string; }' is not assignable to type 'HeadersInit | undefined'.", "category": 1, "code": 2322, "next": [{"messageText": "Type '{ 'x-api-key': string | undefined; 'anthropic-version': string; }' is not assignable to type 'undefined'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}, {"start": 8651, "length": 6, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'AIResponse | null' is not assignable to type 'AIResponse'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'AIResponse'.", "category": 1, "code": 2322}]}}]], [1165, [{"start": 49, "length": 15, "messageText": "Duplicate identifier 'isAuthenticated'.", "category": 1, "code": 2300}, {"start": 11455, "length": 15, "messageText": "Duplicate identifier 'isAuthenticated'.", "category": 1, "code": 2300}, {"start": 4502, "length": 29, "code": 2339, "category": 1, "messageText": "Property 'generateContextualSuggestions' does not exist on type 'AISearchEngine'."}, {"start": 4686, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'rankResults' does not exist on type 'AISearchEngine'."}, {"start": 6272, "length": 20, "code": 2339, "category": 1, "messageText": "Property 'calculateEnergyBoost' does not exist on type 'AISearchEngine'."}, {"start": 6342, "length": 26, "code": 2339, "category": 1, "messageText": "Property 'calculateUMatterGeneration' does not exist on type 'AISearchEngine'."}, {"start": 7663, "length": 6, "code": 2322, "category": 1, "messageText": "Type '\"authentic_iot\"' is not assignable to type '\"iot\" | \"web\" | \"nuos\" | \"extension\" | \"biometric\"'.", "relatedInformation": [{"start": 181, "length": 6, "messageText": "The expected type comes from property 'source' which is declared here on type 'SearchResult'", "category": 3, "code": 6500}]}, {"start": 9326, "length": 29, "code": 2339, "category": 1, "messageText": "Property 'calculateBiometricEnergyBoost' does not exist on type 'AISearchEngine'."}, {"start": 13340, "length": 12, "messageText": "Cannot find name 'searchEngine'. Did you mean 'AISearchEngine'?", "category": 1, "code": 2552, "canonicalHead": {"code": 2304, "messageText": "Cannot find name 'search<PERSON><PERSON><PERSON>'."}, "relatedInformation": [{"start": 600, "length": 14, "messageText": "'AISearchEngine' is declared here.", "category": 3, "code": 2728}]}, {"start": 13917, "length": 12, "messageText": "Cannot find name 'searchEngine'. Did you mean 'AISearchEngine'?", "category": 1, "code": 2552, "canonicalHead": {"code": 2304, "messageText": "Cannot find name 'search<PERSON><PERSON><PERSON>'."}, "relatedInformation": [{"start": 600, "length": 14, "messageText": "'AISearchEngine' is declared here.", "category": 3, "code": 2728}]}]], [1170, [{"start": 12214, "length": 11, "messageText": "'t.createdAt' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 14476, "length": 11, "messageText": "'t.createdAt' is possibly 'null'.", "category": 1, "code": 18047}]], [1171, [{"start": 686, "length": 4, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: null, options?: (SignOptions & { algorithm: \"none\"; }) | undefined): string', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type 'string' is not assignable to parameter of type 'null'.", "category": 1, "code": 2345}]}, {"messageText": "Overload 2 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Buffer<ArrayBufferLike> | Secret | PrivateKeyInput | JsonWebKeyInput, options?: SignOptions | undefined): string', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Type 'string' is not assignable to type 'number | StringValue | undefined'.", "category": 1, "code": 2322}]}, {"messageText": "Overload 3 of 5, '(payload: string | object | Buffer<ArrayBufferLike>, secretOrPrivateKey: Buffer<ArrayBufferLike> | Secret | PrivateKeyInput | JsonWebKeyInput, callback: SignCallback): void', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Object literal may only specify known properties, and 'expiresIn' does not exist in type 'SignCallback'.", "category": 1, "code": 2353}]}]}, "relatedInformation": []}, {"start": 2278, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'getUserByEmail' does not exist on type 'DatabaseStorage'."}, {"start": 2613, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'createUser' does not exist on type 'DatabaseStorage'."}, {"start": 3563, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'getUserByEmail' does not exist on type 'DatabaseStorage'."}]], [1174, [{"start": 2231, "length": 15, "messageText": "Property 'broadcastToUser' does not exist on type 'typeof import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/server/websocket\")'.", "category": 1, "code": 2339}, {"start": 9260, "length": 10, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 9279, "length": 10, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 9312, "length": 10, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}]], [1175, [{"start": 238, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 894, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 2156, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 3113, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 3910, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 4726, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 6579, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'Request<{ adId: string; }, any, any, ParsedQs, Record<string, any>>'."}]], [1176, [{"start": 52, "length": 10, "code": 7016, "category": 1, "messageText": {"messageText": "Could not find a declaration file for module 'archiver'. '/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/archiver/index.js' implicitly has an 'any' type.", "category": 1, "code": 7016, "next": [{"info": {"moduleReference": "archiver", "mode": 99}}]}}, {"start": 618, "length": 3, "messageText": "Parameter 'err' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [1177, [{"start": 7721, "length": 15, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ 'e-commerce': string; entertainment: string; automotive: string; food: string; general: string; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ 'e-commerce': string; entertainment: string; automotive: string; food: string; general: string; }'.", "category": 1, "code": 7054}]}}, {"start": 8321, "length": 17, "messageText": "Variable 'realOpportunities' implicitly has type 'any[]' in some locations where its type cannot be determined.", "category": 1, "code": 7034}, {"start": 9070, "length": 17, "messageText": "Variable 'realOpportunities' implicitly has an 'any[]' type.", "category": 1, "code": 7005}]], [1193, [{"start": 7722, "length": 1, "messageText": "'m' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 14039, "length": 78, "messageText": "Object is of type 'unknown'.", "category": 1, "code": 2571}, {"start": 21890, "length": 5, "code": 2740, "category": 1, "messageText": "Type 'Omit<PgSelectBase<\"vibe_marketplace\", { id: PgColumn<{ name: \"id\"; tableName: \"vibe_marketplace\"; dataType: \"string\"; columnType: \"PgUUID\"; data: string; driverParam: string; notNull: true; hasDefault: true; isPrimaryKey: true; ... 5 more ...; generated: undefined; }, {}, {}>; ... 11 more ...; updatedAt: PgColumn<.....' is missing the following properties from type 'PgSelectBase<\"vibe_marketplace\", { id: PgColumn<{ name: \"id\"; tableName: \"vibe_marketplace\"; dataType: \"string\"; columnType: \"PgUUID\"; data: string; driverParam: string; notNull: true; hasDefault: true; isPrimaryKey: true; ... 5 more ...; generated: undefined; }, {}, {}>; ... 11 more ...; updatedAt: PgColumn<...>; }...': config, joinsNotNullableMap, tableName, isPartialSelect, and 5 more.", "canonicalHead": {"code": 2322, "messageText": "Type 'Omit<PgSelectBase<\"vibe_marketplace\", { id: PgColumn<{ name: \"id\"; tableName: \"vibe_marketplace\"; dataType: \"string\"; columnType: \"PgUUID\"; data: string; driverParam: string; notNull: true; hasDefault: true; isPrimaryKey: true; ... 5 more ...; generated: undefined; }, {}, {}>; ... 11 more ...; updatedAt: PgColumn<.....' is not assignable to type 'PgSelectBase<\"vibe_marketplace\", { id: PgColumn<{ name: \"id\"; tableName: \"vibe_marketplace\"; dataType: \"string\"; columnType: \"PgUUID\"; data: string; driverParam: string; notNull: true; hasDefault: true; isPrimaryKey: true; ... 5 more ...; generated: undefined; }, {}, {}>; ... 11 more ...; updatedAt: PgColumn<...>; }...'."}}]], [1194, [{"start": 2381, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'priceCache' does not exist on type 'RealMarketDataConnector'."}, {"start": 4148, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'priceCache' does not exist on type 'RealMarketDataConnector'."}]], [1195, [{"start": 395, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 947, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 2612, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'source' does not exist on type '{ balance: number; transactionCount: number; }'."}, {"start": 3205, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 4345, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 6181, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'balance' does not exist on type 'number'."}, {"start": 6305, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'balance' does not exist on type 'number'."}, {"start": 7274, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 8029, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'balance' does not exist on type 'number'."}, {"start": 8134, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'balance' does not exist on type 'number'."}, {"start": 8180, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'transactionCount' does not exist on type 'number'."}, {"start": 9270, "length": 18, "code": 2339, "category": 1, "messageText": "Property 'getConversionRates' does not exist on type 'RealMarketDataConnector'."}, {"start": 9332, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 9753, "length": 2, "messageText": "Cannot find name 'db'.", "category": 1, "code": 2304}, {"start": 9763, "length": 18, "messageText": "Cannot find name 'energyTransactions'.", "category": 1, "code": 2304}, {"start": 10149, "length": 2, "messageText": "Cannot find name 'db'.", "category": 1, "code": 2304}, {"start": 10159, "length": 18, "messageText": "Cannot find name 'energyTransactions'.", "category": 1, "code": 2304}, {"start": 11752, "length": 2, "messageText": "Cannot find name 'db'.", "category": 1, "code": 2304}, {"start": 11762, "length": 18, "messageText": "Cannot find name 'energyTransactions'.", "category": 1, "code": 2304}, {"start": 12096, "length": 2, "messageText": "Cannot find name 'db'.", "category": 1, "code": 2304}, {"start": 12106, "length": 18, "messageText": "Cannot find name 'energyTransactions'.", "category": 1, "code": 2304}]], [1196, [{"start": 337, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 2213, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 2747, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 3212, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 4034, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 7071, "length": 12, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 4, '(value: string | number | Date): Date', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type 'Date | null' is not assignable to parameter of type 'string | number | Date'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string | number | Date'.", "category": 1, "code": 2322}]}]}, {"messageText": "Overload 2 of 4, '(value: string | number): Date', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type 'Date | null' is not assignable to parameter of type 'string | number'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string | number'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}, {"start": 7223, "length": 12, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 4, '(value: string | number | Date): Date', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type 'Date | null' is not assignable to parameter of type 'string | number | Date'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string | number | Date'.", "category": 1, "code": 2322}]}]}, {"messageText": "Overload 2 of 4, '(value: string | number): Date', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type 'Date | null' is not assignable to parameter of type 'string | number'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string | number'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}, {"start": 7379, "length": 12, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 4, '(value: string | number | Date): Date', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type 'Date | null' is not assignable to parameter of type 'string | number | Date'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string | number | Date'.", "category": 1, "code": 2322}]}]}, {"messageText": "Overload 2 of 4, '(value: string | number): Date', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type 'Date | null' is not assignable to parameter of type 'string | number'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string | number'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}, {"start": 8038, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'isActive' does not exist on type '{ id: string; name: string; type: string; status: string; }'."}, {"start": 8118, "length": 18, "code": 2339, "category": 1, "messageText": "Property 'energyContribution' does not exist on type '{ id: string; name: string; type: string; status: string; }'."}, {"start": 8207, "length": 18, "code": 2339, "category": 1, "messageText": "Property 'energyContribution' does not exist on type '{ id: string; name: string; type: string; status: string; }'."}, {"start": 8230, "length": 18, "code": 2339, "category": 1, "messageText": "Property 'energyContribution' does not exist on type '{ id: string; name: string; type: string; status: string; }'."}, {"start": 8320, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'deviceType' does not exist on type '{ id: string; name: string; type: string; status: string; }'."}, {"start": 8341, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'deviceType' does not exist on type '{ id: string; name: string; type: string; status: string; }'."}, {"start": 8692, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'activeDevices' does not exist on type '{ totalEnergy: number; participants: number; avgContribution: number; }'."}, {"start": 8742, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'totalUMatter' does not exist on type '{ totalEnergy: number; participants: number; avgContribution: number; }'."}, {"start": 8890, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'totalDevices' does not exist on type '{ totalEnergy: number; participants: number; avgContribution: number; }'."}, {"start": 10380, "length": 12, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 4, '(value: string | number | Date): Date', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type 'Date | null' is not assignable to parameter of type 'string | number | Date'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string | number | Date'.", "category": 1, "code": 2322}]}]}, {"messageText": "Overload 2 of 4, '(value: string | number): Date', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type 'Date | null' is not assignable to parameter of type 'string | number'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string | number'.", "category": 1, "code": 2322}]}]}]}, "relatedInformation": []}, {"start": 12795, "length": 1, "messageText": "'p' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 12866, "length": 1, "messageText": "'p' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 12926, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'totalUMatter' does not exist on type '{ totalEnergy: number; participants: number; avgContribution: number; }'."}, {"start": 13197, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'totalDevices' does not exist on type '{ totalEnergy: number; participants: number; avgContribution: number; }'."}]], [1198, [{"start": 1216, "length": 6, "code": 2322, "category": 1, "messageText": "Type 'Promise<number>' is not assignable to type 'number'."}, {"start": 3447, "length": 7, "messageText": "Cannot find name 'storage'.", "category": 1, "code": 2304}, {"start": 4330, "length": 7, "messageText": "Cannot find name 'storage'.", "category": 1, "code": 2304}, {"start": 4938, "length": 15, "messageText": "Cannot find name 'EnergyBatchItem'.", "category": 1, "code": 2304}, {"start": 4999, "length": 11, "messageText": "Cannot find name '<PERSON><PERSON><PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 5220, "length": 7, "messageText": "Cannot find name 'storage'.", "category": 1, "code": 2304}, {"start": 5674, "length": 5, "messageText": "Cannot find name 'items'.", "category": 1, "code": 2304}]], [1199, [{"start": 2892, "length": 18, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 2927, "length": 18, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 3004, "length": 18, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 3101, "length": 18, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 3310, "length": 23, "code": 2551, "category": 1, "messageText": "Property 'recordWebAdInterception' does not exist on type 'DatabaseStorage'. Did you mean 'storeWebAdInterception'?", "relatedInformation": [{"file": "../../server/storage-interface.ts", "start": 6524, "length": 22, "messageText": "'storeWebAdInterception' is declared here.", "category": 3, "code": 2728}]}, {"start": 5225, "length": 16, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 5258, "length": 16, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 5303, "length": 16, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 5880, "length": 28, "code": 2551, "category": 1, "messageText": "Property 'createDataMarketplaceListing' does not exist on type 'DatabaseStorage'. Did you mean 'createVibeMarketplaceListing'?", "relatedInformation": [{"file": "../../server/storage-interface.ts", "start": 21466, "length": 28, "messageText": "'createVibeMarketplaceListing' is declared here.", "category": 3, "code": 2728}]}, {"start": 8555, "length": 15, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 8585, "length": 15, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}]], [1202, [{"start": 6429, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 16587, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'udpSocket' does not exist on type 'RealDeviceMessenger'."}, {"start": 16636, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 17609, "length": 5, "messageText": "Cannot find name 'd<PERSON>'.", "category": 1, "code": 2304}, {"start": 17894, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 21684, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'udpSocket' does not exist on type 'RealDeviceMessenger'."}, {"start": 21740, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 24770, "length": 5, "messageText": "Cannot find namespace 'dgram'.", "category": 1, "code": 2503}, {"start": 25018, "length": 5, "messageText": "Cannot find namespace 'dgram'.", "category": 1, "code": 2503}, {"start": 26253, "length": 5, "messageText": "Cannot find namespace 'dgram'.", "category": 1, "code": 2503}, {"start": 26621, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'udpSocket' does not exist on type 'RealDeviceMessenger'."}]], [1203, [{"start": 11669, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'sessionId' does not exist on type 'never'."}, {"start": 11739, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'chunkId' does not exist on type 'never'."}, {"start": 11781, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'sessionId' does not exist on type 'never'."}, {"start": 11825, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'chunkData' does not exist on type 'never'."}, {"start": 11889, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'chunkData' does not exist on type 'never'."}, {"start": 11929, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'embeddings' does not exist on type 'never'."}, {"start": 11973, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'keywords' does not exist on type 'never'."}, {"start": 12016, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'timestamp' does not exist on type 'never'."}, {"start": 12068, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'encrypted' does not exist on type 'never'."}, {"start": 12377, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 12411, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 12464, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}]], [1204, [{"start": 7304, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 23460, "length": 5, "messageText": "'count' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 26355, "length": 22, "messageText": "Duplicate function implementation.", "category": 1, "code": 2393}, {"start": 30111, "length": 15, "code": 2322, "category": 1, "messageText": "Type '\"local\"' is not assignable to type '\"google\" | \"amazon\" | \"ibm\" | \"rigetti\" | \"ionq\"'.", "relatedInformation": [{"start": 1638, "length": 15, "messageText": "The expected type comes from property 'quantumProvider' which is declared here on type 'AuthenticQuantumState'", "category": 3, "code": 6500}]}, {"start": 31353, "length": 22, "messageText": "Duplicate function implementation.", "category": 1, "code": 2393}]], [1205, [{"start": 1534, "length": 41, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "Overload 1 of 3, '(callbackfn: (previousValue: unknown, currentValue: unknown, currentIndex: number, array: unknown[]) => unknown, initialValue: unknown): unknown', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '(sum: number, mult: number) => number' is not assignable to parameter of type '(previousValue: unknown, currentValue: unknown, currentIndex: number, array: unknown[]) => unknown'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of parameters 'sum' and 'previousValue' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'unknown' is not assignable to type 'number'.", "category": 1, "code": 2322}]}]}]}, {"messageText": "Overload 2 of 3, '(callbackfn: (previousValue: number, currentValue: unknown, currentIndex: number, array: unknown[]) => number, initialValue: number): number', gave the following error.", "category": 1, "code": 2772, "next": [{"messageText": "Argument of type '(sum: number, mult: number) => number' is not assignable to parameter of type '(previousValue: number, currentValue: unknown, currentIndex: number, array: unknown[]) => number'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of parameters 'mult' and 'currentValue' are incompatible.", "category": 1, "code": 2328, "next": [{"messageText": "Type 'unknown' is not assignable to type 'number'.", "category": 1, "code": 2322}]}]}]}]}, "relatedInformation": []}, {"start": 1633, "length": 15, "messageText": "'totalMultiplier' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 2749, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'currentBalance' does not exist on type '{ balance: number; }'."}, {"start": 3674, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'totalEarned' does not exist on type '{ balance: number; }'."}, {"start": 4120, "length": 15, "messageText": "Property 'broadcastToUser' does not exist on type 'typeof import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/server/websocket\")'.", "category": 1, "code": 2339}, {"start": 6554, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'currentBalance' does not exist on type '{ balance: number; }'."}, {"start": 6807, "length": 13, "messageText": "Expected 2 arguments, but got 1.", "category": 1, "code": 2554, "relatedInformation": [{"file": "../../server/storage-interface.ts", "start": 31568, "length": 14, "messageText": "An argument for 'amount' was not provided.", "category": 3, "code": 6210}]}, {"start": 6873, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type '{ balance: number; }'."}, {"start": 7068, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'currentBalance' does not exist on type '{ balance: number; }'."}, {"start": 7267, "length": 15, "messageText": "Property 'broadcastToUser' does not exist on type 'typeof import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/server/websocket\")'.", "category": 1, "code": 2339}, {"start": 9900, "length": 10, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 9961, "length": 10, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 13194, "length": 14, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 13217, "length": 14, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 13256, "length": 14, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 13430, "length": 14, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 13481, "length": 14, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 13697, "length": 11, "code": 2739, "category": 1, "messageText": "Type '{}' is missing the following properties from type '{ biometric: number; activity: number; social: number; focus: number; joy: number; }': biometric, activity, social, focus, joy", "relatedInformation": [{"start": 177, "length": 11, "messageText": "The expected type comes from property 'multipliers' which is declared here on type 'NumentumState'", "category": 3, "code": 6500}], "canonicalHead": {"code": 2322, "messageText": "Type '{}' is not assignable to type '{ biometric: number; activity: number; social: number; focus: number; joy: number; }'."}}, {"start": 14864, "length": 12, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 14885, "length": 12, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 14926, "length": 12, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 15280, "length": 12, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 15301, "length": 12, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 15342, "length": 12, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 15481, "length": 10, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 15500, "length": 10, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 15539, "length": 10, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}]], [1206, [{"start": 3666, "length": 6, "code": 2678, "category": 1, "messageText": "Type '\"flow\"' is not comparable to type '\"search\" | \"factor\" | \"qaoa\" | \"hhl\"'."}, {"start": 3704, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'runNUFlow' does not exist on type 'NQUFClient'."}, {"start": 4057, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'getUMatterState' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 4158, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4163, "length": 3, "messageText": "Parameter 'amp' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4286, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'saveUMatterState' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 4650, "length": 10, "messageText": "Cannot find name 'finalState'.", "category": 1, "code": 2304}, {"start": 5303, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'applyQuantumGate' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 5527, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'applyQuantumGate' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 5634, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'applyQuantumGate' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 5921, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'measureUMatter' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 6844, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'applyQuantumGate' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 7250, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'amplifyAmplitudes' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 7361, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'applyQuantumGate' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 7421, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'applyQuantumGate' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 7481, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'applyQuantumGate' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 7836, "length": 6, "messageText": "Parameter 'result' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7881, "length": 1, "messageText": "Parameter 'a' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7884, "length": 1, "messageText": "Parameter 'b' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 8032, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'measureUMatter' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 9070, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'applyQuantumGate' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 9276, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'initializeUbit' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 9344, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'entangleDevices' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 9779, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'applyQuantumGate' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 9922, "length": 3, "messageText": "Parameter 'bit' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 9975, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'amplifyAmplitudes' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 10376, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'measureUbit' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 11310, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'applyQuantumGate' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 11548, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'initializeUbit' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 11616, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'entangleDevices' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 12631, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'amplifyAmplitudes' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 12739, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'measureUMatter' does not exist on type 'AuthenticNUPhysicsEngine'."}]], [1207, [{"start": 2004, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 2983, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 4557, "length": 12, "messageText": "'quantumError' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 5096, "length": 21, "messageText": "Property 'executeQuantumCircuit' is private and only accessible within class 'AuthenticNUPhysicsEngine'.", "category": 1, "code": 2341}, {"start": 5201, "length": 28, "messageText": "Expected 3 arguments, but got 4.", "category": 1, "code": 2554}, {"start": 5978, "length": 13, "messageText": "'hardwareError' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 6645, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'enhancedSearch' does not exist on type 'NQEProcessor'."}, {"start": 6764, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'enhancedQAOA' does not exist on type 'NQEProcessor'."}, {"start": 6880, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'enhancedHHL' does not exist on type 'NQEProcessor'."}, {"start": 19512, "length": 6, "messageText": "Function lacks ending return statement and return type does not include 'undefined'.", "category": 1, "code": 2366}, {"start": 20578, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 21136, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 21925, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 22190, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 22643, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}]], [1210, [{"start": 6312, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}]], [1211, [{"start": 299, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 2448, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}]], [1212, [{"start": 247, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 1527, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}]], [1213, [{"start": 1978, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}]], [1214, [{"start": 11500, "length": 23, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ iPhone: number; Android: number; iPad: number; Laptop: number; Desktop: number; }'."}]], [1216, [{"start": 1843, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'."}, {"start": 4104, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'session' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 4402, "length": 2, "messageText": "Parameter 'tx' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4630, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4635, "length": 2, "messageText": "Parameter 'tx' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [1217, [{"start": 3024, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 3050, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 3118, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 4849, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 4875, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 4942, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 5051, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 5691, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 5717, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 5777, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 6263, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 6289, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 6472, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 15033, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'umatterGenerated' does not exist on type '{}'."}, {"start": 15091, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'truTokens' does not exist on type '{}'."}, {"start": 15143, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'nuvaTokens' does not exist on type '{}'."}, {"start": 15200, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'batteryDrainWh' does not exist on type '{}'."}, {"start": 15775, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'timestamp' does not exist on type '{}'."}, {"start": 26103, "length": 9, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 26123, "length": 9, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 26369, "length": 9, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 26387, "length": 6, "messageText": "Parameter 'client' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 27646, "length": 4, "code": 2740, "category": 1, "messageText": "Type '{ id: any; email: any; firstName: any; lastName: any; profileImageUrl: any; companyName: null; accountType: null; dataMonetizationEnabled: boolean; createdAt: Date; updatedAt: Date; }' is missing the following properties from type '{ id: string; email: string | null; firstName: string | null; lastName: string | null; profileImageUrl: string | null; companyName: string | null; accountType: string | null; dataMonetizationEnabled: boolean | null; ... 7 more ...; updatedAt: Date | null; }': did, walletAddress, kycStatus, accountStatus, and 2 more.", "canonicalHead": {"code": 2322, "messageText": "Type '{ id: any; email: any; firstName: any; lastName: any; profileImageUrl: any; companyName: null; accountType: null; dataMonetizationEnabled: boolean; createdAt: Date; updatedAt: Date; }' is not assignable to type '{ id: string; email: string | null; firstName: string | null; lastName: string | null; profileImageUrl: string | null; companyName: string | null; accountType: string | null; dataMonetizationEnabled: boolean | null; ... 7 more ...; updatedAt: Date | null; }'."}}, {"start": 33843, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 33984, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'totalUMatter' does not exist on type 'any[]'."}, {"start": 34047, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'batteryGenerated' does not exist on type 'never'."}, {"start": 34110, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'cpuGenerated' does not exist on type 'never'."}, {"start": 34172, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'memoryGenerated' does not exist on type 'never'."}, {"start": 34389, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'lastUpdate' does not exist on type 'any[]'."}, {"start": 34632, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'totalCount' does not exist on type 'any[]'."}, {"start": 34705, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'lastUpdate' does not exist on type 'any[]'."}, {"start": 34774, "length": 19, "code": 2339, "category": 1, "messageText": "Property 'recentInterceptions' does not exist on type 'any[]'."}, {"start": 34886, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'batteryLevel' does not exist on type 'never'."}, {"start": 34942, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'cpuUsage' does not exist on type 'never'."}, {"start": 34995, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'memoryUsage' does not exist on type 'never'."}, {"start": 35177, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'networkActivity' does not exist on type 'never'."}, {"start": 35387, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 36414, "length": 23, "code": 2551, "category": 1, "messageText": "Property 'recordWebAdInterception' does not exist on type 'DatabaseStorage'. Did you mean 'storeWebAdInterception'?", "relatedInformation": [{"file": "../../server/storage-interface.ts", "start": 6524, "length": 22, "messageText": "'storeWebAdInterception' is declared here.", "category": 3, "code": 2728}]}, {"start": 36992, "length": 12, "messageText": "'storageError' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 37090, "length": 14, "messageText": "Cannot find name 'activityRecord'.", "category": 1, "code": 2304}, {"start": 39255, "length": 18, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 39319, "length": 18, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 39374, "length": 1, "messageText": "'b' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 39388, "length": 1, "messageText": "'a' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 39500, "length": 2, "messageText": "'ad' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 39530, "length": 2, "messageText": "'ad' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 39572, "length": 2, "messageText": "'ad' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 39606, "length": 2, "messageText": "'ad' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 46053, "length": 18, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 46090, "length": 18, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 46258, "length": 18, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 51852, "length": 11, "messageText": "Expected 1 arguments, but got 2.", "category": 1, "code": 2554}, {"start": 60880, "length": 21, "messageText": "Expected 2 arguments, but got 1.", "category": 1, "code": 2554, "relatedInformation": [{"file": "../../server/storage-interface.ts", "start": 26687, "length": 13, "messageText": "An argument for 'activity' was not provided.", "category": 3, "code": 6210}]}, {"start": 62751, "length": 20, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ factor: string; search: string; qaoa: string; hhl: string; }'."}, {"start": 63501, "length": 40, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "The last overload gave the following error.", "category": 1, "code": 2770, "next": [{"messageText": "Argument of type '(req: Request, res: Response) => Promise<void>' is not assignable to parameter of type 'Application<Record<string, any>>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(req: Request, res: Response) => Promise<void>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.", "category": 1, "code": 2740}]}]}]}, "relatedInformation": [{"file": "../@types/express-serve-static-core/index.d.ts", "start": 5597, "length": 51, "messageText": "The last overload is declared here.", "category": 1, "code": 2771}]}, {"start": 63869, "length": 593, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 64554, "length": 6, "code": 2349, "category": 1, "messageText": {"messageText": "This expression is not callable.", "category": 1, "code": 2349, "next": [{"messageText": "Type 'Number' has no call signatures.", "category": 1, "code": 2757}]}}, {"start": 65036, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 65758, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 70274, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'resolvedAt' does not exist on type 'never'."}, {"start": 70669, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'resolvedAt' does not exist on type 'never'."}, {"start": 70718, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'resolvedAt' does not exist on type 'never'."}, {"start": 73765, "length": 24, "messageText": "Property 'authenticNUPhysicsEngine' does not exist on type 'typeof import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/server/nuphysics\")'.", "category": 1, "code": 2339}, {"start": 74375, "length": 24, "messageText": "Property 'authenticNUPhysicsEngine' does not exist on type 'typeof import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/server/nuphysics\")'.", "category": 1, "code": 2339}, {"start": 75095, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 75289, "length": 24, "messageText": "Property 'authenticNUPhysicsEngine' does not exist on type 'typeof import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/server/nuphysics\")'.", "category": 1, "code": 2339}, {"start": 75865, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 79721, "length": 12, "messageText": "Expected 2 arguments, but got 3.", "category": 1, "code": 2554}, {"start": 82005, "length": 10, "messageText": "'marketData' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 82083, "length": 10, "messageText": "'marketData' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 82990, "length": 10, "messageText": "'marketData' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 83029, "length": 10, "messageText": "'marketData' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 85159, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'createdAt' does not exist on type 'never'."}, {"start": 86317, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'totalEarnings' does not exist on type 'never[]'."}, {"start": 87634, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'totalEarnings' does not exist on type 'never[]'."}, {"start": 89972, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 90034, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 90108, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 90168, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 93149, "length": 23, "code": 2339, "category": 1, "messageText": "Property 'sendProximityInvitation' does not exist on type 'RealDeviceMessenger'."}, {"start": 94578, "length": 10, "messageText": "Expected 1 arguments, but got 2.", "category": 1, "code": 2554}, {"start": 95144, "length": 4, "messageText": "Expected 1 arguments, but got 2.", "category": 1, "code": 2554}, {"start": 95661, "length": 4, "messageText": "Expected 1 arguments, but got 2.", "category": 1, "code": 2554}, {"start": 96263, "length": 113, "code": 2345, "category": 1, "messageText": "Argument of type '{ userId: any; currentBalance: number; totalEarned: number; compoundRate: number; }' is not assignable to parameter of type 'string'."}, {"start": 96898, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'currentBalance' does not exist on type '{ balance: number; }'."}, {"start": 97061, "length": 13, "messageText": "Expected 2 arguments, but got 1.", "category": 1, "code": 2554, "relatedInformation": [{"file": "../../server/storage-interface.ts", "start": 31568, "length": 14, "messageText": "An argument for 'amount' was not provided.", "category": 3, "code": 6210}]}, {"start": 97120, "length": 2, "code": 2339, "category": 1, "messageText": "Property 'id' does not exist on type '{ balance: number; }'."}, {"start": 103524, "length": 10, "messageText": "Expected 2 arguments, but got 3.", "category": 1, "code": 2554}, {"start": 104974, "length": 18, "messageText": "Expected 2 arguments, but got 1.", "category": 1, "code": 2554, "relatedInformation": [{"file": "../../server/storage-interface.ts", "start": 20729, "length": 14, "messageText": "An argument for 'linkId' was not provided.", "category": 3, "code": 6210}]}, {"start": 106002, "length": 4, "messageText": "Expected 1 arguments, but got 2.", "category": 1, "code": 2554}, {"start": 108303, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 108323, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 108378, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 108493, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 108583, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 108641, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 108728, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 108804, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 109238, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 109297, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 109375, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 109465, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 109523, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 109610, "length": 11, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 110220, "length": 11, "messageText": "Expected 1 arguments, but got 2.", "category": 1, "code": 2554}, {"start": 111748, "length": 16, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 111773, "length": 16, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 111887, "length": 16, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 112056, "length": 16, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 112534, "length": 16, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 112595, "length": 16, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 112845, "length": 16, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 113375, "length": 8, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 113444, "length": 8, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 115561, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'totalDevices' does not exist on type '{ totalEnergy: number; participants: number; avgContribution: number; }'."}, {"start": 115643, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'totalDevices' does not exist on type '{ totalEnergy: number; participants: number; avgContribution: number; }'."}, {"start": 115735, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'totalUMatter' does not exist on type '{ totalEnergy: number; participants: number; avgContribution: number; }'."}, {"start": 115841, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'activeDevices' does not exist on type '{ totalEnergy: number; participants: number; avgContribution: number; }'."}, {"start": 115881, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'totalDevices' does not exist on type '{ totalEnergy: number; participants: number; avgContribution: number; }'."}, {"start": 131098, "length": 23, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ technical: string[]; general: string[]; }'."}, {"start": 131237, "length": 7, "messageText": "Parameter 'keyword' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 132732, "length": 4, "messageText": "Parameter 'word' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 136651, "length": 6, "messageText": "Spread types may only be created from object types.", "category": 1, "code": 2698}, {"start": 138274, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'dataPackageId' does not exist on type '{ id: string; advertiserId: string; dataType: string; targetAudience: unknown; budget: number; duration: number; message: string | null; status: string | null; offeredPrice: number | null; resolvedAt: Date | null; createdAt: Date | null; }'."}, {"start": 138970, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'totalUMatter' does not exist on type 'any[]'."}, {"start": 139042, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'batteryGenerated' does not exist on type 'never'."}, {"start": 139118, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'cpuGenerated' does not exist on type 'never'."}, {"start": 139191, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'batteryLevel' does not exist on type 'never'."}, {"start": 139261, "length": 12, "code": 2339, "category": 1, "messageText": "Property 'batteryLevel' does not exist on type 'never'."}, {"start": 139381, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'totalCount' does not exist on type 'any[]'."}, {"start": 140854, "length": 10, "code": 2339, "category": 1, "messageText": "Property 'totalCount' does not exist on type 'any[]'."}, {"start": 141056, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'fabricPulseRate' does not exist on type '{ fabricNodes: number; globalConsciousness: number; fabricComplexity: number; }'."}, {"start": 141128, "length": 19, "code": 2339, "category": 1, "messageText": "Property 'totalUbitsAllocated' does not exist on type '{ fabricNodes: number; globalConsciousness: number; fabricComplexity: number; }'."}, {"start": 141225, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'umatterDevices' does not exist on type '{ fabricNodes: number; globalConsciousness: number; fabricComplexity: number; }'."}, {"start": 141432, "length": 19, "code": 2339, "category": 1, "messageText": "Property 'entanglementDensity' does not exist on type '{ fabricNodes: number; globalConsciousness: number; fabricComplexity: number; }'."}, {"start": 141499, "length": 17, "code": 2339, "category": 1, "messageText": "Property 'networkEfficiency' does not exist on type '{ fabricNodes: number; globalConsciousness: number; fabricComplexity: number; }'."}, {"start": 141743, "length": 19, "code": 2339, "category": 1, "messageText": "Property 'totalUbitsAllocated' does not exist on type '{ fabricNodes: number; globalConsciousness: number; fabricComplexity: number; }'."}, {"start": 141917, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'umatterDevices' does not exist on type '{ fabricNodes: number; globalConsciousness: number; fabricComplexity: number; }'."}, {"start": 142025, "length": 19, "code": 2339, "category": 1, "messageText": "Property 'totalUbitsAllocated' does not exist on type '{ fabricNodes: number; globalConsciousness: number; fabricComplexity: number; }'."}, {"start": 142092, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'totalEnergy' does not exist on type '{ fabricNodes: number; globalConsciousness: number; fabricComplexity: number; }'."}, {"start": 142153, "length": 19, "code": 2339, "category": 1, "messageText": "Property 'totalUbitsAllocated' does not exist on type '{ fabricNodes: number; globalConsciousness: number; fabricComplexity: number; }'."}, {"start": 142230, "length": 19, "code": 2339, "category": 1, "messageText": "Property 'totalUbitsAllocated' does not exist on type '{ fabricNodes: number; globalConsciousness: number; fabricComplexity: number; }'."}, {"start": 145223, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<{}, any, any, ParsedQs, Record<string, any>>'."}, {"start": 145644, "length": 14, "messageText": "Expected 2 arguments, but got 3.", "category": 1, "code": 2554}, {"start": 160036, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 160072, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 160164, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 161441, "length": 2, "messageText": "Cannot find name 'fs'.", "category": 1, "code": 2304}, {"start": 161934, "length": 16, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 161995, "length": 1, "messageText": "Parameter 'p' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 162068, "length": 1, "messageText": "Parameter 'p' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 162163, "length": 1, "messageText": "Parameter 'p' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 162208, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 162213, "length": 1, "messageText": "Parameter 'p' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 164255, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 164291, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 164355, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 164449, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 164711, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 164849, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 166059, "length": 7, "messageText": "'dbError' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 166250, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 166438, "length": 17, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 166980, "length": 20, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 167011, "length": 20, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 167525, "length": 18, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 167554, "length": 18, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 185426, "length": 20, "messageText": "Cannot find module './routes/wallet.js' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 185501, "length": 21, "messageText": "Cannot find module './routes/trading.js' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 185581, "length": 25, "messageText": "Cannot find module './routes/marketplace.js' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 186165, "length": 40, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "The last overload gave the following error.", "category": 1, "code": 2770, "next": [{"messageText": "Argument of type '(req: Request, res: Response) => Promise<void>' is not assignable to parameter of type 'Application<Record<string, any>>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(req: Request, res: Response) => Promise<void>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.", "category": 1, "code": 2740}]}]}]}, "relatedInformation": [{"file": "../@types/express-serve-static-core/index.d.ts", "start": 5597, "length": 51, "messageText": "The last overload is declared here.", "category": 1, "code": 2771}]}, {"start": 186297, "length": 36, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 186441, "length": 20, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 186508, "length": 40, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "The last overload gave the following error.", "category": 1, "code": 2770, "next": [{"messageText": "Argument of type '(req: Request, res: Response) => Promise<void>' is not assignable to parameter of type 'Application<Record<string, any>>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(req: Request, res: Response) => Promise<void>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.", "category": 1, "code": 2740}]}]}]}, "relatedInformation": [{"file": "../@types/express-serve-static-core/index.d.ts", "start": 5597, "length": 51, "messageText": "The last overload is declared here.", "category": 1, "code": 2771}]}, {"start": 186626, "length": 63, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 186790, "length": 54, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 186940, "length": 40, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "The last overload gave the following error.", "category": 1, "code": 2770, "next": [{"messageText": "Argument of type '(req: Request, res: Response) => Promise<void>' is not assignable to parameter of type 'Application<Record<string, any>>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(req: Request, res: Response) => Promise<void>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.", "category": 1, "code": 2740}]}]}]}, "relatedInformation": [{"file": "../@types/express-serve-static-core/index.d.ts", "start": 5597, "length": 51, "messageText": "The last overload is declared here.", "category": 1, "code": 2771}]}, {"start": 187059, "length": 22, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 187183, "length": 13, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 187245, "length": 40, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "The last overload gave the following error.", "category": 1, "code": 2770, "next": [{"messageText": "Argument of type '(req: Request, res: Response) => Promise<void>' is not assignable to parameter of type 'Application<Record<string, any>>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(req: Request, res: Response) => Promise<void>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.", "category": 1, "code": 2740}]}]}]}, "relatedInformation": [{"file": "../@types/express-serve-static-core/index.d.ts", "start": 5597, "length": 51, "messageText": "The last overload is declared here.", "category": 1, "code": 2771}]}, {"start": 187366, "length": 24, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 187493, "length": 14, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 187562, "length": 40, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "The last overload gave the following error.", "category": 1, "code": 2770, "next": [{"messageText": "Argument of type '(req: Request, res: Response) => Promise<void>' is not assignable to parameter of type 'Application<Record<string, any>>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(req: Request, res: Response) => Promise<void>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.", "category": 1, "code": 2740}]}]}]}, "relatedInformation": [{"file": "../@types/express-serve-static-core/index.d.ts", "start": 5597, "length": 51, "messageText": "The last overload is declared here.", "category": 1, "code": 2771}]}, {"start": 187627, "length": 4, "messageText": "Property 'pair' does not exist on type 'ReadableStream<Uint8Array<ArrayBufferLike>> | null'.", "category": 1, "code": 2339}, {"start": 187633, "length": 4, "messageText": "Property 'type' does not exist on type 'ReadableStream<Uint8Array<ArrayBufferLike>> | null'.", "category": 1, "code": 2339}, {"start": 187639, "length": 6, "messageText": "Property 'amount' does not exist on type 'ReadableStream<Uint8Array<ArrayBufferLike>> | null'.", "category": 1, "code": 2339}, {"start": 187647, "length": 5, "messageText": "Property 'price' does not exist on type 'ReadableStream<Uint8Array<ArrayBufferLike>> | null'.", "category": 1, "code": 2339}, {"start": 187767, "length": 24, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 187888, "length": 6, "code": 2349, "category": 1, "messageText": {"messageText": "This expression is not callable.", "category": 1, "code": 2349, "next": [{"messageText": "Type 'Number' has no call signatures.", "category": 1, "code": 2757}]}}, {"start": 188048, "length": 40, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "The last overload gave the following error.", "category": 1, "code": 2770, "next": [{"messageText": "Argument of type '(req: Request, res: Response) => Promise<void>' is not assignable to parameter of type 'Application<Record<string, any>>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(req: Request, res: Response) => Promise<void>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.", "category": 1, "code": 2740}]}]}]}, "relatedInformation": [{"file": "../@types/express-serve-static-core/index.d.ts", "start": 5597, "length": 51, "messageText": "The last overload is declared here.", "category": 1, "code": 2771}]}, {"start": 188171, "length": 22, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 188299, "length": 13, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 188369, "length": 40, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "The last overload gave the following error.", "category": 1, "code": 2770, "next": [{"messageText": "Argument of type '(req: Request, res: Response) => Promise<void>' is not assignable to parameter of type 'Application<Record<string, any>>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(req: Request, res: Response) => Promise<void>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.", "category": 1, "code": 2740}]}]}]}, "relatedInformation": [{"file": "../@types/express-serve-static-core/index.d.ts", "start": 5597, "length": 51, "messageText": "The last overload is declared here.", "category": 1, "code": 2771}]}, {"start": 188502, "length": 32, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 188645, "length": 18, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 188719, "length": 40, "code": 2769, "category": 1, "messageText": {"messageText": "No overload matches this call.", "category": 1, "code": 2769, "next": [{"messageText": "The last overload gave the following error.", "category": 1, "code": 2770, "next": [{"messageText": "Argument of type '(req: Request, res: Response) => Promise<void>' is not assignable to parameter of type 'Application<Record<string, any>>'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '(req: Request, res: Response) => Promise<void>' is missing the following properties from type 'Application<Record<string, any>>': init, defaultConfiguration, engine, set, and 63 more.", "category": 1, "code": 2740}]}]}]}, "relatedInformation": [{"file": "../@types/express-serve-static-core/index.d.ts", "start": 5597, "length": 51, "messageText": "The last overload is declared here.", "category": 1, "code": 2771}]}, {"start": 188784, "length": 6, "messageText": "Property 'itemId' does not exist on type 'ReadableStream<Uint8Array<ArrayBufferLike>> | null'.", "category": 1, "code": 2339}, {"start": 188792, "length": 5, "messageText": "Property 'price' does not exist on type 'ReadableStream<Uint8Array<ArrayBufferLike>> | null'.", "category": 1, "code": 2339}, {"start": 188910, "length": 27, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"start": 189043, "length": 6, "code": 2349, "category": 1, "messageText": {"messageText": "This expression is not callable.", "category": 1, "code": 2349, "next": [{"messageText": "Type 'Number' has no call signatures.", "category": 1, "code": 2757}]}}]], [1219, [{"start": 596, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'balance' does not exist on type 'number'."}, {"start": 675, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'balance' does not exist on type 'number'."}, {"start": 850, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'transactionCount' does not exist on type 'number'."}, {"start": 912, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'balance' does not exist on type 'number'."}, {"start": 1171, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'transactionCount' does not exist on type 'number'."}, {"start": 1257, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'balance' does not exist on type 'number'."}, {"start": 1448, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'transactionCount' does not exist on type 'number'."}, {"start": 1778, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'transactionCount' does not exist on type 'number'."}, {"start": 1873, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'balance' does not exist on type 'number'."}, {"start": 2067, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'transactionCount' does not exist on type 'number'."}, {"start": 2128, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'transactionCount' does not exist on type 'number'."}, {"start": 2487, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'balance' does not exist on type 'number'."}, {"start": 3002, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'balance' does not exist on type 'number'."}, {"start": 3229, "length": 16, "code": 2339, "category": 1, "messageText": "Property 'transactionCount' does not exist on type 'number'."}, {"start": 3938, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'balance' does not exist on type 'number'."}, {"start": 4488, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'balance' does not exist on type 'number'."}]], [1264, [{"start": 5452, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}]], [1265, [{"start": 3915, "length": 9, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 4119, "length": 9, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}]], [1266, [{"start": 3815, "length": 5, "messageText": "'error' is of type 'unknown'.", "category": 1, "code": 18046}, {"start": 6063, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'emit' does not exist on type 'RealMarketDataProvider'."}]], [1267, [{"start": 1831, "length": 27, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ factor: number; search: number; qaoa: number; hhl: number; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ factor: number; search: number; qaoa: number; hhl: number; }'.", "category": 1, "code": 7054}]}}, {"start": 6444, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 7219, "length": 5, "code": 2741, "category": 1, "messageText": "Property 'where' is missing in type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...' but required in type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...'.", "relatedInformation": [{"file": "../drizzle-orm/pg-core/query-builders/select.d.ts", "start": 17315, "length": 5, "messageText": "'where' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2719, "messageText": "Type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...' is not assignable to type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...'. Two different types with this name exist, but they are unrelated."}}, {"start": 7392, "length": 5, "code": 2741, "category": 1, "messageText": "Property 'orderBy' is missing in type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...' but required in type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...'.", "relatedInformation": [{"file": "../drizzle-orm/pg-core/query-builders/select.d.ts", "start": 19954, "length": 7, "messageText": "'orderBy' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2719, "messageText": "Type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...' is not assignable to type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...'. Two different types with this name exist, but they are unrelated."}}, {"start": 7506, "length": 5, "code": 2741, "category": 1, "messageText": "Property 'orderBy' is missing in type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...' but required in type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...'.", "relatedInformation": [{"file": "../drizzle-orm/pg-core/query-builders/select.d.ts", "start": 19954, "length": 7, "messageText": "'orderBy' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2719, "messageText": "Type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...' is not assignable to type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...'. Two different types with this name exist, but they are unrelated."}}, {"start": 7593, "length": 5, "code": 2741, "category": 1, "messageText": "Property 'orderBy' is missing in type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...' but required in type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...'.", "relatedInformation": [{"file": "../drizzle-orm/pg-core/query-builders/select.d.ts", "start": 19954, "length": 7, "messageText": "'orderBy' is declared here.", "category": 3, "code": 2728}], "canonicalHead": {"code": 2719, "messageText": "Type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...' is not assignable to type 'Omit<import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/query-builders/select\").PgSelectBase<\"marketplace_listings\", { listing: import(\"/Users/<USER>/Desktop/SPUNDERSV2 2/node_modules/drizzle-orm/pg-core/table\").PgTableWithColumns<{ name: \"marketplace_listings\"; schema: undefined; column...'. Two different types with this name exist, but they are unrelated."}}, {"start": 8454, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'."}, {"start": 9409, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 9513, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 10360, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'."}, {"start": 10459, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 10833, "length": 24, "code": 2339, "category": 1, "messageText": "Property 'UBIT_PER_BATTERY_PERCENT' does not exist on type 'typeof UbitEconomyCalculator'."}, {"start": 11539, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 12427, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 12744, "length": 29, "code": 2339, "category": 1, "messageText": "Property 'generateSecureTransactionHash' does not exist on type 'MarketplaceController'."}, {"start": 13053, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 13274, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 13410, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 13609, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'processTask' does not exist on type 'NQEProcessor'."}, {"start": 13757, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 14445, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 14933, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 15249, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'."}, {"start": 15337, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 15574, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 16310, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 16985, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 17504, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 18452, "length": 24, "code": 2339, "category": 1, "messageText": "Property 'UBIT_PER_BATTERY_PERCENT' does not exist on type 'typeof UbitEconomyCalculator'."}, {"start": 18531, "length": 28, "code": 2339, "category": 1, "messageText": "Property 'VIRTUAL_QUBITS_PER_100_UBITS' does not exist on type 'typeof UbitEconomyCalculator'."}, {"start": 18604, "length": 20, "code": 2339, "category": 1, "messageText": "Property 'NUVA_CONVERSION_RATE' does not exist on type 'typeof UbitEconomyCalculator'."}, {"start": 18663, "length": 24, "code": 2339, "category": 1, "messageText": "Property 'PREMIUM_EFFICIENCY_BOOST' does not exist on type 'typeof UbitEconomyCalculator'."}, {"start": 20695, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 21497, "length": 22, "code": 2339, "category": 1, "messageText": "Property 'verifyQuantumSignature' does not exist on type 'AuthenticNUPhysicsEngine'."}, {"start": 21956, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 22924, "length": 18, "messageText": "Cannot find name 'energyTransactions'.", "category": 1, "code": 2304}, {"start": 23446, "length": 7, "messageText": "Cannot find name 'storage'.", "category": 1, "code": 2304}, {"start": 23876, "length": 23, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | null' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 24784, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'user' does not exist on type 'Request<ParamsDictionary, any, any, ParsedQs, Record<string, any>>'."}, {"start": 24941, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 25188, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 25591, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 26295, "length": 15, "code": 2339, "category": 1, "messageText": "Property 'BASE_TASK_COSTS' does not exist on type 'typeof UbitEconomyCalculator'."}, {"start": 27597, "length": 14, "code": 7053, "category": 1, "messageText": {"messageText": "Element implicitly has an 'any' type because expression of type 'string' can't be used to index type '{ factor: number; search: number; qaoa: number; hhl: number; }'.", "category": 1, "code": 7053, "next": [{"messageText": "No index signature with a parameter of type 'string' was found on type '{ factor: number; search: number; qaoa: number; hhl: number; }'.", "category": 1, "code": 7054}]}}, {"start": 28494, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 28756, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 29492, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 30036, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 30215, "length": 20, "messageText": "Cannot find name 'UserMarketplaceStats'.", "category": 1, "code": 2304}, {"start": 30724, "length": 23, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | null' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 30829, "length": 22, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | null' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 30974, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}]], [1268, [{"start": 1810, "length": 32, "code": 7053, "category": 1, "messageText": "Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ '/api/ai': { requests: number; window: number; }; '/api/biometric': { requests: number; window: number; }; '/api/extension': { requests: number; window: number; }; }'."}]], [1271, [{"start": 1480, "length": 6, "code": 2740, "category": 1, "messageText": "Type '{ timestamp: number; cpuPowerWatts: number; memoryPowerWatts: number; diskPowerWatts: number; networkPowerWatts: number; totalPowerWatts: number; isRealMeasurement: boolean; }' is missing the following properties from type 'ServerPowerMeasurement': lastCPUUsage, lastMemoryUsage, lastNetworkStats, measurementInterval, and 12 more.", "canonicalHead": {"code": 2322, "messageText": "Type '{ timestamp: number; cpuPowerWatts: number; memoryPowerWatts: number; diskPowerWatts: number; networkPowerWatts: number; totalPowerWatts: number; isRealMeasurement: boolean; }' is not assignable to type 'ServerPowerMeasurement'."}}, {"start": 1769, "length": 17, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'CpuUsage | null' is not assignable to parameter of type 'CpuUsage | undefined'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'CpuUsage | undefined'.", "category": 1, "code": 2322}]}}]], [1272, [{"start": 2224, "length": 25, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | null' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 2264, "length": 24, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | null' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 3613, "length": 10, "code": 2322, "category": 1, "messageText": {"messageText": "Type 'number | null' is not assignable to type 'number'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'null' is not assignable to type 'number'.", "category": 1, "code": 2322}]}, "relatedInformation": [{"start": 358, "length": 10, "messageText": "The expected type comes from property 'reputation' which is declared here on type 'TaskBid'", "category": 3, "code": 6500}]}, {"start": 4490, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 4942, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 5078, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 5941, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 7105, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 7330, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 7521, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 9277, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 11032, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 11563, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 12674, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 12757, "length": 49, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'string | null' is not assignable to parameter of type 'string'.", "category": 1, "code": 2345, "next": [{"messageText": "Type 'null' is not assignable to type 'string'.", "category": 1, "code": 2322}]}}, {"start": 12860, "length": 50, "messageText": "Object is possibly 'null'.", "category": 1, "code": 2531}, {"start": 13226, "length": 18, "messageText": "'current.reputation' is possibly 'null'.", "category": 1, "code": 18047}, {"start": 13272, "length": 2, "messageText": "'db' is possibly 'null'.", "category": 1, "code": 18047}]]], "affectedFilesPendingEmit": [480, 487, 476, 484, 485, 488, 490, 491, 492, 563, 564, 565, 566, 436, 419, 567, 111, 568, 569, 574, 439, 401, 575, 576, 577, 578, 580, 468, 581, 472, 467, 582, 583, 584, 593, 420, 384, 594, 598, 471, 477, 599, 602, 418, 603, 604, 605, 573, 606, 607, 608, 402, 609, 617, 621, 622, 624, 626, 374, 383, 110, 885, 108, 922, 923, 925, 926, 928, 931, 452, 933, 935, 966, 968, 970, 446, 450, 972, 975, 976, 977, 979, 386, 981, 1003, 399, 1005, 458, 381, 1008, 1007, 1010, 438, 1011, 406, 451, 99, 101, 1015, 1014, 107, 610, 417, 382, 397, 464, 611, 613, 614, 1006, 100, 373, 390, 1016, 486, 597, 470, 396, 1018, 395, 1019, 400, 1020, 1021, 389, 391, 392, 1023, 1024, 1022, 572, 570, 1025, 586, 1026, 393, 1027, 433, 1028, 588, 1029, 600, 601, 453, 589, 571, 590, 1030, 596, 591, 469, 388, 585, 1031, 587, 1033, 482, 1032, 455, 1034, 88, 387, 1035, 1036, 394, 1017, 1037, 1038, 592, 434, 489, 1039, 579, 435, 1040, 1041, 1042, 1043, 454, 1044, 479, 1045, 1046, 595, 440, 416, 1048, 465, 98, 1047, 612, 483, 463, 403, 422, 462, 443, 1049, 421, 441, 442, 1050, 466, 460, 459, 375, 461, 448, 473, 478, 474, 423, 475, 456, 447, 444, 445, 1162, 1163, 1165, 1171, 1174, 1175, 1161, 1176, 1177, 1192, 1195, 1196, 1198, 1199, 1200, 1264, 1213, 1215, 1265, 1266, 1267, 1210, 1203, 1268, 1269, 1212, 1207, 1206, 1205, 1204, 1270, 1197, 1202, 1220, 1194, 1214, 1271, 1164, 1217, 1219, 1216, 1218, 1272, 1211, 1273, 1201, 1193, 1170, 1274, 1209, 1263, 1208, 1173, 1051, 372, 1262], "version": "5.8.3"}