{"version": 3, "sources": ["../src/relations.ts"], "sourcesContent": ["import { type AnyTable, getTableUniqueName, type InferModelFromColumns, Table } from '~/table.ts';\nimport { type AnyColumn, Column } from './column.ts';\nimport { entityKind, is } from './entity.ts';\nimport { PrimaryKeyBuilder } from './pg-core/primary-keys.ts';\nimport {\n\tand,\n\tasc,\n\tbetween,\n\tdesc,\n\teq,\n\texists,\n\tgt,\n\tgte,\n\tilike,\n\tinArray,\n\tisNotNull,\n\tisNull,\n\tlike,\n\tlt,\n\tlte,\n\tne,\n\tnot,\n\tnotBetween,\n\tnotExists,\n\tnotIlike,\n\tnotInArray,\n\tnotLike,\n\tor,\n} from './sql/expressions/index.ts';\nimport { type Placeholder, SQL, sql } from './sql/sql.ts';\nimport type { Assume, ColumnsWithTable, Equal, Simplify, ValueOrArray } from './utils.ts';\n\nexport abstract class Relation<TTableName extends string = string> {\n\tstatic readonly [entityKind]: string = 'Relation';\n\n\tdeclare readonly $brand: 'Relation';\n\treadonly referencedTableName: TTableName;\n\tfieldName!: string;\n\n\tconstructor(\n\t\treadonly sourceTable: Table,\n\t\treadonly referencedTable: AnyTable<{ name: TTableName }>,\n\t\treadonly relationName: string | undefined,\n\t) {\n\t\tthis.referencedTableName = referencedTable[Table.Symbol.Name] as TTableName;\n\t}\n\n\tabstract withFieldName(fieldName: string): Relation<TTableName>;\n}\n\nexport class Relations<\n\tTTableName extends string = string,\n\tTConfig extends Record<string, Relation> = Record<string, Relation>,\n> {\n\tstatic readonly [entityKind]: string = 'Relations';\n\n\tdeclare readonly $brand: 'Relations';\n\n\tconstructor(\n\t\treadonly table: AnyTable<{ name: TTableName }>,\n\t\treadonly config: (helpers: TableRelationsHelpers<TTableName>) => TConfig,\n\t) {}\n}\n\nexport class One<\n\tTTableName extends string = string,\n\tTIsNullable extends boolean = boolean,\n> extends Relation<TTableName> {\n\tstatic override readonly [entityKind]: string = 'One';\n\n\tdeclare protected $relationBrand: 'One';\n\n\tconstructor(\n\t\tsourceTable: Table,\n\t\treferencedTable: AnyTable<{ name: TTableName }>,\n\t\treadonly config:\n\t\t\t| RelationConfig<\n\t\t\t\tTTableName,\n\t\t\t\tstring,\n\t\t\t\tAnyColumn<{ tableName: TTableName }>[]\n\t\t\t>\n\t\t\t| undefined,\n\t\treadonly isNullable: TIsNullable,\n\t) {\n\t\tsuper(sourceTable, referencedTable, config?.relationName);\n\t}\n\n\twithFieldName(fieldName: string): One<TTableName> {\n\t\tconst relation = new One(\n\t\t\tthis.sourceTable,\n\t\t\tthis.referencedTable,\n\t\t\tthis.config,\n\t\t\tthis.isNullable,\n\t\t);\n\t\trelation.fieldName = fieldName;\n\t\treturn relation;\n\t}\n}\n\nexport class Many<TTableName extends string> extends Relation<TTableName> {\n\tstatic override readonly [entityKind]: string = 'Many';\n\n\tdeclare protected $relationBrand: 'Many';\n\n\tconstructor(\n\t\tsourceTable: Table,\n\t\treferencedTable: AnyTable<{ name: TTableName }>,\n\t\treadonly config: { relationName: string } | undefined,\n\t) {\n\t\tsuper(sourceTable, referencedTable, config?.relationName);\n\t}\n\n\twithFieldName(fieldName: string): Many<TTableName> {\n\t\tconst relation = new Many(\n\t\t\tthis.sourceTable,\n\t\t\tthis.referencedTable,\n\t\t\tthis.config,\n\t\t);\n\t\trelation.fieldName = fieldName;\n\t\treturn relation;\n\t}\n}\n\nexport type TableRelationsKeysOnly<\n\tTSchema extends Record<string, unknown>,\n\tTTableName extends string,\n\tK extends keyof TSchema,\n> = TSchema[K] extends Relations<TTableName> ? K : never;\n\nexport type ExtractTableRelationsFromSchema<\n\tTSchema extends Record<string, unknown>,\n\tTTableName extends string,\n> = ExtractObjectValues<\n\t{\n\t\t[\n\t\t\tK in keyof TSchema as TableRelationsKeysOnly<\n\t\t\t\tTSchema,\n\t\t\t\tTTableName,\n\t\t\t\tK\n\t\t\t>\n\t\t]: TSchema[K] extends Relations<TTableName, infer TConfig> ? TConfig : never;\n\t}\n>;\n\nexport type ExtractObjectValues<T> = T[keyof T];\n\nexport type ExtractRelationsFromTableExtraConfigSchema<\n\tTConfig extends unknown[],\n> = ExtractObjectValues<\n\t{\n\t\t[\n\t\t\tK in keyof TConfig as TConfig[K] extends Relations<any> ? K\n\t\t\t\t: never\n\t\t]: TConfig[K] extends Relations<infer TRelationConfig> ? TRelationConfig\n\t\t\t: never;\n\t}\n>;\n\nexport function getOperators() {\n\treturn {\n\t\tand,\n\t\tbetween,\n\t\teq,\n\t\texists,\n\t\tgt,\n\t\tgte,\n\t\tilike,\n\t\tinArray,\n\t\tisNull,\n\t\tisNotNull,\n\t\tlike,\n\t\tlt,\n\t\tlte,\n\t\tne,\n\t\tnot,\n\t\tnotBetween,\n\t\tnotExists,\n\t\tnotLike,\n\t\tnotIlike,\n\t\tnotInArray,\n\t\tor,\n\t\tsql,\n\t};\n}\n\nexport type Operators = ReturnType<typeof getOperators>;\n\nexport function getOrderByOperators() {\n\treturn {\n\t\tsql,\n\t\tasc,\n\t\tdesc,\n\t};\n}\n\nexport type OrderByOperators = ReturnType<typeof getOrderByOperators>;\n\nexport type FindTableByDBName<\n\tTSchema extends TablesRelationalConfig,\n\tTTableName extends string,\n> = ExtractObjectValues<\n\t{\n\t\t[\n\t\t\tK in keyof TSchema as TSchema[K]['dbName'] extends TTableName ? K\n\t\t\t\t: never\n\t\t]: TSchema[K];\n\t}\n>;\n\nexport type DBQueryConfig<\n\tTRelationType extends 'one' | 'many' = 'one' | 'many',\n\tTIsRoot extends boolean = boolean,\n\tTSchema extends TablesRelationalConfig = TablesRelationalConfig,\n\tTTableConfig extends TableRelationalConfig = TableRelationalConfig,\n> =\n\t& {\n\t\tcolumns?:\n\t\t\t| {\n\t\t\t\t[K in keyof TTableConfig['columns']]?: boolean;\n\t\t\t}\n\t\t\t| undefined;\n\t\twith?:\n\t\t\t| {\n\t\t\t\t[K in keyof TTableConfig['relations']]?:\n\t\t\t\t\t| true\n\t\t\t\t\t| DBQueryConfig<\n\t\t\t\t\t\tTTableConfig['relations'][K] extends One ? 'one' : 'many',\n\t\t\t\t\t\tfalse,\n\t\t\t\t\t\tTSchema,\n\t\t\t\t\t\tFindTableByDBName<\n\t\t\t\t\t\t\tTSchema,\n\t\t\t\t\t\t\tTTableConfig['relations'][K]['referencedTableName']\n\t\t\t\t\t\t>\n\t\t\t\t\t>\n\t\t\t\t\t| undefined;\n\t\t\t}\n\t\t\t| undefined;\n\t\textras?:\n\t\t\t| Record<string, SQL.Aliased>\n\t\t\t| ((\n\t\t\t\tfields: Simplify<\n\t\t\t\t\t[TTableConfig['columns']] extends [never] ? {}\n\t\t\t\t\t\t: TTableConfig['columns']\n\t\t\t\t>,\n\t\t\t\toperators: { sql: Operators['sql'] },\n\t\t\t) => Record<string, SQL.Aliased>)\n\t\t\t| undefined;\n\t}\n\t& (TRelationType extends 'many' ?\n\t\t\t& {\n\t\t\t\twhere?:\n\t\t\t\t\t| SQL\n\t\t\t\t\t| undefined\n\t\t\t\t\t| ((\n\t\t\t\t\t\tfields: Simplify<\n\t\t\t\t\t\t\t[TTableConfig['columns']] extends [never] ? {}\n\t\t\t\t\t\t\t\t: TTableConfig['columns']\n\t\t\t\t\t\t>,\n\t\t\t\t\t\toperators: Operators,\n\t\t\t\t\t) => SQL | undefined);\n\t\t\t\torderBy?:\n\t\t\t\t\t| ValueOrArray<AnyColumn | SQL>\n\t\t\t\t\t| ((\n\t\t\t\t\t\tfields: Simplify<\n\t\t\t\t\t\t\t[TTableConfig['columns']] extends [never] ? {}\n\t\t\t\t\t\t\t\t: TTableConfig['columns']\n\t\t\t\t\t\t>,\n\t\t\t\t\t\toperators: OrderByOperators,\n\t\t\t\t\t) => ValueOrArray<AnyColumn | SQL>)\n\t\t\t\t\t| undefined;\n\t\t\t\tlimit?: number | Placeholder | undefined;\n\t\t\t}\n\t\t\t& (TIsRoot extends true ? {\n\t\t\t\t\toffset?: number | Placeholder | undefined;\n\t\t\t\t}\n\t\t\t\t: {})\n\t\t: {});\n\nexport interface TableRelationalConfig {\n\ttsName: string;\n\tdbName: string;\n\tcolumns: Record<string, Column>;\n\trelations: Record<string, Relation>;\n\tprimaryKey: AnyColumn[];\n\tschema?: string;\n}\n\nexport type TablesRelationalConfig = Record<string, TableRelationalConfig>;\n\nexport interface RelationalSchemaConfig<\n\tTSchema extends TablesRelationalConfig,\n> {\n\tfullSchema: Record<string, unknown>;\n\tschema: TSchema;\n\ttableNamesMap: Record<string, string>;\n}\n\nexport type ExtractTablesWithRelations<\n\tTSchema extends Record<string, unknown>,\n> = {\n\t[\n\t\tK in keyof TSchema as TSchema[K] extends Table ? K\n\t\t\t: never\n\t]: TSchema[K] extends Table ? {\n\t\t\ttsName: K & string;\n\t\t\tdbName: TSchema[K]['_']['name'];\n\t\t\tcolumns: TSchema[K]['_']['columns'];\n\t\t\trelations: ExtractTableRelationsFromSchema<\n\t\t\t\tTSchema,\n\t\t\t\tTSchema[K]['_']['name']\n\t\t\t>;\n\t\t\tprimaryKey: AnyColumn[];\n\t\t}\n\t\t: never;\n};\n\nexport type ReturnTypeOrValue<T> = T extends (...args: any[]) => infer R ? R\n\t: T;\n\nexport type BuildRelationResult<\n\tTSchema extends TablesRelationalConfig,\n\tTInclude,\n\tTRelations extends Record<string, Relation>,\n> = {\n\t[\n\t\tK in\n\t\t\t& NonUndefinedKeysOnly<TInclude>\n\t\t\t& keyof TRelations\n\t]: TRelations[K] extends infer TRel extends Relation ? BuildQueryResult<\n\t\t\tTSchema,\n\t\t\tFindTableByDBName<TSchema, TRel['referencedTableName']>,\n\t\t\tAssume<TInclude[K], true | Record<string, unknown>>\n\t\t> extends infer TResult ? TRel extends One ?\n\t\t\t\t\t| TResult\n\t\t\t\t\t| (Equal<TRel['isNullable'], false> extends true ? null : never)\n\t\t\t: TResult[]\n\t\t: never\n\t\t: never;\n};\n\nexport type NonUndefinedKeysOnly<T> =\n\t& ExtractObjectValues<\n\t\t{\n\t\t\t[K in keyof T as T[K] extends undefined ? never : K]: K;\n\t\t}\n\t>\n\t& keyof T;\n\nexport type BuildQueryResult<\n\tTSchema extends TablesRelationalConfig,\n\tTTableConfig extends TableRelationalConfig,\n\tTFullSelection extends true | Record<string, unknown>,\n> = Equal<TFullSelection, true> extends true ? InferModelFromColumns<TTableConfig['columns']>\n\t: TFullSelection extends Record<string, unknown> ? Simplify<\n\t\t\t& (TFullSelection['columns'] extends Record<string, unknown> ? InferModelFromColumns<\n\t\t\t\t\t{\n\t\t\t\t\t\t[\n\t\t\t\t\t\t\tK in Equal<\n\t\t\t\t\t\t\t\tExclude<\n\t\t\t\t\t\t\t\t\tTFullSelection['columns'][\n\t\t\t\t\t\t\t\t\t\t& keyof TFullSelection['columns']\n\t\t\t\t\t\t\t\t\t\t& keyof TTableConfig['columns']\n\t\t\t\t\t\t\t\t\t],\n\t\t\t\t\t\t\t\t\tundefined\n\t\t\t\t\t\t\t\t>,\n\t\t\t\t\t\t\t\tfalse\n\t\t\t\t\t\t\t> extends true ? Exclude<\n\t\t\t\t\t\t\t\t\tkeyof TTableConfig['columns'],\n\t\t\t\t\t\t\t\t\tNonUndefinedKeysOnly<TFullSelection['columns']>\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t:\n\t\t\t\t\t\t\t\t\t& {\n\t\t\t\t\t\t\t\t\t\t[K in keyof TFullSelection['columns']]: Equal<\n\t\t\t\t\t\t\t\t\t\t\tTFullSelection['columns'][K],\n\t\t\t\t\t\t\t\t\t\t\ttrue\n\t\t\t\t\t\t\t\t\t\t> extends true ? K\n\t\t\t\t\t\t\t\t\t\t\t: never;\n\t\t\t\t\t\t\t\t\t}[keyof TFullSelection['columns']]\n\t\t\t\t\t\t\t\t\t& keyof TTableConfig['columns']\n\t\t\t\t\t\t]: TTableConfig['columns'][K];\n\t\t\t\t\t}\n\t\t\t\t>\n\t\t\t\t: InferModelFromColumns<TTableConfig['columns']>)\n\t\t\t& (TFullSelection['extras'] extends\n\t\t\t\t| Record<string, unknown>\n\t\t\t\t| ((...args: any[]) => Record<string, unknown>) ? {\n\t\t\t\t\t[\n\t\t\t\t\t\tK in NonUndefinedKeysOnly<\n\t\t\t\t\t\t\tReturnTypeOrValue<TFullSelection['extras']>\n\t\t\t\t\t\t>\n\t\t\t\t\t]: Assume<\n\t\t\t\t\t\tReturnTypeOrValue<TFullSelection['extras']>[K],\n\t\t\t\t\t\tSQL.Aliased\n\t\t\t\t\t>['_']['type'];\n\t\t\t\t}\n\t\t\t\t: {})\n\t\t\t& (TFullSelection['with'] extends Record<string, unknown> ? BuildRelationResult<\n\t\t\t\t\tTSchema,\n\t\t\t\t\tTFullSelection['with'],\n\t\t\t\t\tTTableConfig['relations']\n\t\t\t\t>\n\t\t\t\t: {})\n\t\t>\n\t: never;\n\nexport interface RelationConfig<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends AnyColumn<{ tableName: TTableName }>[],\n> {\n\trelationName?: string;\n\tfields: TColumns;\n\treferences: ColumnsWithTable<TTableName, TForeignTableName, TColumns>;\n}\n\nexport function extractTablesRelationalConfig<\n\tTTables extends TablesRelationalConfig,\n>(\n\tschema: Record<string, unknown>,\n\tconfigHelpers: (table: Table) => any,\n): { tables: TTables; tableNamesMap: Record<string, string> } {\n\tif (\n\t\tObject.keys(schema).length === 1\n\t\t&& 'default' in schema\n\t\t&& !is(schema['default'], Table)\n\t) {\n\t\tschema = schema['default'] as Record<string, unknown>;\n\t}\n\n\t// table DB name -> schema table key\n\tconst tableNamesMap: Record<string, string> = {};\n\t// Table relations found before their tables - need to buffer them until we know the schema table key\n\tconst relationsBuffer: Record<\n\t\tstring,\n\t\t{ relations: Record<string, Relation>; primaryKey?: AnyColumn[] }\n\t> = {};\n\tconst tablesConfig: TablesRelationalConfig = {};\n\tfor (const [key, value] of Object.entries(schema)) {\n\t\tif (is(value, Table)) {\n\t\t\tconst dbName = getTableUniqueName(value);\n\t\t\tconst bufferedRelations = relationsBuffer[dbName];\n\t\t\ttableNamesMap[dbName] = key;\n\t\t\ttablesConfig[key] = {\n\t\t\t\ttsName: key,\n\t\t\t\tdbName: value[Table.Symbol.Name],\n\t\t\t\tschema: value[Table.Symbol.Schema],\n\t\t\t\tcolumns: value[Table.Symbol.Columns],\n\t\t\t\trelations: bufferedRelations?.relations ?? {},\n\t\t\t\tprimaryKey: bufferedRelations?.primaryKey ?? [],\n\t\t\t};\n\n\t\t\t// Fill in primary keys\n\t\t\tfor (\n\t\t\t\tconst column of Object.values(\n\t\t\t\t\t(value as Table)[Table.Symbol.Columns],\n\t\t\t\t)\n\t\t\t) {\n\t\t\t\tif (column.primary) {\n\t\t\t\t\ttablesConfig[key]!.primaryKey.push(column);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst extraConfig = value[Table.Symbol.ExtraConfigBuilder]?.((value as Table)[Table.Symbol.ExtraConfigColumns]);\n\t\t\tif (extraConfig) {\n\t\t\t\tfor (const configEntry of Object.values(extraConfig)) {\n\t\t\t\t\tif (is(configEntry, PrimaryKeyBuilder)) {\n\t\t\t\t\t\ttablesConfig[key]!.primaryKey.push(...configEntry.columns);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (is(value, Relations)) {\n\t\t\tconst dbName = getTableUniqueName(value.table);\n\t\t\tconst tableName = tableNamesMap[dbName];\n\t\t\tconst relations: Record<string, Relation> = value.config(\n\t\t\t\tconfigHelpers(value.table),\n\t\t\t);\n\t\t\tlet primaryKey: AnyColumn[] | undefined;\n\n\t\t\tfor (const [relationName, relation] of Object.entries(relations)) {\n\t\t\t\tif (tableName) {\n\t\t\t\t\tconst tableConfig = tablesConfig[tableName]!;\n\t\t\t\t\ttableConfig.relations[relationName] = relation;\n\t\t\t\t\tif (primaryKey) {\n\t\t\t\t\t\ttableConfig.primaryKey.push(...primaryKey);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tif (!(dbName in relationsBuffer)) {\n\t\t\t\t\t\trelationsBuffer[dbName] = {\n\t\t\t\t\t\t\trelations: {},\n\t\t\t\t\t\t\tprimaryKey,\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\trelationsBuffer[dbName]!.relations[relationName] = relation;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn { tables: tablesConfig as TTables, tableNamesMap };\n}\n\nexport function relations<\n\tTTableName extends string,\n\tTRelations extends Record<string, Relation<any>>,\n>(\n\ttable: AnyTable<{ name: TTableName }>,\n\trelations: (helpers: TableRelationsHelpers<TTableName>) => TRelations,\n): Relations<TTableName, TRelations> {\n\treturn new Relations<TTableName, TRelations>(\n\t\ttable,\n\t\t(helpers: TableRelationsHelpers<TTableName>) =>\n\t\t\tObject.fromEntries(\n\t\t\t\tObject.entries(relations(helpers)).map(([key, value]) => [\n\t\t\t\t\tkey,\n\t\t\t\t\tvalue.withFieldName(key),\n\t\t\t\t]),\n\t\t\t) as TRelations,\n\t);\n}\n\nexport function createOne<TTableName extends string>(sourceTable: Table) {\n\treturn function one<\n\t\tTForeignTable extends Table,\n\t\tTColumns extends [\n\t\t\tAnyColumn<{ tableName: TTableName }>,\n\t\t\t...AnyColumn<{ tableName: TTableName }>[],\n\t\t],\n\t>(\n\t\ttable: TForeignTable,\n\t\tconfig?: RelationConfig<TTableName, TForeignTable['_']['name'], TColumns>,\n\t): One<\n\t\tTForeignTable['_']['name'],\n\t\tEqual<TColumns[number]['_']['notNull'], true>\n\t> {\n\t\treturn new One(\n\t\t\tsourceTable,\n\t\t\ttable,\n\t\t\tconfig,\n\t\t\t(config?.fields.reduce<boolean>((res, f) => res && f.notNull, true)\n\t\t\t\t?? false) as Equal<TColumns[number]['_']['notNull'], true>,\n\t\t);\n\t};\n}\n\nexport function createMany(sourceTable: Table) {\n\treturn function many<TForeignTable extends Table>(\n\t\treferencedTable: TForeignTable,\n\t\tconfig?: { relationName: string },\n\t): Many<TForeignTable['_']['name']> {\n\t\treturn new Many(sourceTable, referencedTable, config);\n\t};\n}\n\nexport interface NormalizedRelation {\n\tfields: AnyColumn[];\n\treferences: AnyColumn[];\n}\n\nexport function normalizeRelation(\n\tschema: TablesRelationalConfig,\n\ttableNamesMap: Record<string, string>,\n\trelation: Relation,\n): NormalizedRelation {\n\tif (is(relation, One) && relation.config) {\n\t\treturn {\n\t\t\tfields: relation.config.fields,\n\t\t\treferences: relation.config.references,\n\t\t};\n\t}\n\n\tconst referencedTableTsName = tableNamesMap[getTableUniqueName(relation.referencedTable)];\n\tif (!referencedTableTsName) {\n\t\tthrow new Error(\n\t\t\t`Table \"${relation.referencedTable[Table.Symbol.Name]}\" not found in schema`,\n\t\t);\n\t}\n\n\tconst referencedTableConfig = schema[referencedTableTsName];\n\tif (!referencedTableConfig) {\n\t\tthrow new Error(`Table \"${referencedTableTsName}\" not found in schema`);\n\t}\n\n\tconst sourceTable = relation.sourceTable;\n\tconst sourceTableTsName = tableNamesMap[getTableUniqueName(sourceTable)];\n\tif (!sourceTableTsName) {\n\t\tthrow new Error(\n\t\t\t`Table \"${sourceTable[Table.Symbol.Name]}\" not found in schema`,\n\t\t);\n\t}\n\n\tconst reverseRelations: Relation[] = [];\n\tfor (\n\t\tconst referencedTableRelation of Object.values(\n\t\t\treferencedTableConfig.relations,\n\t\t)\n\t) {\n\t\tif (\n\t\t\t(relation.relationName\n\t\t\t\t&& relation !== referencedTableRelation\n\t\t\t\t&& referencedTableRelation.relationName === relation.relationName)\n\t\t\t|| (!relation.relationName\n\t\t\t\t&& referencedTableRelation.referencedTable === relation.sourceTable)\n\t\t) {\n\t\t\treverseRelations.push(referencedTableRelation);\n\t\t}\n\t}\n\n\tif (reverseRelations.length > 1) {\n\t\tthrow relation.relationName\n\t\t\t? new Error(\n\t\t\t\t`There are multiple relations with name \"${relation.relationName}\" in table \"${referencedTableTsName}\"`,\n\t\t\t)\n\t\t\t: new Error(\n\t\t\t\t`There are multiple relations between \"${referencedTableTsName}\" and \"${\n\t\t\t\t\trelation.sourceTable[Table.Symbol.Name]\n\t\t\t\t}\". Please specify relation name`,\n\t\t\t);\n\t}\n\n\tif (\n\t\treverseRelations[0]\n\t\t&& is(reverseRelations[0], One)\n\t\t&& reverseRelations[0].config\n\t) {\n\t\treturn {\n\t\t\tfields: reverseRelations[0].config.references,\n\t\t\treferences: reverseRelations[0].config.fields,\n\t\t};\n\t}\n\n\tthrow new Error(\n\t\t`There is not enough information to infer relation \"${sourceTableTsName}.${relation.fieldName}\"`,\n\t);\n}\n\nexport function createTableRelationsHelpers<TTableName extends string>(\n\tsourceTable: AnyTable<{ name: TTableName }>,\n) {\n\treturn {\n\t\tone: createOne<TTableName>(sourceTable),\n\t\tmany: createMany(sourceTable),\n\t};\n}\n\nexport type TableRelationsHelpers<TTableName extends string> = ReturnType<\n\ttypeof createTableRelationsHelpers<TTableName>\n>;\n\nexport interface BuildRelationalQueryResult<\n\tTTable extends Table = Table,\n\tTColumn extends Column = Column,\n> {\n\ttableTsKey: string;\n\tselection: {\n\t\tdbKey: string;\n\t\ttsKey: string;\n\t\tfield: TColumn | SQL | SQL.Aliased;\n\t\trelationTableTsKey: string | undefined;\n\t\tisJson: boolean;\n\t\tisExtra?: boolean;\n\t\tselection: BuildRelationalQueryResult<TTable>['selection'];\n\t}[];\n\tsql: TTable | SQL;\n}\n\nexport function mapRelationalRow(\n\ttablesConfig: TablesRelationalConfig,\n\ttableConfig: TableRelationalConfig,\n\trow: unknown[],\n\tbuildQueryResultSelection: BuildRelationalQueryResult['selection'],\n\tmapColumnValue: (value: unknown) => unknown = (value) => value,\n): Record<string, unknown> {\n\tconst result: Record<string, unknown> = {};\n\n\tfor (\n\t\tconst [\n\t\t\tselectionItemIndex,\n\t\t\tselectionItem,\n\t\t] of buildQueryResultSelection.entries()\n\t) {\n\t\tif (selectionItem.isJson) {\n\t\t\tconst relation = tableConfig.relations[selectionItem.tsKey]!;\n\t\t\tconst rawSubRows = row[selectionItemIndex] as\n\t\t\t\t| unknown[]\n\t\t\t\t| null\n\t\t\t\t| [null]\n\t\t\t\t| string;\n\t\t\tconst subRows = typeof rawSubRows === 'string'\n\t\t\t\t? (JSON.parse(rawSubRows) as unknown[])\n\t\t\t\t: rawSubRows;\n\t\t\tresult[selectionItem.tsKey] = is(relation, One)\n\t\t\t\t? subRows\n\t\t\t\t\t&& mapRelationalRow(\n\t\t\t\t\t\ttablesConfig,\n\t\t\t\t\t\ttablesConfig[selectionItem.relationTableTsKey!]!,\n\t\t\t\t\t\tsubRows,\n\t\t\t\t\t\tselectionItem.selection,\n\t\t\t\t\t\tmapColumnValue,\n\t\t\t\t\t)\n\t\t\t\t: (subRows as unknown[][]).map((subRow) =>\n\t\t\t\t\tmapRelationalRow(\n\t\t\t\t\t\ttablesConfig,\n\t\t\t\t\t\ttablesConfig[selectionItem.relationTableTsKey!]!,\n\t\t\t\t\t\tsubRow,\n\t\t\t\t\t\tselectionItem.selection,\n\t\t\t\t\t\tmapColumnValue,\n\t\t\t\t\t)\n\t\t\t\t);\n\t\t} else {\n\t\t\tconst value = mapColumnValue(row[selectionItemIndex]);\n\t\t\tconst field = selectionItem.field!;\n\t\t\tlet decoder;\n\t\t\tif (is(field, Column)) {\n\t\t\t\tdecoder = field;\n\t\t\t} else if (is(field, SQL)) {\n\t\t\t\tdecoder = field.decoder;\n\t\t\t} else {\n\t\t\t\tdecoder = field.sql.decoder;\n\t\t\t}\n\t\t\tresult[selectionItem.tsKey] = value === null ? null : decoder.mapFromDriverValue(value);\n\t\t}\n\t}\n\n\treturn result;\n}\n"], "mappings": "AAAA,SAAwB,oBAAgD,aAAa;AACrF,SAAyB,cAAc;AACvC,SAAS,YAAY,UAAU;AAC/B,SAAS,yBAAyB;AAClC;AAAA,EACC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACM;AACP,SAA2B,KAAK,WAAW;AAGpC,MAAe,SAA6C;AAAA,EAOlE,YACU,aACA,iBACA,cACR;AAHQ;AACA;AACA;AAET,SAAK,sBAAsB,gBAAgB,MAAM,OAAO,IAAI;AAAA,EAC7D;AAAA,EAZA,QAAiB,UAAU,IAAY;AAAA,EAG9B;AAAA,EACT;AAWD;AAEO,MAAM,UAGX;AAAA,EAKD,YACU,OACA,QACR;AAFQ;AACA;AAAA,EACP;AAAA,EAPH,QAAiB,UAAU,IAAY;AAQxC;AAEO,MAAM,YAGH,SAAqB;AAAA,EAK9B,YACC,aACA,iBACS,QAOA,YACR;AACD,UAAM,aAAa,iBAAiB,QAAQ,YAAY;AAT/C;AAOA;AAAA,EAGV;AAAA,EAjBA,QAA0B,UAAU,IAAY;AAAA,EAmBhD,cAAc,WAAoC;AACjD,UAAM,WAAW,IAAI;AAAA,MACpB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACN;AACA,aAAS,YAAY;AACrB,WAAO;AAAA,EACR;AACD;AAEO,MAAM,aAAwC,SAAqB;AAAA,EAKzE,YACC,aACA,iBACS,QACR;AACD,UAAM,aAAa,iBAAiB,QAAQ,YAAY;AAF/C;AAAA,EAGV;AAAA,EAVA,QAA0B,UAAU,IAAY;AAAA,EAYhD,cAAc,WAAqC;AAClD,UAAM,WAAW,IAAI;AAAA,MACpB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACN;AACA,aAAS,YAAY;AACrB,WAAO;AAAA,EACR;AACD;AAqCO,SAAS,eAAe;AAC9B,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AAIO,SAAS,sBAAsB;AACrC,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AA8NO,SAAS,8BAGf,QACA,eAC6D;AAC7D,MACC,OAAO,KAAK,MAAM,EAAE,WAAW,KAC5B,aAAa,UACb,CAAC,GAAG,OAAO,SAAS,GAAG,KAAK,GAC9B;AACD,aAAS,OAAO,SAAS;AAAA,EAC1B;AAGA,QAAM,gBAAwC,CAAC;AAE/C,QAAM,kBAGF,CAAC;AACL,QAAM,eAAuC,CAAC;AAC9C,aAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AAClD,QAAI,GAAG,OAAO,KAAK,GAAG;AACrB,YAAM,SAAS,mBAAmB,KAAK;AACvC,YAAM,oBAAoB,gBAAgB,MAAM;AAChD,oBAAc,MAAM,IAAI;AACxB,mBAAa,GAAG,IAAI;AAAA,QACnB,QAAQ;AAAA,QACR,QAAQ,MAAM,MAAM,OAAO,IAAI;AAAA,QAC/B,QAAQ,MAAM,MAAM,OAAO,MAAM;AAAA,QACjC,SAAS,MAAM,MAAM,OAAO,OAAO;AAAA,QACnC,WAAW,mBAAmB,aAAa,CAAC;AAAA,QAC5C,YAAY,mBAAmB,cAAc,CAAC;AAAA,MAC/C;AAGA,iBACO,UAAU,OAAO;AAAA,QACrB,MAAgB,MAAM,OAAO,OAAO;AAAA,MACtC,GACC;AACD,YAAI,OAAO,SAAS;AACnB,uBAAa,GAAG,EAAG,WAAW,KAAK,MAAM;AAAA,QAC1C;AAAA,MACD;AAEA,YAAM,cAAc,MAAM,MAAM,OAAO,kBAAkB,IAAK,MAAgB,MAAM,OAAO,kBAAkB,CAAC;AAC9G,UAAI,aAAa;AAChB,mBAAW,eAAe,OAAO,OAAO,WAAW,GAAG;AACrD,cAAI,GAAG,aAAa,iBAAiB,GAAG;AACvC,yBAAa,GAAG,EAAG,WAAW,KAAK,GAAG,YAAY,OAAO;AAAA,UAC1D;AAAA,QACD;AAAA,MACD;AAAA,IACD,WAAW,GAAG,OAAO,SAAS,GAAG;AAChC,YAAM,SAAS,mBAAmB,MAAM,KAAK;AAC7C,YAAM,YAAY,cAAc,MAAM;AACtC,YAAMA,aAAsC,MAAM;AAAA,QACjD,cAAc,MAAM,KAAK;AAAA,MAC1B;AACA,UAAI;AAEJ,iBAAW,CAAC,cAAc,QAAQ,KAAK,OAAO,QAAQA,UAAS,GAAG;AACjE,YAAI,WAAW;AACd,gBAAM,cAAc,aAAa,SAAS;AAC1C,sBAAY,UAAU,YAAY,IAAI;AACtC,cAAI,YAAY;AACf,wBAAY,WAAW,KAAK,GAAG,UAAU;AAAA,UAC1C;AAAA,QACD,OAAO;AACN,cAAI,EAAE,UAAU,kBAAkB;AACjC,4BAAgB,MAAM,IAAI;AAAA,cACzB,WAAW,CAAC;AAAA,cACZ;AAAA,YACD;AAAA,UACD;AACA,0BAAgB,MAAM,EAAG,UAAU,YAAY,IAAI;AAAA,QACpD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAEA,SAAO,EAAE,QAAQ,cAAyB,cAAc;AACzD;AAEO,SAAS,UAIf,OACAA,YACoC;AACpC,SAAO,IAAI;AAAA,IACV;AAAA,IACA,CAAC,YACA,OAAO;AAAA,MACN,OAAO,QAAQA,WAAU,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AAAA,QACxD;AAAA,QACA,MAAM,cAAc,GAAG;AAAA,MACxB,CAAC;AAAA,IACF;AAAA,EACF;AACD;AAEO,SAAS,UAAqC,aAAoB;AACxE,SAAO,SAAS,IAOf,OACA,QAIC;AACD,WAAO,IAAI;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACC,QAAQ,OAAO,OAAgB,CAAC,KAAK,MAAM,OAAO,EAAE,SAAS,IAAI,KAC9D;AAAA,IACL;AAAA,EACD;AACD;AAEO,SAAS,WAAW,aAAoB;AAC9C,SAAO,SAAS,KACf,iBACA,QACmC;AACnC,WAAO,IAAI,KAAK,aAAa,iBAAiB,MAAM;AAAA,EACrD;AACD;AAOO,SAAS,kBACf,QACA,eACA,UACqB;AACrB,MAAI,GAAG,UAAU,GAAG,KAAK,SAAS,QAAQ;AACzC,WAAO;AAAA,MACN,QAAQ,SAAS,OAAO;AAAA,MACxB,YAAY,SAAS,OAAO;AAAA,IAC7B;AAAA,EACD;AAEA,QAAM,wBAAwB,cAAc,mBAAmB,SAAS,eAAe,CAAC;AACxF,MAAI,CAAC,uBAAuB;AAC3B,UAAM,IAAI;AAAA,MACT,UAAU,SAAS,gBAAgB,MAAM,OAAO,IAAI,CAAC;AAAA,IACtD;AAAA,EACD;AAEA,QAAM,wBAAwB,OAAO,qBAAqB;AAC1D,MAAI,CAAC,uBAAuB;AAC3B,UAAM,IAAI,MAAM,UAAU,qBAAqB,uBAAuB;AAAA,EACvE;AAEA,QAAM,cAAc,SAAS;AAC7B,QAAM,oBAAoB,cAAc,mBAAmB,WAAW,CAAC;AACvE,MAAI,CAAC,mBAAmB;AACvB,UAAM,IAAI;AAAA,MACT,UAAU,YAAY,MAAM,OAAO,IAAI,CAAC;AAAA,IACzC;AAAA,EACD;AAEA,QAAM,mBAA+B,CAAC;AACtC,aACO,2BAA2B,OAAO;AAAA,IACvC,sBAAsB;AAAA,EACvB,GACC;AACD,QACE,SAAS,gBACN,aAAa,2BACb,wBAAwB,iBAAiB,SAAS,gBAClD,CAAC,SAAS,gBACV,wBAAwB,oBAAoB,SAAS,aACxD;AACD,uBAAiB,KAAK,uBAAuB;AAAA,IAC9C;AAAA,EACD;AAEA,MAAI,iBAAiB,SAAS,GAAG;AAChC,UAAM,SAAS,eACZ,IAAI;AAAA,MACL,2CAA2C,SAAS,YAAY,eAAe,qBAAqB;AAAA,IACrG,IACE,IAAI;AAAA,MACL,yCAAyC,qBAAqB,UAC7D,SAAS,YAAY,MAAM,OAAO,IAAI,CACvC;AAAA,IACD;AAAA,EACF;AAEA,MACC,iBAAiB,CAAC,KACf,GAAG,iBAAiB,CAAC,GAAG,GAAG,KAC3B,iBAAiB,CAAC,EAAE,QACtB;AACD,WAAO;AAAA,MACN,QAAQ,iBAAiB,CAAC,EAAE,OAAO;AAAA,MACnC,YAAY,iBAAiB,CAAC,EAAE,OAAO;AAAA,IACxC;AAAA,EACD;AAEA,QAAM,IAAI;AAAA,IACT,sDAAsD,iBAAiB,IAAI,SAAS,SAAS;AAAA,EAC9F;AACD;AAEO,SAAS,4BACf,aACC;AACD,SAAO;AAAA,IACN,KAAK,UAAsB,WAAW;AAAA,IACtC,MAAM,WAAW,WAAW;AAAA,EAC7B;AACD;AAuBO,SAAS,iBACf,cACA,aACA,KACA,2BACA,iBAA8C,CAAC,UAAU,OAC/B;AAC1B,QAAM,SAAkC,CAAC;AAEzC,aACO;AAAA,IACL;AAAA,IACA;AAAA,EACD,KAAK,0BAA0B,QAAQ,GACtC;AACD,QAAI,cAAc,QAAQ;AACzB,YAAM,WAAW,YAAY,UAAU,cAAc,KAAK;AAC1D,YAAM,aAAa,IAAI,kBAAkB;AAKzC,YAAM,UAAU,OAAO,eAAe,WAClC,KAAK,MAAM,UAAU,IACtB;AACH,aAAO,cAAc,KAAK,IAAI,GAAG,UAAU,GAAG,IAC3C,WACE;AAAA,QACF;AAAA,QACA,aAAa,cAAc,kBAAmB;AAAA,QAC9C;AAAA,QACA,cAAc;AAAA,QACd;AAAA,MACD,IACE,QAAwB;AAAA,QAAI,CAAC,WAC/B;AAAA,UACC;AAAA,UACA,aAAa,cAAc,kBAAmB;AAAA,UAC9C;AAAA,UACA,cAAc;AAAA,UACd;AAAA,QACD;AAAA,MACD;AAAA,IACF,OAAO;AACN,YAAM,QAAQ,eAAe,IAAI,kBAAkB,CAAC;AACpD,YAAM,QAAQ,cAAc;AAC5B,UAAI;AACJ,UAAI,GAAG,OAAO,MAAM,GAAG;AACtB,kBAAU;AAAA,MACX,WAAW,GAAG,OAAO,GAAG,GAAG;AAC1B,kBAAU,MAAM;AAAA,MACjB,OAAO;AACN,kBAAU,MAAM,IAAI;AAAA,MACrB;AACA,aAAO,cAAc,KAAK,IAAI,UAAU,OAAO,OAAO,QAAQ,mBAAmB,KAAK;AAAA,IACvF;AAAA,EACD;AAEA,SAAO;AACR;", "names": ["relations"]}