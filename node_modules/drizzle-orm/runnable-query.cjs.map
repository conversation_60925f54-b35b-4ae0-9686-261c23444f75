{"version": 3, "sources": ["../src/runnable-query.ts"], "sourcesContent": ["import type { Dialect } from './column-builder.ts';\nimport type { PreparedQuery } from './session.ts';\n\nexport interface RunnableQuery<T, TDialect extends Dialect> {\n\treadonly _: {\n\t\treadonly dialect: TDialect;\n\t\treadonly result: T;\n\t};\n\n\t/** @internal */\n\t_prepare(): PreparedQuery;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}