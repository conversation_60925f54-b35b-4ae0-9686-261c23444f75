{"version": 3, "sources": ["../src/migrator.ts"], "sourcesContent": ["import crypto from 'node:crypto';\nimport fs from 'node:fs';\n\nexport interface KitConfig {\n\tout: string;\n\tschema: string;\n}\n\nexport interface MigrationConfig {\n\tmigrationsFolder: string;\n\tmigrationsTable?: string;\n\tmigrationsSchema?: string;\n}\n\nexport interface MigrationMeta {\n\tsql: string[];\n\tfolderMillis: number;\n\thash: string;\n\tbps: boolean;\n}\n\nexport function readMigrationFiles(config: MigrationConfig): MigrationMeta[] {\n\tconst migrationFolderTo = config.migrationsFolder;\n\n\tconst migrationQueries: MigrationMeta[] = [];\n\n\tconst journalPath = `${migrationFolderTo}/meta/_journal.json`;\n\tif (!fs.existsSync(journalPath)) {\n\t\tthrow new Error(`Can't find meta/_journal.json file`);\n\t}\n\n\tconst journalAsString = fs.readFileSync(`${migrationFolderTo}/meta/_journal.json`).toString();\n\n\tconst journal = JSON.parse(journalAsString) as {\n\t\tentries: { idx: number; when: number; tag: string; breakpoints: boolean }[];\n\t};\n\n\tfor (const journalEntry of journal.entries) {\n\t\tconst migrationPath = `${migrationFolderTo}/${journalEntry.tag}.sql`;\n\n\t\ttry {\n\t\t\tconst query = fs.readFileSync(`${migrationFolderTo}/${journalEntry.tag}.sql`).toString();\n\n\t\t\tconst result = query.split('--> statement-breakpoint').map((it) => {\n\t\t\t\treturn it;\n\t\t\t});\n\n\t\t\tmigrationQueries.push({\n\t\t\t\tsql: result,\n\t\t\t\tbps: journalEntry.breakpoints,\n\t\t\t\tfolderMillis: journalEntry.when,\n\t\t\t\thash: crypto.createHash('sha256').update(query).digest('hex'),\n\t\t\t});\n\t\t} catch {\n\t\t\tthrow new Error(`No file ${migrationPath} found in ${migrationFolderTo} folder`);\n\t\t}\n\t}\n\n\treturn migrationQueries;\n}\n"], "mappings": "AAAA,OAAO,YAAY;AACnB,OAAO,QAAQ;AAoBR,SAAS,mBAAmB,QAA0C;AAC5E,QAAM,oBAAoB,OAAO;AAEjC,QAAM,mBAAoC,CAAC;AAE3C,QAAM,cAAc,GAAG,iBAAiB;AACxC,MAAI,CAAC,GAAG,WAAW,WAAW,GAAG;AAChC,UAAM,IAAI,MAAM,oCAAoC;AAAA,EACrD;AAEA,QAAM,kBAAkB,GAAG,aAAa,GAAG,iBAAiB,qBAAqB,EAAE,SAAS;AAE5F,QAAM,UAAU,KAAK,MAAM,eAAe;AAI1C,aAAW,gBAAgB,QAAQ,SAAS;AAC3C,UAAM,gBAAgB,GAAG,iBAAiB,IAAI,aAAa,GAAG;AAE9D,QAAI;AACH,YAAM,QAAQ,GAAG,aAAa,GAAG,iBAAiB,IAAI,aAAa,GAAG,MAAM,EAAE,SAAS;AAEvF,YAAM,SAAS,MAAM,MAAM,0BAA0B,EAAE,IAAI,CAAC,OAAO;AAClE,eAAO;AAAA,MACR,CAAC;AAED,uBAAiB,KAAK;AAAA,QACrB,KAAK;AAAA,QACL,KAAK,aAAa;AAAA,QAClB,cAAc,aAAa;AAAA,QAC3B,MAAM,OAAO,WAAW,QAAQ,EAAE,OAAO,KAAK,EAAE,OAAO,KAAK;AAAA,MAC7D,CAAC;AAAA,IACF,QAAQ;AACP,YAAM,IAAI,MAAM,WAAW,aAAa,aAAa,iBAAiB,SAAS;AAAA,IAChF;AAAA,EACD;AAEA,SAAO;AACR;", "names": []}