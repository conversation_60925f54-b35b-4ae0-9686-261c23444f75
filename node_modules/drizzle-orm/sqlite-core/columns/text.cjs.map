{"version": 3, "sources": ["../../../src/sqlite-core/columns/text.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySQLiteTable } from '~/sqlite-core/table.ts';\nimport { type Equal, getColumnNameAndConfig, type Writable } from '~/utils.ts';\nimport { SQLiteColumn, SQLiteColumnBuilder } from './common.ts';\n\nexport type SQLiteTextBuilderInitial<\n\tTName extends string,\n\tTEnum extends [string, ...string[]],\n\tTLength extends number | undefined,\n> = SQLiteTextBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'SQLiteText';\n\tdata: TEnum[number];\n\tdriverParam: string;\n\tenumValues: TEnum;\n\tlength: TLength;\n}>;\n\nexport class SQLiteTextBuilder<\n\tT extends ColumnBuilderBaseConfig<'string', 'SQLiteText'> & { length?: number | undefined },\n> extends SQLiteColumnBuilder<\n\tT,\n\t{ length: T['length']; enumValues: T['enumValues'] },\n\t{ length: T['length'] }\n> {\n\tstatic override readonly [entityKind]: string = 'SQLiteTextBuilder';\n\n\tconstructor(name: T['name'], config: SQLiteTextConfig<'text', T['enumValues'], T['length']>) {\n\t\tsuper(name, 'string', 'SQLiteText');\n\t\tthis.config.enumValues = config.enum;\n\t\tthis.config.length = config.length;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteText<MakeColumnConfig<T, TTableName> & { length: T['length'] }> {\n\t\treturn new SQLiteText<MakeColumnConfig<T, TTableName> & { length: T['length'] }>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteText<T extends ColumnBaseConfig<'string', 'SQLiteText'> & { length?: number | undefined }>\n\textends SQLiteColumn<T, { length: T['length']; enumValues: T['enumValues'] }>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteText';\n\n\toverride readonly enumValues = this.config.enumValues;\n\n\treadonly length: T['length'] = this.config.length;\n\n\tconstructor(\n\t\ttable: AnySQLiteTable<{ name: T['tableName'] }>,\n\t\tconfig: SQLiteTextBuilder<T>['config'],\n\t) {\n\t\tsuper(table, config);\n\t}\n\n\tgetSQLType(): string {\n\t\treturn `text${this.config.length ? `(${this.config.length})` : ''}`;\n\t}\n}\n\nexport type SQLiteTextJsonBuilderInitial<TName extends string> = SQLiteTextJsonBuilder<{\n\tname: TName;\n\tdataType: 'json';\n\tcolumnType: 'SQLiteTextJson';\n\tdata: unknown;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SQLiteTextJsonBuilder<T extends ColumnBuilderBaseConfig<'json', 'SQLiteTextJson'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteTextJsonBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'json', 'SQLiteTextJson');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteTextJson<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteTextJson<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SQLiteTextJson<T extends ColumnBaseConfig<'json', 'SQLiteTextJson'>>\n\textends SQLiteColumn<T, { length: number | undefined; enumValues: T['enumValues'] }>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteTextJson';\n\n\tgetSQLType(): string {\n\t\treturn 'text';\n\t}\n\n\toverride mapFromDriverValue(value: string): T['data'] {\n\t\treturn JSON.parse(value);\n\t}\n\n\toverride mapToDriverValue(value: T['data']): string {\n\t\treturn JSON.stringify(value);\n\t}\n}\n\nexport type SQLiteTextConfig<\n\tTMode extends 'text' | 'json' = 'text' | 'json',\n\tTEnum extends readonly string[] | string[] | undefined = readonly string[] | string[] | undefined,\n\tTLength extends number | undefined = number | undefined,\n> = TMode extends 'text' ? {\n\t\tmode?: TMode;\n\t\tlength?: TLength;\n\t\tenum?: TEnum;\n\t}\n\t: {\n\t\tmode?: TMode;\n\t};\n\nexport function text(): SQLiteTextBuilderInitial<'', [string, ...string[]], undefined>;\nexport function text<\n\tU extends string,\n\tT extends Readonly<[U, ...U[]]>,\n\tL extends number | undefined,\n\tTMode extends 'text' | 'json' = 'text' | 'json',\n>(\n\tconfig?: SQLiteTextConfig<TMode, T | Writable<T>, L>,\n): Equal<TMode, 'json'> extends true ? SQLiteTextJsonBuilderInitial<''>\n\t: SQLiteTextBuilderInitial<'', Writable<T>, L>;\nexport function text<\n\tTName extends string,\n\tU extends string,\n\tT extends Readonly<[U, ...U[]]>,\n\tL extends number | undefined,\n\tTMode extends 'text' | 'json' = 'text' | 'json',\n>(\n\tname: TName,\n\tconfig?: SQLiteTextConfig<TMode, T | Writable<T>, L>,\n): Equal<TMode, 'json'> extends true ? SQLiteTextJsonBuilderInitial<TName>\n\t: SQLiteTextBuilderInitial<TName, Writable<T>, L>;\nexport function text(a?: string | SQLiteTextConfig, b: SQLiteTextConfig = {}): any {\n\tconst { name, config } = getColumnNameAndConfig<SQLiteTextConfig>(a, b);\n\tif (config.mode === 'json') {\n\t\treturn new SQLiteTextJsonBuilder(name);\n\t}\n\treturn new SQLiteTextBuilder(name, config as any);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAkE;AAClE,oBAAkD;AAgB3C,MAAM,0BAEH,kCAIR;AAAA,EACD,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB,QAAgE;AAC5F,UAAM,MAAM,UAAU,YAAY;AAClC,SAAK,OAAO,aAAa,OAAO;AAChC,SAAK,OAAO,SAAS,OAAO;AAAA,EAC7B;AAAA;AAAA,EAGS,MACR,OACwE;AACxE,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,mBACJ,2BACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAE9B,aAAa,KAAK,OAAO;AAAA,EAElC,SAAsB,KAAK,OAAO;AAAA,EAE3C,YACC,OACA,QACC;AACD,UAAM,OAAO,MAAM;AAAA,EACpB;AAAA,EAEA,aAAqB;AACpB,WAAO,OAAO,KAAK,OAAO,SAAS,IAAI,KAAK,OAAO,MAAM,MAAM,EAAE;AAAA,EAClE;AACD;AAYO,MAAM,8BACJ,kCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,QAAQ,gBAAgB;AAAA,EACrC;AAAA;AAAA,EAGS,MACR,OACkD;AAClD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,uBACJ,2BACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AAAA,EAES,mBAAmB,OAA0B;AACrD,WAAO,KAAK,MAAM,KAAK;AAAA,EACxB;AAAA,EAES,iBAAiB,OAA0B;AACnD,WAAO,KAAK,UAAU,KAAK;AAAA,EAC5B;AACD;AAoCO,SAAS,KAAK,GAA+B,IAAsB,CAAC,GAAQ;AAClF,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAyC,GAAG,CAAC;AACtE,MAAI,OAAO,SAAS,QAAQ;AAC3B,WAAO,IAAI,sBAAsB,IAAI;AAAA,EACtC;AACA,SAAO,IAAI,kBAAkB,MAAM,MAAa;AACjD;", "names": []}