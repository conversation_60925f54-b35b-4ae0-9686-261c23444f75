{"version": 3, "sources": ["../../../src/sqlite-core/columns/real.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySQLiteTable } from '../table.ts';\nimport { SQLiteColumn, SQLiteColumnBuilder } from './common.ts';\n\nexport type SQLiteRealBuilderInitial<TName extends string> = SQLiteRealBuilder<{\n\tname: TName;\n\tdataType: 'number';\n\tcolumnType: 'SQLiteReal';\n\tdata: number;\n\tdriverParam: number;\n\tenumValues: undefined;\n}>;\n\nexport class SQLiteRealBuilder<T extends ColumnBuilderBaseConfig<'number', 'SQLiteReal'>>\n\textends SQLiteColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'SQLiteRealBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'SQLiteReal');\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySQLiteTable<{ name: TTableName }>,\n\t): SQLiteReal<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SQLiteReal<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class SQLiteReal<T extends ColumnBaseConfig<'number', 'SQLiteReal'>> extends SQLiteColumn<T> {\n\tstatic override readonly [entityKind]: string = 'SQLiteReal';\n\n\tgetSQLType(): string {\n\t\treturn 'real';\n\t}\n}\n\nexport function real(): SQLiteRealBuilderInitial<''>;\nexport function real<TName extends string>(name: TName): SQLiteRealBuilderInitial<TName>;\nexport function real(name?: string) {\n\treturn new SQLiteRealBuilder(name ?? '');\n}\n"], "mappings": "AAEA,SAAS,kBAAkB;AAE3B,SAAS,cAAc,2BAA2B;AAW3C,MAAM,0BACJ,oBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,YAAY;AAAA,EACnC;AAAA;AAAA,EAGS,MACR,OAC8C;AAC9C,WAAO,IAAI,WAA4C,OAAO,KAAK,MAA8C;AAAA,EAClH;AACD;AAEO,MAAM,mBAAuE,aAAgB;AAAA,EACnG,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,KAAK,MAAe;AACnC,SAAO,IAAI,kBAAkB,QAAQ,EAAE;AACxC;", "names": []}