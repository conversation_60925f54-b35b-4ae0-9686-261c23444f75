{"version": 3, "sources": ["../../../src/prisma/mysql/session.ts"], "sourcesContent": ["import type { PrismaClient } from '@prisma/client/extension';\n\nimport { entityKind } from '~/entity.ts';\nimport { type Lo<PERSON>, NoopLogger } from '~/logger.ts';\nimport type {\n\tMySqlDialect,\n\tMySqlPreparedQueryConfig,\n\tMySqlPreparedQueryHKT,\n\tMySqlQueryResultHKT,\n\tMySqlTransaction,\n\tMySqlTransactionConfig,\n} from '~/mysql-core/index.ts';\nimport { MySqlPreparedQuery, MySqlSession } from '~/mysql-core/index.ts';\nimport { fillPlaceholders } from '~/sql/sql.ts';\nimport type { Query, SQL } from '~/sql/sql.ts';\nimport type { Assume } from '~/utils.ts';\n\nexport class PrismaMySqlPreparedQuery<T> extends MySqlPreparedQuery<MySqlPreparedQueryConfig & { execute: T }> {\n\toverride iterator(_placeholderValues?: Record<string, unknown> | undefined): AsyncGenerator<unknown, any, unknown> {\n\t\tthrow new Error('Method not implemented.');\n\t}\n\tstatic override readonly [entityKind]: string = 'PrismaMySqlPreparedQuery';\n\n\tconstructor(\n\t\tprivate readonly prisma: PrismaClient,\n\t\tprivate readonly query: Query,\n\t\tprivate readonly logger: Logger,\n\t) {\n\t\tsuper();\n\t}\n\n\toverride execute(placeholderValues?: Record<string, unknown>): Promise<T> {\n\t\tconst params = fillPlaceholders(this.query.params, placeholderValues ?? {});\n\t\tthis.logger.logQuery(this.query.sql, params);\n\t\treturn this.prisma.$queryRawUnsafe(this.query.sql, ...params);\n\t}\n}\n\nexport interface PrismaMySqlSessionOptions {\n\tlogger?: Logger;\n}\n\nexport class PrismaMySqlSession extends MySqlSession {\n\tstatic override readonly [entityKind]: string = 'PrismaMySqlSession';\n\n\tprivate readonly logger: Logger;\n\n\tconstructor(\n\t\tdialect: MySqlDialect,\n\t\tprivate readonly prisma: PrismaClient,\n\t\tprivate readonly options: PrismaMySqlSessionOptions,\n\t) {\n\t\tsuper(dialect);\n\t\tthis.logger = options.logger ?? new NoopLogger();\n\t}\n\n\toverride execute<T>(query: SQL): Promise<T> {\n\t\treturn this.prepareQuery<MySqlPreparedQueryConfig & { execute: T }>(this.dialect.sqlToQuery(query)).execute();\n\t}\n\n\toverride all<T = unknown>(_query: SQL): Promise<T[]> {\n\t\tthrow new Error('Method not implemented.');\n\t}\n\n\toverride prepareQuery<T extends MySqlPreparedQueryConfig = MySqlPreparedQueryConfig>(\n\t\tquery: Query,\n\t): MySqlPreparedQuery<T> {\n\t\treturn new PrismaMySqlPreparedQuery(this.prisma, query, this.logger);\n\t}\n\n\toverride transaction<T>(\n\t\t_transaction: (\n\t\t\ttx: MySqlTransaction<\n\t\t\t\tPrismaMySqlQueryResultHKT,\n\t\t\t\tPrismaMySqlPreparedQueryHKT,\n\t\t\t\tRecord<string, never>,\n\t\t\t\tRecord<string, never>\n\t\t\t>,\n\t\t) => Promise<T>,\n\t\t_config?: MySqlTransactionConfig,\n\t): Promise<T> {\n\t\tthrow new Error('Method not implemented.');\n\t}\n}\n\nexport interface PrismaMySqlQueryResultHKT extends MySqlQueryResultHKT {\n\ttype: [];\n}\n\nexport interface PrismaMySqlPreparedQueryHKT extends MySqlPreparedQueryHKT {\n\ttype: PrismaMySqlPreparedQuery<Assume<this['config'], MySqlPreparedQueryConfig>>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAC3B,oBAAwC;AASxC,wBAAiD;AACjD,iBAAiC;AAI1B,MAAM,iCAAoC,qCAA8D;AAAA,EAM9G,YACkB,QACA,OACA,QAChB;AACD,UAAM;AAJW;AACA;AACA;AAAA,EAGlB;AAAA,EAXS,SAAS,oBAAiG;AAClH,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC1C;AAAA,EACA,QAA0B,wBAAU,IAAY;AAAA,EAUvC,QAAQ,mBAAyD;AACzE,UAAM,aAAS,6BAAiB,KAAK,MAAM,QAAQ,qBAAqB,CAAC,CAAC;AAC1E,SAAK,OAAO,SAAS,KAAK,MAAM,KAAK,MAAM;AAC3C,WAAO,KAAK,OAAO,gBAAgB,KAAK,MAAM,KAAK,GAAG,MAAM;AAAA,EAC7D;AACD;AAMO,MAAM,2BAA2B,+BAAa;AAAA,EAKpD,YACC,SACiB,QACA,SAChB;AACD,UAAM,OAAO;AAHI;AACA;AAGjB,SAAK,SAAS,QAAQ,UAAU,IAAI,yBAAW;AAAA,EAChD;AAAA,EAXA,QAA0B,wBAAU,IAAY;AAAA,EAE/B;AAAA,EAWR,QAAW,OAAwB;AAC3C,WAAO,KAAK,aAAwD,KAAK,QAAQ,WAAW,KAAK,CAAC,EAAE,QAAQ;AAAA,EAC7G;AAAA,EAES,IAAiB,QAA2B;AACpD,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC1C;AAAA,EAES,aACR,OACwB;AACxB,WAAO,IAAI,yBAAyB,KAAK,QAAQ,OAAO,KAAK,MAAM;AAAA,EACpE;AAAA,EAES,YACR,cAQA,SACa;AACb,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC1C;AACD;", "names": []}