{"version": 3, "sources": ["../../src/mysql-core/schema.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport { type MySqlTableFn, mysqlTableWithSchema } from './table.ts';\nimport { type mysqlView, mysqlViewWithSchema } from './view.ts';\n\nexport class MySqlSchema<TName extends string = string> {\n\tstatic readonly [entityKind]: string = 'MySqlSchema';\n\n\tconstructor(\n\t\tpublic readonly schemaName: TName,\n\t) {}\n\n\ttable: MySqlTableFn<TName> = (name, columns, extraConfig) => {\n\t\treturn mysqlTableWithSchema(name, columns, extraConfig, this.schemaName);\n\t};\n\n\tview = ((name, columns) => {\n\t\treturn mysqlViewWithSchema(name, columns, this.schemaName);\n\t}) as typeof mysqlView;\n}\n\n/** @deprecated - use `instanceof MySqlSchema` */\nexport function isMySqlSchema(obj: unknown): obj is MySqlSchema {\n\treturn is(obj, MySqlSchema);\n}\n\n/**\n * Create a MySQL schema.\n * https://dev.mysql.com/doc/refman/8.0/en/create-database.html\n *\n * @param name mysql use schema name\n * @returns MySQL schema\n */\nexport function mysqlDatabase<TName extends string>(name: TName) {\n\treturn new MySqlSchema(name);\n}\n\n/**\n * @see mysqlDatabase\n */\nexport const mysqlSchema = mysqlDatabase;\n"], "mappings": "AAAA,SAAS,YAAY,UAAU;AAC/B,SAA4B,4BAA4B;AACxD,SAAyB,2BAA2B;AAE7C,MAAM,YAA2C;AAAA,EAGvD,YACiB,YACf;AADe;AAAA,EACd;AAAA,EAJH,QAAiB,UAAU,IAAY;AAAA,EAMvC,QAA6B,CAAC,MAAM,SAAS,gBAAgB;AAC5D,WAAO,qBAAqB,MAAM,SAAS,aAAa,KAAK,UAAU;AAAA,EACxE;AAAA,EAEA,OAAQ,CAAC,MAAM,YAAY;AAC1B,WAAO,oBAAoB,MAAM,SAAS,KAAK,UAAU;AAAA,EAC1D;AACD;AAGO,SAAS,cAAc,KAAkC;AAC/D,SAAO,GAAG,KAAK,WAAW;AAC3B;AASO,SAAS,cAAoC,MAAa;AAChE,SAAO,IAAI,YAAY,IAAI;AAC5B;AAKO,MAAM,cAAc;", "names": []}