{"version": 3, "sources": ["../../src/mysql-core/table.ts"], "sourcesContent": ["import type { BuildColumns, BuildExtraConfigColumns } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport { Table, type TableConfig as TableConfigBase, type UpdateTableConfig } from '~/table.ts';\nimport type { CheckBuilder } from './checks.ts';\nimport { getMySqlColumnBuilders, type MySqlColumnBuilders } from './columns/all.ts';\nimport type { MySqlColumn, MySqlColumnBuilder, MySqlColumnBuilderBase } from './columns/common.ts';\nimport type { ForeignKey, ForeignKeyBuilder } from './foreign-keys.ts';\nimport type { AnyIndexBuilder } from './indexes.ts';\nimport type { PrimaryKeyBuilder } from './primary-keys.ts';\nimport type { UniqueConstraintBuilder } from './unique-constraint.ts';\n\nexport type MySqlTableExtraConfigValue =\n\t| AnyIndexBuilder\n\t| CheckBuilder\n\t| ForeignKeyBuilder\n\t| PrimaryKeyBuilder\n\t| UniqueConstraintBuilder;\n\nexport type MySqlTableExtraConfig = Record<\n\tstring,\n\tMySqlTableExtraConfigValue\n>;\n\nexport type TableConfig = TableConfigBase<MySqlColumn>;\n\n/** @internal */\nexport const InlineForeignKeys = Symbol.for('drizzle:MySqlInlineForeignKeys');\n\nexport class MySqlTable<T extends TableConfig = TableConfig> extends Table<T> {\n\tstatic override readonly [entityKind]: string = 'MySqlTable';\n\n\tdeclare protected $columns: T['columns'];\n\n\t/** @internal */\n\tstatic override readonly Symbol = Object.assign({}, Table.Symbol, {\n\t\tInlineForeignKeys: InlineForeignKeys as typeof InlineForeignKeys,\n\t});\n\n\t/** @internal */\n\toverride [Table.Symbol.Columns]!: NonNullable<T['columns']>;\n\n\t/** @internal */\n\t[InlineForeignKeys]: ForeignKey[] = [];\n\n\t/** @internal */\n\toverride [Table.Symbol.ExtraConfigBuilder]:\n\t\t| ((self: Record<string, MySqlColumn>) => MySqlTableExtraConfig)\n\t\t| undefined = undefined;\n}\n\nexport type AnyMySqlTable<TPartial extends Partial<TableConfig> = {}> = MySqlTable<\n\tUpdateTableConfig<TableConfig, TPartial>\n>;\n\nexport type MySqlTableWithColumns<T extends TableConfig> =\n\t& MySqlTable<T>\n\t& {\n\t\t[Key in keyof T['columns']]: T['columns'][Key];\n\t};\n\nexport function mysqlTableWithSchema<\n\tTTableName extends string,\n\tTSchemaName extends string | undefined,\n\tTColumnsMap extends Record<string, MySqlColumnBuilderBase>,\n>(\n\tname: TTableName,\n\tcolumns: TColumnsMap | ((columnTypes: MySqlColumnBuilders) => TColumnsMap),\n\textraConfig:\n\t\t| ((\n\t\t\tself: BuildColumns<TTableName, TColumnsMap, 'mysql'>,\n\t\t) => MySqlTableExtraConfig | MySqlTableExtraConfigValue[])\n\t\t| undefined,\n\tschema: TSchemaName,\n\tbaseName = name,\n): MySqlTableWithColumns<{\n\tname: TTableName;\n\tschema: TSchemaName;\n\tcolumns: BuildColumns<TTableName, TColumnsMap, 'mysql'>;\n\tdialect: 'mysql';\n}> {\n\tconst rawTable = new MySqlTable<{\n\t\tname: TTableName;\n\t\tschema: TSchemaName;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'mysql'>;\n\t\tdialect: 'mysql';\n\t}>(name, schema, baseName);\n\n\tconst parsedColumns: TColumnsMap = typeof columns === 'function' ? columns(getMySqlColumnBuilders()) : columns;\n\n\tconst builtColumns = Object.fromEntries(\n\t\tObject.entries(parsedColumns).map(([name, colBuilderBase]) => {\n\t\t\tconst colBuilder = colBuilderBase as MySqlColumnBuilder;\n\t\t\tcolBuilder.setName(name);\n\t\t\tconst column = colBuilder.build(rawTable);\n\t\t\trawTable[InlineForeignKeys].push(...colBuilder.buildForeignKeys(column, rawTable));\n\t\t\treturn [name, column];\n\t\t}),\n\t) as unknown as BuildColumns<TTableName, TColumnsMap, 'mysql'>;\n\n\tconst table = Object.assign(rawTable, builtColumns);\n\n\ttable[Table.Symbol.Columns] = builtColumns;\n\ttable[Table.Symbol.ExtraConfigColumns] = builtColumns as unknown as BuildExtraConfigColumns<\n\t\tTTableName,\n\t\tTColumnsMap,\n\t\t'mysql'\n\t>;\n\n\tif (extraConfig) {\n\t\ttable[MySqlTable.Symbol.ExtraConfigBuilder] = extraConfig as unknown as (\n\t\t\tself: Record<string, MySqlColumn>,\n\t\t) => MySqlTableExtraConfig;\n\t}\n\n\treturn table;\n}\n\nexport interface MySqlTableFn<TSchemaName extends string | undefined = undefined> {\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, MySqlColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: TColumnsMap,\n\t\textraConfig?: (\n\t\t\tself: BuildColumns<TTableName, TColumnsMap, 'mysql'>,\n\t\t) => MySqlTableExtraConfigValue[],\n\t): MySqlTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchemaName;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'mysql'>;\n\t\tdialect: 'mysql';\n\t}>;\n\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, MySqlColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: (columnTypes: MySqlColumnBuilders) => TColumnsMap,\n\t\textraConfig?: (self: BuildColumns<TTableName, TColumnsMap, 'mysql'>) => MySqlTableExtraConfigValue[],\n\t): MySqlTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchemaName;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'mysql'>;\n\t\tdialect: 'mysql';\n\t}>;\n\t/**\n\t * @deprecated The third parameter of mysqlTable is changing and will only accept an array instead of an object\n\t *\n\t * @example\n\t * Deprecated version:\n\t * ```ts\n\t * export const users = mysqlTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => ({\n\t * \tidx: index('custom_name').on(t.id)\n\t * }));\n\t * ```\n\t *\n\t * New API:\n\t * ```ts\n\t * export const users = mysqlTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => [\n\t * \tindex('custom_name').on(t.id)\n\t * ]);\n\t * ```\n\t */\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, MySqlColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: TColumnsMap,\n\t\textraConfig: (self: BuildColumns<TTableName, TColumnsMap, 'mysql'>) => MySqlTableExtraConfig,\n\t): MySqlTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchemaName;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'mysql'>;\n\t\tdialect: 'mysql';\n\t}>;\n\n\t/**\n\t * @deprecated The third parameter of mysqlTable is changing and will only accept an array instead of an object\n\t *\n\t * @example\n\t * Deprecated version:\n\t * ```ts\n\t * export const users = mysqlTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => ({\n\t * \tidx: index('custom_name').on(t.id)\n\t * }));\n\t * ```\n\t *\n\t * New API:\n\t * ```ts\n\t * export const users = mysqlTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => [\n\t * \tindex('custom_name').on(t.id)\n\t * ]);\n\t * ```\n\t */\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, MySqlColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: (columnTypes: MySqlColumnBuilders) => TColumnsMap,\n\t\textraConfig: (self: BuildColumns<TTableName, TColumnsMap, 'mysql'>) => MySqlTableExtraConfig,\n\t): MySqlTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchemaName;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'mysql'>;\n\t\tdialect: 'mysql';\n\t}>;\n}\n\nexport const mysqlTable: MySqlTableFn = (name, columns, extraConfig) => {\n\treturn mysqlTableWithSchema(name, columns, extraConfig, undefined, name);\n};\n\nexport function mysqlTableCreator(customizeTableName: (name: string) => string): MySqlTableFn {\n\treturn (name, columns, extraConfig) => {\n\t\treturn mysqlTableWithSchema(customizeTableName(name) as typeof name, columns, extraConfig, undefined, name);\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAC3B,mBAAmF;AAEnF,iBAAiE;AAsB1D,MAAM,oBAAoB,OAAO,IAAI,gCAAgC;AAErE,MAAM,mBAAwD,mBAAS;AAAA,EAC7E,QAA0B,wBAAU,IAAY;AAAA;AAAA,EAKhD,OAAyB,SAAS,OAAO,OAAO,CAAC,GAAG,mBAAM,QAAQ;AAAA,IACjE;AAAA,EACD,CAAC;AAAA;AAAA,EAGD,CAAU,mBAAM,OAAO,OAAO;AAAA;AAAA,EAG9B,CAAC,iBAAiB,IAAkB,CAAC;AAAA;AAAA,EAGrC,CAAU,mBAAM,OAAO,kBAAkB,IAE1B;AAChB;AAYO,SAAS,qBAKf,MACA,SACA,aAKA,QACA,WAAW,MAMT;AACF,QAAM,WAAW,IAAI,WAKlB,MAAM,QAAQ,QAAQ;AAEzB,QAAM,gBAA6B,OAAO,YAAY,aAAa,YAAQ,mCAAuB,CAAC,IAAI;AAEvG,QAAM,eAAe,OAAO;AAAA,IAC3B,OAAO,QAAQ,aAAa,EAAE,IAAI,CAAC,CAACA,OAAM,cAAc,MAAM;AAC7D,YAAM,aAAa;AACnB,iBAAW,QAAQA,KAAI;AACvB,YAAM,SAAS,WAAW,MAAM,QAAQ;AACxC,eAAS,iBAAiB,EAAE,KAAK,GAAG,WAAW,iBAAiB,QAAQ,QAAQ,CAAC;AACjF,aAAO,CAACA,OAAM,MAAM;AAAA,IACrB,CAAC;AAAA,EACF;AAEA,QAAM,QAAQ,OAAO,OAAO,UAAU,YAAY;AAElD,QAAM,mBAAM,OAAO,OAAO,IAAI;AAC9B,QAAM,mBAAM,OAAO,kBAAkB,IAAI;AAMzC,MAAI,aAAa;AAChB,UAAM,WAAW,OAAO,kBAAkB,IAAI;AAAA,EAG/C;AAEA,SAAO;AACR;AAyGO,MAAM,aAA2B,CAAC,MAAM,SAAS,gBAAgB;AACvE,SAAO,qBAAqB,MAAM,SAAS,aAAa,QAAW,IAAI;AACxE;AAEO,SAAS,kBAAkB,oBAA4D;AAC7F,SAAO,CAAC,MAAM,SAAS,gBAAgB;AACtC,WAAO,qBAAqB,mBAAmB,IAAI,GAAkB,SAAS,aAAa,QAAW,IAAI;AAAA,EAC3G;AACD;", "names": ["name"]}