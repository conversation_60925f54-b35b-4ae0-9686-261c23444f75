{"version": 3, "sources": ["../../src/pg-core/schema.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\nimport { SQL, sql, type SQLWrapper } from '~/sql/sql.ts';\nimport type { pgEnum } from './columns/enum.ts';\nimport { pgEnumWithSchema } from './columns/enum.ts';\nimport { type pgSequence, pgSequenceWithSchema } from './sequence.ts';\nimport { type PgTableFn, pgTableWithSchema } from './table.ts';\nimport { type pgMaterializedView, pgMaterializedViewWithSchema, type pgView, pgViewWithSchema } from './view.ts';\n\nexport class PgSchema<TName extends string = string> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'PgSchema';\n\tconstructor(\n\t\tpublic readonly schemaName: TName,\n\t) {}\n\n\ttable: PgTableFn<TName> = ((name, columns, extraConfig) => {\n\t\treturn pgTableWithSchema(name, columns, extraConfig, this.schemaName);\n\t});\n\n\tview = ((name, columns) => {\n\t\treturn pgViewWithSchema(name, columns, this.schemaName);\n\t}) as typeof pgView;\n\n\tmaterializedView = ((name, columns) => {\n\t\treturn pgMaterializedViewWithSchema(name, columns, this.schemaName);\n\t}) as typeof pgMaterializedView;\n\n\tenum: typeof pgEnum = ((name, values) => {\n\t\treturn pgEnumWithSchema(name, values, this.schemaName);\n\t});\n\n\tsequence: typeof pgSequence = ((name, options) => {\n\t\treturn pgSequenceWithSchema(name, options, this.schemaName);\n\t});\n\n\tgetSQL(): SQL {\n\t\treturn new SQL([sql.identifier(this.schemaName)]);\n\t}\n\n\tshouldOmitSQLParens(): boolean {\n\t\treturn true;\n\t}\n}\n\nexport function isPgSchema(obj: unknown): obj is PgSchema {\n\treturn is(obj, PgSchema);\n}\n\nexport function pgSchema<T extends string>(name: T) {\n\tif (name === 'public') {\n\t\tthrow new Error(\n\t\t\t`You can't specify 'public' as schema name. Postgres is using public schema by default. If you want to use 'public' schema, just use pgTable() instead of creating a schema`,\n\t\t);\n\t}\n\n\treturn new PgSchema(name);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA+B;AAC/B,iBAA0C;AAE1C,kBAAiC;AACjC,sBAAsD;AACtD,mBAAkD;AAClD,kBAAqG;AAE9F,MAAM,SAA8D;AAAA,EAE1E,YACiB,YACf;AADe;AAAA,EACd;AAAA,EAHH,QAAiB,wBAAU,IAAY;AAAA,EAKvC,QAA2B,CAAC,MAAM,SAAS,gBAAgB;AAC1D,eAAO,gCAAkB,MAAM,SAAS,aAAa,KAAK,UAAU;AAAA,EACrE;AAAA,EAEA,OAAQ,CAAC,MAAM,YAAY;AAC1B,eAAO,8BAAiB,MAAM,SAAS,KAAK,UAAU;AAAA,EACvD;AAAA,EAEA,mBAAoB,CAAC,MAAM,YAAY;AACtC,eAAO,0CAA6B,MAAM,SAAS,KAAK,UAAU;AAAA,EACnE;AAAA,EAEA,OAAuB,CAAC,MAAM,WAAW;AACxC,eAAO,8BAAiB,MAAM,QAAQ,KAAK,UAAU;AAAA,EACtD;AAAA,EAEA,WAA+B,CAAC,MAAM,YAAY;AACjD,eAAO,sCAAqB,MAAM,SAAS,KAAK,UAAU;AAAA,EAC3D;AAAA,EAEA,SAAc;AACb,WAAO,IAAI,eAAI,CAAC,eAAI,WAAW,KAAK,UAAU,CAAC,CAAC;AAAA,EACjD;AAAA,EAEA,sBAA+B;AAC9B,WAAO;AAAA,EACR;AACD;AAEO,SAAS,WAAW,KAA+B;AACzD,aAAO,kBAAG,KAAK,QAAQ;AACxB;AAEO,SAAS,SAA2B,MAAS;AACnD,MAAI,SAAS,UAAU;AACtB,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,SAAO,IAAI,SAAS,IAAI;AACzB;", "names": []}