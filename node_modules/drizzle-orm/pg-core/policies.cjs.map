{"version": 3, "sources": ["../../src/pg-core/policies.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport type { PgRole } from './roles.ts';\nimport type { PgTable } from './table.ts';\n\nexport type PgPolicyToOption =\n\t| 'public'\n\t| 'current_role'\n\t| 'current_user'\n\t| 'session_user'\n\t| (string & {})\n\t| PgPolicyToOption[]\n\t| PgRole;\n\nexport interface PgPolicyConfig {\n\tas?: 'permissive' | 'restrictive';\n\tfor?: 'all' | 'select' | 'insert' | 'update' | 'delete';\n\tto?: PgPolicyToOption;\n\tusing?: SQL;\n\twithCheck?: SQL;\n}\n\nexport class PgPolicy implements PgPolicyConfig {\n\tstatic readonly [entityKind]: string = 'PgPolicy';\n\n\treadonly as: PgPolicyConfig['as'];\n\treadonly for: PgPolicyConfig['for'];\n\treadonly to: PgPolicyConfig['to'];\n\treadonly using: PgPolicyConfig['using'];\n\treadonly withCheck: PgPolicyConfig['withCheck'];\n\n\t/** @internal */\n\t_linkedTable?: PgTable;\n\n\tconstructor(\n\t\treadonly name: string,\n\t\tconfig?: PgPolicyConfig,\n\t) {\n\t\tif (config) {\n\t\t\tthis.as = config.as;\n\t\t\tthis.for = config.for;\n\t\t\tthis.to = config.to;\n\t\t\tthis.using = config.using;\n\t\t\tthis.withCheck = config.withCheck;\n\t\t}\n\t}\n\n\tlink(table: PgTable): this {\n\t\tthis._linkedTable = table;\n\t\treturn this;\n\t}\n}\n\nexport function pgPolicy(name: string, config?: PgPolicyConfig) {\n\treturn new PgPolicy(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAsBpB,MAAM,SAAmC;AAAA,EAY/C,YACU,MACT,QACC;AAFQ;AAGT,QAAI,QAAQ;AACX,WAAK,KAAK,OAAO;AACjB,WAAK,MAAM,OAAO;AAClB,WAAK,KAAK,OAAO;AACjB,WAAK,QAAQ,OAAO;AACpB,WAAK,YAAY,OAAO;AAAA,IACzB;AAAA,EACD;AAAA,EAtBA,QAAiB,wBAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAGT;AAAA,EAeA,KAAK,OAAsB;AAC1B,SAAK,eAAe;AACpB,WAAO;AAAA,EACR;AACD;AAEO,SAAS,SAAS,MAAc,QAAyB;AAC/D,SAAO,IAAI,SAAS,MAAM,MAAM;AACjC;", "names": []}