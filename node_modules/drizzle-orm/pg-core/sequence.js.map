{"version": 3, "sources": ["../../src/pg-core/sequence.ts"], "sourcesContent": ["import { entityKind, is } from '~/entity.ts';\n\nexport type PgSequenceOptions = {\n\tincrement?: number | string;\n\tminValue?: number | string;\n\tmaxValue?: number | string;\n\tstartWith?: number | string;\n\tcache?: number | string;\n\tcycle?: boolean;\n};\n\nexport class PgSequence {\n\tstatic readonly [entityKind]: string = 'PgSequence';\n\n\tconstructor(\n\t\tpublic readonly seqName: string | undefined,\n\t\tpublic readonly seqOptions: PgSequenceOptions | undefined,\n\t\tpublic readonly schema: string | undefined,\n\t) {\n\t}\n}\n\nexport function pgSequence(\n\tname: string,\n\toptions?: PgSequenceOptions,\n): PgSequence {\n\treturn pgSequenceWithSchema(name, options, undefined);\n}\n\n/** @internal */\nexport function pgSequenceWithSchema(\n\tname: string,\n\toptions?: PgSequenceOptions,\n\tschema?: string,\n): PgSequence {\n\treturn new PgSequence(name, options, schema);\n}\n\nexport function isPgSequence(obj: unknown): obj is PgSequence {\n\treturn is(obj, PgSequence);\n}\n"], "mappings": "AAAA,SAAS,YAAY,UAAU;AAWxB,MAAM,WAAW;AAAA,EAGvB,YACiB,SACA,YACA,QACf;AAHe;AACA;AACA;AAAA,EAEjB;AAAA,EAPA,QAAiB,UAAU,IAAY;AAQxC;AAEO,SAAS,WACf,MACA,SACa;AACb,SAAO,qBAAqB,MAAM,SAAS,MAAS;AACrD;AAGO,SAAS,qBACf,MACA,SACA,QACa;AACb,SAAO,IAAI,WAAW,MAAM,SAAS,MAAM;AAC5C;AAEO,SAAS,aAAa,KAAiC;AAC7D,SAAO,GAAG,KAAK,UAAU;AAC1B;", "names": []}