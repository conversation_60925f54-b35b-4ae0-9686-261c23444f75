{"version": 3, "sources": ["../../../src/pg-core/columns/smallserial.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilderRuntimeConfig,\n\tHas<PERSON><PERSON><PERSON>,\n\tMakeColumnConfig,\n\tNotNull,\n} from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgSmallSerialBuilderInitial<TName extends string> = NotNull<\n\tHasDefault<\n\t\tPgSmallSerialBuilder<{\n\t\t\tname: TName;\n\t\t\tdataType: 'number';\n\t\t\tcolumnType: 'PgSmallSerial';\n\t\t\tdata: number;\n\t\t\tdriverParam: number;\n\t\t\tenumValues: undefined;\n\t\t}>\n\t>\n>;\n\nexport class PgSmallSerialBuilder<T extends ColumnBuilderBaseConfig<'number', 'PgSmallSerial'>>\n\textends PgColumnBuilder<T>\n{\n\tstatic override readonly [entityKind]: string = 'PgSmallSerialBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'PgSmallSerial');\n\t\tthis.config.hasDefault = true;\n\t\tthis.config.notNull = true;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgSmallSerial<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgSmallSerial<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class PgSmallSerial<T extends ColumnBaseConfig<'number', 'PgSmallSerial'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgSmallSerial';\n\n\tgetSQLType(): string {\n\t\treturn 'smallserial';\n\t}\n}\n\nexport function smallserial(): PgSmallSerialBuilderInitial<''>;\nexport function smallserial<TName extends string>(name: TName): PgSmallSerialBuilderInitial<TName>;\nexport function smallserial(name?: string) {\n\treturn new PgSmallSerialBuilder(name ?? '');\n}\n"], "mappings": "AAQA,SAAS,kBAAkB;AAE3B,SAAS,UAAU,uBAAuB;AAenC,MAAM,6BACJ,gBACT;AAAA,EACC,QAA0B,UAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,eAAe;AACrC,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,UAAU;AAAA,EACvB;AAAA;AAAA,EAGS,MACR,OACiD;AACjD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,sBAA6E,SAAY;AAAA,EACrG,QAA0B,UAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,YAAY,MAAe;AAC1C,SAAO,IAAI,qBAAqB,QAAQ,EAAE;AAC3C;", "names": []}