{"version": 3, "sources": ["../../../src/pg-core/columns/serial.ts"], "sourcesContent": ["import type {\n\tColumnBuilderBaseConfig,\n\tColumnBuilderRuntimeConfig,\n\tHas<PERSON><PERSON><PERSON>,\n\tMakeColumnConfig,\n\tNotNull,\n} from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnyPgTable } from '~/pg-core/table.ts';\nimport { PgColumn, PgColumnBuilder } from './common.ts';\n\nexport type PgSerialBuilderInitial<TName extends string> = NotNull<\n\tHasDefault<\n\t\tPgSerialBuilder<{\n\t\t\tname: TName;\n\t\t\tdataType: 'number';\n\t\t\tcolumnType: 'PgSerial';\n\t\t\tdata: number;\n\t\t\tdriverParam: number;\n\t\t\tenumValues: undefined;\n\t\t}>\n\t>\n>;\n\nexport class PgSerialBuilder<T extends ColumnBuilderBaseConfig<'number', 'PgSerial'>> extends PgColumnBuilder<T> {\n\tstatic override readonly [entityKind]: string = 'PgSerialBuilder';\n\n\tconstructor(name: T['name']) {\n\t\tsuper(name, 'number', 'PgSerial');\n\t\tthis.config.hasDefault = true;\n\t\tthis.config.notNull = true;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnyPgTable<{ name: TTableName }>,\n\t): PgSerial<MakeColumnConfig<T, TTableName>> {\n\t\treturn new PgSerial<MakeColumnConfig<T, TTableName>>(table, this.config as ColumnBuilderRuntimeConfig<any, any>);\n\t}\n}\n\nexport class PgSerial<T extends ColumnBaseConfig<'number', 'PgSerial'>> extends PgColumn<T> {\n\tstatic override readonly [entityKind]: string = 'PgSerial';\n\n\tgetSQLType(): string {\n\t\treturn 'serial';\n\t}\n}\n\nexport function serial(): PgSerialBuilderInitial<''>;\nexport function serial<TName extends string>(name: TName): PgSerialBuilderInitial<TName>;\nexport function serial(name?: string) {\n\treturn new PgSerialBuilder(name ?? '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,oBAA2B;AAE3B,oBAA0C;AAenC,MAAM,wBAAiF,8BAAmB;AAAA,EAChH,QAA0B,wBAAU,IAAY;AAAA,EAEhD,YAAY,MAAiB;AAC5B,UAAM,MAAM,UAAU,UAAU;AAChC,SAAK,OAAO,aAAa;AACzB,SAAK,OAAO,UAAU;AAAA,EACvB;AAAA;AAAA,EAGS,MACR,OAC4C;AAC5C,WAAO,IAAI,SAA0C,OAAO,KAAK,MAA8C;AAAA,EAChH;AACD;AAEO,MAAM,iBAAmE,uBAAY;AAAA,EAC3F,QAA0B,wBAAU,IAAY;AAAA,EAEhD,aAAqB;AACpB,WAAO;AAAA,EACR;AACD;AAIO,SAAS,OAAO,MAAe;AACrC,SAAO,IAAI,gBAAgB,QAAQ,EAAE;AACtC;", "names": []}