{"version": 3, "sources": ["../src/utils.ts"], "sourcesContent": ["import type { AnyColumn } from './column.ts';\nimport { Column } from './column.ts';\nimport { is } from './entity.ts';\nimport type { Logger } from './logger.ts';\nimport type { SelectedFieldsOrdered } from './operations.ts';\nimport type { TableLike } from './query-builders/select.types.ts';\nimport { Param, SQL, View } from './sql/sql.ts';\nimport type { DriverValueDecoder } from './sql/sql.ts';\nimport { Subquery } from './subquery.ts';\nimport { getTableName, Table } from './table.ts';\nimport { ViewBaseConfig } from './view-common.ts';\n\n/** @internal */\nexport function mapResultRow<TResult>(\n\tcolumns: SelectedFieldsOrdered<AnyColumn>,\n\trow: unknown[],\n\tjoinsNotNullableMap: Record<string, boolean> | undefined,\n): TResult {\n\t// Key -> nested object key, value -> table name if all fields in the nested object are from the same table, false otherwise\n\tconst nullifyMap: Record<string, string | false> = {};\n\n\tconst result = columns.reduce<Record<string, any>>(\n\t\t(result, { path, field }, columnIndex) => {\n\t\t\tlet decoder: DriverValueDecoder<unknown, unknown>;\n\t\t\tif (is(field, Column)) {\n\t\t\t\tdecoder = field;\n\t\t\t} else if (is(field, SQL)) {\n\t\t\t\tdecoder = field.decoder;\n\t\t\t} else {\n\t\t\t\tdecoder = field.sql.decoder;\n\t\t\t}\n\t\t\tlet node = result;\n\t\t\tfor (const [pathChunkIndex, pathChunk] of path.entries()) {\n\t\t\t\tif (pathChunkIndex < path.length - 1) {\n\t\t\t\t\tif (!(pathChunk in node)) {\n\t\t\t\t\t\tnode[pathChunk] = {};\n\t\t\t\t\t}\n\t\t\t\t\tnode = node[pathChunk];\n\t\t\t\t} else {\n\t\t\t\t\tconst rawValue = row[columnIndex]!;\n\t\t\t\t\tconst value = node[pathChunk] = rawValue === null ? null : decoder.mapFromDriverValue(rawValue);\n\n\t\t\t\t\tif (joinsNotNullableMap && is(field, Column) && path.length === 2) {\n\t\t\t\t\t\tconst objectName = path[0]!;\n\t\t\t\t\t\tif (!(objectName in nullifyMap)) {\n\t\t\t\t\t\t\tnullifyMap[objectName] = value === null ? getTableName(field.table) : false;\n\t\t\t\t\t\t} else if (\n\t\t\t\t\t\t\ttypeof nullifyMap[objectName] === 'string' && nullifyMap[objectName] !== getTableName(field.table)\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tnullifyMap[objectName] = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn result;\n\t\t},\n\t\t{},\n\t);\n\n\t// Nullify all nested objects from nullifyMap that are nullable\n\tif (joinsNotNullableMap && Object.keys(nullifyMap).length > 0) {\n\t\tfor (const [objectName, tableName] of Object.entries(nullifyMap)) {\n\t\t\tif (typeof tableName === 'string' && !joinsNotNullableMap[tableName]) {\n\t\t\t\tresult[objectName] = null;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn result as TResult;\n}\n\n/** @internal */\nexport function orderSelectedFields<TColumn extends AnyColumn>(\n\tfields: Record<string, unknown>,\n\tpathPrefix?: string[],\n): SelectedFieldsOrdered<TColumn> {\n\treturn Object.entries(fields).reduce<SelectedFieldsOrdered<AnyColumn>>((result, [name, field]) => {\n\t\tif (typeof name !== 'string') {\n\t\t\treturn result;\n\t\t}\n\n\t\tconst newPath = pathPrefix ? [...pathPrefix, name] : [name];\n\t\tif (is(field, Column) || is(field, SQL) || is(field, SQL.Aliased)) {\n\t\t\tresult.push({ path: newPath, field });\n\t\t} else if (is(field, Table)) {\n\t\t\tresult.push(...orderSelectedFields(field[Table.Symbol.Columns], newPath));\n\t\t} else {\n\t\t\tresult.push(...orderSelectedFields(field as Record<string, unknown>, newPath));\n\t\t}\n\t\treturn result;\n\t}, []) as SelectedFieldsOrdered<TColumn>;\n}\n\nexport function haveSameKeys(left: Record<string, unknown>, right: Record<string, unknown>) {\n\tconst leftKeys = Object.keys(left);\n\tconst rightKeys = Object.keys(right);\n\n\tif (leftKeys.length !== rightKeys.length) {\n\t\treturn false;\n\t}\n\n\tfor (const [index, key] of leftKeys.entries()) {\n\t\tif (key !== rightKeys[index]) {\n\t\t\treturn false;\n\t\t}\n\t}\n\n\treturn true;\n}\n\n/** @internal */\nexport function mapUpdateSet(table: Table, values: Record<string, unknown>): UpdateSet {\n\tconst entries: [string, UpdateSet[string]][] = Object.entries(values)\n\t\t.filter(([, value]) => value !== undefined)\n\t\t.map(([key, value]) => {\n\t\t\t// eslint-disable-next-line unicorn/prefer-ternary\n\t\t\tif (is(value, SQL) || is(value, Column)) {\n\t\t\t\treturn [key, value];\n\t\t\t} else {\n\t\t\t\treturn [key, new Param(value, table[Table.Symbol.Columns][key])];\n\t\t\t}\n\t\t});\n\n\tif (entries.length === 0) {\n\t\tthrow new Error('No values to set');\n\t}\n\n\treturn Object.fromEntries(entries);\n}\n\nexport type UpdateSet = Record<string, SQL | Param | AnyColumn | null | undefined>;\n\nexport type OneOrMany<T> = T | T[];\n\nexport type Update<T, TUpdate> =\n\t& {\n\t\t[K in Exclude<keyof T, keyof TUpdate>]: T[K];\n\t}\n\t& TUpdate;\n\nexport type Simplify<T> =\n\t& {\n\t\t// @ts-ignore - \"Type parameter 'K' has a circular constraint\", not sure why\n\t\t[K in keyof T]: T[K];\n\t}\n\t& {};\n\nexport type SimplifyMappedType<T> = [T] extends [unknown] ? T : never;\n\nexport type ShallowRecord<K extends keyof any, T> = SimplifyMappedType<{ [P in K]: T }>;\n\nexport type Assume<T, U> = T extends U ? T : U;\n\nexport type Equal<X, Y> = (<T>() => T extends X ? 1 : 2) extends (<T>() => T extends Y ? 1 : 2) ? true : false;\n\nexport interface DrizzleTypeError<T extends string> {\n\t$drizzleTypeError: T;\n}\n\nexport type ValueOrArray<T> = T | T[];\n\n/** @internal */\nexport function applyMixins(baseClass: any, extendedClasses: any[]) {\n\tfor (const extendedClass of extendedClasses) {\n\t\tfor (const name of Object.getOwnPropertyNames(extendedClass.prototype)) {\n\t\t\tif (name === 'constructor') continue;\n\n\t\t\tObject.defineProperty(\n\t\t\t\tbaseClass.prototype,\n\t\t\t\tname,\n\t\t\t\tObject.getOwnPropertyDescriptor(extendedClass.prototype, name) || Object.create(null),\n\t\t\t);\n\t\t}\n\t}\n}\n\nexport type Or<T1, T2> = T1 extends true ? true : T2 extends true ? true : false;\n\nexport type IfThenElse<If, Then, Else> = If extends true ? Then : Else;\n\nexport type PromiseOf<T> = T extends Promise<infer U> ? U : T;\n\nexport type Writable<T> = {\n\t-readonly [P in keyof T]: T[P];\n};\n\nexport function getTableColumns<T extends Table>(table: T): T['_']['columns'] {\n\treturn table[Table.Symbol.Columns];\n}\n\nexport function getViewSelectedFields<T extends View>(view: T): T['_']['selectedFields'] {\n\treturn view[ViewBaseConfig].selectedFields;\n}\n\n/** @internal */\nexport function getTableLikeName(table: TableLike): string | undefined {\n\treturn is(table, Subquery)\n\t\t? table._.alias\n\t\t: is(table, View)\n\t\t? table[ViewBaseConfig].name\n\t\t: is(table, SQL)\n\t\t? undefined\n\t\t: table[Table.Symbol.IsAlias]\n\t\t? table[Table.Symbol.Name]\n\t\t: table[Table.Symbol.BaseName];\n}\n\nexport type ColumnsWithTable<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends AnyColumn<{ tableName: TTableName }>[],\n> = { [Key in keyof TColumns]: AnyColumn<{ tableName: TForeignTableName }> };\n\nexport type Casing = 'snake_case' | 'camelCase';\n\nexport interface DrizzleConfig<TSchema extends Record<string, unknown> = Record<string, never>> {\n\tlogger?: boolean | Logger;\n\tschema?: TSchema;\n\tcasing?: Casing;\n}\nexport type ValidateShape<T, ValidShape, TResult = T> = T extends ValidShape\n\t? Exclude<keyof T, keyof ValidShape> extends never ? TResult\n\t: DrizzleTypeError<\n\t\t`Invalid key(s): ${Exclude<(keyof T) & (string | number | bigint | boolean | null | undefined), keyof ValidShape>}`\n\t>\n\t: never;\n\nexport type KnownKeysOnly<T, U> = {\n\t[K in keyof T]: K extends keyof U ? T[K] : never;\n};\n\nexport type IsAny<T> = 0 extends (1 & T) ? true : false;\n\n/** @internal */\nexport function getColumnNameAndConfig<\n\tTConfig extends Record<string, any> | undefined,\n>(a: string | TConfig | undefined, b: TConfig | undefined) {\n\treturn {\n\t\tname: typeof a === 'string' && a.length > 0 ? a : '' as string,\n\t\tconfig: typeof a === 'object' ? a : b as TConfig,\n\t};\n}\n\nexport type IfNotImported<T, Y, N> = unknown extends T ? Y : N;\n\nexport type ImportTypeError<TPackageName extends string> =\n\t`Please install \\`${TPackageName}\\` to allow Drizzle ORM to connect to the database`;\n\nexport type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Keys extends any\n\t? Required<Pick<T, Keys>> & Partial<Omit<T, Keys>>\n\t: never;\n\ntype ExpectedConfigShape = {\n\tlogger?: boolean | {\n\t\tlogQuery(query: string, params: unknown[]): void;\n\t};\n\tschema?: Record<string, never>;\n\tcasing?: 'snake_case' | 'camelCase';\n};\n\n// If this errors, you must update config shape checker function with new config specs\nconst _: DrizzleConfig = {} as ExpectedConfigShape;\nconst __: ExpectedConfigShape = {} as DrizzleConfig;\n\nexport function isConfig(data: any): boolean {\n\tif (typeof data !== 'object' || data === null) return false;\n\n\tif (data.constructor.name !== 'Object') return false;\n\n\tif ('logger' in data) {\n\t\tconst type = typeof data['logger'];\n\t\tif (\n\t\t\ttype !== 'boolean' && (type !== 'object' || typeof data['logger']['logQuery'] !== 'function')\n\t\t\t&& type !== 'undefined'\n\t\t) return false;\n\n\t\treturn true;\n\t}\n\n\tif ('schema' in data) {\n\t\tconst type = typeof data['logger'];\n\t\tif (type !== 'object' && type !== 'undefined') return false;\n\n\t\treturn true;\n\t}\n\n\tif ('casing' in data) {\n\t\tconst type = typeof data['logger'];\n\t\tif (type !== 'string' && type !== 'undefined') return false;\n\n\t\treturn true;\n\t}\n\n\tif ('mode' in data) {\n\t\tif (data['mode'] !== 'default' || data['mode'] !== 'planetscale' || data['mode'] !== undefined) return false;\n\n\t\treturn true;\n\t}\n\n\tif ('connection' in data) {\n\t\tconst type = typeof data['connection'];\n\t\tif (type !== 'string' && type !== 'object' && type !== 'undefined') return false;\n\n\t\treturn true;\n\t}\n\n\tif ('client' in data) {\n\t\tconst type = typeof data['client'];\n\t\tif (type !== 'object' && type !== 'function' && type !== 'undefined') return false;\n\n\t\treturn true;\n\t}\n\n\tif (Object.keys(data).length === 0) return true;\n\n\treturn false;\n}\n\nexport type NeonAuthToken = string | (() => string | Promise<string>);\n"], "mappings": "AACA,SAAS,cAAc;AACvB,SAAS,UAAU;AAInB,SAAS,OAAO,KAAK,YAAY;AAEjC,SAAS,gBAAgB;AACzB,SAAS,cAAc,aAAa;AACpC,SAAS,sBAAsB;AAGxB,SAAS,aACf,SACA,KACA,qBACU;AAEV,QAAM,aAA6C,CAAC;AAEpD,QAAM,SAAS,QAAQ;AAAA,IACtB,CAACA,SAAQ,EAAE,MAAM,MAAM,GAAG,gBAAgB;AACzC,UAAI;AACJ,UAAI,GAAG,OAAO,MAAM,GAAG;AACtB,kBAAU;AAAA,MACX,WAAW,GAAG,OAAO,GAAG,GAAG;AAC1B,kBAAU,MAAM;AAAA,MACjB,OAAO;AACN,kBAAU,MAAM,IAAI;AAAA,MACrB;AACA,UAAI,OAAOA;AACX,iBAAW,CAAC,gBAAgB,SAAS,KAAK,KAAK,QAAQ,GAAG;AACzD,YAAI,iBAAiB,KAAK,SAAS,GAAG;AACrC,cAAI,EAAE,aAAa,OAAO;AACzB,iBAAK,SAAS,IAAI,CAAC;AAAA,UACpB;AACA,iBAAO,KAAK,SAAS;AAAA,QACtB,OAAO;AACN,gBAAM,WAAW,IAAI,WAAW;AAChC,gBAAM,QAAQ,KAAK,SAAS,IAAI,aAAa,OAAO,OAAO,QAAQ,mBAAmB,QAAQ;AAE9F,cAAI,uBAAuB,GAAG,OAAO,MAAM,KAAK,KAAK,WAAW,GAAG;AAClE,kBAAM,aAAa,KAAK,CAAC;AACzB,gBAAI,EAAE,cAAc,aAAa;AAChC,yBAAW,UAAU,IAAI,UAAU,OAAO,aAAa,MAAM,KAAK,IAAI;AAAA,YACvE,WACC,OAAO,WAAW,UAAU,MAAM,YAAY,WAAW,UAAU,MAAM,aAAa,MAAM,KAAK,GAChG;AACD,yBAAW,UAAU,IAAI;AAAA,YAC1B;AAAA,UACD;AAAA,QACD;AAAA,MACD;AACA,aAAOA;AAAA,IACR;AAAA,IACA,CAAC;AAAA,EACF;AAGA,MAAI,uBAAuB,OAAO,KAAK,UAAU,EAAE,SAAS,GAAG;AAC9D,eAAW,CAAC,YAAY,SAAS,KAAK,OAAO,QAAQ,UAAU,GAAG;AACjE,UAAI,OAAO,cAAc,YAAY,CAAC,oBAAoB,SAAS,GAAG;AACrE,eAAO,UAAU,IAAI;AAAA,MACtB;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAGO,SAAS,oBACf,QACA,YACiC;AACjC,SAAO,OAAO,QAAQ,MAAM,EAAE,OAAyC,CAAC,QAAQ,CAAC,MAAM,KAAK,MAAM;AACjG,QAAI,OAAO,SAAS,UAAU;AAC7B,aAAO;AAAA,IACR;AAEA,UAAM,UAAU,aAAa,CAAC,GAAG,YAAY,IAAI,IAAI,CAAC,IAAI;AAC1D,QAAI,GAAG,OAAO,MAAM,KAAK,GAAG,OAAO,GAAG,KAAK,GAAG,OAAO,IAAI,OAAO,GAAG;AAClE,aAAO,KAAK,EAAE,MAAM,SAAS,MAAM,CAAC;AAAA,IACrC,WAAW,GAAG,OAAO,KAAK,GAAG;AAC5B,aAAO,KAAK,GAAG,oBAAoB,MAAM,MAAM,OAAO,OAAO,GAAG,OAAO,CAAC;AAAA,IACzE,OAAO;AACN,aAAO,KAAK,GAAG,oBAAoB,OAAkC,OAAO,CAAC;AAAA,IAC9E;AACA,WAAO;AAAA,EACR,GAAG,CAAC,CAAC;AACN;AAEO,SAAS,aAAa,MAA+B,OAAgC;AAC3F,QAAM,WAAW,OAAO,KAAK,IAAI;AACjC,QAAM,YAAY,OAAO,KAAK,KAAK;AAEnC,MAAI,SAAS,WAAW,UAAU,QAAQ;AACzC,WAAO;AAAA,EACR;AAEA,aAAW,CAAC,OAAO,GAAG,KAAK,SAAS,QAAQ,GAAG;AAC9C,QAAI,QAAQ,UAAU,KAAK,GAAG;AAC7B,aAAO;AAAA,IACR;AAAA,EACD;AAEA,SAAO;AACR;AAGO,SAAS,aAAa,OAAc,QAA4C;AACtF,QAAM,UAAyC,OAAO,QAAQ,MAAM,EAClE,OAAO,CAAC,CAAC,EAAE,KAAK,MAAM,UAAU,MAAS,EACzC,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AAEtB,QAAI,GAAG,OAAO,GAAG,KAAK,GAAG,OAAO,MAAM,GAAG;AACxC,aAAO,CAAC,KAAK,KAAK;AAAA,IACnB,OAAO;AACN,aAAO,CAAC,KAAK,IAAI,MAAM,OAAO,MAAM,MAAM,OAAO,OAAO,EAAE,GAAG,CAAC,CAAC;AAAA,IAChE;AAAA,EACD,CAAC;AAEF,MAAI,QAAQ,WAAW,GAAG;AACzB,UAAM,IAAI,MAAM,kBAAkB;AAAA,EACnC;AAEA,SAAO,OAAO,YAAY,OAAO;AAClC;AAkCO,SAAS,YAAY,WAAgB,iBAAwB;AACnE,aAAW,iBAAiB,iBAAiB;AAC5C,eAAW,QAAQ,OAAO,oBAAoB,cAAc,SAAS,GAAG;AACvE,UAAI,SAAS;AAAe;AAE5B,aAAO;AAAA,QACN,UAAU;AAAA,QACV;AAAA,QACA,OAAO,yBAAyB,cAAc,WAAW,IAAI,KAAK,uBAAO,OAAO,IAAI;AAAA,MACrF;AAAA,IACD;AAAA,EACD;AACD;AAYO,SAAS,gBAAiC,OAA6B;AAC7E,SAAO,MAAM,MAAM,OAAO,OAAO;AAClC;AAEO,SAAS,sBAAsC,MAAmC;AACxF,SAAO,KAAK,cAAc,EAAE;AAC7B;AAGO,SAAS,iBAAiB,OAAsC;AACtE,SAAO,GAAG,OAAO,QAAQ,IACtB,MAAM,EAAE,QACR,GAAG,OAAO,IAAI,IACd,MAAM,cAAc,EAAE,OACtB,GAAG,OAAO,GAAG,IACb,SACA,MAAM,MAAM,OAAO,OAAO,IAC1B,MAAM,MAAM,OAAO,IAAI,IACvB,MAAM,MAAM,OAAO,QAAQ;AAC/B;AA6BO,SAAS,uBAEd,GAAiC,GAAwB;AAC1D,SAAO;AAAA,IACN,MAAM,OAAO,MAAM,YAAY,EAAE,SAAS,IAAI,IAAI;AAAA,IAClD,QAAQ,OAAO,MAAM,WAAW,IAAI;AAAA,EACrC;AACD;AAoBA,MAAM,IAAmB,CAAC;AAC1B,MAAM,KAA0B,CAAC;AAE1B,SAAS,SAAS,MAAoB;AAC5C,MAAI,OAAO,SAAS,YAAY,SAAS;AAAM,WAAO;AAEtD,MAAI,KAAK,YAAY,SAAS;AAAU,WAAO;AAE/C,MAAI,YAAY,MAAM;AACrB,UAAM,OAAO,OAAO,KAAK,QAAQ;AACjC,QACC,SAAS,cAAc,SAAS,YAAY,OAAO,KAAK,QAAQ,EAAE,UAAU,MAAM,eAC/E,SAAS;AACX,aAAO;AAET,WAAO;AAAA,EACR;AAEA,MAAI,YAAY,MAAM;AACrB,UAAM,OAAO,OAAO,KAAK,QAAQ;AACjC,QAAI,SAAS,YAAY,SAAS;AAAa,aAAO;AAEtD,WAAO;AAAA,EACR;AAEA,MAAI,YAAY,MAAM;AACrB,UAAM,OAAO,OAAO,KAAK,QAAQ;AACjC,QAAI,SAAS,YAAY,SAAS;AAAa,aAAO;AAEtD,WAAO;AAAA,EACR;AAEA,MAAI,UAAU,MAAM;AACnB,QAAI,KAAK,MAAM,MAAM,aAAa,KAAK,MAAM,MAAM,iBAAiB,KAAK,MAAM,MAAM;AAAW,aAAO;AAEvG,WAAO;AAAA,EACR;AAEA,MAAI,gBAAgB,MAAM;AACzB,UAAM,OAAO,OAAO,KAAK,YAAY;AACrC,QAAI,SAAS,YAAY,SAAS,YAAY,SAAS;AAAa,aAAO;AAE3E,WAAO;AAAA,EACR;AAEA,MAAI,YAAY,MAAM;AACrB,UAAM,OAAO,OAAO,KAAK,QAAQ;AACjC,QAAI,SAAS,YAAY,SAAS,cAAc,SAAS;AAAa,aAAO;AAE7E,WAAO;AAAA,EACR;AAEA,MAAI,OAAO,KAAK,IAAI,EAAE,WAAW;AAAG,WAAO;AAE3C,SAAO;AACR;", "names": ["result"]}