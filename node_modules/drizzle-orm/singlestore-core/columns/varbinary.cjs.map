{"version": 3, "sources": ["../../../src/singlestore-core/columns/varbinary.ts"], "sourcesContent": ["import type { ColumnBuilderBaseConfig, ColumnBuilderRuntimeConfig, MakeColumnConfig } from '~/column-builder.ts';\nimport type { ColumnBaseConfig } from '~/column.ts';\nimport { entityKind } from '~/entity.ts';\nimport type { AnySingleStoreTable } from '~/singlestore-core/table.ts';\nimport { getColumnNameAndConfig } from '~/utils.ts';\nimport { SingleStoreColumn, SingleStoreColumnBuilder } from './common.ts';\n\nexport type SingleStoreVarBinaryBuilderInitial<TName extends string> = SingleStoreVarBinaryBuilder<{\n\tname: TName;\n\tdataType: 'string';\n\tcolumnType: 'SingleStoreVarBinary';\n\tdata: string;\n\tdriverParam: string;\n\tenumValues: undefined;\n\tgenerated: undefined;\n}>;\n\nexport class SingleStoreVarBinaryBuilder<T extends ColumnBuilderBaseConfig<'string', 'SingleStoreVarBinary'>>\n\textends SingleStoreColumnBuilder<T, SingleStoreVarbinaryOptions>\n{\n\tstatic override readonly [entityKind]: string = 'SingleStoreVarBinaryBuilder';\n\n\t/** @internal */\n\tconstructor(name: T['name'], config: SingleStoreVarbinaryOptions) {\n\t\tsuper(name, 'string', 'SingleStoreVarBinary');\n\t\tthis.config.length = config?.length;\n\t}\n\n\t/** @internal */\n\toverride build<TTableName extends string>(\n\t\ttable: AnySingleStoreTable<{ name: TTableName }>,\n\t): SingleStoreVarBinary<MakeColumnConfig<T, TTableName>> {\n\t\treturn new SingleStoreVarBinary<MakeColumnConfig<T, TTableName>>(\n\t\t\ttable,\n\t\t\tthis.config as ColumnBuilderRuntimeConfig<any, any>,\n\t\t);\n\t}\n}\n\nexport class SingleStoreVarBinary<\n\tT extends ColumnBaseConfig<'string', 'SingleStoreVarBinary'>,\n> extends SingleStoreColumn<T, SingleStoreVarbinaryOptions> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreVarBinary';\n\n\tlength: number | undefined = this.config.length;\n\n\tgetSQLType(): string {\n\t\treturn this.length === undefined ? `varbinary` : `varbinary(${this.length})`;\n\t}\n}\n\nexport interface SingleStoreVarbinaryOptions {\n\tlength: number;\n}\n\nexport function varbinary(\n\tconfig: SingleStoreVarbinaryOptions,\n): SingleStoreVarBinaryBuilderInitial<''>;\nexport function varbinary<TName extends string>(\n\tname: TName,\n\tconfig: SingleStoreVarbinaryOptions,\n): SingleStoreVarBinaryBuilderInitial<TName>;\nexport function varbinary(a?: string | SingleStoreVarbinaryOptions, b?: SingleStoreVarbinaryOptions) {\n\tconst { name, config } = getColumnNameAndConfig<SingleStoreVarbinaryOptions>(a, b);\n\treturn new SingleStoreVarBinaryBuilder(name, config);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,oBAA2B;AAE3B,mBAAuC;AACvC,oBAA4D;AAYrD,MAAM,oCACJ,uCACT;AAAA,EACC,QAA0B,wBAAU,IAAY;AAAA;AAAA,EAGhD,YAAY,MAAiB,QAAqC;AACjE,UAAM,MAAM,UAAU,sBAAsB;AAC5C,SAAK,OAAO,SAAS,QAAQ;AAAA,EAC9B;AAAA;AAAA,EAGS,MACR,OACwD;AACxD,WAAO,IAAI;AAAA,MACV;AAAA,MACA,KAAK;AAAA,IACN;AAAA,EACD;AACD;AAEO,MAAM,6BAEH,gCAAkD;AAAA,EAC3D,QAA0B,wBAAU,IAAY;AAAA,EAEhD,SAA6B,KAAK,OAAO;AAAA,EAEzC,aAAqB;AACpB,WAAO,KAAK,WAAW,SAAY,cAAc,aAAa,KAAK,MAAM;AAAA,EAC1E;AACD;AAaO,SAAS,UAAU,GAA0C,GAAiC;AACpG,QAAM,EAAE,MAAM,OAAO,QAAI,qCAAoD,GAAG,CAAC;AACjF,SAAO,IAAI,4BAA4B,MAAM,MAAM;AACpD;", "names": []}