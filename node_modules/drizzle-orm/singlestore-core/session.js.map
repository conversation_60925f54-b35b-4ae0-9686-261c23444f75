{"version": 3, "sources": ["../../src/singlestore-core/session.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { TransactionRollbackError } from '~/errors.ts';\nimport type { RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { type Query, type SQL, sql } from '~/sql/sql.ts';\nimport type { Assume, Equal } from '~/utils.ts';\nimport { SingleStoreDatabase } from './db.ts';\nimport type { SingleStoreDialect } from './dialect.ts';\nimport type { SelectedFieldsOrdered } from './query-builders/select.types.ts';\n\nexport interface SingleStoreQueryResultHKT {\n\treadonly $brand: 'SingleStoreQueryResultHKT';\n\treadonly row: unknown;\n\treadonly type: unknown;\n}\n\nexport interface AnySingleStoreQueryResultHKT extends SingleStoreQueryResultHKT {\n\treadonly type: any;\n}\n\nexport type SingleStoreQueryResultKind<TKind extends SingleStoreQueryResultHKT, TRow> = (TKind & {\n\treadonly row: TRow;\n})['type'];\n\nexport interface SingleStorePreparedQueryConfig {\n\texecute: unknown;\n\titerator: unknown;\n}\n\nexport interface SingleStorePreparedQueryHKT {\n\treadonly $brand: 'SingleStorePreparedQueryHKT';\n\treadonly config: unknown;\n\treadonly type: unknown;\n}\n\nexport type PreparedQueryKind<\n\tTKind extends SingleStorePreparedQueryHKT,\n\tTConfig extends SingleStorePreparedQueryConfig,\n\tTAssume extends boolean = false,\n> = Equal<TAssume, true> extends true\n\t? Assume<(TKind & { readonly config: TConfig })['type'], SingleStorePreparedQuery<TConfig>>\n\t: (TKind & { readonly config: TConfig })['type'];\n\nexport abstract class SingleStorePreparedQuery<T extends SingleStorePreparedQueryConfig> {\n\tstatic readonly [entityKind]: string = 'SingleStorePreparedQuery';\n\n\t/** @internal */\n\tjoinsNotNullableMap?: Record<string, boolean>;\n\n\tabstract execute(placeholderValues?: Record<string, unknown>): Promise<T['execute']>;\n\n\tabstract iterator(placeholderValues?: Record<string, unknown>): AsyncGenerator<T['iterator']>;\n}\n\nexport interface SingleStoreTransactionConfig {\n\twithConsistentSnapshot?: boolean;\n\taccessMode?: 'read only' | 'read write';\n\tisolationLevel: 'read committed'; // SingleStore only supports read committed isolation level (https://docs.singlestore.com/db/v8.7/introduction/faqs/durability/)\n}\n\nexport abstract class SingleStoreSession<\n\tTQueryResult extends SingleStoreQueryResultHKT = SingleStoreQueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase = PreparedQueryHKTBase,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> {\n\tstatic readonly [entityKind]: string = 'SingleStoreSession';\n\n\tconstructor(protected dialect: SingleStoreDialect) {}\n\n\tabstract prepareQuery<\n\t\tT extends SingleStorePreparedQueryConfig,\n\t\tTPreparedQueryHKT extends SingleStorePreparedQueryHKT,\n\t>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tcustomResultMapper?: (rows: unknown[][]) => T['execute'],\n\t\tgeneratedIds?: Record<string, unknown>[],\n\t\treturningIds?: SelectedFieldsOrdered,\n\t): PreparedQueryKind<TPreparedQueryHKT, T>;\n\n\texecute<T>(query: SQL): Promise<T> {\n\t\treturn this.prepareQuery<SingleStorePreparedQueryConfig & { execute: T }, PreparedQueryHKTBase>(\n\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\tundefined,\n\t\t).execute();\n\t}\n\n\tabstract all<T = unknown>(query: SQL): Promise<T[]>;\n\n\tasync count(sql: SQL): Promise<number> {\n\t\tconst res = await this.execute<[[{ count: string }]]>(sql);\n\n\t\treturn Number(\n\t\t\tres[0][0]['count'],\n\t\t);\n\t}\n\n\tabstract transaction<T>(\n\t\ttransaction: (tx: SingleStoreTransaction<TQueryResult, TPreparedQueryHKT, TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: SingleStoreTransactionConfig,\n\t): Promise<T>;\n\n\tprotected getSetTransactionSQL(config: SingleStoreTransactionConfig): SQL | undefined {\n\t\tconst parts: string[] = [];\n\n\t\tif (config.isolationLevel) {\n\t\t\tparts.push(`isolation level ${config.isolationLevel}`);\n\t\t}\n\n\t\treturn parts.length ? sql`set transaction ${sql.raw(parts.join(' '))}` : undefined;\n\t}\n\n\tprotected getStartTransactionSQL(config: SingleStoreTransactionConfig): SQL | undefined {\n\t\tconst parts: string[] = [];\n\n\t\tif (config.withConsistentSnapshot) {\n\t\t\tparts.push('with consistent snapshot');\n\t\t}\n\n\t\tif (config.accessMode) {\n\t\t\tparts.push(config.accessMode);\n\t\t}\n\n\t\treturn parts.length ? sql`start transaction ${sql.raw(parts.join(' '))}` : undefined;\n\t}\n}\n\nexport abstract class SingleStoreTransaction<\n\tTQueryResult extends SingleStoreQueryResultHKT,\n\tTPreparedQueryHKT extends PreparedQueryHKTBase,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> extends SingleStoreDatabase<TQueryResult, TPreparedQueryHKT, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreTransaction';\n\n\tconstructor(\n\t\tdialect: SingleStoreDialect,\n\t\tsession: SingleStoreSession,\n\t\tprotected schema: RelationalSchemaConfig<TSchema> | undefined,\n\t\tprotected readonly nestedIndex: number,\n\t) {\n\t\tsuper(dialect, session, schema);\n\t}\n\n\trollback(): never {\n\t\tthrow new TransactionRollbackError();\n\t}\n\n\t/** Nested transactions (aka savepoints) only work with InnoDB engine. */\n\tabstract override transaction<T>(\n\t\ttransaction: (tx: SingleStoreTransaction<TQueryResult, TPreparedQueryHKT, TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T>;\n}\n\nexport interface PreparedQueryHKTBase extends SingleStorePreparedQueryHKT {\n\ttype: SingleStorePreparedQuery<Assume<this['config'], SingleStorePreparedQueryConfig>>;\n}\n"], "mappings": "AAAA,SAAS,kBAAkB;AAC3B,SAAS,gCAAgC;AAEzC,SAA+B,WAAW;AAE1C,SAAS,2BAA2B;AAqC7B,MAAe,yBAAmE;AAAA,EACxF,QAAiB,UAAU,IAAY;AAAA;AAAA,EAGvC;AAKD;AAQO,MAAe,mBAKpB;AAAA,EAGD,YAAsB,SAA6B;AAA7B;AAAA,EAA8B;AAAA,EAFpD,QAAiB,UAAU,IAAY;AAAA,EAevC,QAAW,OAAwB;AAClC,WAAO,KAAK;AAAA,MACX,KAAK,QAAQ,WAAW,KAAK;AAAA,MAC7B;AAAA,IACD,EAAE,QAAQ;AAAA,EACX;AAAA,EAIA,MAAM,MAAMA,MAA2B;AACtC,UAAM,MAAM,MAAM,KAAK,QAA+BA,IAAG;AAEzD,WAAO;AAAA,MACN,IAAI,CAAC,EAAE,CAAC,EAAE,OAAO;AAAA,IAClB;AAAA,EACD;AAAA,EAOU,qBAAqB,QAAuD;AACrF,UAAM,QAAkB,CAAC;AAEzB,QAAI,OAAO,gBAAgB;AAC1B,YAAM,KAAK,mBAAmB,OAAO,cAAc,EAAE;AAAA,IACtD;AAEA,WAAO,MAAM,SAAS,sBAAsB,IAAI,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK;AAAA,EAC1E;AAAA,EAEU,uBAAuB,QAAuD;AACvF,UAAM,QAAkB,CAAC;AAEzB,QAAI,OAAO,wBAAwB;AAClC,YAAM,KAAK,0BAA0B;AAAA,IACtC;AAEA,QAAI,OAAO,YAAY;AACtB,YAAM,KAAK,OAAO,UAAU;AAAA,IAC7B;AAEA,WAAO,MAAM,SAAS,wBAAwB,IAAI,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK;AAAA,EAC5E;AACD;AAEO,MAAe,+BAKZ,oBAA2E;AAAA,EAGpF,YACC,SACA,SACU,QACS,aAClB;AACD,UAAM,SAAS,SAAS,MAAM;AAHpB;AACS;AAAA,EAGpB;AAAA,EATA,QAA0B,UAAU,IAAY;AAAA,EAWhD,WAAkB;AACjB,UAAM,IAAI,yBAAyB;AAAA,EACpC;AAMD;", "names": ["sql"]}