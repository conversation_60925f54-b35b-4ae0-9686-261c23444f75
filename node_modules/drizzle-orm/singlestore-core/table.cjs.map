{"version": 3, "sources": ["../../src/singlestore-core/table.ts"], "sourcesContent": ["import type { BuildColumns, BuildExtraConfigColumns } from '~/column-builder.ts';\nimport { entityKind } from '~/entity.ts';\nimport { Table, type TableConfig as TableConfigBase, type UpdateTableConfig } from '~/table.ts';\nimport { getSingleStoreColumnBuilders, type SingleStoreColumnBuilders } from './columns/all.ts';\nimport type { SingleStoreColumn, SingleStoreColumnBuilder, SingleStoreColumnBuilderBase } from './columns/common.ts';\nimport type { AnyIndexBuilder } from './indexes.ts';\nimport type { PrimaryKeyBuilder } from './primary-keys.ts';\nimport type { UniqueConstraintBuilder } from './unique-constraint.ts';\n\nexport type SingleStoreTableExtraConfigValue =\n\t| AnyIndexBuilder\n\t| PrimaryKeyBuilder\n\t| UniqueConstraintBuilder;\n\nexport type SingleStoreTableExtraConfig = Record<\n\tstring,\n\tSingleStoreTableExtraConfigValue\n>;\n\nexport type TableConfig = TableConfigBase<SingleStoreColumn>;\n\nexport class SingleStoreTable<T extends TableConfig = TableConfig> extends Table<T> {\n\tstatic override readonly [entityKind]: string = 'SingleStoreTable';\n\n\tdeclare protected $columns: T['columns'];\n\n\t/** @internal */\n\tstatic override readonly Symbol = Object.assign({}, Table.Symbol, {});\n\n\t/** @internal */\n\toverride [Table.Symbol.Columns]!: NonNullable<T['columns']>;\n\n\t/** @internal */\n\toverride [Table.Symbol.ExtraConfigBuilder]:\n\t\t| ((self: Record<string, SingleStoreColumn>) => SingleStoreTableExtraConfig)\n\t\t| undefined = undefined;\n}\n\nexport type AnySingleStoreTable<TPartial extends Partial<TableConfig> = {}> = SingleStoreTable<\n\tUpdateTableConfig<TableConfig, TPartial>\n>;\n\nexport type SingleStoreTableWithColumns<T extends TableConfig> =\n\t& SingleStoreTable<T>\n\t& {\n\t\t[Key in keyof T['columns']]: T['columns'][Key];\n\t};\n\nexport function singlestoreTableWithSchema<\n\tTTableName extends string,\n\tTSchemaName extends string | undefined,\n\tTColumnsMap extends Record<string, SingleStoreColumnBuilderBase>,\n>(\n\tname: TTableName,\n\tcolumns: TColumnsMap | ((columnTypes: SingleStoreColumnBuilders) => TColumnsMap),\n\textraConfig:\n\t\t| ((\n\t\t\tself: BuildColumns<TTableName, TColumnsMap, 'singlestore'>,\n\t\t) => SingleStoreTableExtraConfig | SingleStoreTableExtraConfigValue[])\n\t\t| undefined,\n\tschema: TSchemaName,\n\tbaseName = name,\n): SingleStoreTableWithColumns<{\n\tname: TTableName;\n\tschema: TSchemaName;\n\tcolumns: BuildColumns<TTableName, TColumnsMap, 'singlestore'>;\n\tdialect: 'singlestore';\n}> {\n\tconst rawTable = new SingleStoreTable<{\n\t\tname: TTableName;\n\t\tschema: TSchemaName;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'singlestore'>;\n\t\tdialect: 'singlestore';\n\t}>(name, schema, baseName);\n\n\tconst parsedColumns: TColumnsMap = typeof columns === 'function' ? columns(getSingleStoreColumnBuilders()) : columns;\n\n\tconst builtColumns = Object.fromEntries(\n\t\tObject.entries(parsedColumns).map(([name, colBuilderBase]) => {\n\t\t\tconst colBuilder = colBuilderBase as SingleStoreColumnBuilder;\n\t\t\tcolBuilder.setName(name);\n\t\t\tconst column = colBuilder.build(rawTable);\n\t\t\treturn [name, column];\n\t\t}),\n\t) as unknown as BuildColumns<TTableName, TColumnsMap, 'singlestore'>;\n\n\tconst table = Object.assign(rawTable, builtColumns);\n\n\ttable[Table.Symbol.Columns] = builtColumns;\n\ttable[Table.Symbol.ExtraConfigColumns] = builtColumns as unknown as BuildExtraConfigColumns<\n\t\tTTableName,\n\t\tTColumnsMap,\n\t\t'singlestore'\n\t>;\n\n\tif (extraConfig) {\n\t\ttable[SingleStoreTable.Symbol.ExtraConfigBuilder] = extraConfig as unknown as (\n\t\t\tself: Record<string, SingleStoreColumn>,\n\t\t) => SingleStoreTableExtraConfig;\n\t}\n\n\treturn table;\n}\n\nexport interface SingleStoreTableFn<TSchemaName extends string | undefined = undefined> {\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, SingleStoreColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: TColumnsMap,\n\t\textraConfig?: (\n\t\t\tself: BuildColumns<TTableName, TColumnsMap, 'singlestore'>,\n\t\t) => SingleStoreTableExtraConfigValue[],\n\t): SingleStoreTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchemaName;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'singlestore'>;\n\t\tdialect: 'singlestore';\n\t}>;\n\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, SingleStoreColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: (columnTypes: SingleStoreColumnBuilders) => TColumnsMap,\n\t\textraConfig?: (self: BuildColumns<TTableName, TColumnsMap, 'singlestore'>) => SingleStoreTableExtraConfigValue[],\n\t): SingleStoreTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchemaName;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'singlestore'>;\n\t\tdialect: 'singlestore';\n\t}>;\n\t/**\n\t * @deprecated The third parameter of singlestoreTable is changing and will only accept an array instead of an object\n\t *\n\t * @example\n\t * Deprecated version:\n\t * ```ts\n\t * export const users = singlestoreTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => ({\n\t * \tidx: index('custom_name').on(t.id)\n\t * }));\n\t * ```\n\t *\n\t * New API:\n\t * ```ts\n\t * export const users = singlestoreTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => [\n\t * \tindex('custom_name').on(t.id)\n\t * ]);\n\t * ```\n\t */\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, SingleStoreColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: TColumnsMap,\n\t\textraConfig?: (self: BuildColumns<TTableName, TColumnsMap, 'singlestore'>) => SingleStoreTableExtraConfig,\n\t): SingleStoreTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchemaName;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'singlestore'>;\n\t\tdialect: 'singlestore';\n\t}>;\n\n\t/**\n\t * @deprecated The third parameter of singlestoreTable is changing and will only accept an array instead of an object\n\t *\n\t * @example\n\t * Deprecated version:\n\t * ```ts\n\t * export const users = singlestoreTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => ({\n\t * \tidx: index('custom_name').on(t.id)\n\t * }));\n\t * ```\n\t *\n\t * New API:\n\t * ```ts\n\t * export const users = singlestoreTable(\"users\", {\n\t * \tid: int(),\n\t * }, (t) => [\n\t * \tindex('custom_name').on(t.id)\n\t * ]);\n\t * ```\n\t */\n\t<\n\t\tTTableName extends string,\n\t\tTColumnsMap extends Record<string, SingleStoreColumnBuilderBase>,\n\t>(\n\t\tname: TTableName,\n\t\tcolumns: (columnTypes: SingleStoreColumnBuilders) => TColumnsMap,\n\t\textraConfig?: (self: BuildColumns<TTableName, TColumnsMap, 'singlestore'>) => SingleStoreTableExtraConfig,\n\t): SingleStoreTableWithColumns<{\n\t\tname: TTableName;\n\t\tschema: TSchemaName;\n\t\tcolumns: BuildColumns<TTableName, TColumnsMap, 'singlestore'>;\n\t\tdialect: 'singlestore';\n\t}>;\n}\n\nexport const singlestoreTable: SingleStoreTableFn = (name, columns, extraConfig) => {\n\treturn singlestoreTableWithSchema(name, columns, extraConfig, undefined, name);\n};\n\nexport function singlestoreTableCreator(customizeTableName: (name: string) => string): SingleStoreTableFn {\n\treturn (name, columns, extraConfig) => {\n\t\treturn singlestoreTableWithSchema(customizeTableName(name) as typeof name, columns, extraConfig, undefined, name);\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,oBAA2B;AAC3B,mBAAmF;AACnF,iBAA6E;AAkBtE,MAAM,yBAA8D,mBAAS;AAAA,EACnF,QAA0B,wBAAU,IAAY;AAAA;AAAA,EAKhD,OAAyB,SAAS,OAAO,OAAO,CAAC,GAAG,mBAAM,QAAQ,CAAC,CAAC;AAAA;AAAA,EAGpE,CAAU,mBAAM,OAAO,OAAO;AAAA;AAAA,EAG9B,CAAU,mBAAM,OAAO,kBAAkB,IAE1B;AAChB;AAYO,SAAS,2BAKf,MACA,SACA,aAKA,QACA,WAAW,MAMT;AACF,QAAM,WAAW,IAAI,iBAKlB,MAAM,QAAQ,QAAQ;AAEzB,QAAM,gBAA6B,OAAO,YAAY,aAAa,YAAQ,yCAA6B,CAAC,IAAI;AAE7G,QAAM,eAAe,OAAO;AAAA,IAC3B,OAAO,QAAQ,aAAa,EAAE,IAAI,CAAC,CAACA,OAAM,cAAc,MAAM;AAC7D,YAAM,aAAa;AACnB,iBAAW,QAAQA,KAAI;AACvB,YAAM,SAAS,WAAW,MAAM,QAAQ;AACxC,aAAO,CAACA,OAAM,MAAM;AAAA,IACrB,CAAC;AAAA,EACF;AAEA,QAAM,QAAQ,OAAO,OAAO,UAAU,YAAY;AAElD,QAAM,mBAAM,OAAO,OAAO,IAAI;AAC9B,QAAM,mBAAM,OAAO,kBAAkB,IAAI;AAMzC,MAAI,aAAa;AAChB,UAAM,iBAAiB,OAAO,kBAAkB,IAAI;AAAA,EAGrD;AAEA,SAAO;AACR;AAyGO,MAAM,mBAAuC,CAAC,MAAM,SAAS,gBAAgB;AACnF,SAAO,2BAA2B,MAAM,SAAS,aAAa,QAAW,IAAI;AAC9E;AAEO,SAAS,wBAAwB,oBAAkE;AACzG,SAAO,CAAC,MAAM,SAAS,gBAAgB;AACtC,WAAO,2BAA2B,mBAAmB,IAAI,GAAkB,SAAS,aAAa,QAAW,IAAI;AAAA,EACjH;AACD;", "names": ["name"]}