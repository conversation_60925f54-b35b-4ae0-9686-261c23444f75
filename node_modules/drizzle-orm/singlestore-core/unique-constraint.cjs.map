{"version": 3, "sources": ["../../src/singlestore-core/unique-constraint.ts"], "sourcesContent": ["import { entityKind } from '~/entity.ts';\nimport { TableName } from '~/table.utils.ts';\nimport type { SingleStoreColumn } from './columns/index.ts';\nimport type { SingleStoreTable } from './table.ts';\n\nexport function unique(name?: string): UniqueOnConstraintBuilder {\n\treturn new UniqueOnConstraintBuilder(name);\n}\n\nexport function uniqueKeyName(table: SingleStoreTable, columns: string[]) {\n\treturn `${table[TableName]}_${columns.join('_')}_unique`;\n}\n\nexport class UniqueConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'SingleStoreUniqueConstraintBuilder';\n\n\t/** @internal */\n\tcolumns: SingleStoreColumn[];\n\n\tconstructor(\n\t\tcolumns: SingleStoreColumn[],\n\t\tprivate name?: string,\n\t) {\n\t\tthis.columns = columns;\n\t}\n\n\t/** @internal */\n\tbuild(table: SingleStoreTable): UniqueConstraint {\n\t\treturn new UniqueConstraint(table, this.columns, this.name);\n\t}\n}\n\nexport class UniqueOnConstraintBuilder {\n\tstatic readonly [entityKind]: string = 'SingleStoreUniqueOnConstraintBuilder';\n\n\t/** @internal */\n\tname?: string;\n\n\tconstructor(\n\t\tname?: string,\n\t) {\n\t\tthis.name = name;\n\t}\n\n\ton(...columns: [SingleStoreColumn, ...SingleStoreColumn[]]) {\n\t\treturn new UniqueConstraintBuilder(columns, this.name);\n\t}\n}\n\nexport class UniqueConstraint {\n\tstatic readonly [entityKind]: string = 'SingleStoreUniqueConstraint';\n\n\treadonly columns: SingleStoreColumn[];\n\treadonly name?: string;\n\treadonly nullsNotDistinct: boolean = false;\n\n\tconstructor(readonly table: SingleStoreTable, columns: SingleStoreColumn[], name?: string) {\n\t\tthis.columns = columns;\n\t\tthis.name = name ?? uniqueKeyName(this.table, this.columns.map((column) => column.name));\n\t}\n\n\tgetName() {\n\t\treturn this.name;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA2B;AAC3B,yBAA0B;AAInB,SAAS,OAAO,MAA0C;AAChE,SAAO,IAAI,0BAA0B,IAAI;AAC1C;AAEO,SAAS,cAAc,OAAyB,SAAmB;AACzE,SAAO,GAAG,MAAM,4BAAS,CAAC,IAAI,QAAQ,KAAK,GAAG,CAAC;AAChD;AAEO,MAAM,wBAAwB;AAAA,EAMpC,YACC,SACQ,MACP;AADO;AAER,SAAK,UAAU;AAAA,EAChB;AAAA,EAVA,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC;AAAA;AAAA,EAUA,MAAM,OAA2C;AAChD,WAAO,IAAI,iBAAiB,OAAO,KAAK,SAAS,KAAK,IAAI;AAAA,EAC3D;AACD;AAEO,MAAM,0BAA0B;AAAA,EACtC,QAAiB,wBAAU,IAAY;AAAA;AAAA,EAGvC;AAAA,EAEA,YACC,MACC;AACD,SAAK,OAAO;AAAA,EACb;AAAA,EAEA,MAAM,SAAsD;AAC3D,WAAO,IAAI,wBAAwB,SAAS,KAAK,IAAI;AAAA,EACtD;AACD;AAEO,MAAM,iBAAiB;AAAA,EAO7B,YAAqB,OAAyB,SAA8B,MAAe;AAAtE;AACpB,SAAK,UAAU;AACf,SAAK,OAAO,QAAQ,cAAc,KAAK,OAAO,KAAK,QAAQ,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC;AAAA,EACxF;AAAA,EATA,QAAiB,wBAAU,IAAY;AAAA,EAE9B;AAAA,EACA;AAAA,EACA,mBAA4B;AAAA,EAOrC,UAAU;AACT,WAAO,KAAK;AAAA,EACb;AACD;", "names": []}