<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="energyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00ff41;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#00dd37;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00cc33;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="glowEffect" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#00ff41;stop-opacity:0.3" />
      <stop offset="70%" style="stop-color:#00ff41;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#00ff41;stop-opacity:0" />
    </radialGradient>
  </defs>
  <!-- Background -->
  <rect width="512" height="512" fill="#000000" rx="64"/>
  
  <!-- Glow effect -->
  <circle cx="256" cy="256" r="200" fill="url(#glowEffect)"/>
  
  <!-- Main energy symbol -->
  <path d="M180 120L320 256C250 350 180 320 180 320V120Z" fill="#00ff41" opacity="0.9"/>
  <path d="M332 392L192 256C262 162 332 192 332 192V392Z" fill="#00cc33" opacity="0.9"/>
  
  <!-- Energy core -->
  <circle cx="256" cy="256" r="40" fill="url(#energyGradient)" opacity="0.8"/>
  
  <!-- Text -->
  <text x="256" y="450" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="#00ff41" text-anchor="middle">nU Universe</text>
</svg>