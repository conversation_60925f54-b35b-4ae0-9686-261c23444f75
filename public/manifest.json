{"name": "nU Universe - Universal Energy Platform", "short_name": "nU Universe", "description": "Convert real device energy into UMatter tokens on iOS, Android, Desktop, and all browsers", "start_url": "/", "display": "standalone", "background_color": "#000000", "theme_color": "#00ff41", "orientation": "portrait-primary", "scope": "/", "lang": "en-US", "dir": "ltr", "categories": ["productivity", "utilities", "finance"], "screenshots": [{"src": "/icons/screenshot-mobile.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow"}, {"src": "/icons/screenshot-desktop.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide"}], "icons": [{"src": "/icons/icon-48x48.png", "sizes": "48x48", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/icons/icon-256x256.png", "sizes": "256x256", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "shortcuts": [{"name": "Dashboard", "short_name": "Dashboard", "description": "View your energy dashboard", "url": "/dashboard", "icons": [{"src": "/icons/shortcut-dashboard.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Wallet", "short_name": "Wallet", "description": "Check your UMatter balance", "url": "/wallet", "icons": [{"src": "/icons/shortcut-wallet.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Energy Hub", "short_name": "Energy Hub", "description": "Monitor real-time energy generation", "url": "/energy-hub", "icons": [{"src": "/icons/shortcut-energy.png", "sizes": "96x96", "type": "image/png"}]}], "share_target": {"action": "/share", "method": "POST", "enctype": "multipart/form-data", "params": {"title": "title", "text": "text", "url": "url"}}, "protocol_handlers": [{"protocol": "web+nuuniverse", "url": "/?action=%s"}], "prefer_related_applications": false, "related_applications": [], "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "capture_links": "new-client", "file_handlers": [{"action": "/import", "accept": {"application/json": [".json"], "text/csv": [".csv"]}}]}