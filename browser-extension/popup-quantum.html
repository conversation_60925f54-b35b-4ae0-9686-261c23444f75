<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    @import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;600;700&display=swap');
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      width: 380px;
      height: 600px;
      font-family: 'JetBrains Mono', monospace;
      background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
      color: #ffffff;
      overflow: hidden;
      position: relative;
    }
    
    /* Animated background particles */
    .bg-particles {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: 0;
    }
    
    .particle {
      position: absolute;
      width: 2px;
      height: 2px;
      background: rgba(0, 255, 255, 0.6);
      border-radius: 50%;
      animation: float 6s infinite linear;
    }
    
    @keyframes float {
      0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
      10% { opacity: 1; }
      90% { opacity: 1; }
      100% { transform: translateY(-10px) rotate(360deg); opacity: 0; }
    }
    
    .content {
      position: relative;
      z-index: 1;
      padding: 20px;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    
    .header {
      text-align: center;
      margin-bottom: 25px;
      padding-bottom: 20px;
      border-bottom: 1px solid rgba(0, 255, 255, 0.3);
    }
    
    .logo {
      font-size: 28px;
      font-weight: 700;
      background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00);
      background-size: 200% 200%;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: gradient-shift 3s ease-in-out infinite;
      margin-bottom: 8px;
      letter-spacing: 2px;
    }
    
    @keyframes gradient-shift {
      0%, 100% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
    }
    
    .subtitle {
      font-size: 11px;
      color: rgba(0, 255, 255, 0.8);
      text-transform: uppercase;
      letter-spacing: 2px;
      font-weight: 600;
    }
    
    .quantum-status {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 18px;
      background: linear-gradient(135deg, rgba(0, 255, 255, 0.15), rgba(255, 0, 255, 0.15));
      border: 1px solid rgba(0, 255, 255, 0.4);
      border-radius: 12px;
      margin-bottom: 25px;
      backdrop-filter: blur(10px);
    }
    
    .status-text {
      font-size: 13px;
      font-weight: 600;
      color: #00ffff;
    }
    
    .quantum-indicator {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      background: radial-gradient(circle, #00ff00, #00cc00);
      box-shadow: 0 0 15px #00ff00, 0 0 30px rgba(0, 255, 0, 0.3);
      animation: quantum-pulse 1.5s infinite;
    }
    
    @keyframes quantum-pulse {
      0%, 100% { 
        transform: scale(1); 
        box-shadow: 0 0 15px #00ff00, 0 0 30px rgba(0, 255, 0, 0.3);
      }
      50% { 
        transform: scale(1.2); 
        box-shadow: 0 0 25px #00ff00, 0 0 50px rgba(0, 255, 0, 0.6);
      }
    }
    
    .metrics-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin-bottom: 25px;
      flex-grow: 1;
    }
    
    .metric-card {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
      border: 1px solid rgba(255, 255, 255, 0.15);
      border-radius: 12px;
      padding: 18px 12px;
      text-align: center;
      backdrop-filter: blur(5px);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      cursor: pointer;
    }
    
    .metric-card.clickable {
      transform: translateZ(0);
    }
    
    .metric-card .click-hint {
      position: absolute;
      bottom: 5px;
      left: 50%;
      transform: translateX(-50%);
      font-size: 8px;
      color: rgba(0, 255, 255, 0.6);
      opacity: 0;
      transition: opacity 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-weight: 600;
    }
    
    .metric-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00ffff, transparent);
      transition: left 0.5s ease;
    }
    
    .metric-card:hover::before {
      left: 100%;
    }
    
    .metric-card:hover {
      transform: translateY(-3px) scale(1.02);
      border-color: rgba(0, 255, 255, 0.5);
      box-shadow: 0 8px 25px rgba(0, 255, 255, 0.2);
      background: linear-gradient(135deg, rgba(0, 255, 255, 0.15), rgba(255, 0, 255, 0.1));
    }
    
    .metric-card:hover .click-hint {
      opacity: 1;
    }
    
    .metric-card:active {
      transform: translateY(-1px) scale(0.98);
      box-shadow: 0 4px 15px rgba(0, 255, 255, 0.4);
    }
    
    .metric-value {
      font-size: 18px;
      font-weight: 700;
      color: #00ffff;
      margin-bottom: 8px;
      font-family: 'JetBrains Mono', monospace;
      text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    }
    
    .metric-label {
      font-size: 10px;
      color: rgba(255, 255, 255, 0.7);
      text-transform: uppercase;
      letter-spacing: 1px;
      font-weight: 600;
    }
    
    .actions {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-bottom: 20px;
    }
    
    .quantum-btn {
      padding: 15px 20px;
      border: none;
      border-radius: 10px;
      font-size: 13px;
      font-weight: 600;
      font-family: 'JetBrains Mono', monospace;
      cursor: pointer;
      transition: all 0.3s ease;
      text-transform: uppercase;
      letter-spacing: 1px;
      position: relative;
      overflow: hidden;
    }
    
    .quantum-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s ease;
    }
    
    .quantum-btn:hover::before {
      left: 100%;
    }
    
    .btn-primary {
      background: linear-gradient(45deg, #00ffff, #0080ff, #8000ff);
      color: #000;
      border: 1px solid rgba(0, 255, 255, 0.5);
      text-shadow: none;
    }
    
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 255, 255, 0.4);
      background: linear-gradient(45deg, #20ffff, #2090ff, #a020ff);
    }
    
    .btn-secondary {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
      color: #fff;
      border: 1px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(5px);
    }
    
    .btn-secondary:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
      border-color: rgba(0, 255, 255, 0.5);
      box-shadow: 0 5px 15px rgba(0, 255, 255, 0.2);
    }
    
    .btn-tertiary {
      background: linear-gradient(45deg, #ff00ff, #8000ff, #4000ff);
      color: #fff;
      border: 1px solid rgba(255, 0, 255, 0.5);
    }
    
    .btn-tertiary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(255, 0, 255, 0.4);
      background: linear-gradient(45deg, #ff20ff, #a020ff, #6020ff);
    }
    
    .footer {
      text-align: center;
      font-size: 9px;
      color: rgba(255, 255, 255, 0.5);
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      padding-top: 15px;
    }
    
    .footer::before {
      content: '⚡ ';
      color: #00ffff;
    }
    
    .footer::after {
      content: ' ⚡';
      color: #ff00ff;
    }
    
    /* Real-time data animation */
    .updating {
      animation: data-update 0.5s ease-in-out;
    }
    
    @keyframes data-update {
      0% { color: #00ffff; transform: scale(1); }
      50% { color: #ffff00; transform: scale(1.1); }
      100% { color: #00ffff; transform: scale(1); }
    }
    
    /* Glitch effect for logo */
    .logo:hover {
      animation: glitch 0.5s ease-in-out;
    }
    
    @keyframes glitch {
      0%, 100% { transform: translate(0); }
      20% { transform: translate(-2px, 2px); }
      40% { transform: translate(-2px, -2px); }
      60% { transform: translate(2px, 2px); }
      80% { transform: translate(2px, -2px); }
    }
    
    /* Click animation for tiles */
    @keyframes clickPulse {
      0% { transform: scale(1); }
      50% { transform: scale(0.95); box-shadow: 0 0 30px rgba(0, 255, 255, 0.8); }
      100% { transform: scale(1); }
    }
  </style>
</head>
<body>
  <div class="bg-particles"></div>
  
  <div class="content">
    <div class="header">
      <div class="logo">nU UNIVERSE</div>
      <div class="subtitle">QUANTUM ENERGY PLATFORM</div>
    </div>
    
    <div class="quantum-status">
      <div class="status-text">QUANTUM SYNC ACTIVE</div>
      <div class="quantum-indicator"></div>
    </div>
    
    <div class="metrics-grid">
      <div class="metric-card clickable" data-action="umatter" data-feature="UMatter Wallet">
        <div class="metric-value" id="umatter-count">0.000000</div>
        <div class="metric-label">UMatter Generated</div>
        <div class="click-hint">View Wallet</div>
      </div>
      <div class="metric-card clickable" data-action="energy" data-feature="Energy Dashboard">
        <div class="metric-value" id="battery-level">100%</div>
        <div class="metric-label">Battery Power</div>
        <div class="click-hint">Energy Flow</div>
      </div>
      <div class="metric-card clickable" data-action="ads" data-feature="Ad Harvesting">
        <div class="metric-value" id="ads-blocked">0</div>
        <div class="metric-label">Ads Harvested</div>
        <div class="click-hint">View Harvest</div>
      </div>
      <div class="metric-card clickable" data-action="marketplace" data-feature="Data Marketplace">
        <div class="metric-value" id="earnings">$0.00</div>
        <div class="metric-label">Total Value</div>
        <div class="click-hint">Marketplace</div>
      </div>
      <div class="metric-card clickable" data-action="network" data-feature="Network Analytics">
        <div class="metric-value" id="network-speed">10</div>
        <div class="metric-label">Network Mbps</div>
        <div class="click-hint">Analytics</div>
      </div>
      <div class="metric-card clickable" data-action="quantum" data-feature="Quantum Lab">
        <div class="metric-value" id="quantum-efficiency">98.3%</div>
        <div class="metric-label">Quantum Fidelity</div>
        <div class="click-hint">Q-Lab</div>
      </div>
    </div>
    
    <div class="actions">
      <button class="quantum-btn btn-primary" id="sync-now">⚡ QUANTUM SYNC ⚡</button>
      <button class="quantum-btn btn-secondary" id="view-dashboard">🚀 LAUNCH DASHBOARD 🚀</button>
      <button class="quantum-btn btn-tertiary" id="ai-assist">🤖 AI ASSISTANT 🤖</button>
    </div>
    
    <div class="footer">
      nU QUANTUM PHYSICS ENGINE
    </div>
  </div>
  
  <script src="popup-quantum-complete.js"></script>
</body>
</html>