/**
 * nU Universe Extension - Content Script
 * Monitors page interactions and generates UMatter
 */

(function() {
  'use strict';

  console.log('[nU Universe Content] Script loaded on:', window.location.href);

  // Track page interactions for UMatter generation
  let interactionCount = 0;
  let sessionId = 'content_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

  // Send interaction to background script
  function sendInteraction(type, data) {
    try {
      chrome.runtime.sendMessage({
        action: 'generateUMatter',
        type: type,
        data: {
          ...data,
          url: window.location.href,
          timestamp: Date.now(),
          sessionId: sessionId
        }
      });
    } catch (error) {
      console.log('[nU Universe Content] Message send failed:', error);
    }
  }

  // Monitor clicks
  document.addEventListener('click', function(event) {
    interactionCount++;
    sendInteraction('click', {
      target: event.target.tagName,
      x: event.clientX,
      y: event.clientY
    });
  });

  // Monitor scrolling
  let scrollTimeout;
  window.addEventListener('scroll', function() {
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(() => {
      interactionCount++;
      sendInteraction('scroll', {
        scrollY: window.scrollY,
        scrollX: window.scrollX
      });
    }, 500);
  });

  // Monitor form submissions
  document.addEventListener('submit', function(event) {
    interactionCount++;
    sendInteraction('form_submit', {
      formAction: event.target.action || 'unknown',
      formMethod: event.target.method || 'unknown'
    });
  });

  // Monitor page visibility changes
  document.addEventListener('visibilitychange', function() {
    sendInteraction('visibility_change', {
      hidden: document.hidden,
      visibilityState: document.visibilityState
    });
  });

  // Send initial page load interaction
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      sendInteraction('page_load', {
        readyState: document.readyState,
        referrer: document.referrer
      });
    });
  } else {
    sendInteraction('page_load', {
      readyState: document.readyState,
      referrer: document.referrer
    });
  }

  console.log('[nU Universe Content] Monitoring started for session:', sessionId);
})();