// nU Universe Extension - Quantum Energy Popup
console.log('[nU Extension] 🚀 Initializing Quantum Interface...');

const SERVER_URL = 'http://localhost:5000';

let quantumMetrics = {
  umatter: 0,
  earnings: 0,
  adsHarvested: 0,
  batteryLevel: 100,
  networkSpeed: 0,
  quantumFidelity: 98.3
};

// Feature routing configuration
const FEATURE_ROUTES = {
  umatter: '/?tab=wallet',
  energy: '/?tab=energy',
  ads: '/?tab=harvesting', 
  marketplace: '/?tab=marketplace',
  network: '/?tab=analytics',
  quantum: '/?tab=quantum',
  dashboard: '/',
  ai: '/?tab=ai'
};

// Animated background particles
function initializeParticles() {
  const particleContainer = document.querySelector('.bg-particles');
  const particleCount = 15;
  
  for (let i = 0; i < particleCount; i++) {
    const particle = document.createElement('div');
    particle.className = 'particle';
    particle.style.left = Math.random() * 100 + '%';
    particle.style.animationDelay = Math.random() * 6 + 's';
    particle.style.animationDuration = (Math.random() * 3 + 4) + 's';
    particleContainer.appendChild(particle);
  }
}

// Update metrics with quantum animations
function updateQuantumMetrics() {
  const elements = {
    'umatter-count': quantumMetrics.umatter.toFixed(6),
    'battery-level': `${quantumMetrics.batteryLevel}%`,
    'ads-blocked': quantumMetrics.adsHarvested,
    'earnings': `$${quantumMetrics.earnings.toFixed(2)}`,
    'network-speed': quantumMetrics.networkSpeed,
    'quantum-efficiency': `${quantumMetrics.quantumFidelity.toFixed(1)}%`
  };
  
  Object.entries(elements).forEach(([id, value]) => {
    const element = document.getElementById(id);
    if (element) {
      // Add updating animation
      element.classList.add('updating');
      element.textContent = value;
      
      setTimeout(() => {
        element.classList.remove('updating');
      }, 500);
    }
  });
  
  console.log('[nU Extension] ⚡ Quantum metrics updated:', quantumMetrics);
}

// Load authentic device data
async function loadQuantumData() {
  try {
    // Get real battery data
    if ('getBattery' in navigator) {
      const battery = await navigator.getBattery();
      quantumMetrics.batteryLevel = Math.round(battery.level * 100);
    }
    
    // Get network speed
    if ('connection' in navigator) {
      quantumMetrics.networkSpeed = navigator.connection.downlink || 0;
    }
    
    // Load stored quantum data
    chrome.storage.local.get(['quantumMetrics'], (result) => {
      if (result.quantumMetrics) {
        quantumMetrics = { ...quantumMetrics, ...result.quantumMetrics };
      }
      updateQuantumMetrics();
    });
    
  } catch (error) {
    console.log('[nU Extension] ⚠️ Quantum data loading error:', error);
  }
}

// Simulate real-time UMatter generation
function simulateQuantumGeneration() {
  const baseRate = 0.000423; // Base UMatter generation rate
  const batteryFactor = quantumMetrics.batteryLevel / 100;
  const networkFactor = Math.min(quantumMetrics.networkSpeed / 10, 1);
  
  const generatedUMatter = baseRate * batteryFactor * (0.7 + networkFactor * 0.3);
  
  quantumMetrics.umatter += generatedUMatter;
  quantumMetrics.earnings = quantumMetrics.umatter * 0.01; // $0.01 per UMatter
  
  // Add some quantum uncertainty
  quantumMetrics.quantumFidelity = 98.3 + (Math.random() - 0.5) * 0.6;
  
  updateQuantumMetrics();
  
  // Store updated metrics
  chrome.storage.local.set({ quantumMetrics });
}

// Quantum sync to main application
function performQuantumSync() {
  console.log('[nU Extension] 🌀 Initiating quantum sync...');
  
  const syncButton = document.getElementById('sync-now');
  const originalText = syncButton.textContent;
  
  // Visual feedback
  syncButton.textContent = '🌀 SYNCING...';
  syncButton.style.background = 'linear-gradient(45deg, #ff00ff, #00ffff)';
  
  const quantumData = {
    type: 'quantum_sync',
    metrics: quantumMetrics,
    timestamp: Date.now(),
    source: 'nU_extension_quantum_interface'
  };
  
  // Send to all tabs running nU Universe
  chrome.tabs.query({}, (tabs) => {
    tabs.forEach(tab => {
      if (tab.url && tab.url.includes('localhost:5000')) {
        chrome.tabs.sendMessage(tab.id, quantumData, (response) => {
          if (response && response.success) {
            console.log('[nU Extension] ✅ Quantum sync successful to tab:', tab.id);
          }
        });
      }
    });
  });
  
  // Reset button after sync
  setTimeout(() => {
    syncButton.textContent = originalText;
    syncButton.style.background = '';
    
    // Add success glow effect
    syncButton.style.boxShadow = '0 0 20px #00ff00';
    setTimeout(() => {
      syncButton.style.boxShadow = '';
    }, 1000);
    
  }, 2000);
  
  // Store sync timestamp
  chrome.storage.local.set({ 
    quantumMetrics,
    lastQuantumSync: Date.now() 
  });
}

// Launch quantum dashboard
function launchQuantumDashboard() {
  console.log('[nU Extension] 🚀 Launching quantum dashboard...');
  
  const dashboardButton = document.getElementById('view-dashboard');
  const originalText = dashboardButton.textContent;
  
  dashboardButton.textContent = '🚀 LAUNCHING...';
  dashboardButton.style.background = 'linear-gradient(45deg, #ffff00, #ff00ff)';
  
  // Open dashboard with quantum parameters
  const dashboardUrl = 'http://localhost:5000?quantum_source=extension&sync_time=' + Date.now();
  chrome.tabs.create({ url: dashboardUrl });
  
  setTimeout(() => {
    dashboardButton.textContent = originalText;
    dashboardButton.style.background = '';
  }, 1500);
}

// Initialize quantum interface
document.addEventListener('DOMContentLoaded', () => {
  console.log('[nU Extension] 🌟 Quantum interface initialized');
  
  // Initialize visual effects
  initializeParticles();
  
  // Load initial data
  loadQuantumData();
  
  // Set up event listeners
  document.getElementById('sync-now').addEventListener('click', performQuantumSync);
  document.getElementById('view-dashboard').addEventListener('click', launchQuantumDashboard);
  
  // Real-time quantum generation
  setInterval(simulateQuantumGeneration, 2000);
  
  // Periodic data refresh
  setInterval(loadQuantumData, 10000);
  
  // Add hover effects to metric cards
  document.querySelectorAll('.metric-card').forEach(card => {
    card.addEventListener('mouseenter', () => {
      card.style.transform = 'translateY(-5px) rotateX(5deg)';
    });
    
    card.addEventListener('mouseleave', () => {
      card.style.transform = '';
    });
  });
});

// Listen for quantum messages from background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'quantum_metrics_update') {
    quantumMetrics = { ...quantumMetrics, ...message.metrics };
    updateQuantumMetrics();
    sendResponse({ success: true, quantum_state: 'synchronized' });
  }
  
  if (message.type === 'ad_harvested') {
    quantumMetrics.adsHarvested++;
    quantumMetrics.umatter += 0.001; // Bonus UMatter for ad harvesting
    updateQuantumMetrics();
    
    // Visual feedback for ad harvesting
    const indicator = document.querySelector('.quantum-indicator');
    indicator.style.background = 'radial-gradient(circle, #ffff00, #ff8800)';
    setTimeout(() => {
      indicator.style.background = '';
    }, 1000);
    
    sendResponse({ success: true });
  }
});