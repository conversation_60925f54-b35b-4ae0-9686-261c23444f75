# nU Universe Browser Extension - Installation Guide

## Overview
The nU Universe browser extension enables real-time UMatter generation from web interactions, ad interception, and seamless synchronization with your nU Universe account.

## Features
- Real-time UMatter generation from web browsing
- Privacy-focused ad interception and blocking
- Automatic synchronization with nU Universe server
- Cross-browser compatibility (Chrome, Edge, Firefox)
- Lightweight (22.7KB) with minimal performance impact

## Installation Instructions

### Step 1: Download the Extension
1. Download the extension package: `nu-universe-extension.tar.gz`
2. Extract the contents to a folder on your computer

### Step 2: Enable Developer Mode

#### For Chrome/Chromium:
1. Open Chrome and navigate to `chrome://extensions/`
2. Toggle "Developer mode" ON (top-right corner)
3. Click "Load unpacked"
4. Select the extracted extension folder
5. The nU Universe extension should now appear in your extensions list

#### For Edge:
1. Open Edge and navigate to `edge://extensions/`
2. Toggle "Developer mode" ON (left sidebar)
3. Click "Load unpacked"
4. Select the extracted extension folder

#### For Firefox:
1. Open Firefox and navigate to `about:debugging`
2. Click "This Firefox"
3. Click "Load Temporary Add-on"
4. Select the `manifest.json` file from the extracted folder

### Step 3: Configuration
1. Click the nU Universe extension icon in your browser toolbar
2. The extension will automatically attempt to connect to your local nU Universe server
3. Ensure your nU Universe application is running (typically at http://localhost:5000)
4. Once connected, you'll see real-time metrics in the extension popup

## Troubleshooting

### Extension Not Connecting
- Ensure the nU Universe application is running
- Check that your server is accessible at http://localhost:5000
- Try refreshing the extension or reloading it

### Permission Issues
- Make sure you granted all necessary permissions during installation
- Check browser security settings if the extension fails to load

### Server Communication
- The extension communicates with your local nU Universe server
- No external servers or third-party services are contacted
- All data remains on your local machine

## Privacy & Security
- The extension only communicates with your local nU Universe server
- No browsing data is sent to external servers
- Ad interception happens locally for privacy protection
- All UMatter generation is calculated locally

## Uninstallation
To remove the extension:
1. Go to your browser's extensions page
2. Find "nU Universe" in the list
3. Click "Remove" or the trash icon
4. Confirm removal

## Support
If you encounter issues:
1. Check the browser console for error messages
2. Verify the nU Universe application is running
3. Try reloading the extension
4. Restart your browser if problems persist

## Version: 3.0.0
Last updated: June 25, 2025