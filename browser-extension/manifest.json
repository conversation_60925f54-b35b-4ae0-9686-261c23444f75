{"manifest_version": 3, "name": "nU Universe Extension", "version": "3.0.0", "description": "Official nU Universe browser extension for UMatter generation, SpUnder web tracking, and quantum-like processing", "permissions": ["storage", "activeTab", "tabs", "declarativeNetRequest", "declarativeNetRequestWithHostAccess", "scripting", "webNavigation", "cookies", "notifications"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_start", "all_frames": true}], "action": {"default_popup": "popup-quantum.html", "default_title": "nU Universe", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "declarative_net_request": {"rule_resources": [{"id": "ad_blocking_rules", "enabled": true, "path": "rules.json"}]}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self';"}, "web_accessible_resources": [{"resources": ["icons/*.png"], "matches": ["<all_urls>"]}]}