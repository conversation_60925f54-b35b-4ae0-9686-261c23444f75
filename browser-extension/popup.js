/**
 * nU Universe Extension - Popup Script
 * Displays extension status and UMatter balance
 */

document.addEventListener('DOMContentLoaded', function() {
  console.log('[nU Universe Popup] Loaded');

  // Get extension status from background script
  chrome.runtime.sendMessage({
    action: 'getStatus'
  }, function(response) {
    if (chrome.runtime.lastError) {
      console.error('[nU Universe Popup] Error:', chrome.runtime.lastError);
      updateUI({
        connected: false,
        umatterBalance: 0,
        version: 'Unknown'
      });
      return;
    }

    updateUI(response);
  });

  function updateUI(status) {
    // Update connection status
    const connectionEl = document.getElementById('connection-status');
    if (connectionEl) {
      connectionEl.textContent = status.connected ? 'Connected' : 'Disconnected';
      connectionEl.className = status.connected ? 'status connected' : 'status disconnected';
    }

    // Update UMatter balance
    const balanceEl = document.getElementById('umatter-balance');
    if (balanceEl) {
      balanceEl.textContent = status.umatterBalance ? status.umatterBalance.toFixed(3) : '0.000';
    }

    // Update version
    const versionEl = document.getElementById('version');
    if (versionEl) {
      versionEl.textContent = status.version || '3.0.0';
    }
  }

  // Handle open dashboard button
  const openDashboardBtn = document.getElementById('open-dashboard');
  if (openDashboardBtn) {
    openDashboardBtn.addEventListener('click', function() {
      chrome.tabs.create({
        url: 'http://localhost:5000'
      });
    });
  }
});