// nU Universe Browser Extension - Safe Background Script
// Enhanced error handling and safe DOM manipulation

console.log('[nU Universe] Extension loading...');

const BACKEND_URL = 'http://localhost:5000';
let adInterceptionCount = 0;
let totalUMatter = 0;
let connectionStatus = false;

// Enhanced ad detection patterns
const AD_PATTERNS = [
  /googleads\.g\.doubleclick\.net/,
  /googlesyndication\.com/,
  /googletagmanager\.com/,
  /google-analytics\.com/,
  /facebook\.com\/tr/,
  /connect\.facebook\.net/,
  /amazon-adsystem\.com/,
  /doubleclick\.net/,
  /adsystem\.com/,
  /advertising\.com/,
  /\/ads\//,
  /\/advertisement/,
  /\/tracking/,
  /\/analytics/,
  /\/pixel/
];

// Safe extension environment check
function isExtensionEnvironment() {
  return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id;
}

// Create notification element with error handling
function createNotificationElement() {
  try {
    if (!document || !document.createElement) return null;
    
    const notificationDiv = document.createElement('div');
    notificationDiv.id = 'nu-extension-notification';
    notificationDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #1a1a1a;
      color: #00ff88;
      padding: 12px 16px;
      border-radius: 8px;
      border: 1px solid #00ff88;
      z-index: 999999;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      box-shadow: 0 4px 12px rgba(0, 255, 136, 0.3);
      transition: all 0.3s ease;
    `;
    
    // Add styles safely
    if (document.head && !document.getElementById('nu-extension-styles')) {
      const style = document.createElement('style');
      style.id = 'nu-extension-styles';
      style.textContent = `
        @keyframes nuSlideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        #nu-extension-notification { animation: nuSlideIn 0.3s ease-out; }
      `;
      document.head.appendChild(style);
    }
    
    return notificationDiv;
  } catch (error) {
    console.log('[nU Extension] Notification creation failed:', error);
    return null;
  }
}

// Safe notification display
function showNotification(message) {
  try {
    if (!document || !document.body) {
      console.log('[nU Extension] Document not ready for notification');
      return;
    }
    
    const notification = createNotificationElement();
    if (!notification) return;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
      try {
        if (notification && notification.parentNode) {
          notification.style.opacity = '0';
          setTimeout(() => {
            if (notification.parentNode) {
              notification.parentNode.removeChild(notification);
            }
          }, 300);
        }
      } catch (error) {
        console.log('[nU Extension] Error removing notification:', error);
      }
    }, 3000);
  } catch (error) {
    console.log('[nU Extension] Notification display error:', error);
  }
}

// Check if URL matches ad patterns
function isAdRequest(url) {
  try {
    return AD_PATTERNS.some(pattern => pattern.test(url));
  } catch (error) {
    console.log('[nU Extension] URL pattern check error:', error);
    return false;
  }
}

// Generate UMatter for ad interception
function generateUMatter() {
  return (Math.random() * 0.2 + 0.1).toFixed(6);
}

// Send ad interception to backend
async function reportAdInterception(url) {
  try {
    const umatterGenerated = parseFloat(generateUMatter());
    
    const response = await fetch(`${BACKEND_URL}/api/web-ads/intercept`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        url: url,
        timestamp: Date.now(),
        umatterGenerated: umatterGenerated,
        domain: new URL(url).hostname
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      adInterceptionCount++;
      totalUMatter += umatterGenerated;
      
      console.log(`[Extension] Ad intercepted: ${new URL(url).hostname} UMatter: ${umatterGenerated}`);
      showNotification(`🛡️ Blocked ${new URL(url).hostname} (+${umatterGenerated} UMatter)`);
      
      return data;
    } else {
      console.log('[nU Extension] Failed to report ad interception:', response.status);
    }
  } catch (error) {
    console.log('[nU Extension] Ad reporting error:', error);
  }
}

// Connect to backend with fallback handling
async function connectToBackend() {
  try {
    const payload = {
      extensionId: isExtensionEnvironment() ? chrome.runtime.id : 'web-fallback',
      version: isExtensionEnvironment() && chrome.runtime.getManifest ? 
        chrome.runtime.getManifest().version : '1.0.0',
      timestamp: Date.now()
    };
    
    const response = await fetch(`${BACKEND_URL}/api/extension/connect`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload)
    });
    
    if (response.ok) {
      const data = await response.json();
      connectionStatus = true;
      console.log('[nU Universe] Connected to backend:', data);
      return true;
    } else {
      console.log('[nU Universe] Backend connection failed:', response.status);
      connectionStatus = false;
      return false;
    }
  } catch (error) {
    console.log('[nU Universe] Backend connection error:', error);
    connectionStatus = false;
    return false;
  }
}

// Sync extension data with backend
async function syncWithBackend() {
  try {
    const response = await fetch(`${BACKEND_URL}/api/web-ads/recent`);
    if (response.ok) {
      const data = await response.json();
      console.log('[nU Extension] Synced with backend:', data);
    }
  } catch (error) {
    console.log('[nU Extension] Sync error:', error);
  }
}

// Start monitoring for ad requests
function startAdMonitoring() {
  try {
    // Monitor fetch requests
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
      const url = args[0];
      if (typeof url === 'string' && isAdRequest(url)) {
        reportAdInterception(url);
      }
      return originalFetch.apply(this, args);
    };
    
    // Monitor XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, ...rest) {
      if (typeof url === 'string' && isAdRequest(url)) {
        reportAdInterception(url);
      }
      return originalXHROpen.apply(this, [method, url, ...rest]);
    };
    
    // Monitor dynamic script/image loading
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1) { // Element node
            const src = node.src || node.href;
            if (src && isAdRequest(src)) {
              reportAdInterception(src);
            }
          }
        });
      });
    });
    
    if (document.body) {
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    }
    
    console.log('[nU Universe] Ad monitoring started');
  } catch (error) {
    console.log('[nU Extension] Ad monitoring setup error:', error);
  }
}

// Initialize extension safely
function initializeExtension() {
  try {
    console.log('[nU Universe] Extension initializing...');
    
    connectToBackend();
    startAdMonitoring();
    
    // Set up periodic sync
    setInterval(syncWithBackend, 30000);
    
    // Simulate some ad blocking for demo
    setTimeout(() => {
      const demoAds = [
        'https://googletagmanager.com/gtag/js',
        'https://amazon-adsystem.com/widgets.js',
        'https://doubleclick.net/gampad/ads'
      ];
      
      demoAds.forEach((url, index) => {
        setTimeout(() => reportAdInterception(url), index * 12000);
      });
    }, 5000);
    
    console.log('[nU Universe] Extension initialized successfully');
  } catch (error) {
    console.log('[nU Universe] Extension initialization error:', error);
  }
}

// Safe initialization with retries
function safeInitialize() {
  try {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeExtension);
    } else {
      initializeExtension();
    }
  } catch (error) {
    console.log('[nU Extension] Initialization error, retrying:', error);
    setTimeout(() => {
      try {
        initializeExtension();
      } catch (retryError) {
        console.log('[nU Extension] Retry failed:', retryError);
      }
    }, 1000);
  }
}

// Start the extension
safeInitialize();