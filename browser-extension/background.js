// nU Universe Extension - Background Service Worker
// Real server connection and UMatter generation

console.log('[nU Extension Background] Starting service worker...');

class NUExtensionBackground {
  constructor() {
    this.serverUrl = 'http://localhost:5000';
    this.isConnected = false;
    this.extensionId = null;
    this.umatterBalance = 0;
    this.lastSync = 0;
    this.init();
  }

  async init() {
    console.log('[nU Background] Initializing background service...');
    
    // Generate unique extension ID
    this.extensionId = await this.getOrCreateExtensionId();
    
    // Try to connect to server
    await this.connectToServer();
    
    // Start periodic sync
    this.startPeriodicSync();
    
    // Setup message handlers
    this.setupMessageHandlers();
    
    console.log('[nU Background] Background service initialized');
  }

  async getOrCreateExtensionId() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['extensionId'], (result) => {
        if (result.extensionId) {
          resolve(result.extensionId);
        } else {
          const newId = 'ext_' + Math.random().toString(36).substr(2, 16);
          chrome.storage.local.set({ extensionId: newId }, () => {
            resolve(newId);
          });
        }
      });
    });
  }

  async connectToServer() {
    try {
      console.log('[nU Background] Connecting to server:', this.serverUrl);
      console.log('[nU Background] Attempting to connect to server:', this.serverUrl);
      
      const response = await fetch(`${this.serverUrl}/api/extension/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          extensionId: this.extensionId,
          version: chrome.runtime.getManifest().version,
          timestamp: Date.now()
        })
      });

      if (response.ok) {
        const data = await response.json();
        this.isConnected = true;
        this.umatterBalance = data.balance || 0;
        console.log('[nU Background] ✅ Connected to nU Universe server successfully');
        console.log('[nU Background] Extension ID:', this.extensionId);
        console.log('[nU Background] UMatter balance:', this.umatterBalance);
        
        // Store connection status
        chrome.storage.local.set({ 
          isConnected: true, 
          lastConnection: Date.now(),
          umatterBalance: this.umatterBalance
        });
        
        return true;
      } else {
        console.log('[nU Background] Server connection failed:', response.status);
        this.isConnected = false;
        return false;
      }
    } catch (error) {
      console.log('[nU Background] Connection error:', error.message);
      this.isConnected = false;
      return false;
    }
  }

  async generateUMatter() {
    if (!this.isConnected) {
      console.log('[nU Background] Not connected, skipping UMatter generation');
      return 0;
    }

    try {
      // Get system metrics for authentic energy calculation
      const metrics = await this.collectSystemMetrics();
      
      const response = await fetch(`${this.serverUrl}/api/extension/sync`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          extensionId: this.extensionId,
          source: 'browser_extension',
          metrics: metrics,
          timestamp: Date.now()
        })
      });

      if (response.ok) {
        const data = await response.json();
        const serverMetrics = data.serverMetrics || {};
        
        // Update with authentic server data
        this.umatterBalance = serverMetrics.umatter || this.umatterBalance;
        
        console.log('[nU Background] ✅ Server sync successful:', {
          umatter: this.umatterBalance.toFixed(6),
          connected: data.connected
        });
        
        // Store authentic metrics from server
        chrome.storage.local.set({ 
          umatterBalance: this.umatterBalance,
          batteryLevel: serverMetrics.batteryLevel || 100,
          networkSpeed: serverMetrics.networkSpeed || 0,
          quantumFidelity: serverMetrics.quantumFidelity || 98.5,
          adsHarvested: serverMetrics.adsHarvested || 0,
          earnings: serverMetrics.earnings || 0,
          lastServerSync: Date.now(),
          serverConnected: data.connected
        });
        
        return this.umatterBalance;
      } else {
        console.log('[nU Background] UMatter generation failed:', response.status);
        return 0;
      }
    } catch (error) {
      console.log('[nU Background] UMatter generation error:', error.message);
      return 0;
    }
  }

  async collectSystemMetrics() {
    const metrics = {
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      memory: performance.memory ? {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        limit: performance.memory.jsHeapSizeLimit
      } : null,
      connection: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt
      } : null,
      battery: null
    };

    // Try to get battery info if available
    if ('getBattery' in navigator) {
      try {
        const battery = await navigator.getBattery();
        metrics.battery = {
          level: Math.round(battery.level * 100),
          charging: battery.charging
        };
      } catch (e) {
        console.log('[nU Background] Battery API not available');
      }
    }

    return metrics;
  }

  startPeriodicSync() {
    // Generate UMatter every 30 seconds
    setInterval(() => {
      this.generateUMatter();
    }, 30000);

    // Reconnect check every 5 minutes
    setInterval(() => {
      if (!this.isConnected) {
        this.connectToServer();
      }
    }, 5 * 60 * 1000);
  }

  setupMessageHandlers() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      console.log('[nU Background] Received message:', request);
      
      switch (request.action) {
        case 'getStatus':
          sendResponse({
            isConnected: this.isConnected,
            umatterBalance: this.umatterBalance,
            extensionId: this.extensionId,
            lastSync: this.lastSync
          });
          break;
          
        case 'forceSync':
          this.generateUMatter().then(generated => {
            sendResponse({ success: true, generated });
          }).catch(error => {
            sendResponse({ success: false, error: error.message });
          });
          return true; // Will respond asynchronously
          
        case 'openDashboard':
          chrome.tabs.create({ url: this.serverUrl });
          sendResponse({ success: true });
          break;
          
        default:
          sendResponse({ error: 'Unknown action' });
      }
    });
  }
}

// Initialize background service
const nuExtension = new NUExtensionBackground();

// Handle extension install/update
chrome.runtime.onInstalled.addListener((details) => {
  console.log('[nU Background] Extension installed/updated:', details.reason);
  
  if (details.reason === 'install') {
    // Show welcome notification
    chrome.notifications.create({
      type: 'basic',
      iconUrl: 'icons/icon48.png',
      title: 'nU Universe Extension Installed',
      message: 'Start generating UMatter tokens from your browsing energy!'
    });
  }
});

// Handle tab updates for energy harvesting
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // Generate small amount of UMatter for page visits
    nuExtension.generateUMatter();
  }
});