
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>nU Universe Extension</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      width: 350px;
      height: 500px;
      background: linear-gradient(135deg, #0a0a0a, #1a1a2e);
      color: white;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      overflow: hidden;
    }
    
    .header {
      background: linear-gradient(135deg, #00FF88, #0099FF);
      padding: 15px;
      text-align: center;
      position: relative;
    }
    
    .header h1 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 5px;
    }
    
    .header .version {
      font-size: 11px;
      opacity: 0.8;
    }
    
    .status-indicator {
      position: absolute;
      top: 15px;
      right: 15px;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #00FF88;
      animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 1; transform: scale(1); }
      50% { opacity: 0.7; transform: scale(1.2); }
    }
    
    .content {
      padding: 20px;
    }
    
    .metric {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 10px;
      padding: 15px;
      margin-bottom: 15px;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .metric-label {
      font-size: 12px;
      color: #888;
      margin-bottom: 5px;
    }
    
    .metric-value {
      font-size: 20px;
      font-weight: 600;
      color: #00FF88;
    }
    
    .metric-unit {
      font-size: 12px;
      color: #ccc;
      margin-left: 5px;
    }
    
    .actions {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
      margin-top: 20px;
    }
    
    .btn {
      background: rgba(0, 255, 136, 0.1);
      border: 1px solid #00FF88;
      color: #00FF88;
      padding: 10px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.3s ease;
    }
    
    .btn:hover {
      background: rgba(0, 255, 136, 0.2);
      transform: translateY(-1px);
    }
    
    .btn-primary {
      background: #00FF88;
      color: #000;
    }
    
    .btn-primary:hover {
      background: #00cc6a;
    }
    
    .activity-log {
      max-height: 120px;
      overflow-y: auto;
      font-size: 11px;
      margin-top: 15px;
    }
    
    .activity-item {
      padding: 5px 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      justify-content: space-between;
    }
    
    .activity-type {
      color: #00FF88;
    }
    
    .activity-time {
      color: #888;
    }
    
    .footer {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 10px;
      text-align: center;
      font-size: 10px;
      color: #666;
      background: rgba(0, 0, 0, 0.3);
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="status-indicator" id="statusIndicator"></div>
    <h1>nU Universe</h1>
    <div class="version">Extension v3.0.0</div>
  </div>
  
  <div class="content">
    <div class="metric">
      <div class="metric-label">UMatter Balance</div>
      <div class="metric-value" id="umatterBalance">0.000</div>
      <span class="metric-unit">UM</span>
    </div>
    
    <div class="metric">
      <div class="metric-label">SpUnder Interactions</div>
      <div class="metric-value" id="spunderCount">0</div>
      <span class="metric-unit">tracked</span>
    </div>
    
    <div class="metric">
      <div class="metric-label">Connection Status</div>
      <div class="metric-value" id="connectionStatus">Connecting...</div>
    </div>
    
    <div class="actions">
      <button class="btn btn-primary" id="openDashboard">Dashboard</button>
      <button class="btn" id="viewData">View Data</button>
      <button class="btn" id="settings">Settings</button>
      <button class="btn" id="refresh">Refresh</button>
    </div>
    
    <div class="activity-log" id="activityLog">
      <div style="text-align: center; color: #666; padding: 20px;">
        Loading activity...
      </div>
    </div>
  </div>
  
  <div class="footer">
    Powered by nU Universe • Real-time UMatter generation
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
