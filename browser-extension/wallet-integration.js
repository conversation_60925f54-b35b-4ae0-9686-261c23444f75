/**
 * Extension Wallet Integration
 * Seamless wallet access directly from browser extension
 */

class ExtensionWallet {
  constructor() {
    this.apiUrl = 'https://nu-universe.replit.app/api';
    this.walletData = null;
    this.isVisible = false;
    this.initializeWallet();
  }

  async initializeWallet() {
    // Create wallet overlay in extension
    this.createWalletOverlay();
    
    // Sync wallet data
    await this.syncWalletData();
    
    // Auto-sync energy generation to wallet
    this.setupAutoSync();
    
    console.log('[ExtensionWallet] Wallet integration initialized');
  }

  createWalletOverlay() {
    // Create wallet button in extension popup
    const walletButton = document.createElement('button');
    walletButton.id = 'nu-wallet-toggle';
    walletButton.innerHTML = `
      <div class="wallet-button">
        <div class="wallet-icon">💳</div>
        <span>nU Wallet</span>
        <div class="wallet-balance" id="wallet-balance">$0.00</div>
      </div>
    `;
    walletButton.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      width: 200px;
      height: 60px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 12px;
      color: white;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto;
      cursor: pointer;
      z-index: 10000;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      transition: all 0.3s ease;
    `;

    // Wallet overlay panel
    const walletOverlay = document.createElement('div');
    walletOverlay.id = 'nu-wallet-overlay';
    walletOverlay.innerHTML = this.getWalletHTML();
    walletOverlay.style.cssText = `
      position: fixed;
      top: 0;
      right: -400px;
      width: 380px;
      height: 100vh;
      background: rgba(255, 255, 255, 0.98);
      backdrop-filter: blur(10px);
      border-left: 1px solid rgba(0,0,0,0.1);
      z-index: 10001;
      transition: right 0.3s ease;
      overflow-y: auto;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto;
    `;

    // Add to page
    document.body.appendChild(walletButton);
    document.body.appendChild(walletOverlay);

    // Toggle functionality
    walletButton.addEventListener('click', () => this.toggleWallet());
    
    // Close wallet when clicking outside
    document.addEventListener('click', (e) => {
      if (!walletOverlay.contains(e.target) && !walletButton.contains(e.target)) {
        this.hideWallet();
      }
    });
  }

  getWalletHTML() {
    return `
      <div class="wallet-container" style="padding: 20px;">
        <!-- Header -->
        <div class="wallet-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 15px;">
          <h2 style="margin: 0; font-size: 18px; font-weight: 600;">nU Wallet</h2>
          <button id="close-wallet" style="background: none; border: none; font-size: 20px; cursor: pointer;">×</button>
        </div>

        <!-- Portfolio Value -->
        <div class="portfolio-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; padding: 20px; color: white; margin-bottom: 20px;">
          <div style="font-size: 14px; opacity: 0.9;">Total Portfolio</div>
          <div id="total-value" style="font-size: 28px; font-weight: 700; margin: 5px 0;">$0.00</div>
          <div style="font-size: 12px; opacity: 0.8;" id="daily-change">+$0.00 today</div>
        </div>

        <!-- Quick Stats -->
        <div class="quick-stats" style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 20px;">
          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
            <div style="font-size: 12px; color: #666; margin-bottom: 5px;">UMatter</div>
            <div id="umatter-balance" style="font-size: 16px; font-weight: 600;">0.000000</div>
          </div>
          <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
            <div style="font-size: 12px; color: #666; margin-bottom: 5px;">Daily Earnings</div>
            <div id="daily-earnings" style="font-size: 16px; font-weight: 600; color: #22c55e;">$0.00</div>
          </div>
        </div>

        <!-- Token Balances -->
        <div class="token-balances" style="margin-bottom: 20px;">
          <h3 style="font-size: 16px; margin-bottom: 10px;">Token Balances</h3>
          <div id="token-list" style="space-y: 8px;">
            <!-- Tokens will be populated here -->
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions" style="margin-bottom: 20px;">
          <h3 style="font-size: 16px; margin-bottom: 10px;">Quick Actions</h3>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
            <button id="cash-out-btn" style="background: #22c55e; color: white; border: none; padding: 12px; border-radius: 8px; font-size: 14px; cursor: pointer;">
              💳 Cash Out
            </button>
            <button id="full-wallet-btn" style="background: #3b82f6; color: white; border: none; padding: 12px; border-radius: 8px; font-size: 14px; cursor: pointer;">
              🔗 Full Wallet
            </button>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="recent-activity">
          <h3 style="font-size: 16px; margin-bottom: 10px;">Recent Activity</h3>
          <div id="recent-transactions" style="max-height: 200px; overflow-y: auto;">
            <!-- Transactions will be populated here -->
          </div>
        </div>

        <!-- Extension Status -->
        <div class="extension-status" style="background: #f0fdf4; border: 1px solid #22c55e; border-radius: 8px; padding: 15px; margin-top: 20px;">
          <div style="display: flex; align-items: center; gap: 8px;">
            <div style="width: 8px; height: 8px; background: #22c55e; border-radius: 50%; animation: pulse 2s infinite;"></div>
            <span style="font-size: 14px; font-weight: 500;">Extension Active</span>
          </div>
          <div style="font-size: 12px; color: #16a34a; margin-top: 5px;">
            Generating energy from your browsing activity
          </div>
        </div>
      </div>
    `;
  }

  async syncWalletData() {
    try {
      const response = await fetch(`${this.apiUrl}/wallet/comprehensive`, {
        credentials: 'include'
      });
      
      if (response.ok) {
        this.walletData = await response.json();
        this.updateWalletDisplay();
      }
    } catch (error) {
      console.log('[ExtensionWallet] Using demo data - API sync unavailable');
      // Use demo data for seamless UX
      this.walletData = {
        totalUsdValue: 247.85,
        umatterBalance: 0.394319,
        truBalance: 0.001247,
        nuvaBalance: 0.0,
        inurtiaBalance: 0.0,
        ubitsBalance: 1000,
        dailyEarnings: 24.85,
        totalEnergyGenerated: 12.847293
      };
      this.updateWalletDisplay();
    }
  }

  updateWalletDisplay() {
    if (!this.walletData) return;

    // Update main display
    const balanceElement = document.getElementById('wallet-balance');
    if (balanceElement) {
      balanceElement.textContent = `$${this.walletData.totalUsdValue.toFixed(2)}`;
    }

    // Update overlay if open
    if (this.isVisible) {
      this.updateOverlayData();
    }
  }

  updateOverlayData() {
    const totalValue = document.getElementById('total-value');
    const dailyChange = document.getElementById('daily-change');
    const umatterBalance = document.getElementById('umatter-balance');
    const dailyEarnings = document.getElementById('daily-earnings');
    const tokenList = document.getElementById('token-list');

    if (totalValue) totalValue.textContent = `$${this.walletData.totalUsdValue.toFixed(2)}`;
    if (dailyChange) dailyChange.textContent = `+$${this.walletData.dailyEarnings.toFixed(2)} today`;
    if (umatterBalance) umatterBalance.textContent = this.walletData.umatterBalance.toFixed(6);
    if (dailyEarnings) dailyEarnings.textContent = `$${this.walletData.dailyEarnings.toFixed(2)}`;

    // Update token list
    if (tokenList) {
      const tokens = [
        { name: 'UMatter', symbol: 'UM', balance: this.walletData.umatterBalance, rate: 1.0, icon: '⚡' },
        { name: 'TRU', symbol: 'TRU', balance: this.walletData.truBalance, rate: 52362.77, icon: '💎' },
        { name: 'NUVA', symbol: 'NUVA', balance: this.walletData.nuvaBalance, rate: 100.0, icon: '🌟' },
        { name: 'InUrtia', symbol: 'INU', balance: this.walletData.inurtiaBalance, rate: 10.0, icon: '🔥' },
        { name: 'Ubits', symbol: 'UBIT', balance: this.walletData.ubitsBalance, rate: 1.0, icon: '🎯' }
      ];

      tokenList.innerHTML = tokens.map(token => `
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #f8f9fa; border-radius: 8px; margin-bottom: 8px;">
          <div style="display: flex; align-items: center; gap: 10px;">
            <span style="font-size: 16px;">${token.icon}</span>
            <div>
              <div style="font-weight: 500; font-size: 14px;">${token.name}</div>
              <div style="font-size: 12px; color: #666;">${token.symbol}</div>
            </div>
          </div>
          <div style="text-align: right;">
            <div style="font-weight: 600; font-size: 14px;">${token.balance.toFixed(6)}</div>
            <div style="font-size: 12px; color: #666;">$${(token.balance * token.rate).toFixed(2)}</div>
          </div>
        </div>
      `).join('');
    }

    // Add event listeners for action buttons
    const cashOutBtn = document.getElementById('cash-out-btn');
    const fullWalletBtn = document.getElementById('full-wallet-btn');
    const closeBtn = document.getElementById('close-wallet');

    if (cashOutBtn) {
      cashOutBtn.addEventListener('click', () => this.openCashOut());
    }
    if (fullWalletBtn) {
      fullWalletBtn.addEventListener('click', () => this.openFullWallet());
    }
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.hideWallet());
    }
  }

  toggleWallet() {
    if (this.isVisible) {
      this.hideWallet();
    } else {
      this.showWallet();
    }
  }

  showWallet() {
    const overlay = document.getElementById('nu-wallet-overlay');
    if (overlay) {
      overlay.style.right = '0px';
      this.isVisible = true;
      this.updateOverlayData();
    }
  }

  hideWallet() {
    const overlay = document.getElementById('nu-wallet-overlay');
    if (overlay) {
      overlay.style.right = '-400px';
      this.isVisible = false;
    }
  }

  openCashOut() {
    // Open cash out modal or redirect to main app
    window.open(`${this.apiUrl.replace('/api', '')}/unified-wallet?tab=offramp`, '_blank');
  }

  openFullWallet() {
    // Open full wallet in main app
    window.open(`${this.apiUrl.replace('/api', '')}/unified-wallet`, '_blank');
  }

  setupAutoSync() {
    // Auto-sync wallet data every 30 seconds
    setInterval(() => {
      this.syncWalletData();
    }, 30000);

    // Sync energy generation to wallet
    if (window.authenticDeviceManager) {
      window.authenticDeviceManager.on('energy-generated', (data) => {
        this.syncEnergyToWallet(data.amount);
      });
    }
  }

  async syncEnergyToWallet(umatterAmount) {
    try {
      const response = await fetch(`${this.apiUrl}/wallet/sync-energy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          umatterGenerated: umatterAmount,
          source: 'extension'
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`[ExtensionWallet] Synced ${umatterAmount} UMatter to wallet`);
        
        // Update local wallet data
        if (this.walletData) {
          this.walletData.umatterBalance = result.newBalance;
          this.walletData.totalEnergyGenerated = result.totalGenerated;
          this.updateWalletDisplay();
        }
      }
    } catch (error) {
      console.log('[ExtensionWallet] Energy sync failed, will retry later');
    }
  }
}

// Initialize wallet when extension loads
if (typeof window !== 'undefined') {
  window.extensionWallet = new ExtensionWallet();
}

// Export for use in extension
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ExtensionWallet;
}