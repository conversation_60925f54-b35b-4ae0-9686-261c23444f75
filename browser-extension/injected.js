/**
 * nU Universe - Injected Script
 * Runs in the page context for real-time UMatter generation
 */

(function() {
  'use strict';

  // nU Universe UMatter generation system
  class UMatterGenerator {
    constructor() {
      this.umatterBalance = 0;
      this.sessionStart = Date.now();
      this.isActive = true;
      this.init();
    }

    init() {
      // Generate UMatter from page interactions
      this.setupInteractionTracking();
      
      // Auto-generate UMatter every 2 seconds
      setInterval(() => {
        if (this.isActive) {
          this.generateUMatter('background', 1.8);
        }
      }, 2000);

      console.log('[nU Universe] Injected script active - UMatter generation started');
    }

    setupInteractionTracking() {
      // Track clicks for UMatter generation
      document.addEventListener('click', (e) => {
        this.generateUMatter('click', 0.5);
      });

      // Track scrolling for UMatter generation
      let scrollTimeout;
      document.addEventListener('scroll', () => {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(() => {
          this.generateUMatter('scroll', 0.3);
        }, 100);
      });

      // Track key presses for UMatter generation
      document.addEventListener('keydown', () => {
        this.generateUMatter('keypress', 0.2);
      });
    }

    generateUMatter(type, amount) {
      this.umatterBalance += amount;
      
      // Send to content script
      window.postMessage({
        type: 'UMATTER_GENERATED',
        data: {
          amount,
          total: this.umatterBalance,
          source: type,
          timestamp: Date.now()
        }
      }, '*');
    }

    getStats() {
      return {
        balance: this.umatterBalance,
        sessionDuration: Date.now() - this.sessionStart,
        isActive: this.isActive
      };
    }
  }

  // Initialize UMatter generator
  if (!window.nuUniverseGenerator) {
    window.nuUniverseGenerator = new UMatterGenerator();
  }

})();