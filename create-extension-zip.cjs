const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

async function createExtensionZip() {
  const output = fs.createWriteStream('public/nu-universe-extension.zip');
  const archive = archiver('zip', {
    zlib: { level: 9 } // Maximum compression
  });

  return new Promise((resolve, reject) => {
    output.on('close', () => {
      console.log(`Extension ZIP created: ${archive.pointer()} total bytes`);
      resolve();
    });

    archive.on('error', (err) => {
      reject(err);
    });

    archive.pipe(output);

    // Add all essential browser extension files
    const extensionFiles = [
      'manifest.json',
      'background.js',
      'content.js',
      'popup.html',
      'popup.js',
      'popup-quantum.html',
      'popup-quantum.js',
      'popup-quantum-complete.js',
      'injected.js',
      'wallet-integration.js',
      'rules.json',
      'INSTALLATION.md'
    ];

    // Add files from browser-extension directory
    extensionFiles.forEach(file => {
      const filePath = path.join('browser-extension', file);
      if (fs.existsSync(filePath)) {
        archive.file(filePath, { name: file });
        console.log(`Added: ${file}`);
      } else {
        console.log(`Missing: ${file}`);
      }
    });

    // Add icons directory
    const iconsDir = 'browser-extension/icons';
    if (fs.existsSync(iconsDir)) {
      archive.directory(iconsDir, 'icons');
      console.log('Added: icons/ directory');
    }

    archive.finalize();
  });
}

createExtensionZip()
  .then(() => {
    console.log('Browser extension ZIP created successfully!');
    process.exit(0);
  })
  .catch(err => {
    console.error('Error creating ZIP:', err);
    process.exit(1);
  });