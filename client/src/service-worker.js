// nU Universe Service Worker for Mobile PWA
const CACHE_NAME = 'nu-universe-v1';
const urlsToCache = [
  '/',
  '/manifest.json',
  '/vite.svg'
];

// Install service worker and cache resources
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('[ServiceWorker] Caching app shell');
        return cache.addAll(urlsToCache);
      })
  );
});

// Serve cached content when offline - disabled to allow downloads
self.addEventListener('fetch', (event) => {
  // Don't intercept any requests to allow proper downloads
  return;
});

// Handle push notifications for mobile devices
self.addEventListener('push', (event) => {
  const options = {
    body: event.data ? event.data.text() : 'nU Universe notification',
    icon: '/vite.svg',
    badge: '/vite.svg',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: 'View Energy',
        icon: '/vite.svg'
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/vite.svg'
      }
    ]
  };

  event.waitUntil(
    self.registration.showNotification('nU Universe', options)
  );
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// Background sync for mobile energy data
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync-energy') {
    event.waitUntil(syncEnergyData());
  }
});

async function syncEnergyData() {
  try {
    // Sync any pending energy data when online
    const response = await fetch('/api/mobile-sync/status');
    if (response.ok) {
      console.log('[ServiceWorker] Energy data synced successfully');
    }
  } catch (error) {
    console.log('[ServiceWorker] Background sync failed, will retry');
  }
}