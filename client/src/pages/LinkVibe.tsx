import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { PageLayout } from '@/components/PageLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Link, 
  Heart, 
  Users,
  Zap,
  Share2,
  MessageCircle,
  TrendingUp,
  Activity,
  Globe,
  Star
} from 'lucide-react';

export default function LinkVibe() {
  const [activeTab, setActiveTab] = useState('connections');

  // Fetch real connections data
  const { data: connections = [], isLoading: connectionsLoading } = useQuery({
    queryKey: ['/api/social/connections'],
    refetchInterval: 30000
  });

  // Fetch real vibe groups data
  const { data: vibeGroups = [], isLoading: groupsLoading } = useQuery({
    queryKey: ['/api/social/vibe-groups'],
    refetchInterval: 60000
  });

  // Fetch real activity data
  const { data: vibeActivities = [], isLoading: activitiesLoading } = useQuery({
    queryKey: ['/api/social/activity'],
    refetchInterval: 15000
  });

  return (
    <PageLayout className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-pink-400 via-purple-500 to-indigo-600 bg-clip-text text-transparent">
                LinkVibe
              </h1>
              <p className="text-gray-400 mt-2">
                Connect through energy resonance and build meaningful digital relationships
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="bg-pink-500/20 text-pink-300 border-pink-500/30">
                <Heart className="w-3 h-3 mr-1" />
                High Vibe
              </Badge>
              <Badge variant="secondary" className="bg-purple-500/20 text-purple-300 border-purple-500/30">
                <Link className="w-3 h-3 mr-1" />
                47 Links
              </Badge>
            </div>
          </div>
        </div>

        {/* Vibe Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gray-800/50 border-pink-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Vibe Score</p>
                  <p className="text-2xl font-bold text-pink-400">
                    {connectionsLoading ? '...' : 
                      connections.length > 0 ? 
                        Math.round(connections.reduce((sum: number, conn: any) => sum + conn.vibeScore, 0) / connections.length) : 
                        '0'
                    }
                  </p>
                </div>
                <Heart className="h-8 w-8 text-pink-400" />
              </div>
              <p className="text-xs text-pink-400 mt-2">Real-time calculation</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-purple-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Energy Links</p>
                  <p className="text-2xl font-bold text-purple-400">
                    {connectionsLoading ? '...' : connections.length}
                  </p>
                </div>
                <Link className="h-8 w-8 text-purple-400" />
              </div>
              <p className="text-xs text-purple-400 mt-2">Live connections</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-indigo-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Shared Energy</p>
                  <p className="text-2xl font-bold text-indigo-400">
                    {connectionsLoading ? '...' : 
                      connections.reduce((sum: number, conn: any) => sum + (conn.sharedEnergy || 0), 0).toFixed(1)
                    }
                  </p>
                </div>
                <Zap className="h-8 w-8 text-indigo-400" />
              </div>
              <p className="text-xs text-indigo-400 mt-2">UMatter exchanged</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-cyan-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Vibe Groups</p>
                  <p className="text-2xl font-bold text-cyan-400">
                    {groupsLoading ? '...' : vibeGroups.length}
                  </p>
                </div>
                <Users className="h-8 w-8 text-cyan-400" />
              </div>
              <p className="text-xs text-cyan-400 mt-2">
                {vibeGroups.reduce((sum: number, group: any) => sum + (group.members || 0), 0)} total members
              </p>
            </CardContent>
          </Card>
        </div>

        {/* LinkVibe Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-gray-800/50">
            <TabsTrigger value="connections" className="data-[state=active]:bg-pink-500/20 data-[state=active]:text-pink-400">
              Energy Links
            </TabsTrigger>
            <TabsTrigger value="groups" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400">
              Vibe Groups
            </TabsTrigger>
            <TabsTrigger value="activity" className="data-[state=active]:bg-indigo-500/20 data-[state=active]:text-indigo-400">
              Vibe Activity
            </TabsTrigger>
            <TabsTrigger value="discover" className="data-[state=active]:bg-cyan-500/20 data-[state=active]:text-cyan-400">
              Discover Links
            </TabsTrigger>
          </TabsList>

          {/* Energy Links Tab */}
          <TabsContent value="connections" className="space-y-6 mt-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Your Energy Links</h2>
              <Button className="bg-pink-600 hover:bg-pink-700">
                <Heart className="h-4 w-4 mr-2" />
                Find New Links
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {connections.map((connection) => (
                <Card key={connection.id} className="bg-gray-800/50 border-gray-700 hover:border-pink-500/50 transition-colors">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                          {connection.avatar}
                        </div>
                        <div>
                          <h3 className="text-white font-semibold">{connection.name}</h3>
                          <p className="text-gray-400 text-sm">{connection.connectionType}</p>
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-pink-400 font-bold text-lg">{connection.vibeScore}</div>
                        <div className="text-gray-400 text-xs">vibe score</div>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Shared Energy</span>
                        <span className="text-green-400 font-bold">{connection.sharedEnergy} UMatter</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Last Interaction</span>
                        <span className="text-blue-400">{connection.lastInteraction}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Mutual Links</span>
                        <span className="text-purple-400">{connection.mutualConnections}</span>
                      </div>
                    </div>
                    
                    <div className="flex gap-2 mt-4">
                      <Button variant="outline" className="flex-1">
                        <Zap className="h-4 w-4 mr-2" />
                        Send Energy
                      </Button>
                      <Button variant="outline" className="flex-1">
                        <MessageCircle className="h-4 w-4 mr-2" />
                        Vibe
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Vibe Groups Tab */}
          <TabsContent value="groups" className="space-y-6 mt-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Vibe Groups</h2>
              <Button className="bg-purple-600 hover:bg-purple-700">
                <Users className="h-4 w-4 mr-2" />
                Create Group
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {vibeGroups.map((group) => (
                <Card key={group.id} className="bg-gray-800/50 border-gray-700">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-white">{group.name}</CardTitle>
                        <p className="text-gray-400 text-sm mt-1">{group.theme}</p>
                      </div>
                      <div className="text-center">
                        <div className="text-purple-400 font-bold text-lg">{group.averageVibe}</div>
                        <div className="text-gray-400 text-xs">avg vibe</div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-400 text-sm mb-4">{group.description}</p>
                    
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Members</span>
                        <span className="text-blue-400 font-bold">{group.members}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Total Energy</span>
                        <span className="text-green-400 font-bold">{group.totalEnergy.toLocaleString()} UMatter</span>
                      </div>
                    </div>
                    
                    <div className="flex gap-2 mt-4">
                      <Button variant="outline" className="flex-1">
                        Join Vibe
                      </Button>
                      <Button variant="outline" className="flex-1">
                        <Share2 className="h-4 w-4 mr-2" />
                        Share Energy
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Vibe Activity Tab */}
          <TabsContent value="activity" className="space-y-6 mt-6">
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-indigo-400">
                  <Activity className="h-5 w-5" />
                  Recent Vibe Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {vibeActivities.map((activity, index) => (
                    <div key={index} className="flex items-start gap-4 p-4 bg-gray-900/50 rounded-lg">
                      <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                        {activity.type === 'energy_boost' && <Zap className="h-5 w-5 text-white" />}
                        {activity.type === 'connection_made' && <Users className="h-5 w-5 text-white" />}
                        {activity.type === 'vibe_sync' && <Heart className="h-5 w-5 text-white" />}
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <p className="text-white font-medium">{activity.from}</p>
                            <p className="text-gray-400 text-sm">{activity.message}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-green-400 font-bold text-sm">{activity.energy}</p>
                            <p className="text-gray-500 text-xs">{activity.time}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Discover Links Tab */}
          <TabsContent value="discover" className="space-y-6 mt-6">
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-cyan-400">
                  <Globe className="h-5 w-5" />
                  Discover Energy Links
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Find by Interests</h3>
                    <div className="flex flex-wrap gap-2">
                      {['Technology', 'Art', 'Music', 'Science', 'Wellness', 'Gaming', 'Nature', 'Travel'].map((interest) => (
                        <Badge key={interest} variant="outline" className="cursor-pointer hover:bg-cyan-500/20">
                          {interest}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Suggested Connections</h3>
                    <div className="space-y-3">
                      {[
                        { name: 'Jordan Smith', compatibility: 94, reason: 'Similar energy patterns' },
                        { name: 'Alex Rivera', compatibility: 89, reason: 'Shared quantum interests' },
                        { name: 'Sam Taylor', compatibility: 92, reason: 'Complementary vibe scores' }
                      ].map((suggestion, i) => (
                        <div key={i} className="flex justify-between items-center p-3 bg-gray-900/50 rounded-lg">
                          <div>
                            <p className="text-white font-medium">{suggestion.name}</p>
                            <p className="text-gray-400 text-sm">{suggestion.reason}</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-cyan-400 font-bold">{suggestion.compatibility}%</span>
                            <Button size="sm" variant="outline">
                              Connect
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="pt-6 border-t border-gray-700">
                  <Button className="w-full bg-cyan-600 hover:bg-cyan-700">
                    <Star className="h-4 w-4 mr-2" />
                    Explore Vibe Network
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
}