import React, { useState, useEffect } from 'react';
import { PageLayout } from '../components/PageLayout';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { useRealBalance } from '../hooks/useRealBalance';
import { TrendingUp, TrendingDown, ArrowUpDown, DollarSign, Clock } from 'lucide-react';

interface TradePair {
  id: string;
  from: string;
  to: string;
  rate: number;
  volume24h: number;
  change24h: number;
  lastPrice: number;
}

interface TradeOrder {
  id: string;
  type: 'buy' | 'sell';
  pair: string;
  amount: number;
  price: number;
  total: number;
  status: 'pending' | 'completed' | 'cancelled';
  timestamp: number;
}

export function Trading() {
  const { balance, isAuthentic } = useRealBalance();
  const [tradePairs, setTradePairs] = useState<TradePair[]>([]);
  const [activeOrders, setActiveOrders] = useState<TradeOrder[]>([]);
  const [selectedPair, setSelectedPair] = useState<TradePair | null>(null);
  const [orderType, setOrderType] = useState<'buy' | 'sell'>('buy');
  const [amount, setAmount] = useState('');
  const [price, setPrice] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchTradingData = async () => {
      try {
        const pairsResponse = await fetch('/api/trading/pairs');
        if (pairsResponse.ok) {
          const data = await pairsResponse.json();
          setTradePairs(data.pairs || []);
          if (data.pairs && data.pairs.length > 0) {
            setSelectedPair(data.pairs[0]);
            setPrice(data.pairs[0].lastPrice.toString());
          }
        }

        const ordersResponse = await fetch('/api/trading/orders');
        if (ordersResponse.ok) {
          const data = await ordersResponse.json();
          setActiveOrders(data.orders || []);
        }
      } catch (error) {
        console.error('Failed to fetch trading data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTradingData();
    const interval = setInterval(fetchTradingData, 10000);
    return () => clearInterval(interval);
  }, []);

  const handleTrade = async () => {
    if (!selectedPair || !amount || !price) return;

    try {
      const response = await fetch('/api/trading/place-order', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pair: selectedPair.id,
          type: orderType,
          amount: parseFloat(amount),
          price: parseFloat(price)
        })
      });

      if (response.ok) {
        setAmount('');
        // Refresh orders
        const ordersResponse = await fetch('/api/trading/orders');
        if (ordersResponse.ok) {
          const data = await ordersResponse.json();
          setActiveOrders(data.orders || []);
        }
      }
    } catch (error) {
      console.error('Failed to place order:', error);
    }
  };

  const calculateTotal = () => {
    const amountNum = parseFloat(amount) || 0;
    const priceNum = parseFloat(price) || 0;
    return (amountNum * priceNum).toFixed(6);
  };

  if (isLoading) {
    return (
      <PageLayout>
        <div className="min-h-screen bg-background flex items-center justify-center">
          <div className="text-center space-y-4">
            <TrendingUp className="w-16 h-16 mx-auto text-neon-cyan animate-pulse" />
            <h2 className="text-2xl font-bold text-neon-cyan">Loading Trading...</h2>
            <p className="text-text-secondary">Fetching real market data...</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="min-h-screen bg-gradient-to-br from-background to-background-alt p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <div className="text-center space-y-2">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-neon-cyan to-neon-purple bg-clip-text text-transparent">
              Energy Trading
            </h1>
            <p className="text-text-secondary">Trade energy tokens on the real-time marketplace</p>
          </div>

          {/* Current Balance */}
          <Card className="bg-gradient-to-r from-blue-900/20 to-cyan-900/20 border-blue-500/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Available Balance</p>
                  <p className="text-2xl font-bold text-neon-cyan">
                    {balance !== null ? balance.toFixed(6) : '0.000000'} UMatter
                  </p>
                </div>
                <Badge variant={isAuthentic ? "default" : "secondary"}>
                  {isAuthentic ? 'Real Data' : 'No Data'}
                </Badge>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Trading Pairs */}
            <Card>
              <CardHeader>
                <CardTitle>Trading Pairs</CardTitle>
              </CardHeader>
              <CardContent>
                {tradePairs.length > 0 ? (
                  <div className="space-y-2">
                    {tradePairs.map((pair) => (
                      <div
                        key={pair.id}
                        className={`p-3 rounded-lg cursor-pointer transition-colors ${
                          selectedPair?.id === pair.id
                            ? 'bg-neon-cyan/20 border border-neon-cyan'
                            : 'bg-gray-800/50 hover:bg-gray-700/50'
                        }`}
                        onClick={() => {
                          setSelectedPair(pair);
                          setPrice(pair.lastPrice.toString());
                        }}
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium">{pair.from}/{pair.to}</p>
                            <p className="text-sm text-text-secondary">
                              Vol: {pair.volume24h.toFixed(2)}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-medium">{pair.lastPrice.toFixed(6)}</p>
                            <div className={`flex items-center text-sm ${
                              pair.change24h >= 0 ? 'text-green-400' : 'text-red-400'
                            }`}>
                              {pair.change24h >= 0 ? (
                                <TrendingUp className="w-3 h-3 mr-1" />
                              ) : (
                                <TrendingDown className="w-3 h-3 mr-1" />
                              )}
                              {Math.abs(pair.change24h).toFixed(2)}%
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <ArrowUpDown className="w-12 h-12 mx-auto text-gray-500 mb-4" />
                    <p className="text-text-secondary">No trading pairs available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Trade Form */}
            <Card>
              <CardHeader>
                <CardTitle>Place Order</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedPair ? (
                  <>
                    <div className="flex space-x-2">
                      <Button
                        variant={orderType === 'buy' ? 'default' : 'outline'}
                        className={orderType === 'buy' ? 'bg-green-600 hover:bg-green-700' : ''}
                        onClick={() => setOrderType('buy')}
                        size="sm"
                      >
                        Buy
                      </Button>
                      <Button
                        variant={orderType === 'sell' ? 'default' : 'outline'}
                        className={orderType === 'sell' ? 'bg-red-600 hover:bg-red-700' : ''}
                        onClick={() => setOrderType('sell')}
                        size="sm"
                      >
                        Sell
                      </Button>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <label className="text-sm text-text-secondary">Price ({selectedPair.to})</label>
                        <Input
                          type="number"
                          value={price}
                          onChange={(e) => setPrice(e.target.value)}
                          placeholder="0.000000"
                          step="0.000001"
                        />
                      </div>

                      <div>
                        <label className="text-sm text-text-secondary">Amount ({selectedPair.from})</label>
                        <Input
                          type="number"
                          value={amount}
                          onChange={(e) => setAmount(e.target.value)}
                          placeholder="0.000000"
                          step="0.000001"
                        />
                      </div>

                      <div>
                        <label className="text-sm text-text-secondary">Total ({selectedPair.to})</label>
                        <div className="p-3 bg-gray-800 rounded-md text-sm">
                          {calculateTotal()}
                        </div>
                      </div>

                      <Button
                        onClick={handleTrade}
                        className={`w-full ${
                          orderType === 'buy'
                            ? 'bg-green-600 hover:bg-green-700'
                            : 'bg-red-600 hover:bg-red-700'
                        }`}
                        disabled={!amount || !price}
                      >
                        {orderType === 'buy' ? 'Place Buy Order' : 'Place Sell Order'}
                      </Button>
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <DollarSign className="w-12 h-12 mx-auto text-gray-500 mb-4" />
                    <p className="text-text-secondary">Select a trading pair to start</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Active Orders */}
            <Card>
              <CardHeader>
                <CardTitle>Active Orders</CardTitle>
              </CardHeader>
              <CardContent>
                {activeOrders.length > 0 ? (
                  <div className="space-y-2">
                    {activeOrders.map((order) => (
                      <div key={order.id} className="p-3 bg-gray-800/50 rounded-lg">
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex items-center gap-2">
                            <Badge
                              variant={order.type === 'buy' ? 'default' : 'secondary'}
                              className={
                                order.type === 'buy'
                                  ? 'bg-green-600'
                                  : 'bg-red-600'
                              }
                            >
                              {order.type.toUpperCase()}
                            </Badge>
                            <span className="text-sm font-medium">{order.pair}</span>
                          </div>
                          <Badge variant={
                            order.status === 'completed' ? 'default' :
                            order.status === 'pending' ? 'secondary' : 'destructive'
                          }>
                            {order.status}
                          </Badge>
                        </div>
                        <div className="text-sm text-text-secondary space-y-1">
                          <div className="flex justify-between">
                            <span>Amount:</span>
                            <span>{order.amount.toFixed(6)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Price:</span>
                            <span>{order.price.toFixed(6)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Total:</span>
                            <span>{order.total.toFixed(6)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Time:</span>
                            <span>{new Date(order.timestamp).toLocaleTimeString()}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Clock className="w-12 h-12 mx-auto text-gray-500 mb-4" />
                    <p className="text-text-secondary">No active orders</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}

export default Trading;