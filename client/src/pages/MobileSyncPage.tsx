import React from 'react';
import { MobileFirstLayout } from '@/components/MobileFirstLayout';
import { MobileNativeSync } from '@/components/MobileNativeSync';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Smartphone, 
  Zap, 
  Wifi, 
  Battery,
  Activity,
  CheckCircle,
  Bluetooth
} from "lucide-react";
import { motion } from "framer-motion";

export function MobileSyncPage() {
  return (
    <MobileFirstLayout title="Mobile Device Sync">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <motion.div 
          className="text-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
            Mobile Device Sync
          </h1>
          <p className="text-lg text-gray-300 max-w-3xl mx-auto">
            Sync your mobile devices, tablets, and IoT devices for real-time energy tracking and UMatter generation
          </p>
        </motion.div>

        {/* Mobile Support Features */}
        <motion.div 
          className="grid md:grid-cols-3 gap-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-green-400">
                <CheckCircle className="w-5 h-5" />
                <span>Mobile Ready</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 text-sm">
                Full mobile browser support with native device API integration for iOS and Android
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-blue-400">
                <Wifi className="w-5 h-5" />
                <span>Real-time Sync</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 text-sm">
                Continuous synchronization with authentic device metrics and energy generation
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-purple-400">
                <Zap className="w-5 h-5" />
                <span>Energy Tracking</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 text-sm">
                Convert mobile interactions, battery usage, and device motion into UMatter tokens
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Mobile Device Sync Component */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <MobileNativeSync />
        </motion.div>

        {/* Mobile Features Grid */}
        <motion.div 
          className="grid md:grid-cols-2 gap-6 mt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-cyan-400">
                <Battery className="w-5 h-5" />
                <span>Battery Integration</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Real Battery API</span>
                <Badge variant="outline" className="bg-green-900/30 text-green-400">Active</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Charging Detection</span>
                <Badge variant="outline" className="bg-green-900/30 text-green-400">Active</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Power Optimization</span>
                <Badge variant="outline" className="bg-green-900/30 text-green-400">Active</Badge>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-orange-400">
                <Activity className="w-5 h-5" />
                <span>Sensor Integration</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Touch Tracking</span>
                <Badge variant="outline" className="bg-blue-900/30 text-blue-400">Enabled</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Motion Sensors</span>
                <Badge variant="outline" className="bg-blue-900/30 text-blue-400">Enabled</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Ambient Light</span>
                <Badge variant="outline" className="bg-blue-900/30 text-blue-400">Enabled</Badge>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* PWA Installation Notice */}
        <motion.div 
          className="mt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <Card className="bg-gradient-to-r from-blue-900/20 to-purple-900/20 border-blue-600/30">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-blue-400">
                <Smartphone className="w-5 h-5" />
                <span>Progressive Web App</span>
                <Badge variant="outline" className="bg-blue-900/30 text-blue-400">Available</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 mb-4">
                Install nU Universe as a Progressive Web App for the best mobile experience with offline capabilities and native-like performance.
              </p>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-semibold text-white mb-2">iOS Installation:</h4>
                  <ol className="space-y-1 text-gray-300 list-decimal list-inside">
                    <li>Open in Safari</li>
                    <li>Tap Share button</li>
                    <li>Select "Add to Home Screen"</li>
                  </ol>
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-2">Android Installation:</h4>
                  <ol className="space-y-1 text-gray-300 list-decimal list-inside">
                    <li>Open in Chrome</li>
                    <li>Tap menu (3 dots)</li>
                    <li>Select "Add to Home screen"</li>
                  </ol>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </MobileFirstLayout>
  );
}