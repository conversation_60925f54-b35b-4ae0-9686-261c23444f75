import React, { useState, useEffect } from 'react';
import { PageLayout } from '../components/PageLayout';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { useRealBalance } from '../hooks/useRealBalance';
import { Wallet as WalletIcon, TrendingUp, ArrowUpRight, ArrowDownLeft, Database } from 'lucide-react';

interface Transaction {
  id: string;
  type: 'credit' | 'debit';
  amount: number;
  token: string;
  description: string;
  timestamp: number;
  status: 'completed' | 'pending' | 'failed';
}

export function Wallet() {
  const { balance, isAuthentic } = useRealBalance();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [walletStats, setWalletStats] = useState({
    totalEarned: 0,
    totalSpent: 0,
    transactionCount: 0
  });

  useEffect(() => {
    const fetchWalletData = async () => {
      try {
        // Fetch real transactions from database
        const transactionsResponse = await fetch('/api/wallet/transactions');
        if (transactionsResponse.ok) {
          const data = await transactionsResponse.json();
          setTransactions(data.transactions || []);
        }

        // Fetch wallet statistics
        const statsResponse = await fetch('/api/wallet/stats');
        if (statsResponse.ok) {
          const data = await statsResponse.json();
          setWalletStats(data);
        }
      } catch (error) {
        console.error('Failed to fetch wallet data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchWalletData();
    const interval = setInterval(fetchWalletData, 30000);
    return () => clearInterval(interval);
  }, []);

  const formatTokenAmount = (amount: number) => {
    if (amount >= 1) return amount.toFixed(6);
    if (amount >= 0.000001) return amount.toFixed(8);
    return amount.toExponential(3);
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };

  if (isLoading) {
    return (
      <PageLayout>
        <div className="min-h-screen bg-background flex items-center justify-center">
          <div className="text-center space-y-4">
            <WalletIcon className="w-16 h-16 mx-auto text-neon-cyan animate-pulse" />
            <h2 className="text-2xl font-bold text-neon-cyan">Loading Wallet...</h2>
            <p className="text-text-secondary">Fetching real transaction data...</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="min-h-screen bg-gradient-to-br from-background to-background-alt p-6">
        <div className="max-w-6xl mx-auto space-y-6">
          <div className="text-center space-y-2">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-neon-cyan to-neon-purple bg-clip-text text-transparent">
              Energy Wallet
            </h1>
            <p className="text-text-secondary">Manage your energy tokens and view transaction history</p>
          </div>

          {/* Balance Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="bg-gradient-to-r from-blue-900/20 to-cyan-900/20 border-blue-500/30">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <WalletIcon className="w-4 h-4 text-neon-cyan" />
                  Current Balance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-neon-cyan">
                  {balance !== null ? formatTokenAmount(balance) : '0.000000'} UMatter
                </div>
                <p className="text-xs text-text-secondary">
                  {isAuthentic ? 'Real-time from database' : 'Data not available'}
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-green-900/20 to-emerald-900/20 border-green-500/30">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <TrendingUp className="w-4 h-4 text-green-400" />
                  Total Earned
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-400">
                  {formatTokenAmount(walletStats.totalEarned)}
                </div>
                <p className="text-xs text-text-secondary">All-time earnings</p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-orange-900/20 to-red-900/20 border-orange-500/30">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <ArrowDownLeft className="w-4 h-4 text-orange-400" />
                  Total Spent
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-400">
                  {formatTokenAmount(walletStats.totalSpent)}
                </div>
                <p className="text-xs text-text-secondary">Total expenditure</p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-purple-900/20 to-pink-900/20 border-purple-500/30">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Database className="w-4 h-4 text-purple-400" />
                  Transactions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-400">
                  {walletStats.transactionCount}
                </div>
                <p className="text-xs text-text-secondary">Total transactions</p>
              </CardContent>
            </Card>
          </div>

          {/* Transaction History */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Transaction History</span>
                <Badge variant="outline" className="text-neon-cyan border-neon-cyan">
                  {transactions.length} transactions
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {transactions.length > 0 ? (
                <div className="space-y-3">
                  {transactions.map((transaction) => (
                    <div
                      key={transaction.id}
                      className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg border border-gray-700"
                    >
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-full ${
                          transaction.type === 'credit' 
                            ? 'bg-green-500/20 text-green-400' 
                            : 'bg-red-500/20 text-red-400'
                        }`}>
                          {transaction.type === 'credit' ? (
                            <ArrowUpRight className="w-4 h-4" />
                          ) : (
                            <ArrowDownLeft className="w-4 h-4" />
                          )}
                        </div>
                        <div>
                          <p className="font-medium">{transaction.description}</p>
                          <p className="text-xs text-text-secondary">
                            {formatTime(transaction.timestamp)}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`font-bold ${
                          transaction.type === 'credit' ? 'text-green-400' : 'text-red-400'
                        }`}>
                          {transaction.type === 'credit' ? '+' : '-'}
                          {formatTokenAmount(transaction.amount)} {transaction.token}
                        </p>
                        <Badge 
                          variant={transaction.status === 'completed' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {transaction.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <WalletIcon className="w-12 h-12 mx-auto text-gray-500 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No transactions yet</h3>
                  <p className="text-text-secondary mb-4">
                    Start earning energy tokens to see your transaction history
                  </p>
                  <Button 
                    className="bg-neon-cyan hover:bg-neon-cyan/80 text-background"
                    onClick={() => window.location.href = '/energy-hub'}
                  >
                    Start Earning Energy
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="cursor-pointer hover:border-neon-cyan/50 transition-colors">
              <CardContent className="p-6 text-center">
                <TrendingUp className="w-8 h-8 text-neon-cyan mx-auto mb-2" />
                <h3 className="font-semibold mb-2">Energy Trading</h3>
                <p className="text-sm text-text-secondary mb-4">
                  Trade your energy tokens on the marketplace
                </p>
                <Button 
                  variant="outline" 
                  className="border-neon-cyan text-neon-cyan hover:bg-neon-cyan/10"
                  onClick={() => window.location.href = '/trading'}
                >
                  Go to Trading
                </Button>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:border-neon-purple/50 transition-colors">
              <CardContent className="p-6 text-center">
                <Database className="w-8 h-8 text-neon-purple mx-auto mb-2" />
                <h3 className="font-semibold mb-2">Energy Banking</h3>
                <p className="text-sm text-text-secondary mb-4">
                  Store and grow your energy tokens with interest
                </p>
                <Button 
                  variant="outline" 
                  className="border-neon-purple text-neon-purple hover:bg-neon-purple/10"
                  onClick={() => window.location.href = '/energy-banking'}
                >
                  Open Banking
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}

export default Wallet;