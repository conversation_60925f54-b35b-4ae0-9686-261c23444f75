import React, { useState, useEffect } from 'react';
import { PageLayout } from '../components/PageLayout';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { useRealBalance } from '../hooks/useRealBalance';
import { ShoppingCart, Package, Star, Filter, Search } from 'lucide-react';

interface MarketplaceItem {
  id: string;
  title: string;
  description: string;
  price: number;
  currency: string;
  seller: string;
  category: string;
  rating: number;
  reviews: number;
  available: boolean;
  timestamp: number;
}

interface Category {
  id: string;
  name: string;
  count: number;
}

export function Marketplace() {
  const { balance, isAuthentic } = useRealBalance();
  const [items, setItems] = useState<MarketplaceItem[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'price' | 'rating' | 'newest'>('newest');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchMarketplaceData = async () => {
      try {
        const itemsResponse = await fetch('/api/marketplace/items');
        if (itemsResponse.ok) {
          const data = await itemsResponse.json();
          setItems(data.items || []);
        }

        const categoriesResponse = await fetch('/api/marketplace/categories');
        if (categoriesResponse.ok) {
          const data = await categoriesResponse.json();
          setCategories(data.categories || []);
        }
      } catch (error) {
        console.error('Failed to fetch marketplace data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMarketplaceData();
    const interval = setInterval(fetchMarketplaceData, 30000);
    return () => clearInterval(interval);
  }, []);

  const handlePurchase = async (itemId: string, price: number) => {
    try {
      const response = await fetch('/api/marketplace/purchase', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ itemId, price })
      });

      if (response.ok) {
        // Refresh marketplace data
        const itemsResponse = await fetch('/api/marketplace/items');
        if (itemsResponse.ok) {
          const data = await itemsResponse.json();
          setItems(data.items || []);
        }
      }
    } catch (error) {
      console.error('Failed to purchase item:', error);
    }
  };

  const filteredItems = items
    .filter(item => 
      (selectedCategory === 'all' || item.category === selectedCategory) &&
      (searchTerm === '' || 
       item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
       item.description.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return a.price - b.price;
        case 'rating':
          return b.rating - a.rating;
        case 'newest':
        default:
          return b.timestamp - a.timestamp;
      }
    });

  if (isLoading) {
    return (
      <PageLayout>
        <div className="min-h-screen bg-background flex items-center justify-center">
          <div className="text-center space-y-4">
            <ShoppingCart className="w-16 h-16 mx-auto text-neon-cyan animate-pulse" />
            <h2 className="text-2xl font-bold text-neon-cyan">Loading Marketplace...</h2>
            <p className="text-text-secondary">Fetching real marketplace data...</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="min-h-screen bg-gradient-to-br from-background to-background-alt p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <div className="text-center space-y-2">
            <h1 className="text-4xl font-bold bg-gradient-to-r from-neon-cyan to-neon-purple bg-clip-text text-transparent">
              Energy Marketplace
            </h1>
            <p className="text-text-secondary">Trade energy tokens and digital assets</p>
          </div>

          {/* Balance Display */}
          <Card className="bg-gradient-to-r from-blue-900/20 to-cyan-900/20 border-blue-500/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Available Balance</p>
                  <p className="text-2xl font-bold text-neon-cyan">
                    {balance !== null ? balance.toFixed(6) : '0.000000'} UMatter
                  </p>
                </div>
                <Badge variant={isAuthentic ? "default" : "secondary"}>
                  {isAuthentic ? 'Real Balance' : 'No Data'}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Search and Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <Input
                      placeholder="Search marketplace..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-sm"
                  >
                    <option value="all">All Categories</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name} ({category.count})
                      </option>
                    ))}
                  </select>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as 'price' | 'rating' | 'newest')}
                    className="px-3 py-2 bg-gray-800 border border-gray-600 rounded-md text-sm"
                  >
                    <option value="newest">Newest</option>
                    <option value="price">Price (Low to High)</option>
                    <option value="rating">Highest Rated</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Marketplace Items */}
          {filteredItems.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredItems.map((item) => (
                <Card key={item.id} className="overflow-hidden">
                  <CardHeader className="pb-3">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <CardTitle className="text-lg line-clamp-1">{item.title}</CardTitle>
                        <p className="text-sm text-text-secondary mt-1">by {item.seller}</p>
                      </div>
                      <Badge variant={item.available ? "default" : "secondary"}>
                        {item.available ? 'Available' : 'Sold Out'}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-text-secondary line-clamp-3">
                      {item.description}
                    </p>

                    <div className="flex items-center gap-2">
                      <div className="flex items-center">
                        <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        <span className="text-sm ml-1">{item.rating.toFixed(1)}</span>
                      </div>
                      <span className="text-xs text-text-secondary">
                        ({item.reviews} reviews)
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {item.category}
                      </Badge>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-2xl font-bold text-neon-cyan">
                          {item.price.toFixed(6)}
                        </p>
                        <p className="text-xs text-text-secondary">{item.currency}</p>
                      </div>
                      <Button
                        onClick={() => handlePurchase(item.id, item.price)}
                        disabled={!item.available || (balance !== null && balance < item.price)}
                        className="bg-neon-cyan hover:bg-neon-cyan/80 text-background"
                      >
                        <ShoppingCart className="w-4 h-4 mr-2" />
                        Buy Now
                      </Button>
                    </div>

                    <div className="text-xs text-text-secondary">
                      Listed {new Date(item.timestamp).toLocaleDateString()}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Package className="w-16 h-16 mx-auto text-gray-500 mb-4" />
                <h3 className="text-xl font-semibold mb-2">No items found</h3>
                <p className="text-text-secondary mb-4">
                  {searchTerm || selectedCategory !== 'all' 
                    ? 'Try adjusting your search or filter criteria'
                    : 'No marketplace items available at this time'
                  }
                </p>
                {(searchTerm || selectedCategory !== 'all') && (
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedCategory('all');
                    }}
                  >
                    Clear Filters
                  </Button>
                )}
              </CardContent>
            </Card>
          )}

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <Package className="w-8 h-8 text-neon-cyan mx-auto mb-2" />
                <p className="text-2xl font-bold">{items.length}</p>
                <p className="text-sm text-text-secondary">Total Items</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <Filter className="w-8 h-8 text-neon-purple mx-auto mb-2" />
                <p className="text-2xl font-bold">{categories.length}</p>
                <p className="text-sm text-text-secondary">Categories</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <Star className="w-8 h-8 text-yellow-400 mx-auto mb-2" />
                <p className="text-2xl font-bold">
                  {items.length > 0 ? (items.reduce((sum, item) => sum + item.rating, 0) / items.length).toFixed(1) : '0.0'}
                </p>
                <p className="text-sm text-text-secondary">Avg Rating</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}

export default Marketplace;