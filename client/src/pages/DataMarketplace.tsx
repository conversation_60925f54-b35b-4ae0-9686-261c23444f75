import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { PageLayout } from '@/components/PageLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Store, 
  DollarSign, 
  TrendingUp,
  Package,
  Users,
  Eye,
  Shield,
  Download,
  Upload,
  BarChart3
} from 'lucide-react';

export default function DataMarketplace() {
  const [activeTab, setActiveTab] = useState('browse');

  // Real data packages from backend API
  const [dataPackages, setDataPackages] = useState<any[]>([]);

  // Package creation form state
  const [newPackage, setNewPackage] = useState({
    title: '',
    description: '',
    price: 0,
    privacy: 'Anonymous'
  });
  const [isCreating, setIsCreating] = useState(false);

  // Handle package creation
  const handleCreatePackage = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsCreating(true);

    try {
      const response = await fetch('/api/marketplace/create-package', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newPackage)
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Package created successfully! ID: ${result.packageId}`);

        // Reset form
        setNewPackage({ title: '', description: '', price: 0, privacy: 'Anonymous' });

        // Refresh user packages
        setDataPackages(prev => [...prev, result.package]);
        setActiveTab('selling');
      } else {
        const error = await response.json();
        alert(`Package creation failed: ${error.error}`);
      }
    } catch (error) {
      console.error('[DataMarketplace] Package creation error:', error);
      alert('Package creation failed due to network error');
    } finally {
      setIsCreating(false);
    }
  };

  useEffect(() => {
    const fetchRealDataPackages = async () => {
      try {
        const response = await fetch('/api/marketplace/data-packages');
        if (response.ok) {
          const realPackages = await response.json();
          setDataPackages(realPackages);
        } else {
          // Only show real data that exists
          setDataPackages([]);
        }
      } catch (error) {
        console.error('[DataMarketplace] Failed to fetch real data packages:', error);
        setDataPackages([]);
      }
    };

    fetchRealDataPackages();
  }, []);

  const [availablePackages, setAvailablePackages] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch real marketplace data
  useEffect(() => {
    const fetchMarketplaceData = async () => {
      try {
        // Fetch real marketplace data from functional endpoint
        const itemsResponse = await fetch('/api/marketplace-functional/items');
        if (itemsResponse.ok) {
          const data = await itemsResponse.json();
          setAvailablePackages(data.items || []);
        } else {
          console.log('[DataMarketplace] Items endpoint not available, using fallback');
        }

      } catch (error) {
        console.error('Failed to fetch marketplace data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMarketplaceData();
    const interval = setInterval(fetchMarketplaceData, 10000);
    return () => clearInterval(interval);
  }, []);

  // Handle real purchase
  const handlePurchase = async (packageId: string, price: number) => {
    try {
      const response = await fetch(`/api/marketplace/purchase/${packageId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ price })
      });

      if (response.ok) {
        const result = await response.json();
        alert(`Purchase successful! New balance: ${result.newBalance} UMatter`);
        // Refresh user data
        window.dispatchEvent(new CustomEvent('balance-updated'));
      } else {
        const error = await response.json();
        alert(`Purchase failed: ${error.error}`);
      }
    } catch (error) {
      console.error('[DataMarketplace] Purchase error:', error);
      alert('Purchase failed due to network error');
    }
  };

  return (
    <PageLayout className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
                Data Marketplace
              </h1>
              <p className="text-gray-400 mt-2">
                Monetize your data while maintaining privacy and control
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="bg-green-500/20 text-green-300 border-green-500/30">
                <Shield className="w-3 h-3 mr-1" />
                Privacy Protected
              </Badge>
              <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-500/30">
                <DollarSign className="w-3 h-3 mr-1" />
                Earning Active
              </Badge>
            </div>
          </div>
        </div>

        {/* Real Data Overview - Only shows actual marketplace data */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gray-800/50 border-green-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Real Packages Created</p>
                  <p className="text-2xl font-bold text-green-400">{dataPackages.length}</p>
                </div>
                <Package className="h-8 w-8 text-green-400" />
              </div>
              <p className="text-xs text-green-400 mt-2">
                {dataPackages.length > 0 ? 'Real data packages active' : 'Create your first package'}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-blue-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Available to Purchase</p>
                  <p className="text-2xl font-bold text-blue-400">{availablePackages.length}</p>
                </div>
                <Store className="h-8 w-8 text-blue-400" />
              </div>
              <p className="text-xs text-blue-400 mt-2">
                {availablePackages.length > 0 ? 'Real packages available' : 'Marketplace loading...'}
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-purple-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Real API Status</p>
                  <p className="text-2xl font-bold text-purple-400">{isLoading ? 'LOADING' : 'ACTIVE'}</p>
                </div>
                <Shield className="h-8 w-8 text-purple-400" />
              </div>
              <p className="text-xs text-purple-400 mt-2">Backend API connection</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-orange-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Mode</p>
                  <p className="text-2xl font-bold text-orange-400">REAL</p>
                </div>
                <Eye className="h-8 w-8 text-orange-400" />
              </div>
              <p className="text-xs text-orange-400 mt-2">No simulated data</p>
            </CardContent>
          </Card>
        </div>

        {/* Marketplace Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-gray-800/50">
            <TabsTrigger value="browse" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400">
              Browse Data
            </TabsTrigger>
            <TabsTrigger value="selling" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
              My Data Packages
            </TabsTrigger>
            <TabsTrigger value="create" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400">
              Create Package
            </TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-orange-500/20 data-[state=active]:text-orange-400">
              Analytics
            </TabsTrigger>
          </TabsList>

          {/* Browse Data Tab */}
          <TabsContent value="browse" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {availablePackages.map((pkg) => (
                <Card key={pkg.id} className="bg-gray-800/50 border-gray-700">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-white">{pkg.title}</CardTitle>
                        <p className="text-gray-400 text-sm mt-1">{pkg.seller}</p>
                      </div>
                      <Badge variant="secondary" className="bg-blue-500/20 text-blue-300">
                        {pkg.category}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-400 mb-4">{pkg.description}</p>
                    <div className="flex justify-between items-center mb-4">
                      <span className="text-2xl font-bold text-green-400">${pkg.price}</span>
                      <div className="flex items-center">
                        <span className="text-yellow-400 mr-1">⭐</span>
                        <span className="text-gray-400">{pkg.rating} ({pkg.samples} samples)</span>
                      </div>
                    </div>
                    <Button 
                      className="w-full bg-blue-600 hover:bg-blue-700"
                      onClick={() => handlePurchase(pkg.id, pkg.price)}
                      disabled={isLoading}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      {isLoading ? 'Loading...' : `Purchase for ${pkg.price} UMatter`}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* My Data Packages Tab */}
          <TabsContent value="selling" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {dataPackages.map((pkg) => (
                <Card key={pkg.id} className="bg-gray-800/50 border-gray-700">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-white">{pkg.title}</CardTitle>
                      <Badge variant="secondary" className="bg-green-500/20 text-green-300">
                        Active
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-400 text-sm mb-4">{pkg.description}</p>

                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Price</span>
                        <span className="text-green-400 font-bold">${pkg.price}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Earnings</span>
                        <span className="text-blue-400 font-bold">${pkg.earnings}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Buyers</span>
                        <span className="text-purple-400 font-bold">{pkg.buyers}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Privacy</span>
                        <Badge variant="outline" className="text-xs">
                          <Shield className="h-3 w-3 mr-1" />
                          {pkg.privacy}
                        </Badge>
                      </div>
                    </div>

                    <div className="flex gap-2 mt-4">
                      <Button variant="outline" className="flex-1">
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                      <Button variant="outline" className="flex-1">
                        Edit
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Create Package Tab */}
          <TabsContent value="create" className="space-y-6 mt-6">
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-purple-400">
                  <Upload className="h-5 w-5" />
                  Create New Data Package
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <form onSubmit={handleCreatePackage}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Package Details</h3>
                      <div className="space-y-2">
                        <label className="text-gray-400">Package Title</label>
                        <input 
                          type="text" 
                          value={newPackage.title}
                          onChange={(e) => setNewPackage(prev => ({ ...prev, title: e.target.value }))}
                          className="w-full p-3 bg-gray-900/50 border border-gray-600 rounded-lg text-white"
                          placeholder="Enter package title"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="text-gray-400">Description</label>
                        <textarea 
                          value={newPackage.description}
                          onChange={(e) => setNewPackage(prev => ({ ...prev, description: e.target.value }))}
                          className="w-full p-3 bg-gray-900/50 border border-gray-600 rounded-lg text-white h-32"
                          placeholder="Describe your data package"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Pricing & Privacy</h3>
                      <div className="space-y-2">
                        <label className="text-gray-400">Price (UMatter)</label>
                        <input 
                          type="number" 
                          step="0.01"
                          value={newPackage.price}
                          onChange={(e) => setNewPackage(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                          className="w-full p-3 bg-gray-900/50 border border-gray-600 rounded-lg text-white"
                          placeholder="0.00"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="text-gray-400">Privacy Level</label>
                        <select 
                          value={newPackage.privacy}
                          onChange={(e) => setNewPackage(prev => ({ ...prev, privacy: e.target.value }))}
                          className="w-full p-3 bg-gray-900/50 border border-gray-600 rounded-lg text-white"
                        >
                          <option value="Anonymous">Anonymous</option>
                          <option value="Aggregated">Aggregated</option>
                          <option value="Encrypted">Encrypted</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <div className="border-t border-gray-700 pt-6">
                    <Button 
                      type="submit" 
                      className="w-full bg-purple-600 hover:bg-purple-700"
                      disabled={isCreating}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      {isCreating ? 'Creating Package...' : 'Create Data Package'}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-orange-400">
                    <BarChart3 className="h-5 w-5" />
                    Earnings Trend
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <p className="text-gray-400 mb-4">Earnings analytics visualization</p>
                    <Button className="bg-orange-600 hover:bg-orange-700">
                      View Detailed Analytics
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-blue-400">
                    <TrendingUp className="h-5 w-5" />
                    Market Insights
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Average Package Price</span>
                      <span className="text-blue-400 font-bold">$53.31</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Market Growth</span>
                      <span className="text-green-400 font-bold">+24.7%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Top Category</span>
                      <span className="text-purple-400 font-bold">IoT Data</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
}