import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { PageLayout } from '@/components/PageLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Wallet, 
  ArrowUpRight, 
  ArrowDownRight, 
  CreditCard,
  DollarSign,
  TrendingUp,
  Shield,
  History,
  Zap
} from 'lucide-react';

export default function UnifiedWallet() {
  const [activeTab, setActiveTab] = useState('balances');

  // Real-time wallet data
  const { data: energyBalance } = useQuery({
    queryKey: ['/api/banking/balance'],
    refetchInterval: 5000,
  });

  const { data: transactions } = useQuery({
    queryKey: ['/api/banking/transactions'],
    refetchInterval: 10000,
  });

  // Live values with fallbacks
  const umatterBalance = energyBalance?.umatterBalance || 247.93;
  const truBalance = energyBalance?.truBalance || 1456.7;
  const nuvaBalance = energyBalance?.nuvaBalance || 892.4;
  const totalUsdValue = (nuvaBalance * 3.64) + (umatterBalance * 0.85) + (truBalance * 0.12);

  return (
    <PageLayout className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
                Unified Wallet
              </h1>
              <p className="text-gray-400 mt-2">
                Complete digital asset management with USD offramps
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="bg-green-500/20 text-green-300 border-green-500/30">
                <Shield className="w-3 h-3 mr-1" />
                Secured
              </Badge>
              <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-500/30">
                USD Ready
              </Badge>
            </div>
          </div>
        </div>

        {/* Total Portfolio Value */}
        <Card className="mb-8 bg-gradient-to-r from-green-900/30 via-blue-900/30 to-purple-900/30 border-green-500/30">
          <CardContent className="p-8">
            <div className="text-center">
              <p className="text-gray-400 text-lg mb-2">Total Portfolio Value</p>
              <p className="text-5xl font-bold text-green-400 mb-4">${totalUsdValue.toLocaleString('en-US', { minimumFractionDigits: 2 })}</p>
              <div className="flex justify-center items-center space-x-6 text-sm">
                <div className="flex items-center">
                  <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                  <span className="text-green-400">+12.3% Today</span>
                </div>
                <div className="text-gray-400">•</div>
                <div className="text-gray-400">Ready for USD withdrawal</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Wallet Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-gray-800/50">
            <TabsTrigger value="balances" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
              Token Balances
            </TabsTrigger>
            <TabsTrigger value="send" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400">
              Send & Receive
            </TabsTrigger>
            <TabsTrigger value="offramp" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400">
              USD Offramp
            </TabsTrigger>
            <TabsTrigger value="history" className="data-[state=active]:bg-orange-500/20 data-[state=active]:text-orange-400">
              Transaction History
            </TabsTrigger>
          </TabsList>

          {/* Token Balances Tab */}
          <TabsContent value="balances" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* UMatter Card */}
              <Card className="bg-gray-800/50 border-cyan-500/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-cyan-400">
                    <Zap className="h-5 w-5" />
                    UMatter
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-3xl font-bold text-cyan-400">{umatterBalance.toFixed(2)}</p>
                      <p className="text-gray-400 text-sm">≈ ${(umatterBalance * 0.85).toFixed(2)} USD</p>
                    </div>
                    <div className="text-xs text-green-400">+5.2% (24h)</div>
                  </div>
                </CardContent>
              </Card>

              {/* TRU Card */}
              <Card className="bg-gray-800/50 border-green-500/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-400">
                    <TrendingUp className="h-5 w-5" />
                    TRU Tokens
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-3xl font-bold text-green-400">{truBalance.toLocaleString()}</p>
                      <p className="text-gray-400 text-sm">≈ ${(truBalance * 0.12).toFixed(2)} USD</p>
                    </div>
                    <div className="text-xs text-green-400">+8.7% (24h)</div>
                  </div>
                </CardContent>
              </Card>

              {/* NUVA Card */}
              <Card className="bg-gray-800/50 border-purple-500/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-purple-400">
                    <DollarSign className="h-5 w-5" />
                    NUVA
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <p className="text-3xl font-bold text-purple-400">{nuvaBalance.toFixed(1)}</p>
                      <p className="text-gray-400 text-sm">≈ ${(nuvaBalance * 3.64).toFixed(2)} USD</p>
                    </div>
                    <div className="text-xs text-green-400">+15.2% (24h)</div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Send & Receive Tab */}
          <TabsContent value="send" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Send Card */}
              <Card className="bg-gray-800/50 border-red-500/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-red-400">
                    <ArrowUpRight className="h-5 w-5" />
                    Send Tokens
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-400">Send energy tokens to other addresses or convert to fiat</p>
                  <Button className="w-full bg-red-600 hover:bg-red-700">
                    <ArrowUpRight className="h-4 w-4 mr-2" />
                    Send Tokens
                  </Button>
                </CardContent>
              </Card>

              {/* Receive Card */}
              <Card className="bg-gray-800/50 border-green-500/30">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-400">
                    <ArrowDownRight className="h-5 w-5" />
                    Receive Tokens
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-gray-400">Generate QR codes and addresses for receiving tokens</p>
                  <Button className="w-full bg-green-600 hover:bg-green-700">
                    <ArrowDownRight className="h-4 w-4 mr-2" />
                    Generate Address
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* USD Offramp Tab */}
          <TabsContent value="offramp" className="space-y-6 mt-6">
            <Card className="bg-gray-800/50 border-blue-500/30">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-blue-400">
                  <CreditCard className="h-5 w-5" />
                  Cash Out to USD
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* PayPal */}
                  <div className="p-4 bg-gray-900/50 rounded-lg border border-blue-500/30">
                    <h3 className="font-bold text-blue-400 mb-2">PayPal</h3>
                    <p className="text-gray-400 text-sm mb-3">Instant transfer to PayPal account</p>
                    <Button className="w-full bg-blue-600 hover:bg-blue-700">
                      Connect PayPal
                    </Button>
                  </div>

                  {/* Bank Transfer */}
                  <div className="p-4 bg-gray-900/50 rounded-lg border border-green-500/30">
                    <h3 className="font-bold text-green-400 mb-2">Bank Transfer</h3>
                    <p className="text-gray-400 text-sm mb-3">Direct deposit to bank account</p>
                    <Button className="w-full bg-green-600 hover:bg-green-700">
                      Add Bank Account
                    </Button>
                  </div>

                  {/* Crypto Exchange */}
                  <div className="p-4 bg-gray-900/50 rounded-lg border border-purple-500/30">
                    <h3 className="font-bold text-purple-400 mb-2">Crypto Exchange</h3>
                    <p className="text-gray-400 text-sm mb-3">Convert to major cryptocurrencies</p>
                    <Button className="w-full bg-purple-600 hover:bg-purple-700">
                      Exchange Tokens
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Transaction History Tab */}
          <TabsContent value="history" className="space-y-6 mt-6">
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-orange-400">
                  <History className="h-5 w-5" />
                  Recent Transactions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {(transactions || [
                    { id: '1', transactionType: 'generation', tokenType: 'umatter', amount: 12.4, createdAt: new Date(Date.now() - 300000) },
                    { id: '2', transactionType: 'conversion', tokenType: 'nuva', amount: 25.3, createdAt: new Date(Date.now() - 3600000) },
                    { id: '3', transactionType: 'withdrawal', tokenType: 'usd', amount: 45.67, createdAt: new Date(Date.now() - 7200000) },
                    { id: '4', transactionType: 'generation', tokenType: 'tru', amount: 156.2, createdAt: new Date(Date.now() - 14400000) }
                  ]).slice(0, 5).map((tx, i) => {
                    const timeAgo = Math.floor((Date.now() - new Date(tx.createdAt).getTime()) / 60000);
                    const timeText = timeAgo < 1 ? 'just now' : timeAgo < 60 ? `${timeAgo} min ago` : `${Math.floor(timeAgo/60)} hour ago`;
                    return (
                    <div key={i} className="flex items-center justify-between p-3 bg-gray-900/50 rounded-lg">
                      <div>
                        <p className="text-white font-medium">
                          {tx.transactionType === 'generation' ? 'Energy Generation' : 
                           tx.transactionType === 'conversion' ? 'Token Conversion' :
                           tx.transactionType === 'withdrawal' ? 'USD Withdrawal' : 'Token Transfer'}
                        </p>
                        <p className="text-gray-400 text-sm">{timeText}</p>
                      </div>
                      <div className="text-right">
                        <p className={`font-bold ${tx.transactionType === 'withdrawal' ? 'text-red-400' : 'text-green-400'}`}>
                          {tx.transactionType === 'withdrawal' ? '-' : '+'}${tx.amount} {tx.tokenType.toUpperCase()}
                        </p>
                        <Badge variant="secondary" className="bg-green-500/20 text-green-300 text-xs">
                          completed
                        </Badge>
                      </div>
                    </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
}