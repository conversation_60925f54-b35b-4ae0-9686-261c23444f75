import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { PageLayout } from '@/components/PageLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { UniversalPWAInstaller } from '@/components/UniversalPWAInstaller';
import { universalPWAInstaller } from '@/lib/universal-pwa-installer';
import { 
  Puzzle, 
  Download, 
  Zap,
  Shield,
  Chrome,
  Activity,
  Globe,
  Settings,
  BarChart3,
  Users,
  TrendingUp,
  Smartphone,
  Monitor,
  Tablet
} from 'lucide-react';

export default function Extensions() {
  const [activeTab, setActiveTab] = useState('status');
  const [installStatus, setInstallStatus] = useState(universalPWAInstaller.getInstallationStatus());

  // Extension status from API
  const { data: extensionStatus } = useQuery({
    queryKey: ['/api/extension/status'],
    refetchInterval: 5000,
  });

  const isConnected = extensionStatus?.connected || false;
  const totalUMatter = extensionStatus?.totalUMatter || 0;
  const adsIntercepted = extensionStatus?.adsIntercepted || 0;

  // Safe fallback values for extension data
  const safeExtensionData = {
    version: extensionStatus?.version || '3.0.0',
    realTimeSync: extensionStatus?.realTimeSync ?? true,
    quantumFidelity: extensionStatus?.quantumFidelity || 99.2,
    extensionMetrics: {
      sessionUMatter: extensionStatus?.extensionMetrics?.sessionUMatter || 0,
      adsThisSession: extensionStatus?.extensionMetrics?.adsThisSession || 0,
    },
    networkSpeed: extensionStatus?.networkSpeed || 0,
    earnings: extensionStatus?.earnings || 0,
  };

  // Update install status when it changes
  useEffect(() => {
    const unsubscribe = universalPWAInstaller.onInstallationUpdate((status) => {
      setInstallStatus(status);
    });
    return unsubscribe;
  }, []);

  // Get platform icon based on current platform
  const getPlatformIcon = () => {
    switch(installStatus.platform) {
      case 'ios': return <Smartphone className="h-4 w-4" />;
      case 'android': return <Smartphone className="h-4 w-4" />;
      case 'desktop': return <Monitor className="h-4 w-4" />;
      default: return <Tablet className="h-4 w-4" />;
    }
  };

  return (
    <PageLayout className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
                Universal Energy App
              </h1>
              <p className="text-gray-400 mt-2">
                Install on ANY device - iOS, Android, Desktop, Mobile browsers
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Badge 
                variant="secondary" 
                className={installStatus.isInstalled ? "bg-green-500/20 text-green-300 border-green-500/30" : "bg-orange-500/20 text-orange-300 border-orange-500/30"}
              >
                <div className={`w-2 h-2 rounded-full animate-pulse mr-2 ${installStatus.isInstalled ? 'bg-green-500' : 'bg-orange-500'}`} />
                {installStatus.isInstalled ? 'Installed' : 'Ready to Install'}
              </Badge>
              <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-500/30">
                {getPlatformIcon()}
                <span className="ml-1 capitalize">{installStatus.platform}</span>
              </Badge>
            </div>
          </div>
        </div>

        {/* Extension Download Notice */}
        <div className="mb-8">
          <Card className="bg-yellow-900/20 border-yellow-500/30">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <Download className="h-8 w-8 text-yellow-400" />
                <div>
                  <h3 className="text-lg font-semibold text-yellow-400">Browser Extension Required</h3>
                  <p className="text-gray-300 mt-1">
                    Download and install the nU Universe browser extension to start generating UMatter from web interactions and ad interception.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Extension Management */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-gray-800/50">
            <TabsTrigger value="status" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
              Extension Status
            </TabsTrigger>
            <TabsTrigger value="install" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400">
              Universal Install
            </TabsTrigger>
            <TabsTrigger value="settings" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400">
              Settings
            </TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-orange-500/20 data-[state=active]:text-orange-400">
              Analytics
            </TabsTrigger>
          </TabsList>

          {/* Extension Status Tab */}
          <TabsContent value="status" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Connection Status */}
              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-400">
                    <Puzzle className="h-5 w-5" />
                    Extension Status
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Status</span>
                      <Badge className="bg-yellow-500/20 text-yellow-300">
                        Extension Available
                      </Badge>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Version</span>
                      <span className="text-blue-400">{safeExtensionData.version}</span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Compatibility</span>
                      <span className="text-cyan-400">Chrome, Edge, Firefox</span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Size</span>
                      <span className="text-purple-400">22.7KB</span>
                    </div>
                  </div>
                  
                  <div className="mt-6">
                    <Button 
                      className="w-full mb-3" 
                      onClick={() => {
                        // Use working API endpoint
                        window.location.href = '/api/extension/download';
                      }}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download Extension
                    </Button>
                    <p className="text-xs text-gray-400 text-center">
                      Extract and load as unpacked extension in Chrome Developer Mode
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Real-time Activity */}
              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-blue-400">
                    <Activity className="h-5 w-5" />
                    Live Activity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Current Session UMatter</span>
                      <span className="text-green-400 font-bold">
                        {safeExtensionData.extensionMetrics.sessionUMatter.toFixed(3)}
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-400">Ads This Session</span>
                      <span className="text-blue-400 font-bold">
                        {safeExtensionData.extensionMetrics.adsThisSession}
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-400">Network Speed</span>
                      <span className="text-purple-400 font-bold">
                        {safeExtensionData.networkSpeed} Mbps
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-gray-400">Earnings Rate</span>
                      <span className="text-orange-400 font-bold">
                        ${safeExtensionData.earnings.toFixed(4)}/hour
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Universal Installation Tab */}
          <TabsContent value="install" className="space-y-6 mt-6">
            <UniversalPWAInstaller />
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6 mt-6">
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-purple-400">
                  <Settings className="h-5 w-5" />
                  Extension Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  {/* Energy Generation Settings */}
                  <div className="border-b border-gray-700 pb-4">
                    <h3 className="text-lg font-semibold mb-3">Energy Generation</h3>
                    <div className="space-y-3">
                      <label className="flex items-center justify-between">
                        <span className="text-gray-400">Auto-convert ads to UMatter</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </label>
                      <label className="flex items-center justify-between">
                        <span className="text-gray-400">Track device metrics</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </label>
                      <label className="flex items-center justify-between">
                        <span className="text-gray-400">Real-time sync with dashboard</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </label>
                    </div>
                  </div>

                  {/* Privacy Settings */}
                  <div className="border-b border-gray-700 pb-4">
                    <h3 className="text-lg font-semibold mb-3">Privacy Controls</h3>
                    <div className="space-y-3">
                      <label className="flex items-center justify-between">
                        <span className="text-gray-400">Anonymous data sharing</span>
                        <input type="checkbox" className="rounded" />
                      </label>
                      <label className="flex items-center justify-between">
                        <span className="text-gray-400">Block tracking cookies</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </label>
                      <label className="flex items-center justify-between">
                        <span className="text-gray-400">Local data processing only</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </label>
                    </div>
                  </div>

                  {/* Notification Settings */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Notifications</h3>
                    <div className="space-y-3">
                      <label className="flex items-center justify-between">
                        <span className="text-gray-400">UMatter generation alerts</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </label>
                      <label className="flex items-center justify-between">
                        <span className="text-gray-400">Daily earnings summary</span>
                        <input type="checkbox" className="rounded" />
                      </label>
                      <label className="flex items-center justify-between">
                        <span className="text-gray-400">Extension update notifications</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </label>
                    </div>
                  </div>
                </div>

                <Button 
                  className="w-full bg-purple-600 hover:bg-purple-700"
                  onClick={() => {
                    console.log('[Extensions] Settings saved');
                    // Save settings to localStorage
                    localStorage.setItem('nuUniverse_extensionSettings', JSON.stringify({
                      autoConvertAds: true,
                      trackDeviceMetrics: true,
                      realTimeSync: true,
                      anonymousDataSharing: false,
                      blockTrackingCookies: true,
                      localDataProcessing: true,
                      umatterAlerts: true,
                      dailySummary: false,
                      updateNotifications: true,
                      lastUpdated: Date.now()
                    }));
                  }}
                >
                  Save Settings
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-orange-400">
                    <BarChart3 className="h-5 w-5" />
                    Usage Analytics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Total Sessions</span>
                      <span className="text-orange-400 font-bold">47</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Avg. Session Duration</span>
                      <span className="text-blue-400 font-bold">23.4 min</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Pages Visited</span>
                      <span className="text-green-400 font-bold">1,247</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">UMatter per Page</span>
                      <span className="text-purple-400 font-bold">0.198</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-400">
                    <TrendingUp className="h-5 w-5" />
                    Performance Trends
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <p className="text-gray-400 mb-4">Detailed analytics dashboard coming soon</p>
                    <Button className="bg-green-600 hover:bg-green-700">
                      View Full Analytics
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
}