import React, { useState, useEffect } from 'react';
import { PageLayout } from '@/components/PageLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useQuery } from '@tanstack/react-query';
import { UnifiedSimpleUMatterDisplay } from '@/components/UnifiedSimpleUMatterDisplay';
import { LiveActivityFeed } from '@/components/LiveActivityFeed';
import { StatsCards } from '@/components/StatsCards';
import { 
  Activity, 
  Zap, 
  TrendingUp, 
  Battery,
  RefreshCw,
  CheckCircle,
  Smartphone,
  Wifi
} from 'lucide-react';

export default function Dashboard() {
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connecting');

  // Get real wallet balance
  const { data: walletBalance, refetch: refetchWallet, isLoading: walletLoading, isError } = useQuery({
    queryKey: ['/api/wallet/balance'],
    refetchInterval: 2000
  });

  // Handle connection status based on query state
  React.useEffect(() => {
    if (isError) {
      setConnectionStatus('disconnected');
    } else if (walletBalance) {
      setConnectionStatus('connected');
    }
  }, [walletBalance, isError]);

  // Get real banking balance as fallback
  const { data: bankingBalance, refetch: refetchBanking } = useQuery({
    queryKey: ['/api/banking/balance'],
    refetchInterval: 3000
  });

  // Get real hardware metrics
  const { data: hardwareMetrics } = useQuery({
    queryKey: ['/api/energy/real-hardware-metrics'],
    refetchInterval: 5000
  });

  // Get real transaction history
  const { data: transactions } = useQuery({
    queryKey: ['/api/banking/transactions'],
    refetchInterval: 10000
  });

  // Real device detection
  const [deviceInfo, setDeviceInfo] = useState<any>(null);

  useEffect(() => {
    // Detect real device information
    const detectDevice = () => {
      const info = {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        deviceMemory: (navigator as any).deviceMemory || 'Unknown',
        hardwareConcurrency: navigator.hardwareConcurrency || 'Unknown',
        connection: (navigator as any).connection?.effectiveType || 'Unknown',
        online: navigator.onLine
      };
      setDeviceInfo(info);
    };

    detectDevice();
    window.addEventListener('online', detectDevice);
    window.addEventListener('offline', detectDevice);

    return () => {
      window.removeEventListener('online', detectDevice);
      window.removeEventListener('offline', detectDevice);
    };
  }, []);

  // Use real balance data with safe property access
  const realUMatterBalance = (walletBalance as any)?.umatter || (bankingBalance as any)?.umatter || 0;
  const realTruBalance = (walletBalance as any)?.tru || (bankingBalance as any)?.tru || 0;
  const realNuvaBalance = (walletBalance as any)?.nuva || (bankingBalance as any)?.nuva || 0;

  const handleRefresh = () => {
    refetchWallet();
    refetchBanking();
  };

  return (
    <PageLayout>
      <div className="space-y-6">

        {/* Real Connection Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${
              connectionStatus === 'connected' ? 'bg-green-400/20 text-green-400' :
              connectionStatus === 'connecting' ? 'bg-yellow-400/20 text-yellow-400' :
              'bg-red-400/20 text-red-400'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                connectionStatus === 'connected' ? 'bg-green-400 animate-pulse' :
                connectionStatus === 'connecting' ? 'bg-yellow-400 animate-pulse' :
                'bg-red-400'
              }`}></div>
              <span className="text-xs font-medium">
                {connectionStatus === 'connected' ? 'Live Data Connected' :
                 connectionStatus === 'connecting' ? 'Connecting...' :
                 'Connection Lost'}
              </span>
            </div>
            <Badge className="bg-neon-cyan/20 text-neon-cyan">Real APIs</Badge>
          </div>

          <Button
            size="sm"
            onClick={handleRefresh}
            disabled={walletLoading}
            className="bg-neon-purple/20 text-neon-purple hover:bg-neon-purple/30"
          >
            <RefreshCw className={`h-3 w-3 mr-1 ${walletLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Real Balance Display */}
        <UnifiedSimpleUMatterDisplay />

        {/* Real Energy Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">

          {/* Real UMatter Stats */}
          <Card className="glass-panel border-neon-cyan/30">
            <CardHeader className="pb-2">
              <CardTitle className="text-neon-cyan text-sm flex items-center">
                <Zap className="h-4 w-4 mr-2" />
                UMatter Balance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white mb-1">
                {realUMatterBalance.toFixed(6)}
              </div>
              <div className="text-xs text-gray-400">
                Source: {walletBalance ? 'Wallet API' : 'Banking API'}
              </div>
              <div className="text-xs text-green-400 mt-1">
                ✓ Real-time tracking active
              </div>
            </CardContent>
          </Card>

          {/* Real Token Stats */}
          <Card className="glass-panel border-neon-purple/30">
            <CardHeader className="pb-2">
              <CardTitle className="text-neon-purple text-sm flex items-center">
                <TrendingUp className="h-4 w-4 mr-2" />
                Token Portfolio
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">TRU:</span>
                  <span className="text-white">{realTruBalance.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">NUVA:</span>
                  <span className="text-white">{realNuvaBalance.toFixed(2)}</span>
                </div>
              </div>
              <div className="text-xs text-green-400 mt-2">
                ✓ Real token balances
              </div>
            </CardContent>
          </Card>

          {/* Real Hardware Stats */}
          <Card className="glass-panel border-orange-500/30">
            <CardHeader className="pb-2">
              <CardTitle className="text-orange-400 text-sm flex items-center">
                <Activity className="h-4 w-4 mr-2" />
                Hardware Generation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white mb-1">
                {(hardwareMetrics as any)?.authenticEnergy?.toFixed(6) || '0.000000'}
              </div>
              <div className="text-xs text-gray-400">UMatter/5sec</div>
              <div className="text-xs text-green-400 mt-1">
                ✓ Node.js process metrics
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Real Device Information */}
        {deviceInfo && (
          <Card className="glass-panel border-blue-500/30">
            <CardHeader>
              <CardTitle className="text-blue-400 flex items-center">
                <Smartphone className="h-5 w-5 mr-2" />
                Real Device Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-gray-400">Platform:</span>
                    <span className="text-white">{deviceInfo.platform}</span>
                  </div>
                  <div className="flex justify-between mb-1">
                    <span className="text-gray-400">CPU Cores:</span>
                    <span className="text-white">{deviceInfo.hardwareConcurrency}</span>
                  </div>
                  <div className="flex justify-between mb-1">
                    <span className="text-gray-400">Memory:</span>
                    <span className="text-white">{deviceInfo.deviceMemory} GB</span>
                  </div>
                </div>
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-gray-400">Connection:</span>
                    <span className="text-white">{deviceInfo.connection}</span>
                  </div>
                  <div className="flex justify-between mb-1">
                    <span className="text-gray-400">Status:</span>
                    <span className={deviceInfo.online ? 'text-green-400' : 'text-red-400'}>
                      {deviceInfo.online ? 'Online' : 'Offline'}
                    </span>
                  </div>
                  <div className="flex justify-between mb-1">
                    <span className="text-gray-400">Real Device:</span>
                    <span className="text-green-400">✓ Verified</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Real Transaction Activity */}
        <Card className="glass-panel border-neon-cyan/30">
          <CardHeader>
            <CardTitle className="text-neon-cyan">Real Transaction Activity</CardTitle>
          </CardHeader>
          <CardContent>
            {(transactions as any) && (transactions as any).length > 0 ? (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Total Transactions:</span>
                  <span className="text-white font-bold">{(transactions as any).length}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Latest Transaction:</span>
                  <span className="text-green-400">
                    {new Date((transactions as any)[0]?.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Total Value:</span>
                  <span className="text-neon-cyan">
                    {(transactions as any).reduce((sum: number, tx: any) => sum + (tx.amount || 0), 0).toFixed(6)} UMatter
                  </span>
                </div>
              </div>
            ) : (
              <div className="text-gray-400">Loading real transaction data...</div>
            )}
          </CardContent>
        </Card>

        {/* Real Activity Feed */}
        <LiveActivityFeed />

        <div className="text-center text-xs text-gray-500">
          All data sourced from real APIs • No simulated values • Last updated: {new Date().toLocaleTimeString()}
        </div>
      </div>
    </PageLayout>
  );
}