import { useState } from 'react';
import { PageLayout } from '@/components/PageLayout';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  DollarSign, 
  TrendingUp, 
  Shield,
  Eye,
  Users,
  BarChart3,
  Settings,
  Lock,
  Unlock,
  Download
} from 'lucide-react';

export default function DataMonetization() {
  const [activeTab, setActiveTab] = useState('overview');

  const monetizationStreams = [
    {
      id: 1,
      name: 'Biometric Energy Data',
      description: 'Anonymous energy generation patterns from device usage',
      status: 'active',
      earning: 45.67,
      buyers: 12,
      privacy: 'Anonymous',
      dataSize: '2.4 GB'
    },
    {
      id: 2,
      name: 'IoT Device Interactions',
      description: 'Smart home device usage patterns and optimization insights',
      status: 'active',
      earning: 78.23,
      buyers: 8,
      privacy: 'Encrypted',
      dataSize: '5.1 GB'
    },
    {
      id: 3,
      name: 'Quantum Processing Results',
      description: 'Quantum algorithm execution results and performance metrics',
      status: 'paused',
      earning: 123.45,
      buyers: 15,
      privacy: 'Aggregated',
      dataSize: '890 MB'
    }
  ];

  const privacySettings = [
    { type: 'Device Metrics', enabled: true, level: 'Anonymous' },
    { type: 'Energy Patterns', enabled: true, level: 'Encrypted' },
    { type: 'Location Data', enabled: false, level: 'Disabled' },
    { type: 'Usage Analytics', enabled: true, level: 'Aggregated' }
  ];

  return (
    <PageLayout className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
                Data Monetization
              </h1>
              <p className="text-gray-400 mt-2">
                Earn from your data while maintaining complete privacy control
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="bg-green-500/20 text-green-300 border-green-500/30">
                <DollarSign className="w-3 h-3 mr-1" />
                Earning Active
              </Badge>
              <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-500/30">
                <Shield className="w-3 h-3 mr-1" />
                Privacy Protected
              </Badge>
            </div>
          </div>
        </div>

        {/* Earnings Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gray-800/50 border-green-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Total Earnings</p>
                  <p className="text-2xl font-bold text-green-400">$247.35</p>
                </div>
                <DollarSign className="h-8 w-8 text-green-400" />
              </div>
              <p className="text-xs text-green-400 mt-2">+23.4% this month</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-blue-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Active Streams</p>
                  <p className="text-2xl font-bold text-blue-400">2</p>
                </div>
                <BarChart3 className="h-8 w-8 text-blue-400" />
              </div>
              <p className="text-xs text-blue-400 mt-2">1 paused stream</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-purple-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Data Buyers</p>
                  <p className="text-2xl font-bold text-purple-400">35</p>
                </div>
                <Users className="h-8 w-8 text-purple-400" />
              </div>
              <p className="text-xs text-purple-400 mt-2">+5 new this week</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-orange-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Privacy Score</p>
                  <p className="text-2xl font-bold text-orange-400">9.8/10</p>
                </div>
                <Shield className="h-8 w-8 text-orange-400" />
              </div>
              <p className="text-xs text-orange-400 mt-2">Excellent protection</p>
            </CardContent>
          </Card>
        </div>

        {/* Data Monetization Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-gray-800/50">
            <TabsTrigger value="overview" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
              Earnings Overview
            </TabsTrigger>
            <TabsTrigger value="streams" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400">
              Data Streams
            </TabsTrigger>
            <TabsTrigger value="privacy" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400">
              Privacy Control
            </TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-orange-500/20 data-[state=active]:text-orange-400">
              Analytics
            </TabsTrigger>
          </TabsList>

          {/* Earnings Overview Tab */}
          <TabsContent value="overview" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Earnings Chart */}
              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-400">
                    <TrendingUp className="h-5 w-5" />
                    Earnings Trend
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-400">This Month</span>
                      <span className="text-green-400 font-bold">$67.89</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Last Month</span>
                      <span className="text-blue-400 font-bold">$55.02</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Average/Month</span>
                      <span className="text-purple-400 font-bold">$61.45</span>
                    </div>
                    <div className="pt-4">
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">Growth Rate</span>
                        <span className="text-green-400">+23.4%</span>
                      </div>
                      <Progress value={75} className="h-2" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Top Performing Data */}
              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-blue-400">
                    <BarChart3 className="h-5 w-5" />
                    Top Performing Data
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[
                      { name: 'Quantum Results', earnings: '$123.45', growth: '+18%' },
                      { name: 'IoT Interactions', earnings: '$78.23', growth: '+12%' },
                      { name: 'Biometric Energy', earnings: '$45.67', growth: '+8%' }
                    ].map((item, i) => (
                      <div key={i} className="flex justify-between items-center p-3 bg-gray-900/50 rounded-lg">
                        <div>
                          <p className="text-white text-sm font-medium">{item.name}</p>
                          <p className="text-green-400 text-xs">{item.growth} growth</p>
                        </div>
                        <span className="text-green-400 font-bold">{item.earnings}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Data Streams Tab */}
          <TabsContent value="streams" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {monetizationStreams.map((stream) => (
                <Card key={stream.id} className="bg-gray-800/50 border-gray-700">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-white">{stream.name}</CardTitle>
                      <Badge 
                        variant="secondary" 
                        className={stream.status === 'active' ? 'bg-green-500/20 text-green-300' : 'bg-yellow-500/20 text-yellow-300'}
                      >
                        {stream.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-400 text-sm mb-4">{stream.description}</p>
                    
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Monthly Earnings</span>
                        <span className="text-green-400 font-bold">${stream.earning}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Buyers</span>
                        <span className="text-blue-400 font-bold">{stream.buyers}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Data Size</span>
                        <span className="text-purple-400 font-bold">{stream.dataSize}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Privacy Level</span>
                        <Badge variant="outline" className="text-xs">
                          <Shield className="h-3 w-3 mr-1" />
                          {stream.privacy}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="flex gap-2 mt-4">
                      <Button variant="outline" className="flex-1">
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                      <Button variant="outline" className="flex-1">
                        {stream.status === 'active' ? (
                          <>
                            <Lock className="h-4 w-4 mr-2" />
                            Pause
                          </>
                        ) : (
                          <>
                            <Unlock className="h-4 w-4 mr-2" />
                            Resume
                          </>
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Privacy Control Tab */}
          <TabsContent value="privacy" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-purple-400">
                    <Shield className="h-5 w-5" />
                    Privacy Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {privacySettings.map((setting, index) => (
                    <div key={index} className="flex justify-between items-center p-3 bg-gray-900/50 rounded-lg">
                      <div>
                        <p className="text-white text-sm font-medium">{setting.type}</p>
                        <p className="text-gray-400 text-xs">{setting.level}</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input 
                          type="checkbox" 
                          defaultChecked={setting.enabled}
                          className="sr-only peer" 
                        />
                        <div className="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-orange-400">
                    <Settings className="h-5 w-5" />
                    Data Control
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <Button variant="outline" className="w-full justify-start">
                      <Download className="h-4 w-4 mr-2" />
                      Download My Data
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Eye className="h-4 w-4 mr-2" />
                      View Data Usage
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Lock className="h-4 w-4 mr-2" />
                      Revoke All Access
                    </Button>
                    <Button variant="destructive" className="w-full justify-start">
                      <Settings className="h-4 w-4 mr-2" />
                      Delete All Data
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-orange-400">
                    <BarChart3 className="h-5 w-5" />
                    Revenue Analytics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Revenue per GB</span>
                      <span className="text-green-400 font-bold">$15.67</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Avg. Buyer Value</span>
                      <span className="text-blue-400 font-bold">$7.06</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Data Utilization</span>
                      <span className="text-purple-400 font-bold">73%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Market Demand</span>
                      <span className="text-orange-400 font-bold">High</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-blue-400">
                    <TrendingUp className="h-5 w-5" />
                    Market Insights
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <p className="text-gray-400 mb-4">Detailed market analytics coming soon</p>
                    <Button className="bg-blue-600 hover:bg-blue-700">
                      View Full Report
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
}