import { useState } from 'react';
import { PageLayout } from '@/components/PageLayout';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { 
  Cpu, 
  Zap, 
  Calculator,
  TrendingUp,
  Clock,
  Users,
  Award,
  Atom,
  BarChart3,
  Settings
} from 'lucide-react';

export default function QuantumMarketplace() {
  const [activeTab, setActiveTab] = useState('browse');

  const quantumTasks = [
    {
      id: 1,
      title: 'Quantum Optimization Algorithm',
      description: 'Solve complex routing optimization for logistics networks',
      reward: '150 NUVA',
      difficulty: 'Advanced',
      estimatedTime: '2-4 hours',
      requiredQubits: 20,
      algorithm: 'QAOA'
    },
    {
      id: 2,
      title: 'Molecular Simulation',
      description: 'Quantum simulation of protein folding mechanisms',
      reward: '300 NUVA',
      difficulty: 'Expert',
      estimatedTime: '6-12 hours',
      requiredQubits: 50,
      algorithm: 'VQE'
    },
    {
      id: 3,
      title: 'Cryptographic Analysis',
      description: 'Factor large integers using quantum algorithms',
      reward: '500 NUVA',
      difficulty: 'Master',
      estimatedTime: '12-24 hours',
      requiredQubits: 100,
      algorithm: "Shor's"
    }
  ];

  const myTasks = [
    {
      id: 101,
      title: 'Prime Factorization Task',
      status: 'Processing',
      progress: 65,
      reward: '75 NUVA',
      timeRemaining: '2h 15m'
    },
    {
      id: 102,
      title: 'Quantum Search Algorithm',
      status: 'Completed',
      progress: 100,
      reward: '120 NUVA',
      timeRemaining: 'Finished'
    }
  ];

  return (
    <PageLayout className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-600 bg-clip-text text-transparent">
                Quantum Marketplace
              </h1>
              <p className="text-gray-400 mt-2">
                Contribute quantum computing power and earn NUVA tokens
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-500/30">
                <Atom className="w-3 h-3 mr-1" />
                Quantum Active
              </Badge>
              <Badge variant="secondary" className="bg-purple-500/20 text-purple-300 border-purple-500/30">
                <Cpu className="w-3 h-3 mr-1" />
                3 Cores Available
              </Badge>
            </div>
          </div>
        </div>

        {/* Quantum Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gray-800/50 border-blue-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Available Tasks</p>
                  <p className="text-2xl font-bold text-blue-400">24</p>
                </div>
                <Calculator className="h-8 w-8 text-blue-400" />
              </div>
              <p className="text-xs text-blue-400 mt-2">+6 new today</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-purple-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">NUVA Earned</p>
                  <p className="text-2xl font-bold text-purple-400">1,247</p>
                </div>
                <Award className="h-8 w-8 text-purple-400" />
              </div>
              <p className="text-xs text-purple-400 mt-2">+195 this week</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-cyan-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Quantum Cores</p>
                  <p className="text-2xl font-bold text-cyan-400">3/5</p>
                </div>
                <Cpu className="h-8 w-8 text-cyan-400" />
              </div>
              <p className="text-xs text-cyan-400 mt-2">60% utilization</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-green-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Tasks Completed</p>
                  <p className="text-2xl font-bold text-green-400">87</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-400" />
              </div>
              <p className="text-xs text-green-400 mt-2">96% success rate</p>
            </CardContent>
          </Card>
        </div>

        {/* Marketplace Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-gray-800/50">
            <TabsTrigger value="browse" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400">
              Available Tasks
            </TabsTrigger>
            <TabsTrigger value="active" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400">
              My Tasks
            </TabsTrigger>
            <TabsTrigger value="resources" className="data-[state=active]:bg-cyan-500/20 data-[state=active]:text-cyan-400">
              Quantum Resources
            </TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
              Performance
            </TabsTrigger>
          </TabsList>

          {/* Available Tasks Tab */}
          <TabsContent value="browse" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {quantumTasks.map((task) => (
                <Card key={task.id} className="bg-gray-800/50 border-gray-700 hover:border-blue-500/50 transition-colors">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-white">{task.title}</CardTitle>
                      <Badge 
                        variant="secondary" 
                        className={
                          task.difficulty === 'Advanced' ? 'bg-yellow-500/20 text-yellow-300' :
                          task.difficulty === 'Expert' ? 'bg-orange-500/20 text-orange-300' :
                          'bg-red-500/20 text-red-300'
                        }
                      >
                        {task.difficulty}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-400 text-sm mb-4">{task.description}</p>
                    
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Reward</span>
                        <span className="text-green-400 font-bold">{task.reward}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Est. Time</span>
                        <span className="text-blue-400">{task.estimatedTime}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Qubits Required</span>
                        <span className="text-purple-400">{task.requiredQubits}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Algorithm</span>
                        <Badge variant="outline" className="text-xs">
                          {task.algorithm}
                        </Badge>
                      </div>
                    </div>
                    
                    <Button className="w-full mt-4 bg-blue-600 hover:bg-blue-700">
                      <Zap className="h-4 w-4 mr-2" />
                      Accept Task
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* My Tasks Tab */}
          <TabsContent value="active" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {myTasks.map((task) => (
                <Card key={task.id} className="bg-gray-800/50 border-gray-700">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-white">{task.title}</CardTitle>
                      <Badge 
                        variant="secondary" 
                        className={
                          task.status === 'Processing' ? 'bg-yellow-500/20 text-yellow-300' :
                          'bg-green-500/20 text-green-300'
                        }
                      >
                        {task.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between mb-2">
                          <span className="text-gray-400">Progress</span>
                          <span className="text-blue-400">{task.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <div 
                            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${task.progress}%` }}
                          />
                        </div>
                      </div>
                      
                      <div className="flex justify-between">
                        <span className="text-gray-400">Reward</span>
                        <span className="text-green-400 font-bold">{task.reward}</span>
                      </div>
                      
                      <div className="flex justify-between">
                        <span className="text-gray-400">Time Remaining</span>
                        <span className="text-orange-400">{task.timeRemaining}</span>
                      </div>
                    </div>
                    
                    {task.status === 'Processing' && (
                      <Button variant="outline" className="w-full mt-4">
                        <Settings className="h-4 w-4 mr-2" />
                        Monitor Task
                      </Button>
                    )}
                    
                    {task.status === 'Completed' && (
                      <Button className="w-full mt-4 bg-green-600 hover:bg-green-700">
                        <Award className="h-4 w-4 mr-2" />
                        Claim Reward
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Quantum Resources Tab */}
          <TabsContent value="resources" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-cyan-400">
                    <Cpu className="h-5 w-5" />
                    Quantum Core Status
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[1, 2, 3].map((core) => (
                      <div key={core} className="flex justify-between items-center p-3 bg-gray-900/50 rounded-lg">
                        <div>
                          <p className="text-white font-medium">Quantum Core {core}</p>
                          <p className="text-gray-400 text-sm">nU-Q{core} Architecture</p>
                        </div>
                        <Badge className="bg-green-500/20 text-green-300">
                          Active
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-purple-400">
                    <Atom className="h-5 w-5" />
                    Quantum Capacity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Total Qubits</span>
                      <span className="text-purple-400 font-bold">150</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Available Qubits</span>
                      <span className="text-green-400 font-bold">90</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">In Use</span>
                      <span className="text-blue-400 font-bold">60</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Fidelity</span>
                      <span className="text-cyan-400 font-bold">99.2%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-400">
                    <BarChart3 className="h-5 w-5" />
                    Performance Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Tasks Completed</span>
                      <span className="text-green-400 font-bold">87</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Success Rate</span>
                      <span className="text-blue-400 font-bold">96.5%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Avg. Completion Time</span>
                      <span className="text-purple-400 font-bold">4.2 hours</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Total NUVA Earned</span>
                      <span className="text-cyan-400 font-bold">1,247</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-orange-400">
                    <TrendingUp className="h-5 w-5" />
                    Quantum Efficiency
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <p className="text-gray-400 mb-4">Quantum efficiency analytics coming soon</p>
                    <Button className="bg-orange-600 hover:bg-orange-700">
                      View Detailed Analysis
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
}