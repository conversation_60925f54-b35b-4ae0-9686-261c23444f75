import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { socialInvitationSystem, type SocialInvitation } from '@/lib/invitation-system';
import { sbuGenerator, type SBUToken } from '@/lib/sbu-generator';
import { proximityDeviceDiscovery, type DiscoveredDevice, type ProximityInvitation } from '@/lib/proximity-device-discovery';
import { useToast } from '@/hooks/use-toast';
import { Users, Gift, Zap, Share2, QrCode, Trophy, Wallet, Battery, Wifi, Bluetooth, Smartphone, Laptop, Search, MapPin, Signal } from 'lucide-react';

export default function SocialSync() {
  const [invitations, setInvitations] = useState<SocialInvitation[]>([]);
  const [sbuTokens, setSbuTokens] = useState<SBUToken[]>([]);
  const [discoveredDevices, setDiscoveredDevices] = useState<DiscoveredDevice[]>([]);
  const [proximityInvites, setProximityInvites] = useState<ProximityInvitation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [sbuDialogOpen, setSbuDialogOpen] = useState(false);
  const [proximityDialogOpen, setProximityDialogOpen] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<DiscoveredDevice | null>(null);
  const { toast } = useToast();

  // Form states
  const [inviteForm, setInviteForm] = useState({
    name: '',
    phone: '',
    email: '',
    message: ''
  });

  const [sbuForm, setSbuForm] = useState({
    energy: '',
    sourceType: 'kinetic' as const
  });

  useEffect(() => {
    loadData();
    loadRealDevices();
    
    // Poll for real device updates every 5 seconds
    const deviceInterval = setInterval(() => {
      if (isScanning) {
        loadRealDevices();
      }
    }, 5000);

    return () => clearInterval(deviceInterval);
  }, []);

  const loadData = () => {
    setInvitations(socialInvitationSystem.getInvitations());
    setSbuTokens(sbuGenerator.collection.tokens);
    setProximityInvites(proximityDeviceDiscovery.getProximityInvitations());
    loadRealDevices();
  };

  const loadRealDevices = async () => {
    try {
      const response = await fetch('/api/devices/discovered');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const responseText = await response.text();
      let data;
      
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Load devices JSON parse error:', parseError);
        console.error('Load devices response text:', responseText);
        throw new Error('Invalid response format from device API');
      }
      
      if (data && data.success && Array.isArray(data.devices)) {
        const convertedDevices = data.devices.map((device: any) => ({
          id: device.id || `device_${Date.now()}`,
          name: device.name || 'Unknown Device',
          type: (device.type || 'Unknown').toLowerCase(),
          ipAddress: device.ipAddress || 'Unknown IP',
          port: device.port || 80,
          signal: device.isOnline ? 'Strong' : 'Weak',
          distance: '< 100m',
          lastSeen: device.lastSeen || Date.now(),
          connectionType: 'WiFi',
          batteryLevel: device.batteryLevel || 50,
          capabilities: device.capabilities || [],
          networkInfo: { ipAddress: device.ipAddress || 'Unknown IP' },
          metadata: {
            isTargetDevice: device.name && device.name.includes('iPhone') && device.ipAddress === '*************'
          }
        }));
        
        setDiscoveredDevices(convertedDevices);
        console.log(`[SocialSync] Loaded ${convertedDevices.length} real devices from backend`);
      } else {
        setDiscoveredDevices([]);
        console.log('[SocialSync] No devices found from backend API');
      }
    } catch (error) {
      console.error('Failed to load real devices:', error);
      setDiscoveredDevices([]);
    }
  };

  const handleSendInvitation = async () => {
    if (!inviteForm.name || (!inviteForm.phone && !inviteForm.email)) {
      toast({
        title: "Missing Information",
        description: "Please provide a name and either phone or email",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      // Send real cross-device notification
      const response = await fetch('/api/notifications/send-cross-device', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          targetPhone: inviteForm.phone,
          targetEmail: inviteForm.email,
          senderDevice: `${navigator.platform} Device`,
          message: inviteForm.message || `${inviteForm.name}, join nU Universe and start earning from your device's energy!`
        })
      });

      const result = await response.json();

      if (result.success) {
        toast({
          title: "Real Invitation Sent!",
          description: `Sent to ${inviteForm.name} via ${result.deliveryMethods.join(', ')} - 25% NUVA bonus awaits!`,
        });
        setInviteForm({ name: '', phone: '', email: '', message: '' });
        setInviteDialogOpen(false);
        loadData();
      } else {
        throw new Error(result.error || 'Failed to send real notification');
      }
    } catch (error) {
      toast({
        title: "Failed to Send Real Notification",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateSBU = async () => {
    const energy = parseFloat(sbuForm.energy);
    if (isNaN(energy) || energy <= 0) {
      toast({
        title: "Invalid Energy",
        description: "Please enter a valid energy amount",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      const token = await sbuGenerator.generateToken(
        energy,
        sbuForm.sourceType,
        'current-device'
      );

      toast({
        title: "SBU Token Created!",
        description: `Generated ${energy} UMatter token from ${sbuForm.sourceType} source`,
      });
      
      setSbuForm({ energy: '', sourceType: 'kinetic' });
      setSbuDialogOpen(false);
      loadData();
    } catch (error) {
      toast({
        title: "Failed to Create SBU",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartDeviceDiscovery = async () => {
    setIsScanning(true);
    try {
      const response = await fetch('/api/devices/scan', { method: 'POST' });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }
      
      const responseText = await response.text();
      let data;
      
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('JSON parse error:', parseError);
        console.error('Response text:', responseText);
        throw new Error('Invalid response format from server');
      }
      
      if (data && data.success) {
        toast({
          title: "Real Network Scan Started",
          description: "TCP scanning 192.168.x.x networks using authentic socket connections...",
        });
        
        // Wait for scan to complete then load results
        setTimeout(async () => {
          try {
            await loadRealDevices();
            const deviceResponse = await fetch('/api/devices/discovered');
            
            if (!deviceResponse.ok) {
              throw new Error(`HTTP ${deviceResponse.status}: ${deviceResponse.statusText}`);
            }
            
            const deviceResponseText = await deviceResponse.text();
            let deviceData;
            
            try {
              deviceData = JSON.parse(deviceResponseText);
            } catch (parseError) {
              console.error('Device discovery JSON parse error:', parseError);
              console.error('Device response text:', deviceResponseText);
              throw new Error('Invalid device response format');
            }
            
            if (deviceData && deviceData.success) {
              const deviceCount = deviceData.count || 0;
              if (deviceCount === 0) {
                toast({
                  title: "Real Network Scan Complete",
                  description: "Authentic TCP scan found 0 devices (normal in sandboxed environment). In your home network, this would discover real phones and tablets.",
                });
              } else {
                toast({
                  title: "Real Devices Found!",
                  description: `Authentic network scan discovered ${deviceCount} real device(s)`,
                });
              }
            } else {
              toast({
                title: "Scan Complete",
                description: "Network scan finished successfully.",
              });
            }
          } catch (scanError) {
            console.error('Post-scan error:', scanError);
            toast({
              title: "Scan Complete",
              description: "Network scan finished. Ready for real device discovery.",
            });
          }
          setIsScanning(false);
        }, 3000);
        
      } else {
        throw new Error(data.error || 'Scan failed');
      }
    } catch (error) {
      console.error('Device discovery failed:', error);
      toast({
        title: "Network Scan Failed", 
        description: error instanceof Error ? error.message : "Unable to start TCP network scan",
        variant: "destructive",
      });
      setIsScanning(false);
    }
  };

  const handleSendProximityInvite = async (device: DiscoveredDevice, message?: string) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/devices/send-invitation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          deviceId: device.id,
          message: message || `Join nU Universe! Get 25% NUVA bonus + battery charging boost!`
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result && result.success) {
        toast({
          title: "Real Invitation Sent!",
          description: `Direct message sent to ${device.name} at ${device.ipAddress} - they'll receive notification with bonuses!`,
        });
      } else {
        throw new Error(result?.error || 'Failed to send real invitation');
      }

      setProximityDialogOpen(false);
      setSelectedDevice(null);
      await loadRealDevices();

    } catch (error) {
      console.error('Invitation error:', error);
      toast({
        title: "Real Invitation Failed",
        description: error instanceof Error ? error.message : "Failed to reach device",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRequestPermissions = async () => {
    try {
      // Request notification permissions first
      const notificationGranted = await Notification.requestPermission();
      
      // Request Bluetooth permissions
      const bluetoothGranted = await proximityDeviceDiscovery.requestBluetoothPermission();
      
      if (notificationGranted === 'granted' && bluetoothGranted) {
        toast({
          title: "All Permissions Granted",
          description: "Push notifications and Bluetooth access enabled. Real invitations ready!",
        });
      } else if (notificationGranted === 'granted') {
        toast({
          title: "Notifications Enabled",
          description: "Push notifications granted. Bluetooth permission needed for device discovery.",
        });
      } else if (bluetoothGranted) {
        toast({
          title: "Bluetooth Enabled",
          description: "Bluetooth access granted. Notification permission needed for real invitations.",
        });
      } else {
        throw new Error('Permissions required for real invitation delivery');
      }
    } catch (error) {
      toast({
        title: "Permissions Required",
        description: "Both notification and Bluetooth permissions needed for real invitation delivery",
        variant: "destructive"
      });
    }
  };

  const inviteStats = socialInvitationSystem.getInvitationStats();
  const sbuStats = sbuGenerator.getCollectionStats();

  return (
    <div className="min-h-screen bg-gray-950 text-white p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
            Social Sync & Energy Storage
          </h1>
          <p className="text-gray-300 text-lg">
            Invite friends with 25% NUVA rewards and enhance energy storage with SBU tokens
          </p>
        </div>

        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">Total Invitations</CardTitle>
              <Users className="h-4 w-4 text-cyan-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{inviteStats.total}</div>
              <p className="text-xs text-gray-400">
                {inviteStats.accepted} accepted
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gray-900 border-gray-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">NUVA Awarded</CardTitle>
              <Gift className="h-4 w-4 text-yellow-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{inviteStats.totalNuvaAwarded.toFixed(2)}</div>
              <p className="text-xs text-gray-400">
                From referrals
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gray-900 border-gray-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">SBU Tokens</CardTitle>
              <Battery className="h-4 w-4 text-green-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{sbuStats.totalTokens}</div>
              <p className="text-xs text-gray-400">
                {sbuStats.totalEnergy.toFixed(2)} UMatter stored
              </p>
            </CardContent>
          </Card>

          <Card className="bg-gray-900 border-gray-800">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-300">Storage Efficiency</CardTitle>
              <Zap className="h-4 w-4 text-purple-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-white">{(sbuStats.averageEfficiency * 100).toFixed(1)}%</div>
              <p className="text-xs text-gray-400">
                {sbuStats.storage.compressionRatio}x compression
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="proximity" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-gray-900">
            <TabsTrigger value="proximity" className="data-[state=active]:bg-gray-800">
              Proximity Discovery
            </TabsTrigger>
            <TabsTrigger value="invitations" className="data-[state=active]:bg-gray-800">
              Social Invitations
            </TabsTrigger>
            <TabsTrigger value="sbu-storage" className="data-[state=active]:bg-gray-800">
              SBU Energy Storage
            </TabsTrigger>
          </TabsList>

          <TabsContent value="proximity" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-white">Real-Time Device Discovery</h2>
              <div className="flex gap-3">
                <Button onClick={handleRequestPermissions} variant="outline" className="border-gray-700">
                  <Bluetooth className="w-4 h-4 mr-2" />
                  Grant Permissions
                </Button>
                <Button 
                  onClick={handleStartDeviceDiscovery} 
                  disabled={isScanning}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Search className="w-4 h-4 mr-2" />
                  {isScanning ? 'Scanning...' : 'Discover Devices'}
                </Button>
              </div>
            </div>

            {/* Discovery Status */}
            <Card className="bg-gray-900 border-gray-800">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`w-3 h-3 rounded-full ${isScanning ? 'bg-green-500 animate-pulse' : 'bg-gray-500'}`} />
                    <span className="text-white">
                      {isScanning ? 'Actively scanning for nearby devices...' : 'Device discovery inactive'}
                    </span>
                  </div>
                  <Badge className="bg-blue-600">
                    {discoveredDevices.length} devices found
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Discovered Devices */}
            <div className="space-y-4">
              {discoveredDevices.length === 0 ? (
                <Card className="bg-gray-900 border-gray-800">
                  <CardContent className="text-center py-12">
                    <Search className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                    <p className="text-gray-400 text-lg">No devices discovered yet</p>
                    <p className="text-sm text-gray-500 mb-6">
                      Start scanning to find nearby phones, tablets, and computers
                    </p>
                    <Button 
                      onClick={handleStartDeviceDiscovery}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      Start Device Discovery
                    </Button>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {discoveredDevices.map((device) => {
                    const isTargetDevice = device.metadata?.isTargetDevice;
                    const isSurpriseCandidate = device.metadata?.surpriseCandidate;
                    
                    return (
                      <Card 
                        key={device.id} 
                        className={`${isTargetDevice 
                          ? 'bg-gradient-to-br from-pink-900/50 to-purple-900/50 border-pink-500/50 shadow-lg shadow-pink-500/20' 
                          : 'bg-gray-900 border-gray-800'
                        } hover:border-blue-600 transition-colors`}
                      >
                        <CardHeader className="pb-3">
                          <div className="flex justify-between items-start">
                            <div className="flex items-center space-x-3">
                              {device.type === 'phone' ? (
                                <Smartphone className={`w-5 h-5 ${isTargetDevice ? 'text-pink-400' : 'text-blue-400'}`} />
                              ) : device.type === 'laptop' ? (
                                <Laptop className="w-5 h-5 text-purple-400" />
                              ) : (
                                <Users className="w-5 h-5 text-gray-400" />
                              )}
                              <div>
                                <CardTitle className={`${isTargetDevice ? 'text-pink-200' : 'text-white'} text-sm flex items-center gap-2`}>
                                  {device.name}
                                  {isSurpriseCandidate && (
                                    <span className="text-xs bg-pink-600 px-1.5 py-0.5 rounded text-white">
                                      Surprise Target
                                    </span>
                                  )}
                                </CardTitle>
                                <CardDescription className="text-gray-400 text-xs">
                                  {device.platform} • {device.type}
                                  {isTargetDevice && device.distance && (
                                    <span className="text-pink-400 ml-1">• {device.distance.toFixed(1)}m away</span>
                                  )}
                                </CardDescription>
                              </div>
                            </div>
                            <Badge 
                              variant="outline" 
                              className={`text-xs ${
                                isTargetDevice ? 'border-pink-500 text-pink-400' :
                                device.status === 'online' ? 'border-green-500 text-green-400' :
                                device.status === 'discoverable' ? 'border-blue-500 text-blue-400' :
                                'border-gray-500 text-gray-400'
                              }`}
                            >
                              {device.status}
                            </Badge>
                          </div>
                        </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-3">
                          {/* Connection Info */}
                          <div className="flex items-center space-x-2 text-sm">
                            {device.connectionType === 'bluetooth' ? (
                              <Bluetooth className="w-4 h-4 text-blue-400" />
                            ) : (
                              <Wifi className="w-4 h-4 text-green-400" />
                            )}
                            <span className="text-gray-300 capitalize">{device.connectionType}</span>
                            {device.distance && (
                              <>
                                <MapPin className="w-3 h-3 text-gray-500 ml-2" />
                                <span className="text-gray-400 text-xs">{device.distance.toFixed(1)}m</span>
                              </>
                            )}
                          </div>

                          {/* Signal Strength */}
                          {device.rssi && (
                            <div className="flex items-center space-x-2 text-sm">
                              <Signal className="w-4 h-4 text-gray-500" />
                              <span className="text-gray-400 text-xs">{device.rssi} dBm</span>
                              <div className="flex space-x-1">
                                {[1, 2, 3, 4].map(bar => (
                                  <div 
                                    key={bar}
                                    className={`w-1 h-3 rounded ${
                                      device.rssi && device.rssi > (-30 - bar * 15) ? 'bg-green-500' : 'bg-gray-600'
                                    }`}
                                  />
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Capabilities */}
                          <div className="flex flex-wrap gap-1">
                            {device.capabilities.canReceiveInvites && (
                              <Badge className="bg-green-600 text-xs px-2 py-1">Invites</Badge>
                            )}
                            {device.capabilities.supportsP2P && (
                              <Badge className="bg-purple-600 text-xs px-2 py-1">P2P</Badge>
                            )}
                            {device.capabilities.hasNuUniverse && (
                              <Badge className="bg-cyan-600 text-xs px-2 py-1">nU Universe</Badge>
                            )}
                          </div>

                          {/* Send Invite Button */}
                          <Button 
                            onClick={() => {
                              setSelectedDevice(device);
                              setProximityDialogOpen(true);
                            }}
                            className={`w-full text-sm ${
                              isTargetDevice 
                                ? 'bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 shadow-lg' 
                                : 'bg-blue-600 hover:bg-blue-700'
                            }`}
                            disabled={!device.capabilities.canReceiveInvites}
                          >
                            <Share2 className="w-3 h-3 mr-2" />
                            {isSurpriseCandidate ? 'Send Surprise Invite!' : 'Send Invitation'}
                          </Button>

                          {/* Last Seen */}
                          <p className="text-xs text-gray-500 text-center">
                            Seen {Math.round((Date.now() - device.lastSeen) / 1000)}s ago
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  );})}
                </div>
              )}
            </div>

            {/* Special Help Section for iPhone Detection */}
            {discoveredDevices.some(d => d.metadata?.surpriseCandidate) && (
              <Card className="bg-gradient-to-r from-pink-900/20 to-purple-900/20 border-pink-800/50">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-pink-500 rounded-full mt-2 animate-pulse" />
                    <div>
                      <h3 className="text-pink-200 font-medium mb-1">Perfect Surprise Target Detected!</h3>
                      <p className="text-gray-300 text-sm">
                        Found an iPhone nearby that doesn't have nU Universe yet. This is perfect for surprising your girlfriend! 
                        The invitation will appear as a notification on her device.
                      </p>
                      <p className="text-pink-400 text-xs mt-2">
                        ✨ Pro tip: The invitation will look like it came from "nU Universe" and include your device info
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Proximity Invitation Dialog */}
            <Dialog open={proximityDialogOpen} onOpenChange={setProximityDialogOpen}>
              <DialogContent className="bg-gray-900 border-gray-800">
                <DialogHeader>
                  <DialogTitle className="text-white">
                    Send Proximity Invitation
                  </DialogTitle>
                  <DialogDescription className="text-gray-300">
                    Send an invitation directly to {selectedDevice?.name} via {selectedDevice?.connectionType}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="bg-gray-800 rounded-lg p-4">
                    <div className="flex items-center space-x-3 mb-2">
                      {selectedDevice?.type === 'phone' ? (
                        <Smartphone className="w-5 h-5 text-blue-400" />
                      ) : (
                        <Laptop className="w-5 h-5 text-purple-400" />
                      )}
                      <span className="text-white font-medium">{selectedDevice?.name}</span>
                    </div>
                    <div className="text-sm text-gray-400">
                      Connection: {selectedDevice?.connectionType} • Platform: {selectedDevice?.platform}
                    </div>
                    {selectedDevice?.distance && (
                      <div className="text-sm text-gray-400">
                        Distance: ~{selectedDevice.distance.toFixed(1)} meters
                      </div>
                    )}
                  </div>
                  
                  <div className={`${selectedDevice?.metadata?.surpriseCandidate 
                    ? 'bg-gradient-to-r from-pink-900/20 to-purple-900/20 border border-pink-800' 
                    : 'bg-blue-900/20 border border-blue-800'
                  } rounded-lg p-4`}>
                    <div className="flex items-center space-x-2 mb-2">
                      <Gift className="w-4 h-4 text-yellow-400" />
                      <span className="text-white font-medium">
                        {selectedDevice?.metadata?.surpriseCandidate ? 'Surprise Invitation + 25% NUVA Bonus!' : '25% NUVA Bonus Included'}
                      </span>
                    </div>
                    <p className="text-sm text-gray-300">
                      {selectedDevice?.metadata?.surpriseCandidate ? (
                        <>
                          This will send a surprise invitation to her iPhone! She'll get a notification about joining nU Universe 
                          with 25% bonus NUVA tokens. Perfect way to introduce her to the platform!
                          <span className="text-pink-400 block mt-1">💝 Complete surprise - she has no idea this is coming!</span>
                        </>
                      ) : (
                        <>
                          Your friend will receive 25% bonus NUVA tokens when they join nU Universe through your invitation.
                          {selectedDevice?.capabilities.hasNuUniverse && (
                            <span className="text-cyan-400 block mt-1">✨ This device already has nU Universe!</span>
                          )}
                        </>
                      )}
                    </p>
                  </div>

                  <div className="flex space-x-3">
                    <Button 
                      onClick={() => setProximityDialogOpen(false)}
                      variant="outline"
                      className="flex-1 border-gray-700"
                    >
                      Cancel
                    </Button>
                    <Button 
                      onClick={() => selectedDevice && handleSendProximityInvite(selectedDevice)}
                      disabled={isLoading}
                      className={`flex-1 ${selectedDevice?.metadata?.surpriseCandidate 
                        ? 'bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700' 
                        : 'bg-blue-600 hover:bg-blue-700'
                      }`}
                    >
                      {isLoading ? 'Sending...' : 
                       selectedDevice?.metadata?.surpriseCandidate ? 'Send Surprise Invite!' : 'Send Invitation'}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

          <TabsContent value="invitations" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-white">Social Invitations</h2>
              <Dialog open={inviteDialogOpen} onOpenChange={setInviteDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-cyan-600 hover:bg-cyan-700">
                    <Share2 className="w-4 h-4 mr-2" />
                    Send Invitation
                  </Button>
                </DialogTrigger>
                <DialogContent className="bg-gray-900 border-gray-800">
                  <DialogHeader>
                    <DialogTitle className="text-white">Invite Friend to nU Universe</DialogTitle>
                    <DialogDescription className="text-gray-300">
                      Send an invitation with 25% NUVA bonus to get your friend started
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="name" className="text-gray-300">Friend's Name</Label>
                      <Input
                        id="name"
                        value={inviteForm.name}
                        onChange={(e) => setInviteForm({ ...inviteForm, name: e.target.value })}
                        className="bg-gray-800 border-gray-700 text-white"
                        placeholder="Enter their name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone" className="text-gray-300">Phone Number (Optional)</Label>
                      <Input
                        id="phone"
                        value={inviteForm.phone}
                        onChange={(e) => setInviteForm({ ...inviteForm, phone: e.target.value })}
                        className="bg-gray-800 border-gray-700 text-white"
                        placeholder="+****************"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email" className="text-gray-300">Email (Optional)</Label>
                      <Input
                        id="email"
                        type="email"
                        value={inviteForm.email}
                        onChange={(e) => setInviteForm({ ...inviteForm, email: e.target.value })}
                        className="bg-gray-800 border-gray-700 text-white"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <Label htmlFor="message" className="text-gray-300">Custom Message (Optional)</Label>
                      <Textarea
                        id="message"
                        value={inviteForm.message}
                        onChange={(e) => setInviteForm({ ...inviteForm, message: e.target.value })}
                        className="bg-gray-800 border-gray-700 text-white"
                        placeholder="Add a personal message..."
                        rows={3}
                      />
                    </div>
                    <Button 
                      onClick={handleSendInvitation} 
                      disabled={isLoading}
                      className="w-full bg-cyan-600 hover:bg-cyan-700"
                    >
                      {isLoading ? 'Sending...' : 'Send Invitation with 25% NUVA Bonus'}
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* Invitations List */}
            <div className="space-y-4">
              {invitations.length === 0 ? (
                <Card className="bg-gray-900 border-gray-800">
                  <CardContent className="text-center py-8">
                    <Users className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                    <p className="text-gray-400">No invitations sent yet</p>
                    <p className="text-sm text-gray-500">Start inviting friends to earn NUVA rewards!</p>
                  </CardContent>
                </Card>
              ) : (
                invitations.map((invitation) => (
                  <Card key={invitation.id} className="bg-gray-900 border-gray-800">
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-white">{invitation.inviterName}</CardTitle>
                          <CardDescription className="text-gray-400">
                            {invitation.phoneNumber || invitation.email}
                          </CardDescription>
                        </div>
                        <Badge 
                          variant={
                            invitation.status === 'accepted' ? 'default' :
                            invitation.status === 'sent' ? 'secondary' :
                            invitation.status === 'expired' ? 'destructive' : 'outline'
                          }
                          className={
                            invitation.status === 'accepted' ? 'bg-green-600' :
                            invitation.status === 'sent' ? 'bg-blue-600' :
                            invitation.status === 'expired' ? 'bg-red-600' : 'bg-gray-600'
                          }
                        >
                          {invitation.status}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-300 mb-4">{invitation.message}</p>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-400">
                          Sent {new Date(invitation.createdAt).toLocaleDateString()}
                        </span>
                        <span className="text-yellow-400 font-medium">
                          {invitation.nuvaReward * 100}% NUVA Bonus
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          <TabsContent value="sbu-storage" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-white">SBU Energy Storage</h2>
              <Dialog open={sbuDialogOpen} onOpenChange={setSbuDialogOpen}>
                <DialogTrigger asChild>
                  <Button className="bg-green-600 hover:bg-green-700">
                    <Battery className="w-4 h-4 mr-2" />
                    Create SBU Token
                  </Button>
                </DialogTrigger>
                <DialogContent className="bg-gray-900 border-gray-800">
                  <DialogHeader>
                    <DialogTitle className="text-white">Create SBU Storage Token</DialogTitle>
                    <DialogDescription className="text-gray-300">
                      Generate a quantum-encrypted energy storage unit
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="energy" className="text-gray-300">Energy Amount (UMatter)</Label>
                      <Input
                        id="energy"
                        type="number"
                        value={sbuForm.energy}
                        onChange={(e) => setSbuForm({ ...sbuForm, energy: e.target.value })}
                        className="bg-gray-800 border-gray-700 text-white"
                        placeholder="Enter UMatter amount"
                        step="0.1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="sourceType" className="text-gray-300">Energy Source</Label>
                      <select
                        id="sourceType"
                        value={sbuForm.sourceType}
                        onChange={(e) => setSbuForm({ ...sbuForm, sourceType: e.target.value as any })}
                        className="w-full bg-gray-800 border border-gray-700 text-white rounded-md px-3 py-2"
                      >
                        <option value="kinetic">Kinetic</option>
                        <option value="solar">Solar</option>
                        <option value="P2P">P2P</option>
                        <option value="thermal">Thermal</option>
                        <option value="wind">Wind</option>
                        <option value="nuclear">Nuclear</option>
                      </select>
                    </div>
                    <Button 
                      onClick={handleCreateSBU} 
                      disabled={isLoading}
                      className="w-full bg-green-600 hover:bg-green-700"
                    >
                      {isLoading ? 'Creating...' : 'Create SBU Token'}
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            {/* SBU Tokens List */}
            <div className="space-y-4">
              {sbuTokens.length === 0 ? (
                <Card className="bg-gray-900 border-gray-800">
                  <CardContent className="text-center py-8">
                    <Battery className="w-12 h-12 text-gray-600 mx-auto mb-4" />
                    <p className="text-gray-400">No SBU tokens created yet</p>
                    <p className="text-sm text-gray-500">Create quantum-encrypted energy storage units!</p>
                  </CardContent>
                </Card>
              ) : (
                sbuTokens.map((token) => (
                  <Card key={token.id} className="bg-gray-900 border-gray-800">
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-white text-sm font-mono">{token.id}</CardTitle>
                          <CardDescription className="text-gray-400">
                            {token.sourceType} energy source
                          </CardDescription>
                        </div>
                        <Badge className="bg-green-600">
                          {token.energy.toFixed(2)} UM
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-400">Efficiency:</span>
                          <span className="text-white ml-2">{(token.metadata.efficiency * 100).toFixed(1)}%</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Purity:</span>
                          <span className="text-white ml-2">{(token.metadata.purity * 100).toFixed(1)}%</span>
                        </div>
                        <div>
                          <span className="text-gray-400">Resonance:</span>
                          <span className="text-white ml-2">{(token.metadata.resonance * 100).toFixed(1)}%</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center mt-4 text-sm">
                        <span className="text-gray-400">
                          Created {new Date(token.createdAt).toLocaleDateString()}
                        </span>
                        <span className="text-purple-400 font-mono">
                          {token.quantumSignature}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}