import React, { useState, useEffect } from 'react';
import { useParams, useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { Zap, Gift, Smartphone, CheckCircle } from 'lucide-react';

interface InvitationData {
  id: string;
  senderDevice: string;
  message: string;
  nuvaBonus: number;
  timestamp: number;
  status: string;
}

interface AcceptanceResult {
  success: boolean;
  bonuses: {
    nuvaBonus: number;
    batteryBonus: number;
    totalBonus: number;
  };
  message: string;
  redirectUrl: string;
}

export default function InvitationAcceptance() {
  const { invitationId } = useParams();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  
  const [invitation, setInvitation] = useState<InvitationData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAccepting, setIsAccepting] = useState(false);
  const [isAccepted, setIsAccepted] = useState(false);
  const [acceptanceResult, setAcceptanceResult] = useState<AcceptanceResult | null>(null);

  useEffect(() => {
    loadInvitationData();
  }, [invitationId]);

  const loadInvitationData = async () => {
    try {
      const response = await fetch(`/api/notifications/${invitationId}`);
      
      if (response.ok) {
        const data = await response.json();
        setInvitation(data);
      } else {
        toast({
          title: "Invitation Not Found",
          description: "This invitation link is invalid or has expired.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Failed to load invitation:', error);
      toast({
        title: "Connection Error",
        description: "Unable to load invitation details. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAcceptInvitation = async () => {
    setIsAccepting(true);
    try {
      // Get device information for bonus calculation
      const deviceInfo = {
        platform: navigator.platform,
        userAgent: navigator.userAgent,
        battery: (navigator as any).getBattery ? await (navigator as any).getBattery() : null,
        timestamp: Date.now()
      };

      const response = await fetch(`/api/notifications/accept/${invitationId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(deviceInfo)
      });

      const result = await response.json();

      if (result.success) {
        setAcceptanceResult(result);
        setIsAccepted(true);
        
        toast({
          title: "Welcome to nU Universe!",
          description: `You've received ${(result.bonuses.totalBonus * 100).toFixed(1)}% bonus tokens!`,
        });

        // Redirect to dashboard after 3 seconds
        setTimeout(() => {
          setLocation('/dashboard');
        }, 3000);

      } else {
        throw new Error(result.error || 'Failed to accept invitation');
      }

    } catch (error) {
      console.error('Failed to accept invitation:', error);
      toast({
        title: "Acceptance Failed",
        description: error instanceof Error ? error.message : "Unable to accept invitation",
        variant: "destructive"
      });
    } finally {
      setIsAccepting(false);
    }
  };

  const handleDeclineInvitation = () => {
    toast({
      title: "Invitation Declined",
      description: "You can always join nU Universe later!",
    });
    
    // Redirect to home page
    setTimeout(() => {
      setLocation('/');
    }, 2000);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-slate-950 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-cyan-400 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-slate-400">Loading invitation...</p>
        </div>
      </div>
    );
  }

  if (!invitation) {
    return (
      <div className="min-h-screen bg-slate-950 flex items-center justify-center">
        <Card className="max-w-md mx-auto bg-slate-900 border-slate-800">
          <CardHeader className="text-center">
            <CardTitle className="text-red-400">Invitation Not Found</CardTitle>
            <CardDescription className="text-slate-400">
              This invitation link is invalid or has expired.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={() => setLocation('/')}
              className="w-full bg-slate-700 hover:bg-slate-600"
            >
              Go to nU Universe
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isAccepted && acceptanceResult) {
    return (
      <div className="min-h-screen bg-slate-950 flex items-center justify-center p-4">
        <Card className="max-w-lg mx-auto bg-slate-900 border-slate-800">
          <CardHeader className="text-center">
            <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <CardTitle className="text-2xl text-green-400">Welcome to nU Universe!</CardTitle>
            <CardDescription className="text-slate-400">
              Your invitation has been successfully accepted
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-cyan-400 mb-2">Your Bonuses</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-slate-800 p-3 rounded-lg">
                  <div className="text-2xl font-bold text-purple-400">
                    {(acceptanceResult.bonuses.nuvaBonus * 100).toFixed(1)}%
                  </div>
                  <div className="text-sm text-slate-400">NUVA Bonus</div>
                </div>
                <div className="bg-slate-800 p-3 rounded-lg">
                  <div className="text-2xl font-bold text-green-400">
                    {(acceptanceResult.bonuses.batteryBonus * 100).toFixed(1)}%
                  </div>
                  <div className="text-sm text-slate-400">Battery Boost</div>
                </div>
              </div>
            </div>
            
            <Separator className="bg-slate-700" />
            
            <div className="text-center">
              <p className="text-slate-300 mb-4">
                You're now part of the energy revolution! Start earning UMatter tokens from your device's energy.
              </p>
              <p className="text-sm text-slate-400">
                Redirecting to your dashboard in 3 seconds...
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-950 flex items-center justify-center p-4">
      <Card className="max-w-lg mx-auto bg-slate-900 border-slate-800">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-cyan-400 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <Zap className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl text-cyan-400">You're Invited to nU Universe!</CardTitle>
          <CardDescription className="text-slate-400">
            Join the energy revolution and start earning from your device
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="text-center">
            <p className="text-slate-300">
              <span className="font-semibold text-cyan-400">{invitation.senderDevice}</span> has invited you to join nU Universe
            </p>
            {invitation.message && (
              <p className="text-slate-400 mt-2 italic">"{invitation.message}"</p>
            )}
          </div>

          <Separator className="bg-slate-700" />

          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-cyan-400 flex items-center gap-2">
              <Gift className="w-5 h-5" />
              Special Launch Bonuses
            </h3>
            
            <div className="grid gap-3">
              <div className="flex items-center justify-between bg-slate-800 p-3 rounded-lg">
                <span className="text-slate-300">NUVA Token Bonus</span>
                <Badge variant="secondary" className="bg-purple-500/20 text-purple-400">
                  25%
                </Badge>
              </div>
              
              <div className="flex items-center justify-between bg-slate-800 p-3 rounded-lg">
                <span className="text-slate-300">Welcome Bonus</span>
                <Badge variant="secondary" className="bg-green-500/20 text-green-400">
                  15%
                </Badge>
              </div>
              
              <div className="flex items-center justify-between bg-slate-800 p-3 rounded-lg">
                <span className="text-slate-300 flex items-center gap-2">
                  <Smartphone className="w-4 h-4" />
                  Battery Charging Boost
                </span>
                <Badge variant="secondary" className="bg-cyan-500/20 text-cyan-400">
                  Up to 30%
                </Badge>
              </div>
            </div>
          </div>

          <Separator className="bg-slate-700" />

          <div className="space-y-3">
            <Button 
              onClick={handleAcceptInvitation}
              disabled={isAccepting}
              className="w-full bg-gradient-to-r from-cyan-500 to-purple-500 hover:from-cyan-600 hover:to-purple-600 text-white font-semibold py-3"
            >
              {isAccepting ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Activating Bonuses...
                </div>
              ) : (
                'Accept & Get Bonuses'
              )}
            </Button>
            
            <Button 
              variant="outline"
              onClick={handleDeclineInvitation}
              disabled={isAccepting}
              className="w-full border-slate-600 text-slate-400 hover:bg-slate-800"
            >
              Maybe Later
            </Button>
          </div>

          <div className="text-center text-xs text-slate-500">
            By accepting, you agree to start earning UMatter tokens from your device's energy consumption
          </div>
        </CardContent>
      </Card>
    </div>
  );
}