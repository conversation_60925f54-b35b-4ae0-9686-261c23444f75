import { useEffect } from 'react';
import { <PERSON> } from 'wouter';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Network, 
  Eye, 
  Zap,
  Lock,
  Brain,
  Globe,
  ChevronRight,
  Star
} from 'lucide-react';

export default function Landing() {
  useEffect(() => {
    // Add cosmic background animation
    document.body.classList.add('cosmic-bg');
    return () => {
      document.body.classList.remove('cosmic-bg');
    };
  }, []);

  const features = [
    {
      icon: <Shield className="w-6 h-6" />,
      title: "SpUnder Encryption",
      description: "Revolutionary silent privacy tracking with advanced cryptographic protection",
      color: "text-neon-cyan"
    },
    {
      icon: <Network className="w-6 h-6" />,
      title: "Interaction Webs",
      description: "Visualize complex user interaction patterns in real-time network graphs",
      color: "text-neon-purple"
    },
    {
      icon: <Brain className="w-6 h-6" />,
      title: "AI Analysis",
      description: "Meta-AI orchestration with pattern recognition and anomaly detection",
      color: "text-yellow-400"
    },
    {
      icon: <Lock className="w-6 h-6" />,
      title: "DID Authentication",
      description: "Decentralized identity management replacing traditional cookies",
      color: "text-green-400"
    }
  ];

  const stats = [
    { label: "Privacy Compliance", value: "100%", icon: <Shield className="w-4 h-4" /> },
    { label: "Encryption Strength", value: "AES-256", icon: <Lock className="w-4 h-4" /> },
    { label: "Real-time Tracking", value: "∞", icon: <Eye className="w-4 h-4" /> },
    { label: "AI Confidence", value: "94.2%", icon: <Brain className="w-4 h-4" /> }
  ];

  return (
    <div className="min-h-screen bg-space text-text-primary cosmic-bg">
      {/* Hero Section */}
      <header className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-neon-cyan/5 via-transparent to-neon-purple/5"></div>
        
        <div className="relative container mx-auto px-4 py-16">
          <div className="text-center max-w-4xl mx-auto">
            {/* Logo/Brand */}
            <div className="flex items-center justify-center space-x-3 mb-8">
              <div className="w-12 h-12 bg-gradient-to-r from-neon-cyan to-neon-purple rounded-full animate-pulse-neon"></div>
              <h1 className="text-4xl md:text-6xl font-orbitron font-bold animate-glow">
                nU Web
              </h1>
            </div>

            {/* Hero Title */}
            <h2 className="text-2xl md:text-4xl font-bold mb-6 leading-tight">
              Revolutionary{' '}
              <span className="text-neon-cyan">Privacy-Preserving</span>{' '}
              Interaction Tracking
            </h2>

            {/* Hero Subtitle */}
            <p className="text-lg md:text-xl text-text-secondary mb-8 max-w-2xl mx-auto leading-relaxed">
              Replace traditional cookies with encrypted, DID-based tracking. 
              Weave interactive webs of user interactions while maintaining absolute privacy.
            </p>

            {/* Hero CTA */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button 
                size="lg"
                className="bg-neon-cyan text-space hover:bg-neon-cyan/90 font-semibold px-8 py-3"
                onClick={() => window.location.href = '/api/login'}
              >
                <Globe className="w-5 h-5 mr-2" />
                Enter nU Web
                <ChevronRight className="w-5 h-5 ml-2" />
              </Button>
              
              <Button 
                size="lg" 
                variant="outline"
                className="border-neon-purple text-neon-purple hover:bg-neon-purple/10 font-semibold px-8 py-3"
              >
                <Eye className="w-5 h-5 mr-2" />
                Watch Demo
              </Button>
            </div>

            {/* Stats Row */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="flex items-center justify-center space-x-1 text-neon-cyan mb-1">
                    {stat.icon}
                    <span className="font-mono font-bold">{stat.value}</span>
                  </div>
                  <div className="text-xs text-text-secondary">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </header>

      {/* Features Section */}
      <section className="py-16 bg-panel/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <Badge variant="outline" className="mb-4 text-neon-purple border-neon-purple">
              <Star className="w-3 h-3 mr-1" />
              Revolutionary Technology
            </Badge>
            <h3 className="text-3xl font-bold mb-4">Next-Generation Privacy Tracking</h3>
            <p className="text-text-secondary max-w-2xl mx-auto">
              Experience the future of user interaction monitoring with our advanced 
              SpUnder technology and AI-powered analysis systems.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="bg-panel/60 glass-panel border-text-secondary/20 hover:border-neon-cyan/40 transition-colors group">
                <CardContent className="p-6 text-center">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-panel/80 ${feature.color} mb-4 group-hover:animate-float`}>
                    {feature.icon}
                  </div>
                  <h4 className="font-semibold mb-2 text-text-primary">{feature.title}</h4>
                  <p className="text-sm text-text-secondary leading-relaxed">
                    {feature.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Technology Stack Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold mb-4">Powered by nUOS Ecosystem</h3>
            <p className="text-text-secondary max-w-2xl mx-auto">
              Built on cutting-edge technologies for maximum security, privacy, and performance.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {/* SpUnder Technology */}
            <Card className="bg-panel/40 glass-panel border-neon-cyan/20">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <Shield className="w-6 h-6 text-neon-cyan" />
                  <h4 className="font-semibold text-neon-cyan">SpUnder Protocol</h4>
                </div>
                <ul className="space-y-2 text-sm text-text-secondary">
                  <li>• Silent privacy tracking</li>
                  <li>• Cryptographic hashing</li>
                  <li>• Merkle tree verification</li>
                  <li>• Zero-knowledge proofs</li>
                </ul>
              </CardContent>
            </Card>

            {/* Memvid Storage */}
            <Card className="bg-panel/40 glass-panel border-neon-purple/20">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <Network className="w-6 h-6 text-neon-purple" />
                  <h4 className="font-semibold text-neon-purple">Memvid Storage</h4>
                </div>
                <ul className="space-y-2 text-sm text-text-secondary">
                  <li>• Video-based memory storage</li>
                  <li>• Semantic search capabilities</li>
                  <li>• Intelligent compression</li>
                  <li>• IPFS integration</li>
                </ul>
              </CardContent>
            </Card>

            {/* AI Orchestration */}
            <Card className="bg-panel/40 glass-panel border-yellow-400/20">
              <CardContent className="p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <Brain className="w-6 h-6 text-yellow-400" />
                  <h4 className="font-semibold text-yellow-400">Meta-AI Engine</h4>
                </div>
                <ul className="space-y-2 text-sm text-text-secondary">
                  <li>• Multi-model orchestration</li>
                  <li>• Pattern recognition</li>
                  <li>• Anomaly detection</li>
                  <li>• Real-time insights</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-16 bg-gradient-to-r from-neon-cyan/10 via-transparent to-neon-purple/10">
        <div className="container mx-auto px-4 text-center">
          <h3 className="text-3xl font-bold mb-4">Ready to Experience the Future?</h3>
          <p className="text-text-secondary mb-8 max-w-2xl mx-auto">
            Join the privacy revolution. Start tracking user interactions with 
            unprecedented security and transparency.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg"
              className="bg-neon-cyan text-space hover:bg-neon-cyan/90 font-semibold px-8 py-3"
              onClick={() => window.location.href = '/api/login'}
            >
              <Zap className="w-5 h-5 mr-2" />
              Start Tracking Now
            </Button>
            
            <Button 
              size="lg" 
              variant="outline"
              className="border-text-secondary text-text-secondary hover:bg-text-secondary/10 font-semibold px-8 py-3"
            >
              Learn More
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 border-t border-text-secondary/20">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="w-6 h-6 bg-gradient-to-r from-neon-cyan to-neon-purple rounded-full"></div>
              <span className="font-orbitron font-bold">nU Web</span>
            </div>
            
            <div className="text-sm text-text-secondary">
              Powered by nUOS Ecosystem • Privacy First • Quantum Secured
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
