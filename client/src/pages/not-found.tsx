import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "wouter";
import { ArrowLeft, Home, Zap } from "lucide-react";

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black flex items-center justify-center px-4">
      <div className="text-center space-y-8 max-w-md">
        {/* Animated Energy Icon */}
        <div className="relative">
          <div className="w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-pulse">
            <Zap className="w-12 h-12 text-white" />
          </div>
          <div className="absolute inset-0 w-24 h-24 mx-auto border-4 border-cyan-400/30 rounded-full animate-spin"></div>
        </div>
        
        {/* Error Message */}
        <div className="space-y-4">
          <h1 className="text-6xl font-bold text-transparent bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text">
            404
          </h1>
          <h2 className="text-2xl font-semibold text-white">
            Quantum Portal Not Found
          </h2>
          <p className="text-gray-400 leading-relaxed">
            The energy pathway you're looking for doesn't exist in this dimension. 
            Let's redirect your quantum energy back to a known portal.
          </p>
        </div>
        
        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/">
            <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-lg transition-all duration-200 flex items-center gap-2">
              <Home className="w-4 h-4" />
              Return to Dashboard
            </Button>
          </Link>
          
          <Link href="/energy-hub">
            <Button 
              variant="outline" 
              className="border-cyan-500 text-cyan-400 hover:bg-cyan-500/10 px-6 py-2 rounded-lg transition-all duration-200 flex items-center gap-2"
            >
              <Zap className="w-4 h-4" />
              Go to Energy Hub
            </Button>
          </Link>
          
          <Button 
            variant="outline" 
            onClick={() => window.history.back()}
            className="border-gray-600 text-gray-300 hover:bg-gray-800 px-6 py-2 rounded-lg transition-all duration-200 flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Go Back
          </Button>
        </div>
        
        {/* Quick Navigation */}
        <div className="grid grid-cols-2 gap-3 mt-8 text-xs">
          <Link href="/data-marketplace">
            <div className="p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors">
              <div className="text-purple-400 font-medium">Data Market</div>
              <div className="text-gray-400">Monetize your data</div>
            </div>
          </Link>
          <Link href="/quantum-marketplace">
            <div className="p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors">
              <div className="text-blue-400 font-medium">Quantum Market</div>
              <div className="text-gray-400">Computing power</div>
            </div>
          </Link>
          <Link href="/extensions">
            <div className="p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors">
              <div className="text-green-400 font-medium">Extensions</div>
              <div className="text-gray-400">Browser tools</div>
            </div>
          </Link>
          <Link href="/ai-search">
            <div className="p-3 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 transition-colors">
              <div className="text-orange-400 font-medium">AI Search</div>
              <div className="text-gray-400">Smart discovery</div>
            </div>
          </Link>
        </div>
        
        {/* Additional Info */}
        <div className="text-sm text-gray-500 mt-8">
          <p>
            If you believe this is an error, please contact the nU Universe support team.
          </p>
        </div>
      </div>
    </div>
  );
}
