import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { PageLayout } from '@/components/PageLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Battery, 
  Zap, 
  Database,
  TrendingUp,
  Settings,
  Plus,
  ArrowUpDown,
  BarChart3,
  Shield
} from 'lucide-react';

export default function EnergyStorage() {
  const [selectedPool, setSelectedPool] = useState('personal');

  // Real-time storage data
  const { data: energyBalance } = useQuery({
    queryKey: ['/api/banking/balance'],
    refetchInterval: 5000,
  });

  const pools = [
    {
      id: 'personal',
      name: 'Personal Pool',
      type: 'Individual Storage',
      capacity: 500,
      current: energyBalance?.umatterBalance || 247.93,
      efficiency: 94,
      color: 'cyan',
      description: 'Your private energy reservoir'
    },
    {
      id: 'community',
      name: 'Community Pool',
      type: 'Shared Network',
      capacity: 2500,
      current: energyBalance?.truBalance || 1456.7,
      efficiency: 87,
      color: 'green',
      description: 'Collaborative energy sharing'
    },
    {
      id: 'quantum',
      name: 'Quantum Reserve',
      type: 'Advanced Storage',
      capacity: 10000,
      current: energyBalance?.totalEnergyGenerated || 3247.2,
      efficiency: 99,
      color: 'purple',
      description: 'Quantum-enhanced energy storage'
    }
  ];

  const selectedPoolData = pools.find(p => p.id === selectedPool) || pools[0];
  const utilizationPercent = (selectedPoolData.current / selectedPoolData.capacity) * 100;

  return (
    <PageLayout className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-green-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
                Energy Storage
              </h1>
              <p className="text-gray-400 mt-2">
                Manage your energy pools and storage systems
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="bg-green-500/20 text-green-300 border-green-500/30">
                <Shield className="w-3 h-3 mr-1" />
                Secure Storage
              </Badge>
            </div>
          </div>
        </div>

        {/* Storage Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {pools.map((pool) => (
            <Card 
              key={pool.id}
              className={`cursor-pointer transition-all duration-200 bg-gray-800/50 border-${pool.color}-500/30 hover:border-${pool.color}-500/50 ${
                selectedPool === pool.id ? `ring-2 ring-${pool.color}-500/50` : ''
              }`}
              onClick={() => setSelectedPool(pool.id)}
            >
              <CardHeader>
                <CardTitle className={`flex items-center gap-2 text-${pool.color}-400`}>
                  <Battery className="h-5 w-5" />
                  {pool.name}
                </CardTitle>
                <p className="text-gray-400 text-sm">{pool.type}</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-400">Current</span>
                      <span className={`text-${pool.color}-400 font-bold`}>
                        {pool.current.toFixed(1)} UMatter
                      </span>
                    </div>
                    <Progress 
                      value={(pool.current / pool.capacity) * 100}
                      className="h-2"
                    />
                    <div className="flex justify-between mt-1 text-xs text-gray-400">
                      <span>0</span>
                      <span>{pool.capacity} UMatter</span>
                    </div>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Efficiency</span>
                    <span className="text-green-400">{pool.efficiency}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Detailed Pool Management */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Pool Details */}
          <div className="lg:col-span-2">
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className={`flex items-center gap-2 text-${selectedPoolData.color}-400`}>
                  <Database className="h-5 w-5" />
                  {selectedPoolData.name} Management
                </CardTitle>
                <p className="text-gray-400">{selectedPoolData.description}</p>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Capacity Overview */}
                <div className="bg-gray-900/50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Storage Capacity</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Current Storage</span>
                      <span className={`text-2xl font-bold text-${selectedPoolData.color}-400`}>
                        {selectedPoolData.current.toFixed(1)} UMatter
                      </span>
                    </div>
                    
                    <Progress value={utilizationPercent} className="h-4" />
                    
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-400">Utilization</p>
                        <p className={`font-bold text-${selectedPoolData.color}-400`}>
                          {utilizationPercent.toFixed(1)}%
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-400">Available</p>
                        <p className="font-bold text-green-400">
                          {(selectedPoolData.capacity - selectedPoolData.current).toFixed(1)} UMatter
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-400">Efficiency</p>
                        <p className="font-bold text-blue-400">{selectedPoolData.efficiency}%</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Energy Transfer Controls */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button className={`bg-${selectedPoolData.color}-600 hover:bg-${selectedPoolData.color}-700`}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Energy
                  </Button>
                  <Button variant="outline" className={`border-${selectedPoolData.color}-600 text-${selectedPoolData.color}-400 hover:bg-${selectedPoolData.color}-600/20`}>
                    <ArrowUpDown className="h-4 w-4 mr-2" />
                    Transfer Energy
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Storage Analytics */}
          <div className="space-y-6">
            {/* Performance Metrics */}
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-blue-400">
                  <BarChart3 className="h-5 w-5" />
                  Performance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Storage Rate</span>
                    <span className="text-green-400">+12.4 UMatter/hr</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Loss Rate</span>
                    <span className="text-red-400">-0.8 UMatter/hr</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Net Gain</span>
                    <span className="text-blue-400">+11.6 UMatter/hr</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Storage History */}
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-orange-400">
                  <TrendingUp className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { action: 'Energy Added', amount: '+45.2 UMatter', time: '2 min ago' },
                    { action: 'Auto-Transfer', amount: '+12.8 UMatter', time: '15 min ago' },
                    { action: 'Storage Optimized', amount: '+2.1% efficiency', time: '1 hour ago' },
                  ].map((activity, i) => (
                    <div key={i} className="flex justify-between items-center p-2 bg-gray-900/50 rounded">
                      <div>
                        <p className="text-white text-sm font-medium">{activity.action}</p>
                        <p className="text-gray-400 text-xs">{activity.time}</p>
                      </div>
                      <span className="text-green-400 text-sm font-bold">{activity.amount}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Settings */}
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-gray-400">
                  <Settings className="h-5 w-5" />
                  Settings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full">
                  <Settings className="h-4 w-4 mr-2" />
                  Configure Storage
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}