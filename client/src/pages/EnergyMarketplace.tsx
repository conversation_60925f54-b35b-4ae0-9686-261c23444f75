import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, 
  Zap, 
  Battery, 
  Coins, 
  TrendingUp, 
  Globe, 
  Heart, 
  Smartphone,
  ArrowRight,
  Timer,
  DollarSign,
  Settings,
  Activity
} from 'lucide-react';
import { motion } from 'framer-motion';
import { energyEconomy, ENERGY_CONSTANTS, DATA_PRICING, UMATTER_CONVERSIONS } from '@/lib/energyEconomy';
import { EnergyDashboard } from '@/components/EnergyDashboard';
import { UnifiedEnergyDisplay } from '@/components/UnifiedEnergyDisplay';
import { IoTEnergyManager } from '@/components/IoTEnergyManager';
import { useToast } from '@/hooks/use-toast';
import { useNuvaStore } from '@/lib/stores/nuvaStore';
import { NuvaEnergyStorage } from '@/components/NuvaEnergyStorage';
import { useQuery } from '@tanstack/react-query';
import { PageLayout } from '@/components/PageLayout';
import { apiRequest } from '@/lib/queryClient';

interface EnergyPackage {
  id: string;
  name: string;
  description: string;
  dataMB: number;
  umatterRequired: number;
  truValue: number;
  nuvaValue: number;
  usdValue: number;
  energyType: 'sleep' | 'active' | 'joy' | 'mixed';
  duration: string;
  popularity: number;
}

const ENERGY_PACKAGES: EnergyPackage[] = [
  {
    id: 'sleep-data',
    name: 'Sleep Patterns',
    description: 'Passive neural data during 8h sleep cycle',
    dataMB: ENERGY_CONSTANTS.DATA_MB_SLEEP,
    umatterRequired: UMATTER_CONVERSIONS.SLEEP_UM,
    truValue: UMATTER_CONVERSIONS.SLEEP_UM * 0.1,
    nuvaValue: UMATTER_CONVERSIONS.SLEEP_UM,
    usdValue: ENERGY_CONSTANTS.DATA_MB_SLEEP * DATA_PRICING.BLENDED_PRICE_PER_MB,
    energyType: 'sleep',
    duration: '8 hours',
    popularity: 95
  },
  {
    id: 'active-browsing',
    name: 'Active Browsing',
    description: 'High-interaction data from scrolling and clicking',
    dataMB: ENERGY_CONSTANTS.DATA_MB_SCROLL,
    umatterRequired: UMATTER_CONVERSIONS.SCROLL_UM,
    truValue: UMATTER_CONVERSIONS.SCROLL_UM * 0.1,
    nuvaValue: UMATTER_CONVERSIONS.SCROLL_UM,
    usdValue: ENERGY_CONSTANTS.DATA_MB_SCROLL * DATA_PRICING.BLENDED_PRICE_PER_MB,
    energyType: 'active',
    duration: '2 hours',
    popularity: 87
  },
  {
    id: 'joy-premium',
    name: 'Joy State Premium',
    description: 'High-dopamine neural data with 15% efficiency boost',
    dataMB: ENERGY_CONSTANTS.DATA_MB_DAILY,
    umatterRequired: UMATTER_CONVERSIONS.DAILY_UM * 1.15,
    truValue: UMATTER_CONVERSIONS.DAILY_UM * 1.15 * 0.1,
    nuvaValue: UMATTER_CONVERSIONS.DAILY_UM * 1.15,
    usdValue: ENERGY_CONSTANTS.DATA_MB_DAILY * DATA_PRICING.BLENDED_PRICE_PER_MB * 1.3,
    energyType: 'joy',
    duration: '24 hours',
    popularity: 76
  },
  {
    id: 'daily-complete',
    name: 'Complete Daily Package',
    description: 'Full day of neural activity data (sleep + active)',
    dataMB: ENERGY_CONSTANTS.DATA_MB_DAILY,
    umatterRequired: UMATTER_CONVERSIONS.DAILY_UM,
    truValue: UMATTER_CONVERSIONS.DAILY_UM * 0.1,
    nuvaValue: UMATTER_CONVERSIONS.DAILY_UM,
    usdValue: DATA_PRICING.PACKAGE_PRICE_DAILY,
    energyType: 'mixed',
    duration: '24 hours',
    popularity: 92
  }
];

interface EnergyBot {
  id: string;
  name: string;
  status: 'active' | 'inactive' | 'maintenance';
  energyGenerated: number;
  efficiency: number;
  lastUpdate: string;
  location: string;
}

interface EnergyStorage {
  id: string;
  type: string;
  capacity: number;
  currentLevel: number;
  efficiency: number;
  status: 'charging' | 'discharging' | 'idle';
}

export default function EnergyMarketplace() {
  const [selectedPackage, setSelectedPackage] = useState<EnergyPackage | null>(null);
  const [showEnergyStorage, setShowEnergyStorage] = useState(false);
  const [isCreatingPackage, setIsCreatingPackage] = useState(false);
  const { toast } = useToast();

  // Use the comprehensive energy store
  const {
    metrics,
    packages: userPackages,
    devices,
    bots,
    networkStats,
    updateMetrics,
    addPackage,
    calculateEnergyConversion,
    startRealTimeSync,
    stopRealTimeSync
  } = useNuvaStore();

  // Fetch real energy metrics from server
  const { data: serverEnergyMetrics, isLoading: metricsLoading } = useQuery({
    queryKey: ['/api/energy/metrics'],
    refetchInterval: 10000 // Update every 10 seconds
  });

  // Fetch quantum fabric stats for energy-info duality
  const { data: quantumStats } = useQuery({
    queryKey: ['/api/marketplace/stats'],
    refetchInterval: 5000
  });

  // Fetch NQUF capacity for Ubit calculations
  const { data: nqufCapacity } = useQuery({
    queryKey: ['/api/nquf/capacity'],
    refetchInterval: 15000
  });

  useEffect(() => {
    // Start real-time energy synchronization
    startRealTimeSync();

    return () => {
      stopRealTimeSync();
    };
  }, [startRealTimeSync, stopRealTimeSync]);

  // Calculate current energy state based on user activity
  useEffect(() => {
    const updateEnergyState = () => {
      const isActive = Date.now() % 30000 < 15000; // Simulate active/rest cycles
      const conversion = calculateEnergyConversion(
        metrics.batteryLevel,
        isActive,
        metrics.joyLevel
      );

      updateMetrics({
        currentUMatter: metrics.currentUMatter + conversion.umatter * 0.001,
        currentTrU: metrics.currentTrU + conversion.tru * 0.001,
        currentNUva: metrics.currentNUva + conversion.nuva * 0.001,
        dailyDataMB: metrics.dailyDataMB + conversion.dataMB * 0.001
      });
    };

    const interval = setInterval(updateEnergyState, 3000);
    return () => clearInterval(interval);
  }, [metrics, calculateEnergyConversion, updateMetrics]);

  const handleCreatePackage = async (pkg: EnergyPackage) => {
    const currentUbits = serverEnergyMetrics?.ubitConversion?.totalUbitsAllocated || 1000000;
    const requiredUbits = pkg.energyRequired || pkg.umatterRequired * 100000; // Convert UM to Ubits

    if (currentUbits < requiredUbits) {
      toast({
        title: "Insufficient Ubits",
        description: `Need ${requiredUbits.toLocaleString()} Ubits, you have ${currentUbits.toLocaleString()} Ubits`,
        variant: "destructive",
      });
      return;
    }

    setIsCreatingPackage(true);

    try {
      // Create quantum energy package using server API
      const response = await apiRequest('POST', '/api/energy/package', {
        packageType: pkg.energyType,
        ubitsRequired: pkg.energyRequired, // Now this is Ubits
        virtualQubits: pkg.truReward, // Now this is virtual qubits
        batteryPercent: pkg.energyRequired / 100000 // Convert Ubits to battery %
      });

      if (response.ok) {
        const result = await response.json();

        // Add to local store
        addPackage({
          ...pkg,
          id: result.package.id,
          type: pkg.energyType,
          created: new Date()
        });

        // Update user energy
        updateMetrics({
          currentUMatter: metrics.currentUMatter - pkg.umatterRequired
        });

        toast({
          title: "Energy Package Created",
          description: result.message,
        });
      }
    } catch (error) {
      toast({
        title: "Package Creation Failed",
        description: "Unable to create energy package. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreatingPackage(false);
    }
  };

  const globalScale = energyEconomy.getGlobalScale();
  const { balance, isAuthentic } = useRealBalance();
  const [energyBots, setEnergyBots] = useState<EnergyBot[]>([]);
  const [storage, setStorage] = useState<EnergyStorage[]>([]);
  const [realEnergyData, setRealEnergyData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchEnergyData = async () => {
      try {
        // Fetch real energy metrics and balance data
        const energyResponse = await fetch('/api/energy/real-hardware-metrics');
        const balanceResponse = await fetch('/api/banking/balance');

        if (energyResponse.ok && balanceResponse.ok) {
          const energyData = await energyResponse.json();
          const balanceData = await balanceResponse.json();

          setRealEnergyData({
            currentGeneration: energyData.authenticEnergy || 0,
            totalBalance: balanceData.umatter || 0,
            transactionCount: balanceData.transactionCount || 0,
            timestamp: Date.now()
          });

          // Generate energy bots based on real data
          const realBots: EnergyBot[] = [
            {
              id: 'real-hardware-001',
              name: 'Real Device Energy Harvester',
              status: energyData.authenticEnergy > 0 ? 'active' : 'inactive',
              energyGenerated: energyData.authenticEnergy || 0,
              efficiency: Math.min(95, (energyData.authenticEnergy * 100)),
              lastUpdate: new Date().toISOString(),
              location: 'Local Device'
            },
            {
              id: 'banking-processor-002',
              name: 'Transaction Energy Processor',
              status: balanceData.transactionCount > 0 ? 'active' : 'inactive',
              energyGenerated: balanceData.umatter || 0,
              efficiency: Math.min(98, (balanceData.transactionCount / 100)),
              lastUpdate: new Date().toISOString(),
              location: 'Database Engine'
            }
          ];

          // Generate storage based on real balance data
          const realStorage: EnergyStorage[] = [
            {
              id: 'umatter-storage-001',
              type: 'UMatter Bank',
              capacity: 2000,
              currentLevel: balanceData.umatter || 0,
              efficiency: 99.1,
              status: energyData.authenticEnergy > 0 ? 'charging' : 'idle'
            },
            {
              id: 'transaction-buffer-002',
              type: 'Transaction Buffer',
              capacity: 10000,
              currentLevel: balanceData.transactionCount || 0,
              efficiency: 97.8,
              status: 'active'
            }
          ];

          setEnergyBots(realBots);
          setStorage(realStorage);
        } else {
          console.log('[EnergyMarketplace] Real APIs not available, showing empty state');
        }
      } catch (error) {
        console.error('Failed to fetch energy marketplace data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchEnergyData();
    const interval = setInterval(fetchEnergyData, 5000); // Update every 5 seconds for real-time feel
    return () => clearInterval(interval);
  }, []);

  return (
    <PageLayout className="container mx-auto px-4 py-8 space-y-8 bg-gray-900 dark:bg-gray-900 text-white dark:text-white">
      {/* Header */}
      <div className="text-center space-y-4">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-green-600 bg-clip-text text-transparent">
            UMatter Quantum Marketplace
          </h1>
          <p className="text-xl text-muted-foreground mt-2">
            Convert battery energy into Ubits for quantum computation - Energy IS Information
          </p>
        </motion.div>

        {/* Global Scale Banner */}
        <Card className="bg-gradient-to-r from-indigo-500/10 to-purple-500/10 border-indigo-300/30">
          <CardContent className="py-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-indigo-600">
                  {serverEnergyMetrics?.globalScale?.devices || 1}
                </div>
                <div className="text-sm text-muted-foreground">LIVE Devices</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">{globalScale.dailyDataPB.toFixed(1)}PB</div>
                <div className="text-sm text-muted-foreground">Daily Data</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">{(globalScale.dailyTrU / 1_000_000).toFixed(0)}M</div>
                <div className="text-sm text-muted-foreground">Daily trU</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-orange-600">${(globalScale.dailyMarketValue / 1_000_000).toFixed(1)}M</div>
                <div className="text-sm text-muted-foreground">Market Value</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      <Tabs defaultValue="marketplace" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="marketplace">Energy Packages</TabsTrigger>
          <TabsTrigger value="dashboard">Neural Dashboard</TabsTrigger>
          <TabsTrigger value="iot">IoT Network</TabsTrigger>
          <TabsTrigger value="science">Energy Science</TabsTrigger>
        </TabsList>

        <TabsContent value="marketplace" className="space-y-6">
          {/* User Energy Status */}
          <Card className="rounded-lg border text-card-foreground shadow-sm from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20 bg-[#ffffff00]">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-blue-500" />
                Your Neural Energy Status
              </CardTitle>
            </CardHeader>
            <CardContent className="bg-[#ffffff00]">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center justify-between p-3 dark:bg-purple-950/20 rounded-lg border border-purple-300 bg-[#09090b]">
                  <div className="flex items-center gap-2">
                    <Zap className="h-4 w-4 text-purple-500" />
                    <span className="font-medium">Fabric Nodes</span>
                  </div>
                  <span className="text-lg font-bold text-purple-600">
                    {serverEnergyMetrics?.fabricNodes || 1} nodes
                  </span>
                </div>
                <div className="flex items-center justify-between p-3 dark:bg-cyan-950/20 rounded-lg border border-cyan-300 bg-[#09090b]">
                  <div className="flex items-center gap-2">
                    <Coins className="h-4 w-4 text-cyan-500" />
                    <span className="font-medium">Available Ubits</span>
                  </div>
                  <span className="text-lg font-bold text-cyan-600">
                    {serverEnergyMetrics?.ubitConversion?.totalUbitsAllocated ? 
                      (serverEnergyMetrics.ubitConversion.totalUbitsAllocated / 1000).toFixed(0) + 'K' : '3K'}
                  </span>
                </div>
                <div className="flex items-center justify-between p-3 dark:bg-green-950/20 rounded-lg border border-green-300 bg-[#09090b]">
                  <div className="flex items-center gap-2">
                    <Battery className="h-4 w-4 text-green-500" />
                    <span className="font-medium">Active Devices</span>
                  </div>
                  <span className="text-lg font-bold text-green-600">
                    {serverEnergyMetrics?.globalScale?.devices || 1} devices
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Energy Packages Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {ENERGY_PACKAGES.map((pkg) => (
              <motion.div
                key={pkg.id}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
              >
                <Card className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                  selectedPackage?.id === pkg.id ? 'ring-2 ring-blue-500' : ''
                } ${metrics.currentUMatter >= pkg.umatterRequired ? 'border-green-300' : 'border-red-300'}`}
                onClick={() => setSelectedPackage(pkg)}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        {pkg.energyType === 'sleep' && <Timer className="h-5 w-5 text-blue-500" />}
                        {pkg.energyType === 'active' && <Smartphone className="h-5 w-5 text-orange-500" />}
                        {pkg.energyType === 'joy' && <Heart className="h-5 w-5 text-red-500" />}
                        {pkg.energyType === 'mixed' && <Globe className="h-5 w-5 text-purple-500" />}
                        {pkg.name}
                      </CardTitle>
                      <Badge variant={metrics.currentUMatter >= pkg.umatterRequired ? "default" : "destructive"}>
                        {pkg.popularity}% popular
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-muted-foreground">{pkg.description}</p>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="text-sm font-medium text-muted-foreground">Data Size</div>
                        <div className="text-lg font-bold">{pkg.dataMB}MB</div>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-muted-foreground">Duration</div>
                        <div className="text-lg font-bold">{pkg.duration}</div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Energy Required:</span>
                        <span className="font-bold text-green-600">{pkg.umatterRequired.toFixed(3)} UM</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">trU Value:</span>
                        <span className="font-bold text-yellow-600">{pkg.truValue.toFixed(4)} trU</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">USD Value:</span>
                        <span className="font-bold text-green-600">${pkg.usdValue.toFixed(4)}</span>
                      </div>
                    </div>

                    <Progress 
                      value={Math.min((metrics.currentUMatter / pkg.umatterRequired) * 100, 100)} 
                      className="h-2"
                    />

                    <Button 
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCreatePackage(pkg);
                      }}
                      disabled={metrics.currentUMatter < pkg.umatterRequired || isCreatingPackage}
                      className="w-full"
                    >
                      {isCreatingPackage ? (
                        "Converting Neural Energy..."
                      ) : metrics.currentUMatter >= pkg.umatterRequired ? (
                        <>Create Package <ArrowRight className="ml-2 h-4 w-4" /></>
                      ) : (
                        `Need ${(pkg.umatterRequired - metrics.currentUMatter).toFixed(3)} more UM`
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* Energy Storage & Bot Management Controls */}
          <Card className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-950/20 dark:to-indigo-950/20">
            <CardHeader className="bg-[#09090b]">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Settings className="h-5 w-5 text-purple-500" />
                  Energy Management & Bot Controls
                </div>
                <Button
                  onClick={() => setShowEnergyStorage(true)}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  <Activity className="w-4 h-4 mr-2" />
                  Open Energy Storage
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="bg-[#ffffff00]">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Bot Status Cards */}
                <Card className="bg-white/50 dark:bg-black/20">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">Gear-Tick Bot</span>
                      <div className={`w-3 h-3 rounded-full ${bots?.gearTick?.active ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`}></div>
                    </div>
                    <div className="text-sm text-gray-600">Real-time neural monitoring</div>
                    <div className="text-xs text-gray-500 mt-1">
                      Last update: 10:45:50 PM
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-white/50 dark:bg-black/20">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">Drain Bot</span>
                      <div className={`w-3 h-3 rounded-full ${bots?.drain?.active ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`}></div>
                    </div>
                    <div className="text-sm text-gray-600">Battery drain tracking</div>
                    <div className="text-xs text-gray-500 mt-1">
                      Tracked: 35.52MB data
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-white/50 dark:bg-black/20">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">Trade Bot</span>
                      <div className={`w-3 h-3 rounded-full ${bots?.trade?.active ? 'bg-green-400 animate-pulse' : 'bg-red-400'}`}></div>
                    </div>
                    <div className="text-sm text-gray-600">Automated trading</div>
                    <div className="text-xs text-gray-500 mt-1">
                      Auto-trade: {bots?.trade?.autoTrade ? 'Enabled' : 'Disabled'}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Network Stats */}
              <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{(networkStats.totalUsers / 1000000).toFixed(1)}M</div>
                  <div className="text-sm text-gray-600">Global Users</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{(networkStats.dailyUMatter / 1000000000).toFixed(1)}B</div>
                  <div className="text-sm text-gray-600">Daily UMatter</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">${(networkStats.marketValue / 1000000).toFixed(1)}M</div>
                  <div className="text-sm text-gray-600">Market Value</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{(networkStats.energyEfficiencyGlobal * 100).toFixed(1)}%</div>
                  <div className="text-sm text-gray-600">Global Efficiency</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="dashboard">
          <UnifiedEnergyDisplay />
        </TabsContent>

        <TabsContent value="iot">
          <IoTEnergyManager />
        </TabsContent>

        <TabsContent value="science" className="space-y-6">
          {/* Scientific Explanation */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5" />
                The Science Behind Energy-to-Data Conversion
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-blue-600">Neural Activity (20W)</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Neurons:</span>
                      <span className="font-medium">{ENERGY_CONSTANTS.NEURONS.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Synapses:</span>
                      <span className="font-medium">{(ENERGY_CONSTANTS.SYNAPSES / 1_000_000_000_000).toFixed(0)}T</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Daily Energy:</span>
                      <span className="font-medium">{ENERGY_CONSTANTS.BRAIN_POWER_KWH_DAY} kWh</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Energy Cost:</span>
                      <span className="font-medium">${(ENERGY_CONSTANTS.BRAIN_POWER_KWH_DAY * ENERGY_CONSTANTS.ELECTRICITY_COST_PER_KWH).toFixed(4)}/day</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-green-600">Data Production</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Sleep Data:</span>
                      <span className="font-medium">{ENERGY_CONSTANTS.DATA_MB_SLEEP}MB/night</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Active Data:</span>
                      <span className="font-medium">{ENERGY_CONSTANTS.DATA_MB_SCROLL}MB/session</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Daily:</span>
                      <span className="font-medium">{ENERGY_CONSTANTS.DATA_MB_DAILY}MB/day</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Data Value:</span>
                      <span className="font-medium">${DATA_PRICING.BLENDED_PRICE_PER_MB.toFixed(4)}/MB</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-purple-600 mb-4">Energy Conversion Formula</h3>
                <div className="dark:bg-gray-900 p-4 rounded-lg font-mono text-center bg-[#09090b]">
                  <div className="text-lg font-bold mb-2">
                    E_b &gt; U_s &gt; A_nU &gt; K_UM &gt; [UMatter | trU | nUva] &gt; F_4Ce = hU
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Battery Energy → Stored Potential → nU Activation → Kinetic UMatter → Energy Tokens → Force = Human Value
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                    <div className="font-medium text-blue-700 dark:text-blue-300">UMatter (Base)</div>
                    <div className="text-sm text-blue-600 dark:text-blue-400">Raw neural energy unit</div>
                    <div className="text-xs text-muted-foreground mt-1">1Wh ≈ 0.675 UM</div>
                  </div>
                  <div className="p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg">
                    <div className="font-medium text-yellow-700 dark:text-yellow-300">trU (Tradeable)</div>
                    <div className="text-sm text-yellow-600 dark:text-yellow-400">Marketplace currency</div>
                    <div className="text-xs text-muted-foreground mt-1">1 UM = 0.1 trU</div>
                  </div>
                  <div className="p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
                    <div className="font-medium text-purple-700 dark:text-purple-300">nUva (Storage)</div>
                    <div className="text-sm text-purple-600 dark:text-purple-400">Rechargeable energy</div>
                    <div className="text-xs text-muted-foreground mt-1">1 nUva = 0.74Wh</div>
                  </div>
                </div>
              </div>

              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-orange-600 mb-4">Global Impact</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-3 bg-orange-50 dark:bg-orange-950/20 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">100GW</div>
                    <div className="text-sm text-muted-foreground">Global Neural Power</div>
                  </div>
                  <div className="text-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">3.5B</div>
                    <div className="text-sm text-muted-foreground">Daily UMatter</div>
                  </div>
                  <div className="text-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">350M</div>
                    <div className="text-sm text-muted-foreground">Daily trU</div>
                  </div>
                  <div className="text-center p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">$35M</div>
                    <div className="text-sm text-muted-foreground">Daily Market Value</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Philosophy Section */}
          <Card className="bg-gradient-to-r from-pink-50 to-rose-50 dark:from-pink-950/20 dark:to-rose-950/20">
            <CardHeader className="bg-[#09090b]">
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5 text-red-500" />
                100% Free, Love-Based Symbiotic Network
              </CardTitle>
            </CardHeader>
            <CardContent className="bg-[#09090b]">
              <div className="space-y-4 text-sm">
                <p className="text-gray-700 dark:text-gray-300">
                  This energy marketplace operates on love and symbiosis - it creates value without destroying anything. 
                  Your 20W brain power naturally produces data as you live your life. Instead of corporations stealing 
                  this value, you control and benefit from your own neural energy.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-3 bg-green-100 dark:bg-green-950/30 rounded-lg">
                    <div className="font-medium text-green-800 dark:text-green-200 mb-1">Creates Joy</div>
                    <div className="text-green-700 dark:text-green-300 text-xs">
                      Higher dopamine = 15% neural efficiency boost = premium data value
                    </div>
                  </div>
                  <div className="p-3 bg-blue-100 dark:bg-blue-950/30 rounded-lg">
                    <div className="font-medium text-blue-800 dark:text-blue-200 mb-1">Energy Loop</div>
                    <div className="text-blue-700 dark:text-blue-300 text-xs">
                      You spend 1.036Wh, earn back ~0.5Wh in nUva - sustainable energy cycle
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      {/* Energy Storage Modal */}
      <NuvaEnergyStorage 
        isOpen={showEnergyStorage} 
        onClose={() => setShowEnergyStorage(false)} 
      />
    </PageLayout>
  );
}