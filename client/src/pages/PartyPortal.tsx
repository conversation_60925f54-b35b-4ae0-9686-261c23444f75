import { useState } from 'react';
import { PageLayout } from '@/components/PageLayout';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  PartyPopper, 
  Users, 
  Zap,
  Music,
  Calendar,
  MapPin,
  Clock,
  Star,
  Gift,
  Share2,
  Plus
} from 'lucide-react';

export default function PartyPortal() {
  const [activeTab, setActiveTab] = useState('events');

  const upcomingEvents = [
    {
      id: 1,
      title: 'Quantum Gaming Night',
      date: 'Tonight, 8:00 PM',
      location: 'Virtual Space Alpha',
      participants: 24,
      maxParticipants: 30,
      energyPool: 1247.5,
      hostId: 'alex_quantum',
      type: 'Gaming',
      description: 'Multiplayer quantum puzzle challenges with energy rewards'
    },
    {
      id: 2,
      title: 'Energy Sharing Circle',
      date: 'Tomorrow, 7:30 PM',
      location: 'Community Hub Beta',
      participants: 18,
      maxParticipants: 25,
      energyPool: 892.3,
      hostId: 'sarah_energy',
      type: 'Social',
      description: 'Collaborative energy generation and community building'
    },
    {
      id: 3,
      title: 'Tech Innovation Meetup',
      date: 'Friday, 6:00 PM',
      location: 'Innovation Center',
      participants: 45,
      maxParticipants: 50,
      energyPool: 2156.8,
      hostId: 'david_tech',
      type: 'Learning',
      description: 'Share latest tech discoveries and earn collective rewards'
    }
  ];

  const activeParties = [
    {
      id: 1,
      name: 'Energy Hackers',
      members: 12,
      currentActivity: 'Quantum Task Sprint',
      totalEnergy: 3457.2,
      averageRating: 4.8,
      status: 'Live Now'
    },
    {
      id: 2,
      name: 'Creative Collective',
      members: 8,
      currentActivity: 'Art & Music Sync',
      totalEnergy: 1923.6,
      averageRating: 4.9,
      status: 'Starting Soon'
    },
    {
      id: 3,
      name: 'Wellness Warriors',
      members: 15,
      currentActivity: 'Meditation Energy Flow',
      totalEnergy: 2789.4,
      averageRating: 4.7,
      status: 'Live Now'
    }
  ];

  const rewards = [
    {
      event: 'Quantum Gaming Night',
      reward: '+150 UMatter',
      condition: 'Complete 3 quantum puzzles',
      claimed: false
    },
    {
      event: 'Energy Circle Participation',
      reward: '+89 NUVA',
      condition: 'Share energy for 30+ minutes',
      claimed: true
    },
    {
      event: 'Community Builder Badge',
      reward: '+200 TRU',
      condition: 'Host 5 successful events',
      claimed: false
    }
  ];

  return (
    <PageLayout className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-yellow-400 via-orange-500 to-red-600 bg-clip-text text-transparent">
                Party Portal
              </h1>
              <p className="text-gray-400 mt-2">
                Join energy-powered events and collaborative experiences
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="bg-yellow-500/20 text-yellow-300 border-yellow-500/30">
                <PartyPopper className="w-3 h-3 mr-1" />
                3 Live Events
              </Badge>
              <Badge variant="secondary" className="bg-orange-500/20 text-orange-300 border-orange-500/30">
                <Users className="w-3 h-3 mr-1" />
                127 Active
              </Badge>
            </div>
          </div>
        </div>

        {/* Party Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gray-800/50 border-yellow-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Events Joined</p>
                  <p className="text-2xl font-bold text-yellow-400">23</p>
                </div>
                <Calendar className="h-8 w-8 text-yellow-400" />
              </div>
              <p className="text-xs text-yellow-400 mt-2">+5 this month</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-orange-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Energy Earned</p>
                  <p className="text-2xl font-bold text-orange-400">3,247</p>
                </div>
                <Zap className="h-8 w-8 text-orange-400" />
              </div>
              <p className="text-xs text-orange-400 mt-2">From party activities</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-red-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Party Rating</p>
                  <p className="text-2xl font-bold text-red-400">4.9</p>
                </div>
                <Star className="h-8 w-8 text-red-400" />
              </div>
              <p className="text-xs text-red-400 mt-2">Community favorite</p>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/50 border-purple-500/30">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-400">Connections Made</p>
                  <p className="text-2xl font-bold text-purple-400">47</p>
                </div>
                <Users className="h-8 w-8 text-purple-400" />
              </div>
              <p className="text-xs text-purple-400 mt-2">Through events</p>
            </CardContent>
          </Card>
        </div>

        {/* Party Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-gray-800/50">
            <TabsTrigger value="events" className="data-[state=active]:bg-yellow-500/20 data-[state=active]:text-yellow-400">
              Upcoming Events
            </TabsTrigger>
            <TabsTrigger value="live" className="data-[state=active]:bg-orange-500/20 data-[state=active]:text-orange-400">
              Live Parties
            </TabsTrigger>
            <TabsTrigger value="rewards" className="data-[state=active]:bg-red-500/20 data-[state=active]:text-red-400">
              Rewards & Badges
            </TabsTrigger>
            <TabsTrigger value="create" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400">
              Create Event
            </TabsTrigger>
          </TabsList>

          {/* Upcoming Events Tab */}
          <TabsContent value="events" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {upcomingEvents.map((event) => (
                <Card key={event.id} className="bg-gray-800/50 border-gray-700 hover:border-yellow-500/50 transition-colors">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-white">{event.title}</CardTitle>
                        <p className="text-gray-400 text-sm mt-1">by {event.hostId}</p>
                      </div>
                      <Badge variant="secondary" className="bg-yellow-500/20 text-yellow-300">
                        {event.type}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-400 text-sm mb-4">{event.description}</p>
                    
                    <div className="space-y-3">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-gray-400" />
                        <span className="text-white">{event.date}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <span className="text-white">{event.location}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-gray-400" />
                        <span className="text-white">{event.participants}/{event.maxParticipants} participants</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Zap className="h-4 w-4 text-gray-400" />
                        <span className="text-green-400 font-bold">{event.energyPool} UMatter pool</span>
                      </div>
                    </div>
                    
                    <div className="mt-4">
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400 text-sm">Capacity</span>
                        <span className="text-blue-400 text-sm">
                          {Math.round((event.participants / event.maxParticipants) * 100)}%
                        </span>
                      </div>
                      <Progress value={(event.participants / event.maxParticipants) * 100} className="h-2" />
                    </div>
                    
                    <Button className="w-full mt-4 bg-yellow-600 hover:bg-yellow-700">
                      Join Event
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Live Parties Tab */}
          <TabsContent value="live" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {activeParties.map((party) => (
                <Card key={party.id} className="bg-gray-800/50 border-gray-700">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-white">{party.name}</CardTitle>
                        <p className="text-gray-400 text-sm mt-1">{party.currentActivity}</p>
                      </div>
                      <Badge 
                        variant="secondary" 
                        className={party.status === 'Live Now' ? 'bg-red-500/20 text-red-300' : 'bg-orange-500/20 text-orange-300'}
                      >
                        {party.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Members</span>
                        <span className="text-blue-400 font-bold">{party.members}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Total Energy</span>
                        <span className="text-green-400 font-bold">{party.totalEnergy.toLocaleString()} UMatter</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Rating</span>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-400 fill-current" />
                          <span className="text-yellow-400">{party.averageRating}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2 mt-4">
                      <Button className="flex-1 bg-orange-600 hover:bg-orange-700">
                        {party.status === 'Live Now' ? 'Join Now' : 'Get Notified'}
                      </Button>
                      <Button variant="outline" className="flex-1">
                        <Share2 className="h-4 w-4 mr-2" />
                        Share
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Rewards Tab */}
          <TabsContent value="rewards" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {rewards.map((reward, index) => (
                <Card key={index} className="bg-gray-800/50 border-gray-700">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-600 rounded-full flex items-center justify-center">
                          <Gift className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-white font-semibold">{reward.event}</h3>
                          <p className="text-gray-400 text-sm">{reward.condition}</p>
                        </div>
                      </div>
                      {reward.claimed && (
                        <Badge className="bg-green-500/20 text-green-300">
                          Claimed
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-green-400">{reward.reward}</span>
                      {!reward.claimed && (
                        <Button size="sm" className="bg-red-600 hover:bg-red-700">
                          Claim
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Create Event Tab */}
          <TabsContent value="create" className="space-y-6 mt-6">
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-purple-400">
                  <Plus className="h-5 w-5" />
                  Create New Event
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-gray-400">Event Title</label>
                      <input 
                        type="text" 
                        className="w-full p-3 bg-gray-900/50 border border-gray-600 rounded-lg text-white"
                        placeholder="Enter event title"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-gray-400">Event Type</label>
                      <select className="w-full p-3 bg-gray-900/50 border border-gray-600 rounded-lg text-white">
                        <option>Gaming</option>
                        <option>Social</option>
                        <option>Learning</option>
                        <option>Wellness</option>
                        <option>Creative</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <label className="text-gray-400">Date & Time</label>
                      <input 
                        type="datetime-local" 
                        className="w-full p-3 bg-gray-900/50 border border-gray-600 rounded-lg text-white"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-gray-400">Description</label>
                      <textarea 
                        className="w-full p-3 bg-gray-900/50 border border-gray-600 rounded-lg text-white h-32"
                        placeholder="Describe your event"
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-gray-400">Max Participants</label>
                      <input 
                        type="number" 
                        className="w-full p-3 bg-gray-900/50 border border-gray-600 rounded-lg text-white"
                        placeholder="50"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="pt-6 border-t border-gray-700">
                  <Button className="w-full bg-purple-600 hover:bg-purple-700">
                    <PartyPopper className="h-4 w-4 mr-2" />
                    Create Event
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
}