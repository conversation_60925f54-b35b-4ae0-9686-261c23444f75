import { useState } from 'react';
import { PageLayout } from '@/components/PageLayout';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  BarChart3, 
  Activity,
  Target,
  Eye,
  DollarSign,
  Users,
  Clock,
  Zap,
  Settings
} from 'lucide-react';

export default function InUrtia() {
  const [activeTab, setActiveTab] = useState('analytics');

  const analytics = [
    {
      id: 1,
      title: 'Energy Generation Efficiency',
      value: '94.2%',
      change: '+5.7%',
      trend: 'up',
      description: 'Overall energy conversion rate from biometric data'
    },
    {
      id: 2,
      title: 'Data Monetization Rate',
      value: '$247.35',
      change: '+18.3%',
      trend: 'up',
      description: 'Monthly earnings from privacy-protected data sharing'
    },
    {
      id: 3,
      title: 'Network Engagement',
      value: '87.4%',
      change: '+2.1%',
      trend: 'up',
      description: 'Active participation in energy sharing networks'
    },
    {
      id: 4,
      title: 'Quantum Task Success',
      value: '96.8%',
      change: '-0.2%',
      trend: 'down',
      description: 'Completion rate for quantum computing tasks'
    }
  ];

  const insights = [
    {
      category: 'Energy Optimization',
      insight: 'Your peak energy generation occurs between 2-4 PM daily',
      action: 'Schedule intensive tasks during this window',
      impact: 'High',
      confidence: 94
    },
    {
      category: 'Data Value',
      insight: 'IoT device patterns are in high demand this quarter',
      action: 'Enable enhanced IoT data sharing',
      impact: 'Medium',
      confidence: 87
    },
    {
      category: 'Social Network',
      insight: 'Family energy sharing increases efficiency by 23%',
      action: 'Invite more family members to join',
      impact: 'High',
      confidence: 91
    },
    {
      category: 'Quantum Performance',
      insight: 'VQE algorithms perform best on your device configuration',
      action: 'Focus on variational quantum tasks',
      impact: 'Medium',
      confidence: 78
    }
  ];

  return (
    <PageLayout className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-orange-400 via-red-500 to-pink-600 bg-clip-text text-transparent">
                InUrtia Analytics
              </h1>
              <p className="text-gray-400 mt-2">
                Deep insights into your energy ecosystem and optimization opportunities
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="bg-orange-500/20 text-orange-300 border-orange-500/30">
                <Activity className="w-3 h-3 mr-1" />
                Live Analysis
              </Badge>
              <Badge variant="secondary" className="bg-red-500/20 text-red-300 border-red-500/30">
                <TrendingUp className="w-3 h-3 mr-1" />
                AI Powered
              </Badge>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {analytics.map((metric) => (
            <Card key={metric.id} className="bg-gray-800/50 border-gray-700">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-sm text-gray-400">{metric.title}</h3>
                  <div className={`flex items-center ${metric.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                    <TrendingUp className={`h-4 w-4 ${metric.trend === 'down' ? 'rotate-180' : ''}`} />
                  </div>
                </div>
                <div className="flex items-baseline justify-between">
                  <span className="text-2xl font-bold text-white">{metric.value}</span>
                  <span className={`text-sm font-medium ${metric.trend === 'up' ? 'text-green-400' : 'text-red-400'}`}>
                    {metric.change}
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-2">{metric.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Analytics Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-gray-800/50">
            <TabsTrigger value="analytics" className="data-[state=active]:bg-orange-500/20 data-[state=active]:text-orange-400">
              Performance Analytics
            </TabsTrigger>
            <TabsTrigger value="insights" className="data-[state=active]:bg-red-500/20 data-[state=active]:text-red-400">
              AI Insights
            </TabsTrigger>
            <TabsTrigger value="predictions" className="data-[state=active]:bg-pink-500/20 data-[state=active]:text-pink-400">
              Predictions
            </TabsTrigger>
            <TabsTrigger value="optimization" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400">
              Optimization
            </TabsTrigger>
          </TabsList>

          {/* Performance Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Energy Generation Trends */}
              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-orange-400">
                    <BarChart3 className="h-5 w-5" />
                    Energy Generation Trends
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">Today</span>
                      <span className="text-orange-400 font-bold">247.3 UMatter</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">This Week</span>
                      <span className="text-green-400 font-bold">1,456.7 UMatter</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-400">This Month</span>
                      <span className="text-blue-400 font-bold">6,234.2 UMatter</span>
                    </div>
                    <div className="pt-4">
                      <div className="flex justify-between mb-2">
                        <span className="text-gray-400">Monthly Goal Progress</span>
                        <span className="text-purple-400">78.3%</span>
                      </div>
                      <Progress value={78.3} className="h-2" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Efficiency Breakdown */}
              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-red-400">
                    <Activity className="h-5 w-5" />
                    Efficiency Breakdown
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { source: 'Device Sync', efficiency: 96.2, contribution: 45 },
                      { source: 'Extension Activity', efficiency: 89.7, contribution: 30 },
                      { source: 'Quantum Tasks', efficiency: 94.1, contribution: 15 },
                      { source: 'Social Sharing', efficiency: 87.3, contribution: 10 }
                    ].map((item, i) => (
                      <div key={i} className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-gray-400">{item.source}</span>
                          <span className="text-white font-bold">{item.efficiency}%</span>
                        </div>
                        <Progress value={item.efficiency} className="h-2" />
                        <div className="text-xs text-gray-500">
                          {item.contribution}% of total generation
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* AI Insights Tab */}
          <TabsContent value="insights" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {insights.map((insight, index) => (
                <Card key={index} className="bg-gray-800/50 border-gray-700">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-white">{insight.category}</CardTitle>
                      <Badge 
                        variant="secondary" 
                        className={
                          insight.impact === 'High' ? 'bg-red-500/20 text-red-300' :
                          insight.impact === 'Medium' ? 'bg-yellow-500/20 text-yellow-300' :
                          'bg-green-500/20 text-green-300'
                        }
                      >
                        {insight.impact} Impact
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p className="text-gray-400">{insight.insight}</p>
                      
                      <div className="bg-gray-900/50 p-4 rounded-lg">
                        <h4 className="text-white font-medium mb-2">Recommended Action:</h4>
                        <p className="text-blue-400">{insight.action}</p>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-gray-400">Confidence</span>
                        <div className="flex items-center gap-2">
                          <Progress value={insight.confidence} className="w-16 h-2" />
                          <span className="text-green-400 text-sm font-bold">{insight.confidence}%</span>
                        </div>
                      </div>
                      
                      <Button variant="outline" className="w-full">
                        <Target className="h-4 w-4 mr-2" />
                        Apply Recommendation
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Predictions Tab */}
          <TabsContent value="predictions" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-pink-400">
                    <Clock className="h-5 w-5" />
                    Energy Forecast
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center py-8">
                      <div className="text-3xl font-bold text-pink-400 mb-2">8,247 UMatter</div>
                      <p className="text-gray-400">Predicted next 30 days</p>
                      <div className="mt-4 text-sm text-green-400">
                        +23.7% from current pace
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Next Week</span>
                        <span className="text-blue-400 font-bold">1,890 UMatter</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Next Month</span>
                        <span className="text-purple-400 font-bold">8,247 UMatter</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Next Quarter</span>
                        <span className="text-orange-400 font-bold">26,580 UMatter</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gray-800/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-purple-400">
                    <DollarSign className="h-5 w-5" />
                    Earnings Projection
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center py-8">
                      <div className="text-3xl font-bold text-purple-400 mb-2">$487.20</div>
                      <p className="text-gray-400">Estimated monthly revenue</p>
                      <div className="mt-4 text-sm text-green-400">
                        Based on current data value trends
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-400">Data Sales</span>
                        <span className="text-green-400 font-bold">$312.40</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Quantum Tasks</span>
                        <span className="text-blue-400 font-bold">$124.80</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-400">Energy Trading</span>
                        <span className="text-orange-400 font-bold">$50.00</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Optimization Tab */}
          <TabsContent value="optimization" className="space-y-6 mt-6">
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-purple-400">
                  <Settings className="h-5 w-5" />
                  Optimization Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Immediate Actions</h3>
                    <div className="space-y-3">
                      {[
                        { action: 'Enable enhanced device tracking', impact: '+12% energy generation' },
                        { action: 'Optimize quantum task selection', impact: '+8% NUVA earnings' },
                        { action: 'Increase social network size', impact: '+15% sharing efficiency' }
                      ].map((item, i) => (
                        <div key={i} className="flex justify-between items-center p-3 bg-gray-900/50 rounded-lg">
                          <span className="text-white text-sm">{item.action}</span>
                          <span className="text-green-400 text-xs font-bold">{item.impact}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Long-term Strategy</h3>
                    <div className="space-y-3">
                      {[
                        { strategy: 'Diversify data portfolio', timeline: '2-3 months' },
                        { strategy: 'Upgrade quantum capabilities', timeline: '3-6 months' },
                        { strategy: 'Build energy trading network', timeline: '6-12 months' }
                      ].map((item, i) => (
                        <div key={i} className="flex justify-between items-center p-3 bg-gray-900/50 rounded-lg">
                          <span className="text-white text-sm">{item.strategy}</span>
                          <span className="text-blue-400 text-xs">{item.timeline}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="pt-6 border-t border-gray-700">
                  <Button className="w-full bg-purple-600 hover:bg-purple-700">
                    <Zap className="h-4 w-4 mr-2" />
                    Apply All Optimizations
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
}