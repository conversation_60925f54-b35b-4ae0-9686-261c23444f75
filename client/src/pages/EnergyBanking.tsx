import React, { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { PageLayout } from "@/components/PageLayout";
import { Wallet, CreditCard, Activity, Smartphone, Clock, TrendingUp, Zap, RefreshCw } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";

interface EnergyBalance {
  umatter: number;
  tru: number;
  nuva: number;
  inurtia: number;
  ubits: number;
  totalValueUSD: number;
  transactionCount: number;
  authentic: boolean;
  source: string;
  did?: string;
  walletAddress?: string;
}

interface Transaction {
  id: string;
  transactionType: string;
  tokenType: string;
  amount: number;
  balanceBefore: number;
  balanceAfter: number;
  source: string;
  transactionHash: string;
  createdAt: string;
}

interface Device {
  id: string;
  deviceId: string;
  deviceName: string;
  deviceType: string;
  platform: string;
  lastSeen: string;
  energyContribution: number;
}

export default function EnergyBanking() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  // FORCE REAL-TIME BANKING BALANCE - NO CACHE
  const { data: balance, isLoading: balanceLoading, isFetching, refetch } = useQuery<EnergyBalance>({
    queryKey: ['/api/banking/balance'],
    queryFn: async () => {
      const response = await fetch('/api/banking/balance', {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      if (!response.ok) throw new Error('Failed to fetch balance');
      const data = await response.json();
      console.log('[EnergyBanking] FRESH DATA:', data);
      return data;
    },
    refetchInterval: 2000,
    refetchIntervalInBackground: true,
    staleTime: 0,
    gcTime: 0,
    retry: false,
  });

  // Handle balance updates
  useEffect(() => {
    if (balance) {
      setLastUpdate(new Date());
      console.log('[EnergyBanking] Balance updated:', balance);
    }
  }, [balance]);

  // Force manual refresh every 3 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      refetch();
    }, 3000);
    return () => clearInterval(interval);
  }, [refetch]);

  // Fetch transaction history
  const { data: transactionsData, isLoading: transactionsLoading } = useQuery({
    queryKey: ['/api/banking/transactions'],
    refetchInterval: 5000,
  });

  const transactions = Array.isArray(transactionsData) ? transactionsData : 
    (transactionsData?.transactions || []);

  // Fetch devices
  const { data: devices = [], isLoading: devicesLoading } = useQuery<Device[]>({
    queryKey: ['/api/banking/devices'],
  });

  // WORKING DID GENERATION
  const generateDID = useMutation({
    mutationFn: () => apiRequest('/api/banking/generate-did', { method: 'POST' }),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/banking/balance'] });
      toast({
        title: "DID Generated",
        description: `DID created: ${data.did}`,
      });
    },
  });

  // WORKING WALLET CREATION
  const createWallet = useMutation({
    mutationFn: () => apiRequest('/api/banking/create-wallet', { method: 'POST' }),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/banking/balance'] });
      toast({
        title: "Wallet Created", 
        description: `Wallet: ${data.walletAddress}`,
      });
    },
  });

  console.log('[EnergyBanking] Balance data:', balance);

  if (balanceLoading) {
    return (
      <PageLayout className="container mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">Energy Banking</h1>
          <p className="text-gray-300 mt-2">
            Manage your energy tokens and decentralized banking account
          </p>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-400">Last Updated</div>
          <div className="text-sm text-white">{lastUpdate.toLocaleTimeString()}</div>
          {isFetching && <RefreshCw className="h-4 w-4 animate-spin text-blue-400 mt-1" />}
        </div>
      </div>

      {/* Account Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-br from-gray-900 to-slate-800 border-gray-700">
          <CardHeader className="p-6 flex flex-row items-center justify-between space-y-0 pb-2 bg-[#09090b]">
            <CardTitle className="text-sm font-medium text-white">Total Portfolio Value</CardTitle>
            <div className="flex items-center gap-2">
              {isFetching && <RefreshCw className="h-3 w-3 animate-spin text-blue-400" />}
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              ${balance?.totalValueUSD?.toFixed(2) || '0.00'}
            </div>
            <p className="text-xs text-gray-300 mt-1">
              UMatter: {balance?.umatter?.toLocaleString() || '0'} • TRU: {balance?.tru?.toLocaleString() || '0'}
            </p>
            <p className="text-xs text-gray-400 mt-1">
              From {balance?.transactionCount?.toLocaleString() || '0'} real transactions • ${balance?.totalValueUSD?.toFixed(2) || '0.00'} total value
            </p>
            <div className="flex items-center gap-1 mt-1">
              <Zap className={`h-3 w-3 ${balance?.authentic ? 'text-green-400 animate-pulse' : 'text-yellow-400'}`} />
              <span className={`text-xs ${balance?.authentic ? 'text-green-400' : 'text-yellow-400'}`}>
                {balance?.authentic ? 'Live PostgreSQL' : 'Connecting...'}
              </span>
              {balance?.source && (
                <span className="text-xs text-gray-500">• {balance.source}</span>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-gray-900 to-slate-800 border-gray-700">
          <CardHeader className="p-6 flex flex-row items-center justify-between space-y-0 pb-2 bg-[#09090b]">
            <CardTitle className="text-sm font-medium text-white">Energy Status</CardTitle>
            <Activity className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              Active
            </div>
            <p className="text-xs text-gray-300 mt-1">
              Energy Generation: Live from {devices.length} device{devices.length !== 1 ? 's' : ''}
            </p>
            <div className="text-xs text-gray-400 mt-1">
              Source: {balance?.source || 'Connecting to database...'}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-gray-900 to-slate-800 border-gray-700">
          <CardHeader className="p-6 flex flex-row items-center justify-between space-y-0 pb-2 bg-[#09090b]">
            <CardTitle className="text-sm font-medium text-white">Connected Devices</CardTitle>
            <Smartphone className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {devices.length}
            </div>
            <p className="text-xs text-gray-300 mt-1">
              Generating energy across {devices.length} device{devices.length !== 1 ? 's' : ''}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Banking Interface */}
      <Tabs defaultValue="balances" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="balances">Token Balances</TabsTrigger>
          <TabsTrigger value="transactions">Transaction History</TabsTrigger>
          <TabsTrigger value="devices">My Devices</TabsTrigger>
          <TabsTrigger value="identity">Digital Identity</TabsTrigger>
        </TabsList>

        {/* Token Balances */}
        <TabsContent value="balances">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {(balance ? [
              { 
                name: 'UMatter', 
                balance: balance.umatter || 0, 
                symbol: 'UM', 
                color: 'blue', 
                rate: 0.001647,
                authentic: balance.authentic || false 
              },
              { 
                name: 'TRU', 
                balance: balance.tru || 0, 
                symbol: 'TRU', 
                color: 'green', 
                rate: 0.0526,
                authentic: balance.authentic || false 
              },
              { 
                name: 'NUVA', 
                balance: balance.nuva || 0, 
                symbol: 'NUVA', 
                color: 'purple', 
                rate: 0.0105,
                authentic: balance.authentic || false 
              },
              { 
                name: 'InUrtia', 
                balance: balance.inurtia || 0, 
                symbol: 'INU', 
                color: 'orange', 
                rate: 0.0046,
                authentic: balance.authentic || false 
              },
              { 
                name: 'Ubits', 
                balance: balance.ubits || 0, 
                symbol: 'UBIT', 
                color: 'red', 
                rate: 0.0000013,
                authentic: balance.authentic || false 
              },
            ] : []).map((token) => (
              <Card key={token.name} className="bg-gradient-to-br from-gray-900 to-slate-800 border-gray-700">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="text-white">{token.name}</span>
                    <div className="flex items-center gap-2">
                      <Badge 
                        variant="outline" 
                        className={`text-${token.color}-400 border-${token.color}-400`}
                      >
                        {token.symbol}
                      </Badge>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-white flex items-center gap-2">
                    {token.balance.toLocaleString()}
                    {isFetching && <RefreshCw className="h-4 w-4 animate-spin text-blue-400" />}
                  </div>
                  <div className="text-sm text-gray-300 mt-1">
                    ≈ ${(token.balance * token.rate).toLocaleString(undefined, { 
                      minimumFractionDigits: 4, 
                      maximumFractionDigits: 4 
                    })}
                  </div>
                  <div className="text-xs text-gray-400 mt-1 flex items-center gap-1">
                    <div className={`w-2 h-2 rounded-full ${token.authentic ? 'bg-green-400 animate-pulse' : 'bg-yellow-400'}`}></div>
                    {token.authentic ? 'Live Data' : 'Loading'}
                    {token.name === 'UMatter' && balance?.transactionCount && (
                      <span> • {balance.transactionCount.toLocaleString()} txns</span>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Transaction History */}
        <TabsContent value="transactions">
          <Card className="bg-gradient-to-br from-gray-900 to-slate-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Recent Transactions</CardTitle>
              <CardDescription className="text-gray-300">
                Your latest energy banking transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {transactionsLoading ? (
                  <div className="text-gray-400">Loading transactions...</div>
                ) : transactions.length > 0 ? (
                  transactions.slice(0, 10).map((transaction: Transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className={`w-2 h-2 rounded-full ${
                          transaction.transactionType === 'deposit' ? 'bg-green-400' : 'bg-red-400'
                        }`}></div>
                        <div>
                          <div className="text-white font-medium">
                            {transaction.transactionType === 'deposit' ? 'Energy Deposit' : 'Energy Withdrawal'}
                          </div>
                          <div className="text-xs text-gray-400">
                            {new Date(transaction.createdAt).toLocaleString()}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`font-medium ${
                          transaction.transactionType === 'deposit' ? 'text-green-400' : 'text-red-400'
                        }`}>
                          {transaction.transactionType === 'deposit' ? '+' : '-'}{transaction.amount.toFixed(6)} {transaction.tokenType.toUpperCase()}
                        </div>
                        <div className="text-xs text-gray-400">
                          Balance: {transaction.balanceAfter.toFixed(6)}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-gray-400">No transactions found</div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Digital Identity */}
        <TabsContent value="identity">
          <Card className="bg-gradient-to-br from-gray-900 to-slate-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Digital Identity & Wallet</CardTitle>
              <CardDescription className="text-gray-300">
                Your decentralized identity and wallet information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label className="text-sm font-medium text-white">Decentralized Identifier (DID)</label>
                <div className="mt-1 p-3 bg-gray-800 rounded-lg font-mono text-sm">
                  {balance?.did || (
                    <span className="text-gray-400">
                      No DID generated yet. Click "Generate DID" to create your decentralized identity.
                    </span>
                  )}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-white">Wallet Address</label>
                <div className="mt-1 p-3 bg-gray-800 rounded-lg font-mono text-sm">
                  {balance?.walletAddress || (
                    <span className="text-gray-400">
                      No wallet address generated yet. Click "Create Wallet" to generate your energy wallet.
                    </span>
                  )}
                </div>
              </div>

              <div className="bg-gray-800 border border-gray-600 rounded-lg p-4">
                <h4 className="font-medium text-white mb-2">Banking Account Information</h4>
                <div className="space-y-2 text-sm text-gray-300">
                  <div>Account Type: Decentralized Energy Banking</div>
                  <div>Status: {balance ? 'Active' : 'Pending Setup'}</div>
                  <div>Account Created: {balance?.lastEnergyUpdate ? 
                    new Date(balance.lastEnergyUpdate).toLocaleDateString() : 'Not yet created'}</div>
                  <div>Total Lifetime Energy: {balance?.umatter?.toLocaleString() || '0'} UMatter</div>
                </div>
              </div>

              <div className="flex gap-4">
                <Button 
                  onClick={() => generateDID.mutate()}
                  disabled={generateDID.isPending || balance?.did}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {generateDID.isPending ? "Generating..." : 
                   balance?.did ? "DID Created" : "Generate DID"}
                </Button>
                <Button 
                  onClick={() => createWallet.mutate()}
                  disabled={createWallet.isPending || balance?.walletAddress}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {createWallet.isPending ? "Creating..." : 
                   balance?.walletAddress ? "Wallet Created" : "Create Wallet"}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </PageLayout>
  );
}