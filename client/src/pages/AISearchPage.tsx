import { useState } from 'react';
import { PageLayout } from '@/components/PageLayout';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Brain, 
  Zap,
  Globe,
  Database,
  Cpu,
  TrendingUp,
  History,
  Filter,
  Settings
} from 'lucide-react';

export function AISearchPage() {
  const [activeTab, setActiveTab] = useState('search');
  const [searchQuery, setSearchQuery] = useState('');

  const searchResults = [
    {
      id: 1,
      title: 'Energy Optimization Patterns in Smart Homes',
      description: 'AI-powered analysis of energy consumption patterns across 10,000+ smart homes reveals optimal usage strategies.',
      source: 'Energy Database',
      relevance: 95,
      category: 'Energy'
    },
    {
      id: 2,
      title: 'Quantum Computing Applications in Energy Grid Management',
      description: 'Research on quantum algorithms for optimizing renewable energy distribution in smart grids.',
      source: 'Quantum Research',
      relevance: 87,
      category: 'Quantum'
    },
    {
      id: 3,
      title: 'Biometric Data Privacy in Energy Trading',
      description: 'Privacy-preserving techniques for biometric energy data monetization and secure trading.',
      source: 'Privacy Studies',
      relevance: 92,
      category: 'Data'
    }
  ];

  const recentSearches = [
    'Quantum energy algorithms',
    'Biometric data privacy',
    'Smart grid optimization',
    'UMatter token economics',
    'Energy storage systems'
  ];

  return (
    <PageLayout className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 via-purple-500 to-cyan-600 bg-clip-text text-transparent">
                AI Search
              </h1>
              <p className="text-gray-400 mt-2">
                Intelligent search across energy, quantum, and data domains
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary" className="bg-blue-500/20 text-blue-300 border-blue-500/30">
                <Brain className="w-3 h-3 mr-1" />
                AI Powered
              </Badge>
              <Badge variant="secondary" className="bg-green-500/20 text-green-300 border-green-500/30">
                <Zap className="w-3 h-3 mr-1" />
                Real-time
              </Badge>
            </div>
          </div>
        </div>

        {/* Search Interface */}
        <div className="mb-8">
          <Card className="bg-gray-800/50 border-gray-700">
            <CardContent className="p-6">
              <div className="flex gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search across energy data, quantum research, and marketplace insights..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="bg-gray-900/50 border-gray-600 text-white placeholder-gray-400 h-12 text-lg"
                  />
                </div>
                <Button className="bg-blue-600 hover:bg-blue-700 h-12 px-8">
                  <Search className="h-5 w-5 mr-2" />
                  Search
                </Button>
              </div>
              
              <div className="flex flex-wrap gap-2 mt-4">
                {['Energy', 'Quantum', 'Data', 'IoT', 'Biometric', 'Marketplace'].map((tag) => (
                  <Badge 
                    key={tag} 
                    variant="outline" 
                    className="cursor-pointer hover:bg-blue-500/20"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4 bg-gray-800/50">
            <TabsTrigger value="search" className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-blue-400">
              Search Results
            </TabsTrigger>
            <TabsTrigger value="domains" className="data-[state=active]:bg-purple-500/20 data-[state=active]:text-purple-400">
              Search Domains
            </TabsTrigger>
            <TabsTrigger value="history" className="data-[state=active]:bg-green-500/20 data-[state=active]:text-green-400">
              Search History
            </TabsTrigger>
            <TabsTrigger value="settings" className="data-[state=active]:bg-orange-500/20 data-[state=active]:text-orange-400">
              AI Settings
            </TabsTrigger>
          </TabsList>

          {/* Search Results Tab */}
          <TabsContent value="search" className="space-y-6 mt-6">
            <div className="space-y-4">
              {searchResults.map((result) => (
                <Card key={result.id} className="bg-gray-800/50 border-gray-700 hover:border-blue-500/50 transition-colors">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-3">
                      <h3 className="text-xl font-semibold text-white">{result.title}</h3>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary" className="bg-blue-500/20 text-blue-300">
                          {result.category}
                        </Badge>
                        <Badge variant="outline" className="text-green-400">
                          {result.relevance}% match
                        </Badge>
                      </div>
                    </div>
                    <p className="text-gray-400 mb-4">{result.description}</p>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-500 text-sm">Source: {result.source}</span>
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Search Domains Tab */}
          <TabsContent value="domains" className="space-y-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[
                { name: 'Energy Database', icon: Zap, count: '2.4M records', color: 'green' },
                { name: 'Quantum Research', icon: Cpu, count: '847K papers', color: 'purple' },
                { name: 'Data Marketplace', icon: Database, count: '156K datasets', color: 'blue' },
                { name: 'Web Intelligence', icon: Globe, count: 'Real-time', color: 'orange' }
              ].map((domain) => {
                const IconComponent = domain.icon;
                return (
                  <Card key={domain.name} className="bg-gray-800/50 border-gray-700">
                    <CardContent className="p-6 text-center">
                      <IconComponent className={`h-12 w-12 mx-auto mb-4 text-${domain.color}-400`} />
                      <h3 className="text-lg font-semibold mb-2">{domain.name}</h3>
                      <p className="text-gray-400 text-sm mb-4">{domain.count}</p>
                      <Button variant="outline" className="w-full">
                        Search Domain
                      </Button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          {/* Search History Tab */}
          <TabsContent value="history" className="space-y-6 mt-6">
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-400">
                  <History className="h-5 w-5" />
                  Recent Searches
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentSearches.map((search, index) => (
                    <div key={index} className="flex justify-between items-center p-3 bg-gray-900/50 rounded-lg">
                      <span className="text-white">{search}</span>
                      <Button variant="ghost" size="sm">
                        <Search className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* AI Settings Tab */}
          <TabsContent value="settings" className="space-y-6 mt-6">
            <Card className="bg-gray-800/50 border-gray-700">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-orange-400">
                  <Settings className="h-5 w-5" />
                  AI Search Configuration
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="border-b border-gray-700 pb-4">
                    <h3 className="text-lg font-semibold mb-3">Search Preferences</h3>
                    <div className="space-y-3">
                      <label className="flex items-center justify-between">
                        <span className="text-gray-400">Real-time web search</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </label>
                      <label className="flex items-center justify-between">
                        <span className="text-gray-400">Include quantum research</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </label>
                      <label className="flex items-center justify-between">
                        <span className="text-gray-400">Energy data priority</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </label>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-3">AI Models</h3>
                    <div className="space-y-3">
                      <label className="flex items-center justify-between">
                        <span className="text-gray-400">GROK-3 Integration</span>
                        <input type="checkbox" defaultChecked className="rounded" />
                      </label>
                      <label className="flex items-center justify-between">
                        <span className="text-gray-400">Claude Analysis</span>
                        <input type="checkbox" className="rounded" />
                      </label>
                      <label className="flex items-center justify-between">
                        <span className="text-gray-400">OpenAI Processing</span>
                        <input type="checkbox" className="rounded" />
                      </label>
                    </div>
                  </div>
                </div>

                <Button className="w-full bg-orange-600 hover:bg-orange-700">
                  Save AI Settings
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
}