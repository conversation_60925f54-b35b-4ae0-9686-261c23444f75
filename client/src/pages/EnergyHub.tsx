import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Zap, Battery, Wifi, Smartphone, Laptop, Home, RefreshCw, TrendingUp, Activity, Cpu, MemoryStick, Network, DollarSign } from 'lucide-react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import PageLayout from '@/components/PageLayout';
import { UnifiedEnergyDisplay } from '@/components/UnifiedEnergyDisplay';
import { RealTimeEnergyDisplay } from '@/components/RealTimeEnergyDisplay';
import { EnergyFlowVisualization } from '@/components/EnergyFlowVisualization';
import { NuvaEnergyStorage } from '@/components/NuvaEnergyStorage';
interface WalletBalance {
  umatter: number;
  tru: number;
  nuva: number;
}
interface BankingBalance {
  umatter: number;
  tru: number;
  nuva: number;
}
interface HardwareMetrics {
  authenticEnergy: number;
  deviceCount: number;
  networkActivity: number;
  cpuUsage: number;
  memoryUsage: number;
}
export default function EnergyHub() {
  const queryClient = useQueryClient();
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [showEnergyStorage, setShowEnergyStorage] = useState(false);
  // Banking API - Real account balance 
  const { data: bankingBalance, refetch: refetchBanking, isLoading: bankingLoading } = useQuery<BankingBalance>({
    queryKey: ['/api/banking/balance'],
    queryFn: async () => {
      const response = await fetch('/api/banking/balance');
      if (!response.ok) throw new Error('Banking API failed');
      const data = await response.json();
      console.log('[EnergyHub] Banking API Response:', data);
      return data;
    },
    refetchInterval: 3000,
    staleTime: 0,
    cacheTime: 0
  });
  // Wallet API
  const { data: walletBalance, refetch: refetchWallet } = useQuery<WalletBalance>({
    queryKey: ['/api/wallet/balance'],
    refetchInterval: 5000
  });
  // Hardware metrics
  const { data: hardwareMetrics, refetch: refetchHardware } = useQuery<HardwareMetrics>({
    queryKey: ['/api/energy/real-hardware-metrics'],
    refetchInterval: 2000
  });
  // Display exactly what banking API returns - no modifications, no fallbacks
  const realUMatterBalance = bankingBalance?.umatter;
  const realTruBalance = bankingBalance?.tru; 
  const realNuvaBalance = bankingBalance?.nuva;
  // Show real banking API value in console log
  if (realUMatterBalance) console.log('[EnergyHub] EXACT BANKING API VALUE:', realUMatterBalance, 'UNMODIFIED');
  // Calculate real daily generation from hardware metrics
  const realDailyGeneration = hardwareMetrics?.authenticEnergy ? 
    (hardwareMetrics.authenticEnergy * 24 * 60 * 60 / 5) : // Scale 5-second reading to daily
    0;
  // Only show real percentage changes or null if no historical data
  const umatterChange = realUMatterBalance ? "Real-time" : "No Data";
  const truChange = realTruBalance ? "Real-time" : "No Data";
  const nuvaChange = realNuvaBalance ? "Real-time" : "No Data";
  const handleRefreshAll = () => {
    refetchWallet();
    refetchBanking();
    refetchHardware();
    setLastUpdate(new Date());
  };
  return (
    <PageLayout>
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-blue-900 p-6">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Header */}
          <div className="text-center space-y-4">
            <h1 className="text-5xl font-bold bg-gradient-to-r from-neon-cyan via-purple-400 to-neon-purple bg-clip-text text-transparent">
              Energy Hub
            </h1>
            <p className="text-xl text-gray-300">
              Real-time energy generation from your devices
            </p>
            <div className="flex justify-center gap-4">
              <Button 
                onClick={handleRefreshAll}
                className="bg-gradient-to-r from-neon-cyan to-blue-500 hover:from-cyan-400 hover:to-blue-600"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh All
              </Button>
              <Button 
                onClick={() => setShowEnergyStorage(true)}
                variant="outline"
                className="border-neon-purple text-neon-purple hover:bg-neon-purple/10"
              >
                <Zap className="h-4 w-4 mr-2" />
                Energy Storage
              </Button>
            </div>
            <div className="text-sm text-gray-400">
              Last updated: {lastUpdate.toLocaleTimeString()}
            </div>
          </div>
          {/* Real-time Energy Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Real UMatter Balance */}
            <Card className="glass-panel border-neon-cyan/30">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <Zap className="h-6 w-6 text-neon-cyan" />
                    <h3 className="text-lg font-semibold text-neon-cyan">UMatter Balance</h3>
                  </div>
                  <Zap className="h-8 w-8 text-neon-cyan/30" />
                </div>
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-white">
                    {realUMatterBalance ? realUMatterBalance.toFixed(8) : 'Banking API Loading...'}
                  </div>
                  <div className="text-sm text-green-400">
                    Banking API: {realUMatterBalance ? realUMatterBalance.toFixed(8) : 'Waiting for response...'}
                  </div>
                  <div className="text-xs text-gray-400">
                    Direct Banking API - Showing console log value: ~{realUMatterBalance ? Math.round(realUMatterBalance) : 'Loading...'}
                  </div>
                </div>
              </CardContent>
            </Card>
            {/* Real TRU Balance */}
            <Card className="glass-panel border-green-400/30">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-6 w-6 text-green-400" />
                    <h3 className="text-lg font-semibold text-green-400">TRU Token</h3>
                  </div>
                  <TrendingUp className="h-8 w-8 text-green-400/30" />
                </div>
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-white">
                    {realTruBalance ? realTruBalance.toFixed(1) : 'Loading...'}
                  </div>
                  <div className="text-sm text-green-400">{truChange} today</div>
                  <div className="text-xs text-gray-400">Real token balance</div>
                </div>
              </CardContent>
            </Card>
            {/* Real NUVA Balance */}
            <Card className="glass-panel border-purple-400/30">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-6 w-6 text-purple-400" />
                    <h3 className="text-lg font-semibold text-purple-400">NUVA Value</h3>
                  </div>
                  <DollarSign className="h-8 w-8 text-purple-400/30" />
                </div>
                <div className="space-y-2">
                  <div className="text-3xl font-bold text-white">
                    {realNuvaBalance ? `$${(realNuvaBalance * 1.2567).toFixed(3)}` : 'Loading...'}
                  </div>
                  <div className="text-sm text-gray-400">{nuvaChange}</div>
                  <div className="text-xs text-gray-400">Real NUVA: {realNuvaBalance ? realNuvaBalance.toFixed(2) : 'Loading...'}</div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Energy Generation & Hardware Stats */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            
            {/* Real Hardware Metrics */}
            <Card className="glass-panel">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Cpu className="h-5 w-5 text-neon-cyan" />
                  <span>Hardware Energy Generation</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-gray-800/50 rounded-lg">
                    <div className="text-2xl font-bold text-neon-cyan">
                      {hardwareMetrics?.authenticEnergy?.toFixed(6) || '0.000000'}
                    </div>
                    <div className="text-sm text-gray-400">Current Generation (UMatter/5s)</div>
                  </div>
                  <div className="text-center p-4 bg-gray-800/50 rounded-lg">
                    <div className="text-2xl font-bold text-green-400">
                      {realDailyGeneration.toFixed(2)}
                    </div>
                    <div className="text-sm text-gray-400">Projected Daily</div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-300">CPU Usage</span>
                    <span className="text-sm font-mono">{hardwareMetrics?.cpuUsage?.toFixed(1) || '0.0'}%</span>
                  </div>
                  <Progress value={hardwareMetrics?.cpuUsage || 0} className="h-2" />
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-300">Memory Usage</span>
                    <span className="text-sm font-mono">{hardwareMetrics?.memoryUsage?.toFixed(1) || '0.0'}%</span>
                  </div>
                  <Progress value={hardwareMetrics?.memoryUsage || 0} className="h-2" />
                </div>
              </CardContent>
            </Card>
            {/* Energy Flow Visualization */}
            <Card className="glass-panel">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Activity className="h-5 w-5 text-neon-purple" />
                  <span>Energy Flow</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <EnergyFlowVisualization />
              </CardContent>
            </Card>
          </div>
          {/* Energy Storage Modal */}
          <NuvaEnergyStorage 
            isOpen={showEnergyStorage} 
            onClose={() => setShowEnergyStorage(false)} 
          />
        </div>
      </div>
    </PageLayout>
  );
  }