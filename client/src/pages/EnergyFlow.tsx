import { EnergyFlowVisualization } from '@/components/EnergyFlowVisualization';
import { PageLayout } from '@/components/PageLayout';

export default function EnergyFlow() {
  return (
    <PageLayout className="min-h-screen bg-gray-900 dark:bg-gray-900 text-white dark:text-white">
      <div className="max-w-7xl mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
            Energy Flow Visualization
          </h1>
          <p className="text-gray-400 text-lg">
            Real-time energy accumulation, device metrics, and marketplace conversion
          </p>
        </div>

        {/* Energy Flow */}
        <EnergyFlowVisualization />
      </div>
    </PageLayout>
  );
}