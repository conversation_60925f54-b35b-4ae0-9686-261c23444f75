import { ExtensionDownload } from '@/components/ExtensionDownload';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Smartphone, AlertTriangle, Monitor } from 'lucide-react';

export function ExtensionPage() {
  // Detect if user is on mobile
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  const isTablet = /iPad|Android(?=.*Tablet)/i.test(navigator.userAgent);
  
  if (isMobile || isTablet) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Card className="border-blue-600 bg-blue-900/20 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-blue-400">
              <Smartphone className="w-5 h-5" />
              <span>Mobile Device Detected</span>
              <Badge variant="secondary" className="bg-green-900/30 text-green-400">Mobile Supported</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-400 mt-1 flex-shrink-0" />
              <div className="space-y-2">
                <p className="text-gray-300">
                  Welcome! nU Universe provides full mobile support with native device integration and real-time energy tracking.
                </p>
                <div className="bg-gray-800/50 p-3 rounded-lg space-y-2">
                  <p className="text-sm text-gray-400 font-medium">Mobile Features Available:</p>
                  <ul className="text-sm text-gray-300 space-y-1 list-disc list-inside ml-2">
                    <li>Native battery and device monitoring</li>
                    <li>Real-time touch and motion tracking</li>
                    <li>Progressive Web App installation</li>
                    <li>Cross-device energy synchronization</li>
                    <li>Authentic mobile UMatter generation</li>
                    <li>Offline energy banking support</li>
                  </ul>
                </div>
                <div className="bg-gray-800/50 p-4 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-400 mb-2">Get Started on Mobile:</h4>
                  <ol className="text-sm text-gray-300 space-y-1 list-decimal list-inside ml-2">
                    <li>Visit the Mobile Sync page for full device integration</li>
                    <li>Install as Progressive Web App for offline access</li>
                    <li>Enable device permissions for battery and motion tracking</li>
                    <li>Start generating UMatter from mobile interactions</li>
                  </ol>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <Card className="bg-gray-900/50 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-green-400">
            <Monitor className="w-5 h-5" />
            <span>Desktop Browser Extension</span>
            <Badge variant="outline" className="bg-green-900/30 text-green-400">Available</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-300">
            Download and install the nU Universe browser extension for desktop Chrome browsers to enable advanced web tracking and UMatter generation.
          </p>
                    <li>Use Chrome, Edge, or another Chromium-based browser</li>
                    <li>Download and install the extension from the desktop version</li>
                  </ol>
                </div>
                <div className="flex items-center space-x-2 mt-4">
                  <Monitor className="w-4 h-4 text-blue-400" />
                  <span className="text-sm text-blue-400">Switch to desktop to access the full nU Universe experience</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <ExtensionDownload />
    </div>
  );
}