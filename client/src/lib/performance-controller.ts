/**
 * Performance Controller - Completely eliminates individual API calls
 */

// Track all individual deposit attempts and block them
let blockedCalls = 0;

// Override the global fetch to intercept individual calls
const originalFetch = window.fetch;
window.fetch = function(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
  const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url;
  
  // Block all individual deposit-umatter calls (not batch)
  if (url.includes('/api/banking/deposit-umatter') && !url.includes('batch')) {
    blockedCalls++;
    console.log(`[PerformanceController] Blocked individual call #${blockedCalls} - use batching only`);
    
    return Promise.resolve(new Response(
      JSON.stringify({ 
        error: 'Individual calls blocked for performance',
        useInstead: 'batching system',
        blockedCount: blockedCalls,
        redirectTo: '/api/banking/deposit-umatter-batch'
      }),
      { 
        status: 429, 
        statusText: 'Too Many Requests',
        headers: { 'Content-Type': 'application/json' } 
      }
    ));
  }

  // Allow batch requests to pass through
  if (url.includes('/api/banking/deposit-umatter-batch')) {
    console.log(`[PerformanceController] Allowing batch request through`);
  }
  
  return originalFetch.call(this, input, init);
};

console.log('[PerformanceController] Individual API call blocking enabled - batching only');

export { blockedCalls };