import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  const res = await fetch(url, {
    method,
    headers: data ? { "Content-Type": "application/json" } : {},
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include",
  });

  await throwIfResNotOk(res);
  return res;
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const res = await fetch(queryKey[0] as string, {
      credentials: "include",
    });

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    await throwIfResNotOk(res);
    return await res.json();
  };

import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 3,
      staleTime: 2000, // Reduced for more real-time updates
      refetchInterval: false,
      queryFn: async ({ queryKey }) => {
        const url = Array.isArray(queryKey) ? queryKey[0] : queryKey;
        const startTime = performance.now();
        
        console.log(`[QueryClient] 🔄 REAL API Call: ${url}`);

        try {
          const response = await fetch(url as string, {
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json'
            }
          });
          
          const endTime = performance.now();
          const duration = Math.round(endTime - startTime);
          
          if (!response.ok) {
            console.error(`[QueryClient] ❌ API Error: ${response.status} for ${url} (${duration}ms)`);
            throw new Error(`API request failed: ${response.status}`);
          }

          const data = await response.json();
          const hasRealData = data && Object.keys(data).length > 0 && 
            !Object.values(data).every(v => v === 0 || v === false || v === '' || v === null);
          
          console.log(`[QueryClient] ✅ Response for ${url} (${duration}ms):`, {
            hasRealData,
            dataKeys: Object.keys(data),
            sampleData: data
          });
          
          return data;
        } catch (error) {
          const endTime = performance.now();
          const duration = Math.round(endTime - startTime);
          console.error(`[QueryClient] 💥 Network Error for ${url} (${duration}ms):`, error);
          throw error;
        }
      }
    },
  },
});