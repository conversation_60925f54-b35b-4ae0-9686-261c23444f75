
import { useState, useEffect } from 'react';
import { realTimeDeviceManager, DeviceInfo, EnergyMetrics } from '../RealTimeDeviceManager';

interface UseRealTimeDeviceManager {
  devices: DeviceInfo[];
  energyMetrics: EnergyMetrics;
  isMonitoring: boolean;
  discoverDevices: () => Promise<DeviceInfo[]>;
  scanBluetoothDevices: () => Promise<DeviceInfo[]>;
}

export function useRealTimeDeviceManager(): UseRealTimeDeviceManager {
  const [devices, setDevices] = useState<DeviceInfo[]>([]);
  const [energyMetrics, setEnergyMetrics] = useState<EnergyMetrics>({
    batteryDrain: 0,
    cpuUsage: 0,
    memoryUsage: 0,
    networkActivity: 0,
    screenBrightness: 50,
    umatterGenerated: 0
  });
  const [isMonitoring, setIsMonitoring] = useState(true);

  useEffect(() => {
    // Subscribe to device updates
    const unsubscribe = realTimeDeviceManager.subscribe((updatedDevices) => {
      setDevices(updatedDevices);
    });

    // Update energy metrics periodically
    const metricsInterval = setInterval(() => {
      const currentMetrics = realTimeDeviceManager.getEnergyMetrics();
      setEnergyMetrics(currentMetrics);
    }, 1000);

    // Initial load
    setDevices(realTimeDeviceManager.getDevices());
    setEnergyMetrics(realTimeDeviceManager.getEnergyMetrics());

    return () => {
      unsubscribe();
      clearInterval(metricsInterval);
    };
  }, []);

  const discoverDevices = async (): Promise<DeviceInfo[]> => {
    try {
      const discoveredDevices = await realTimeDeviceManager.discoverDevices();
      return discoveredDevices;
    } catch (error) {
      console.error('[useRealTimeDeviceManager] Discovery failed:', error);
      return [];
    }
  };

  const scanBluetoothDevices = async (): Promise<DeviceInfo[]> => {
    try {
      const bluetoothDevices = await realTimeDeviceManager.scanBluetoothDevices();
      return bluetoothDevices;
    } catch (error) {
      console.error('[useRealTimeDeviceManager] Bluetooth scan failed:', error);
      throw error;
    }
  };

  return {
    devices,
    energyMetrics,
    isMonitoring,
    discoverDevices,
    scanBluetoothDevices
  };
}
