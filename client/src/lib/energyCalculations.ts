// Authentic Energy Calculation System
export interface EnergyData {
  generated: number;
  consumed: number;
  stored: number;
  efficiency: number;
  timestamp: Date;
}

export interface EnergySource {
  id: string;
  type: 'solar' | 'wind' | 'biometric' | 'kinetic' | 'thermal';
  output: number;
  efficiency: number;
  isActive: boolean;
}

export interface EnergyCalculationResult {
  totalEnergy: number;
  netEnergy: number;
  efficiency: number;
  projectedOutput: number;
  optimizationSuggestions: string[];
}

class EnergyCalculations {
  private energySources: Map<string, EnergySource> = new Map();
  private energyHistory: EnergyData[] = [];

  calculateTotalEnergy(sources: EnergySource[]): number {
    return sources.reduce((total, source) => {
      return total + (source.isActive ? source.output : 0);
    }, 0);
  }

  calculateEfficiency(generated: number, consumed: number): number {
    if (consumed === 0) return 1;
    return Math.min(1, generated / consumed);
  }

  calculateNetEnergy(generated: number, consumed: number): number {
    return generated - consumed;
  }

  projectEnergyOutput(currentRate: number, timeHours: number): number {
    return currentRate * timeHours;
  }

  optimizeEnergyDistribution(sources: EnergySource[]): EnergyCalculationResult {
    const totalEnergy = this.calculateTotalEnergy(sources);
    const averageEfficiency = sources.reduce((sum, s) => sum + s.efficiency, 0) / sources.length;
    
    return {
      totalEnergy,
      netEnergy: totalEnergy * 0.85, // Account for losses
      efficiency: averageEfficiency,
      projectedOutput: this.projectEnergyOutput(totalEnergy, 24),
      optimizationSuggestions: this.generateOptimizationSuggestions(sources)
    };
  }

  private generateOptimizationSuggestions(sources: EnergySource[]): string[] {
    const suggestions: string[] = [];
    
    sources.forEach(source => {
      if (source.efficiency < 0.7) {
        suggestions.push(`Optimize ${source.type} source efficiency`);
      }
      if (!source.isActive && source.output > 0) {
        suggestions.push(`Activate ${source.type} source`);
      }
    });
    
    return suggestions;
  }

  addEnergySource(source: EnergySource): void {
    this.energySources.set(source.id, source);
  }

  removeEnergySource(id: string): void {
    this.energySources.delete(id);
  }

  getEnergySource(id: string): EnergySource | undefined {
    return this.energySources.get(id);
  }

  getAllEnergySources(): EnergySource[] {
    return Array.from(this.energySources.values());
  }

  recordEnergyData(data: EnergyData): void {
    this.energyHistory.push(data);
    
    // Keep only last 1000 records
    if (this.energyHistory.length > 1000) {
      this.energyHistory = this.energyHistory.slice(-1000);
    }
  }

  getEnergyHistory(limit?: number): EnergyData[] {
    if (limit) {
      return this.energyHistory.slice(-limit);
    }
    return [...this.energyHistory];
  }

  calculateAverageGeneration(hours: number = 24): number {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    const recentData = this.energyHistory.filter(d => d.timestamp >= cutoff);
    
    if (recentData.length === 0) return 0;
    
    return recentData.reduce((sum, d) => sum + d.generated, 0) / recentData.length;
  }

  calculatePeakGeneration(hours: number = 24): number {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    const recentData = this.energyHistory.filter(d => d.timestamp >= cutoff);
    
    if (recentData.length === 0) return 0;
    
    return Math.max(...recentData.map(d => d.generated));
  }

  calculateEnergyTrend(hours: number = 24): 'increasing' | 'decreasing' | 'stable' {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    const recentData = this.energyHistory.filter(d => d.timestamp >= cutoff);
    
    if (recentData.length < 2) return 'stable';
    
    const firstHalf = recentData.slice(0, Math.floor(recentData.length / 2));
    const secondHalf = recentData.slice(Math.floor(recentData.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, d) => sum + d.generated, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, d) => sum + d.generated, 0) / secondHalf.length;
    
    const difference = secondAvg - firstAvg;
    const threshold = firstAvg * 0.05; // 5% threshold
    
    if (difference > threshold) return 'increasing';
    if (difference < -threshold) return 'decreasing';
    return 'stable';
  }
}

export const energyCalculations = new EnergyCalculations();
