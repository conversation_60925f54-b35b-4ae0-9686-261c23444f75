/**
 * Real-Time Synchronization Manager
 * Connects to WebSocket for live data updates across the entire application
 */

interface RealTimeUpdate {
  type: string;
  battery?: number;
  umatter?: number;
  timestamp: number;
}

class RealTimeSync {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private listeners = new Set<(data: RealTimeUpdate) => void>();

  constructor() {
    this.connect();
  }

  private connect() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    
    try {
      this.ws = new WebSocket(`${protocol}//${host}/nu-websocket`);
      
      this.ws.onopen = () => {
        console.log('[RealTimeSync] Connected to live data stream');
        this.reconnectAttempts = 0;
        
        // Send initial battery data if available
        this.sendBatteryUpdate();
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data) as RealTimeUpdate;
          this.notifyListeners(data);
        } catch (error) {
          console.error('[RealTimeSync] Parse error:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('[RealTimeSync] Connection closed');
        this.reconnect();
      };

      this.ws.onerror = (error) => {
        console.error('[RealTimeSync] WebSocket error:', error);
      };

    } catch (error) {
      console.error('[RealTimeSync] Connection failed:', error);
      this.reconnect();
    }
  }

  private reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
      console.log(`[RealTimeSync] Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);
      setTimeout(() => this.connect(), delay);
    }
  }

  private async sendBatteryUpdate() {
    if ('getBattery' in navigator && this.ws?.readyState === WebSocket.OPEN) {
      try {
        const battery = await (navigator as any).getBattery();
        this.ws.send(JSON.stringify({
          type: 'battery_update',
          level: battery.level,
          charging: battery.charging,
          timestamp: Date.now()
        }));
      } catch (error) {
        console.log('[RealTimeSync] Battery API not available');
      }
    }
  }

  public subscribe(callback: (data: RealTimeUpdate) => void): () => void {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  private notifyListeners(data: RealTimeUpdate) {
    this.listeners.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('[RealTimeSync] Listener error:', error);
      }
    });
  }

  public sendUMatterGeneration(amount: number) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'umatter_generated',
        amount,
        timestamp: Date.now()
      }));
    }
  }

  public disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

export const realTimeSync = new RealTimeSync();