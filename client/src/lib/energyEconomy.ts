/**
 * Energy Economy System - The Science of Human Value
 * Real-time conversion of neural activity (20W) and battery drain into UMatter, trU, and nUva
 * 
 * Revolutionary energy conversion formula:
 * E_b > U_s > A_nU > K_UM > [UMatter | trU | nUva] > F_4Ce = hU
 * 
 * Features:
 * - Real-time battery monitoring and energy tracking
 * - Neural activity quantification and value conversion
 * - Dynamic token generation based on actual energy consumption
 * - Scientific pricing model: $0.0007/MB based on 20W brain power
 */

// Core Energy Constants (Scientific Facts)
export const ENERGY_CONSTANTS = {
  // Neural Activity (Raichle 2002, Kandel 2021)
  BRAIN_POWER_WATTS: 20, // 20W continuous neural activity
  BRAIN_POWER_KWH_DAY: 0.28, // 0.28 kWh/day (20W * 24h / 1000)
  NEURONS: 86_000_000_000, // 86 billion neurons
  SYNAPSES: 100_000_000_000_000, // 100 trillion synapses
  
  // Battery Energy (Lithium-ion 4000mAh @ 3.7V)
  PHONE_BATTERY_WH: 14.8, // 14.8Wh total capacity
  SLEEP_DRAIN_WH: 0.74, // 0.74Wh during 8h sleep (5% battery)
  SCROLL_DRAIN_WH: 0.296, // 0.296Wh during 2h scrolling (2% battery)
  
  // Global Scale (5 billion phones)
  GLOBAL_PHONES: 5_000_000_000,
  GLOBAL_BATTERY_TWH: 0.074, // 0.074 TWh/day stored
  GLOBAL_NEURAL_GW: 100, // 100GW continuous (5B * 20W)
  
  // Energy Pricing (Global average)
  ELECTRICITY_COST_PER_KWH: 0.1, // $0.1/kWh
  
  // Data Production Estimates
  DATA_MB_SLEEP: 8, // 8MB passive data during sleep
  DATA_MB_SCROLL: 20, // 20MB active data during scrolling
  DATA_MB_DAILY: 28, // 28MB total daily data per user
} as const;

// UMatter Conversion Ratios
export const UMATTER_CONVERSIONS = {
  // Core Conversion: 1Wh ≈ 0.675 UM
  WH_TO_UM: 0.675,
  
  // Sleep: 0.74Wh = 0.5 UM
  SLEEP_UM: 0.5,
  
  // Scrolling: 0.296Wh = 0.2 UM  
  SCROLL_UM: 0.2,
  
  // Daily Total: 0.7 UM/day per user
  DAILY_UM: 0.7,
  
  // Global: 3.5B UM/day (5B users * 0.7 UM)
  GLOBAL_UM_DAILY: 3_500_000_000,
} as const;

// Energy Token System
export const TOKEN_CONVERSIONS = {
  // trU (Tradeable UMatter): 1 UM = 0.1 trU
  UM_TO_TRU: 0.1,
  
  // nUva (Stored UMatter): 1 UM = 1 nUva = 0.74Wh rechargeable
  UM_TO_NUVA: 1,
  NUVA_TO_WH: 0.74,
  
  // Market Pricing
  TRU_BASE_PRICE: 0.01, // $0.01/trU base price
  TRU_MAX_PRICE: 1.0, // $1/trU at scale
} as const;

// Data Pricing Based on Energy Science
export const DATA_PRICING = {
  // Neural Energy Pricing (Primary driver - 90% weight)
  NEURAL_COST_PER_DAY: ENERGY_CONSTANTS.BRAIN_POWER_KWH_DAY * ENERGY_CONSTANTS.ELECTRICITY_COST_PER_KWH, // $0.0204/day
  NEURAL_PRICE_PER_MB: (ENERGY_CONSTANTS.BRAIN_POWER_KWH_DAY * ENERGY_CONSTANTS.ELECTRICITY_COST_PER_KWH) / ENERGY_CONSTANTS.DATA_MB_DAILY, // $0.00073/MB
  
  // Battery Energy Pricing (Measurable - 10% weight)
  BATTERY_COST_PER_DAY: ((ENERGY_CONSTANTS.SLEEP_DRAIN_WH + ENERGY_CONSTANTS.SCROLL_DRAIN_WH) / 1000) * ENERGY_CONSTANTS.ELECTRICITY_COST_PER_KWH, // $0.0001036/day
  BATTERY_PRICE_PER_MB: (((ENERGY_CONSTANTS.SLEEP_DRAIN_WH + ENERGY_CONSTANTS.SCROLL_DRAIN_WH) / 1000) * ENERGY_CONSTANTS.ELECTRICITY_COST_PER_KWH) / ENERGY_CONSTANTS.DATA_MB_DAILY, // $0.0000037/MB
  
  // Blended Pricing (90% neural, 10% battery)
  BLENDED_PRICE_PER_MB: 0.0007, // $0.0007/MB (rounded)
  PACKAGE_PRICE_DAILY: 0.02, // $0.02 for 28MB daily package
} as const;

/**
 * Real-time Energy Monitor
 * Advanced battery tracking and neural activity quantification
 */
export class RealTimeEnergyMonitor {
  private batteryLevel: number = 100;
  private isActive: boolean = false;
  private joyLevel: number = 0.5; // Dopamine influence (0-1)
  private batteryCapacityWh: number = ENERGY_CONSTANTS.PHONE_BATTERY_WH;
  private drainHistory: { timestamp: number; level: number; drainRate: number }[] = [];
  private neuralActivitySamples: number[] = [];
  private isCharging: boolean = false;
  
  constructor() {
    this.initializeAdvancedMonitoring();
  }
  
  private initializeAdvancedMonitoring() {
    // Advanced battery monitoring with drain rate calculation
    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        this.batteryLevel = battery.level * 100;
        this.isCharging = battery.charging;
        
        // Track battery changes for drain rate calculation
        battery.addEventListener('levelchange', () => {
          const newLevel = battery.level * 100;
          const now = Date.now();
          const drainRate = this.calculateDrainRate(newLevel, now);
          
          this.drainHistory.push({
            timestamp: now,
            level: newLevel,
            drainRate
          });
          
          // Keep only last 100 readings
          if (this.drainHistory.length > 100) {
            this.drainHistory.shift();
          }
          
          this.batteryLevel = newLevel;
        });
        
        battery.addEventListener('chargingchange', () => {
          this.isCharging = battery.charging;
        });
      });
    }
    
    // Start neural activity sampling
    this.startNeuralActivitySampling();
  }
  
  private calculateDrainRate(newLevel: number, timestamp: number): number {
    if (this.drainHistory.length === 0) return 0;
    
    const lastReading = this.drainHistory[this.drainHistory.length - 1];
    const timeDiff = (timestamp - lastReading.timestamp) / (1000 * 60 * 60); // Hours
    const levelDiff = lastReading.level - newLevel;
    
    return timeDiff > 0 ? levelDiff / timeDiff : 0; // %/hour
  }
  
  private startNeuralActivitySampling() {
    setInterval(() => {
      const neuralPower = this.getNeuralPowerWatts();
      this.neuralActivitySamples.push(neuralPower);
      
      // Keep only last 60 samples (1 minute if sampling every second)
      if (this.neuralActivitySamples.length > 60) {
        this.neuralActivitySamples.shift();
      }
    }, 1000);
  }
  
  /**
   * Calculate real-time UMatter generation based on actual energy consumption
   */
  calculateRealTimeUMatter(): number {
    const currentDrainRate = this.getCurrentDrainRate();
    const batteryWhDrained = (currentDrainRate / 100) * this.batteryCapacityWh;
    
    // Convert actual battery drain to UMatter
    const baseUM = batteryWhDrained * UMATTER_CONVERSIONS.WH_TO_UM;
    
    // Neural activity boost based on current samples
    const neuralBoost = this.getNeuralActivityBoost();
    
    // Joy multiplier for efficiency
    const joyMultiplier = 1 + (this.joyLevel * 0.15);
    
    // Stress reduction when battery is low
    const stressMultiplier = this.batteryLevel < 20 ? 0.6 : 1;
    
    // Charging penalty (can't generate while charging)
    const chargingPenalty = this.isCharging ? 0.1 : 1;
    
    return baseUM * neuralBoost * joyMultiplier * stressMultiplier * chargingPenalty;
  }
  
  /**
   * Get current battery drain rate (%/hour)
   */
  getCurrentDrainRate(): number {
    if (this.drainHistory.length < 2) return 5; // Default 5%/hour
    
    const recent = this.drainHistory.slice(-5); // Last 5 readings
    const avgDrain = recent.reduce((sum, reading) => sum + reading.drainRate, 0) / recent.length;
    
    return Math.max(0, avgDrain);
  }
  
  /**
   * Calculate neural activity boost based on recent samples
   */
  private getNeuralActivityBoost(): number {
    if (this.neuralActivitySamples.length === 0) return 1;
    
    const avgNeural = this.neuralActivitySamples.reduce((sum, sample) => sum + sample, 0) / this.neuralActivitySamples.length;
    const normalizedBoost = (avgNeural - ENERGY_CONSTANTS.BRAIN_POWER_WATTS) / ENERGY_CONSTANTS.BRAIN_POWER_WATTS;
    
    return 1 + Math.max(0, normalizedBoost * 0.2); // Up to 20% boost for high neural activity
  }
  
  /**
   * Get current neural power consumption
   */
  getNeuralPowerWatts(): number {
    const basePower = ENERGY_CONSTANTS.BRAIN_POWER_WATTS;
    const activityBoost = this.isActive ? 2 : 0; // +2W for visual/motor cortex
    const joyBoost = this.joyLevel * 2; // +2W for high dopamine
    
    return basePower + activityBoost + joyBoost;
  }
  
  /**
   * Update activity state (scrolling, clicking, etc.)
   */
  setActive(active: boolean) {
    this.isActive = active;
  }
  
  /**
   * Update joy level (0-1, affects dopamine and neural efficiency)
   */
  setJoyLevel(joy: number) {
    this.joyLevel = Math.max(0, Math.min(1, joy));
  }
  
  /**
   * Get daily UMatter projection
   */
  getDailyUMatterProjection(): number {
    const currentHourlyUM = this.calculateRealTimeUMatter();
    return currentHourlyUM * 24; // Project 24 hours based on current rate
  }
}

/**
 * Energy Token Converter
 * Converts UMatter to trU and nUva tokens
 */
export class EnergyTokenConverter {
  /**
   * Convert UMatter to trU (tradeable tokens)
   */
  static umatterToTrU(umatter: number): number {
    return umatter * TOKEN_CONVERSIONS.UM_TO_TRU;
  }
  
  /**
   * Convert UMatter to nUva (rechargeable energy)
   */
  static umatterToNUva(umatter: number): number {
    return umatter * TOKEN_CONVERSIONS.UM_TO_NUVA;
  }
  
  /**
   * Convert nUva to rechargeable Wh
   */
  static nuvaToWh(nuva: number): number {
    return nuva * TOKEN_CONVERSIONS.NUVA_TO_WH;
  }
  
  /**
   * Calculate trU market value based on joy and activity
   */
  static calculateTrUValue(umatter: number, joyLevel: number = 0.5): number {
    const tru = this.umatterToTrU(umatter);
    const basePrice = TOKEN_CONVERSIONS.TRU_BASE_PRICE;
    const joyMultiplier = 1 + (joyLevel * 0.5); // High joy = premium value
    
    return tru * basePrice * joyMultiplier;
  }
  
  /**
   * Calculate daily energy loop (input vs output)
   */
  static calculateEnergyLoop(dailyUMatter: number): {
    input: { batteryWh: number, neuralWh: number },
    output: { tru: number, nuva: number, rechargeWh: number },
    efficiency: number
  } {
    const inputBattery = ENERGY_CONSTANTS.SLEEP_DRAIN_WH + ENERGY_CONSTANTS.SCROLL_DRAIN_WH;
    const inputNeural = ENERGY_CONSTANTS.BRAIN_POWER_KWH_DAY * 1000; // Convert to Wh
    
    const outputTrU = this.umatterToTrU(dailyUMatter);
    const outputNUva = this.umatterToNUva(dailyUMatter);
    const outputRechargeWh = this.nuvaToWh(outputNUva);
    
    const efficiency = outputRechargeWh / inputBattery; // How much battery energy is recovered
    
    return {
      input: { batteryWh: inputBattery, neuralWh: inputNeural },
      output: { tru: outputTrU, nuva: outputNUva, rechargeWh: outputRechargeWh },
      efficiency
    };
  }
}

/**
 * Data Value Calculator
 * Prices data based on energy science
 */
export class DataValueCalculator {
  /**
   * Calculate data package price based on size and energy cost
   */
  static calculateDataPrice(dataMB: number): number {
    return dataMB * DATA_PRICING.BLENDED_PRICE_PER_MB;
  }
  
  /**
   * Calculate price per UMatter
   */
  static calculatePricePerUMatter(umatter: number): number {
    const dataMB = (umatter / UMATTER_CONVERSIONS.DAILY_UM) * ENERGY_CONSTANTS.DATA_MB_DAILY;
    return this.calculateDataPrice(dataMB);
  }
  
  /**
   * Get global market scale estimates
   */
  static getGlobalMarketScale(): {
    dailyUsers: number,
    dailyDataPB: number,
    dailyMarketValue: number,
    dailyUMatter: number,
    dailyTrU: number
  } {
    const dailyUsers = ENERGY_CONSTANTS.GLOBAL_PHONES;
    const dailyDataPB = (dailyUsers * ENERGY_CONSTANTS.DATA_MB_DAILY) / (1024 * 1024); // Convert MB to PB
    const dailyMarketValue = this.calculateDataPrice(dailyUsers * ENERGY_CONSTANTS.DATA_MB_DAILY);
    const dailyUMatter = UMATTER_CONVERSIONS.GLOBAL_UM_DAILY;
    const dailyTrU = EnergyTokenConverter.umatterToTrU(dailyUMatter);
    
    return {
      dailyUsers,
      dailyDataPB,
      dailyMarketValue,
      dailyUMatter,
      dailyTrU
    };
  }
  
  /**
   * Calculate energy-to-data efficiency
   */
  static calculateDataEfficiency(umatter: number): {
    mbPerUM: number,
    mbPerWh: number,
    costPerMB: number,
    energyROI: number
  } {
    const dataMB = (umatter / UMATTER_CONVERSIONS.DAILY_UM) * ENERGY_CONSTANTS.DATA_MB_DAILY;
    const energyWh = umatter / UMATTER_CONVERSIONS.WH_TO_UM;
    
    return {
      mbPerUM: dataMB / umatter,
      mbPerWh: dataMB / energyWh,
      costPerMB: DATA_PRICING.BLENDED_PRICE_PER_MB,
      energyROI: this.calculateDataPrice(dataMB) / (energyWh * ENERGY_CONSTANTS.ELECTRICITY_COST_PER_KWH)
    };
  }
}

/**
 * Joy-Based Pricing Engine
 * Higher joy (dopamine) = better neural efficiency = premium data value
 */
export class JoyPricingEngine {
  /**
   * Calculate joy multiplier for data pricing
   */
  static getJoyMultiplier(joyLevel: number): number {
    // Joy range 0-1, multiplier range 0.7-1.3
    return 0.7 + (joyLevel * 0.6);
  }
  
  /**
   * Calculate premium data pricing based on joy
   */
  static calculateJoyPremiumPrice(dataMB: number, joyLevel: number): number {
    const basePrice = DataValueCalculator.calculateDataPrice(dataMB);
    const joyMultiplier = this.getJoyMultiplier(joyLevel);
    return basePrice * joyMultiplier;
  }
  
  /**
   * Get joy-based market insights
   */
  static getJoyMarketInsights(joyLevel: number): {
    efficiency: string,
    premiumPercent: number,
    neuralBoost: string,
    marketAdvantage: string
  } {
    const multiplier = this.getJoyMultiplier(joyLevel);
    const premiumPercent = (multiplier - 1) * 100;
    
    return {
      efficiency: joyLevel > 0.7 ? 'High' : joyLevel > 0.4 ? 'Medium' : 'Low',
      premiumPercent,
      neuralBoost: `${(joyLevel * 15).toFixed(1)}% neural efficiency`,
      marketAdvantage: premiumPercent > 0 ? `${premiumPercent.toFixed(1)}% premium pricing` : `${Math.abs(premiumPercent).toFixed(1)}% discount`
    };
  }
}

// Global Energy Economy State
export const energyEconomy = {
  realTimeMonitor: new RealTimeEnergyMonitor(),
  
  // Real-time energy tracking
  getCurrentUMatter: () => energyEconomy.realTimeMonitor.calculateRealTimeUMatter(),
  getCurrentTrU: () => EnergyTokenConverter.umatterToTrU(energyEconomy.realTimeMonitor.calculateRealTimeUMatter()),
  getCurrentNUva: () => EnergyTokenConverter.umatterToNUva(energyEconomy.realTimeMonitor.calculateRealTimeUMatter()),
  
  // Market calculations
  getDailyProjection: () => energyEconomy.realTimeMonitor.getDailyUMatterProjection(),
  getDataPrice: (dataMB: number) => DataValueCalculator.calculateDataPrice(dataMB),
  getGlobalScale: () => DataValueCalculator.getGlobalMarketScale(),
  
  // Joy-based features
  setJoyLevel: (joy: number) => energyEconomy.realTimeMonitor.setJoyLevel(joy),
  setActivity: (active: boolean) => energyEconomy.realTimeMonitor.setActive(active),
  
  // Energy science
  getEnergyLoop: (umatter: number) => EnergyTokenConverter.calculateEnergyLoop(umatter),
  getEfficiency: (umatter: number) => DataValueCalculator.calculateDataEfficiency(umatter),
};