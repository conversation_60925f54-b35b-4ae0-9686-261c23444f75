/**
 * Extension Reliability Manager
 * Handles extension detection, reconnection, and fallback UI
 */

interface ExtensionStatus {
  connected: boolean;
  version: string;
  lastSeen: number;
  reconnectAttempts: number;
  totalUMatter: number;
}

class ExtensionReliabilityManager {
  private status: ExtensionStatus = {
    connected: false,
    version: '1.0.0',
    lastSeen: 0,
    reconnectAttempts: 0,
    totalUMatter: 0
  };

  private listeners = new Set<(status: ExtensionStatus) => void>();
  private reconnectInterval?: NodeJS.Timeout;
  private healthCheckInterval?: NodeJS.Timeout;

  constructor() {
    this.startHealthCheck();
    this.setupMessageListener();
  }

  /**
   * Start periodic health checks
   */
  private startHealthCheck() {
    this.healthCheckInterval = setInterval(async () => {
      try {
        const response = await fetch('/api/extension/status', {
          method: 'GET',
          headers: { 'Cache-Control': 'no-cache' }
        });
        
        if (response.ok) {
          const data = await response.json();
          this.updateStatus({
            connected: data.connected || false,
            version: data.version || '1.0.0',
            lastSeen: data.connected ? Date.now() : this.status.lastSeen,
            totalUMatter: data.totalUMatter || this.status.totalUMatter,
            reconnectAttempts: data.connected ? 0 : this.status.reconnectAttempts
          });

          // Start reconnection if disconnected
          if (!data.connected && !this.reconnectInterval) {
            this.startReconnectionAttempts();
          }
        } else {
          this.handleConnectionLoss();
        }
      } catch (error) {
        console.warn('[ExtensionReliability] Health check failed:', error);
        this.handleConnectionLoss();
      }
    }, 10000); // Check every 10 seconds instead of 2 seconds
  }

  /**
   * Handle connection loss
   */
  private handleConnectionLoss() {
    this.updateStatus({
      ...this.status,
      connected: false
    });

    if (!this.reconnectInterval) {
      this.startReconnectionAttempts();
    }
  }

  /**
   * Start reconnection attempts with exponential backoff
   */
  private startReconnectionAttempts() {
    let attempts = 0;
    const maxAttempts = 5;

    this.reconnectInterval = setInterval(async () => {
      attempts++;
      
      try {
        // Try to ping extension
        const response = await fetch('/api/extension/ping', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ timestamp: Date.now() })
        });

        if (response.ok) {
          console.log('[ExtensionReliability] Reconnection successful');
          this.updateStatus({
            ...this.status,
            connected: true,
            lastSeen: Date.now(),
            reconnectAttempts: 0
          });
          
          if (this.reconnectInterval) {
            clearInterval(this.reconnectInterval);
            this.reconnectInterval = undefined;
          }
          return;
        }
      } catch (error) {
        console.warn(`[ExtensionReliability] Reconnection attempt ${attempts} failed:`, error);
      }

      this.updateStatus({
        ...this.status,
        reconnectAttempts: attempts
      });

      // Stop after max attempts
      if (attempts >= maxAttempts) {
        console.log('[ExtensionReliability] Max reconnection attempts reached');
        if (this.reconnectInterval) {
          clearInterval(this.reconnectInterval);
          this.reconnectInterval = undefined;
        }
      }
    }, Math.min(2000 * Math.pow(2, attempts), 30000)); // Exponential backoff, max 30s
  }

  /**
   * Setup message listener for extension communication
   */
  private setupMessageListener() {
    window.addEventListener('message', (event) => {
      if (event.source !== window) return;

      if (event.data.type === 'NU_EXTENSION_STATUS') {
        this.updateStatus({
          connected: true,
          version: event.data.version || '1.0.0',
          lastSeen: Date.now(),
          totalUMatter: event.data.totalUMatter || this.status.totalUMatter,
          reconnectAttempts: 0
        });
      }
    });
  }

  /**
   * Update status and notify listeners
   */
  private updateStatus(newStatus: Partial<ExtensionStatus>) {
    const updated = { ...this.status, ...newStatus };
    const changed = JSON.stringify(updated) !== JSON.stringify(this.status);
    
    this.status = updated;
    
    if (changed) {
      this.listeners.forEach(listener => listener(this.status));
    }
  }

  /**
   * Subscribe to status changes
   */
  subscribe(listener: (status: ExtensionStatus) => void): () => void {
    this.listeners.add(listener);
    
    // Send current status immediately
    listener(this.status);
    
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Get current status
   */
  getStatus(): ExtensionStatus {
    return { ...this.status };
  }

  /**
   * Manual reconnection trigger
   */
  async reconnect(): Promise<boolean> {
    try {
      // Clear existing reconnection
      if (this.reconnectInterval) {
        clearInterval(this.reconnectInterval);
        this.reconnectInterval = undefined;
      }

      // Try immediate connection
      const response = await fetch('/api/extension/connect', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          timestamp: Date.now(),
          forceReconnect: true 
        })
      });

      if (response.ok) {
        this.updateStatus({
          connected: true,
          lastSeen: Date.now(),
          reconnectAttempts: 0
        });
        return true;
      }
    } catch (error) {
      console.error('[ExtensionReliability] Manual reconnection failed:', error);
    }

    // Start automatic reconnection if manual failed
    this.startReconnectionAttempts();
    return false;
  }

  /**
   * Cleanup
   */
  cleanup() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    if (this.reconnectInterval) {
      clearInterval(this.reconnectInterval);
    }
    this.listeners.clear();
  }

  /**
   * Get fallback data when extension is disconnected
   */
  getFallbackData() {
    return {
      connected: false,
      version: 'Disconnected',
      totalUMatter: this.status.totalUMatter, // Keep last known value
      lastSeen: this.status.lastSeen,
      status: 'Extension disconnected - using cached data'
    };
  }
}

export const extensionReliability = new ExtensionReliabilityManager();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  extensionReliability.cleanup();
});