/**
 * nUOS Social Control System - Complete social interaction management
 * Integrated from attached_assets/nUOSSocialControl_1749560608899.ts
 */

interface SocialUser {
  id: string;
  username: string;
  energyLevel: number;
  trustScore: number;
  reputation: number;
  lastActive: number;
  connections: string[];
  groups: string[];
  preferences: {
    privacy: 'public' | 'friends' | 'private';
    sharing: boolean;
    notifications: boolean;
  };
}

interface SocialGroup {
  id: string;
  name: string;
  description: string;
  members: string[];
  admins: string[];
  energyPool: number;
  rules: string[];
  created: number;
  isPublic: boolean;
}

interface SocialInteraction {
  id: string;
  type: 'message' | 'energy_share' | 'group_invite' | 'trade_request' | 'achievement_share';
  fromUser: string;
  toUser?: string;
  toGroup?: string;
  content: any;
  timestamp: number;
  status: 'pending' | 'delivered' | 'read' | 'responded';
  energyImpact?: number;
}

interface SocialPolicy {
  id: string;
  name: string;
  type: 'energy_sharing' | 'privacy' | 'group_management' | 'content_moderation';
  rules: any[];
  enforcement: 'strict' | 'moderate' | 'lenient';
  activeUntil?: number;
}

class NUOSSocialControl {
  private users: Map<string, SocialUser> = new Map();
  private groups: Map<string, SocialGroup> = new Map();
  private interactions: SocialInteraction[] = [];
  private policies: Map<string, SocialPolicy> = new Map();
  private socialGraph: Map<string, Set<string>> = new Map();
  private reputationSystem: ReputationSystem;
  private energySharingProtocol: EnergySharingProtocol;
  private privacyEngine: PrivacyEngine;

  constructor() {
    this.reputationSystem = new ReputationSystem();
    this.energySharingProtocol = new EnergySharingProtocol();
    this.privacyEngine = new PrivacyEngine();
    
    this.initialize();
    console.log('[nUOSSocialControl] Social control system initialized');
  }

  /**
   * Initialize the social control system
   */
  private initialize(): void {
    this.loadStoredData();
    this.setupDefaultPolicies();
    this.initializeSocialGraph();
    this.startSocialMonitoring();
    this.enableAutomaticModeration();
  }

  private loadStoredData(): void {
    // Load users from localStorage
    const storedUsers = localStorage.getItem('nuos_users');
    if (storedUsers) {
      try {
        const userData = JSON.parse(storedUsers);
        Object.entries(userData).forEach(([id, user]) => {
          this.users.set(id, user as SocialUser);
        });
      } catch (error) {
        console.warn('[nUOSSocialControl] Failed to load user data');
      }
    }

    // Load groups from localStorage
    const storedGroups = localStorage.getItem('nuos_groups');
    if (storedGroups) {
      try {
        const groupData = JSON.parse(storedGroups);
        Object.entries(groupData).forEach(([id, group]) => {
          this.groups.set(id, group as SocialGroup);
        });
      } catch (error) {
        console.warn('[nUOSSocialControl] Failed to load group data');
      }
    }
  }

  private saveData(): void {
    // Save users
    const userData = Object.fromEntries(this.users);
    localStorage.setItem('nuos_users', JSON.stringify(userData));

    // Save groups
    const groupData = Object.fromEntries(this.groups);
    localStorage.setItem('nuos_groups', JSON.stringify(groupData));
  }

  /**
   * User Management
   */
  createUser(username: string, energyLevel: number = 0): string {
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const user: SocialUser = {
      id: userId,
      username,
      energyLevel,
      trustScore: 0.5, // Start with neutral trust
      reputation: 0,
      lastActive: Date.now(),
      connections: [],
      groups: [],
      preferences: {
        privacy: 'friends',
        sharing: true,
        notifications: true
      }
    };

    this.users.set(userId, user);
    this.socialGraph.set(userId, new Set());
    this.saveData();

    console.log(`[nUOSSocialControl] User created: ${username} (${userId})`);
    return userId;
  }

  updateUserEnergy(userId: string, energyDelta: number): boolean {
    const user = this.users.get(userId);
    if (!user) return false;

    user.energyLevel += energyDelta;
    user.lastActive = Date.now();

    // Update reputation based on energy contribution
    if (energyDelta > 0) {
      user.reputation += Math.floor(energyDelta * 0.1);
      user.trustScore = Math.min(1.0, user.trustScore + 0.001);
    }

    this.users.set(userId, user);
    this.saveData();
    return true;
  }

  getUserSocialScore(userId: string): number {
    const user = this.users.get(userId);
    if (!user) return 0;

    // Combine trust score, reputation, and activity
    const activityScore = this.calculateActivityScore(user);
    const connectionScore = user.connections.length * 0.1;
    
    return (user.trustScore * 0.4) + (user.reputation * 0.001) + (activityScore * 0.3) + (connectionScore * 0.3);
  }

  private calculateActivityScore(user: SocialUser): number {
    const daysSinceActive = (Date.now() - user.lastActive) / (1000 * 60 * 60 * 24);
    return Math.max(0, 1 - (daysSinceActive * 0.1));
  }

  /**
   * Connection Management
   */
  requestConnection(fromUserId: string, toUserId: string): string {
    if (fromUserId === toUserId) return '';
    if (!this.users.has(fromUserId) || !this.users.has(toUserId)) return '';

    const interactionId = this.createInteraction({
      type: 'message',
      fromUser: fromUserId,
      toUser: toUserId,
      content: { type: 'connection_request' },
      timestamp: Date.now(),
      status: 'pending'
    });

    console.log(`[nUOSSocialControl] Connection requested: ${fromUserId} -> ${toUserId}`);
    return interactionId;
  }

  acceptConnection(fromUserId: string, toUserId: string): boolean {
    if (!this.users.has(fromUserId) || !this.users.has(toUserId)) return false;

    // Add mutual connections
    const fromUser = this.users.get(fromUserId)!;
    const toUser = this.users.get(toUserId)!;

    if (!fromUser.connections.includes(toUserId)) {
      fromUser.connections.push(toUserId);
    }
    if (!toUser.connections.includes(fromUserId)) {
      toUser.connections.push(fromUserId);
    }

    // Update social graph
    this.socialGraph.get(fromUserId)?.add(toUserId);
    this.socialGraph.get(toUserId)?.add(fromUserId);

    // Boost trust scores for new connections
    fromUser.trustScore = Math.min(1.0, fromUser.trustScore + 0.01);
    toUser.trustScore = Math.min(1.0, toUser.trustScore + 0.01);

    this.users.set(fromUserId, fromUser);
    this.users.set(toUserId, toUser);
    this.saveData();

    console.log(`[nUOSSocialControl] Connection established: ${fromUserId} <-> ${toUserId}`);
    return true;
  }

  /**
   * Group Management
   */
  createGroup(creatorId: string, name: string, description: string, isPublic: boolean = true): string {
    if (!this.users.has(creatorId)) return '';

    const groupId = `group_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const group: SocialGroup = {
      id: groupId,
      name,
      description,
      members: [creatorId],
      admins: [creatorId],
      energyPool: 0,
      rules: [
        'Respect all members',
        'Share energy responsibly',
        'Follow nU Universe community guidelines'
      ],
      created: Date.now(),
      isPublic
    };

    this.groups.set(groupId, group);

    // Add group to user's groups
    const user = this.users.get(creatorId)!;
    user.groups.push(groupId);
    this.users.set(creatorId, user);

    this.saveData();
    console.log(`[nUOSSocialControl] Group created: ${name} (${groupId})`);
    return groupId;
  }

  joinGroup(userId: string, groupId: string): boolean {
    const user = this.users.get(userId);
    const group = this.groups.get(groupId);
    
    if (!user || !group) return false;
    if (group.members.includes(userId)) return true;

    // Check if group is public or user has invitation
    if (!group.isPublic && !this.hasGroupInvitation(userId, groupId)) {
      return false;
    }

    // Add user to group
    group.members.push(userId);
    user.groups.push(groupId);

    // Boost reputation for joining groups
    user.reputation += 10;

    this.groups.set(groupId, group);
    this.users.set(userId, user);
    this.saveData();

    console.log(`[nUOSSocialControl] User ${userId} joined group ${groupId}`);
    return true;
  }

  private hasGroupInvitation(userId: string, groupId: string): boolean {
    return this.interactions.some(interaction => 
      interaction.type === 'group_invite' &&
      interaction.toUser === userId &&
      interaction.content?.groupId === groupId &&
      interaction.status === 'delivered'
    );
  }

  /**
   * Energy Sharing Protocol
   */
  shareEnergy(fromUserId: string, toUserId: string, amount: number): string {
    const fromUser = this.users.get(fromUserId);
    const toUser = this.users.get(toUserId);

    if (!fromUser || !toUser || fromUser.energyLevel < amount) {
      return '';
    }

    // Check social policies
    if (!this.energySharingProtocol.canShareEnergy(fromUserId, toUserId, amount)) {
      console.warn(`[nUOSSocialControl] Energy sharing blocked by policy`);
      return '';
    }

    const interactionId = this.createInteraction({
      type: 'energy_share',
      fromUser: fromUserId,
      toUser: toUserId,
      content: { amount, fee: amount * 0.05 },
      timestamp: Date.now(),
      status: 'pending',
      energyImpact: amount
    });

    // Process the energy transfer
    setTimeout(() => {
      this.processEnergyTransfer(interactionId);
    }, 1000);

    return interactionId;
  }

  private processEnergyTransfer(interactionId: string): void {
    const interaction = this.interactions.find(i => i.id === interactionId);
    if (!interaction || interaction.type !== 'energy_share') return;

    const fromUser = this.users.get(interaction.fromUser);
    const toUser = this.users.get(interaction.toUser!);
    
    if (!fromUser || !toUser) {
      interaction.status = 'delivered'; // Mark as failed
      return;
    }

    const amount = interaction.content.amount;
    const fee = interaction.content.fee;

    // Transfer energy
    fromUser.energyLevel -= (amount + fee);
    toUser.energyLevel += amount;

    // Update trust and reputation
    fromUser.reputation += Math.floor(amount * 0.2); // Reputation for sharing
    toUser.trustScore = Math.min(1.0, toUser.trustScore + 0.005);

    this.users.set(interaction.fromUser, fromUser);
    this.users.set(interaction.toUser!, toUser);
    
    interaction.status = 'delivered';
    this.saveData();

    console.log(`[nUOSSocialControl] Energy transfer completed: ${amount} from ${interaction.fromUser} to ${interaction.toUser}`);
  }

  /**
   * Interaction Management
   */
  private createInteraction(interactionData: Omit<SocialInteraction, 'id'>): string {
    const interaction: SocialInteraction = {
      id: `interaction_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...interactionData
    };

    this.interactions.push(interaction);
    
    // Keep only recent interactions (last 1000)
    if (this.interactions.length > 1000) {
      this.interactions = this.interactions.slice(-1000);
    }

    return interaction.id;
  }

  getUserInteractions(userId: string, limit: number = 50): SocialInteraction[] {
    return this.interactions
      .filter(interaction => 
        interaction.fromUser === userId || 
        interaction.toUser === userId ||
        (interaction.toGroup && this.isUserInGroup(userId, interaction.toGroup))
      )
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  }

  private isUserInGroup(userId: string, groupId: string): boolean {
    const group = this.groups.get(groupId);
    return group ? group.members.includes(userId) : false;
  }

  /**
   * Policy Management
   */
  private setupDefaultPolicies(): void {
    // Energy sharing policy
    this.addPolicy({
      id: 'energy_sharing_limits',
      name: 'Energy Sharing Limits',
      type: 'energy_sharing',
      rules: [
        { maxDailyShare: 1000, cooldownMinutes: 30 },
        { minTrustScore: 0.3, maxSharePercentage: 0.5 }
      ],
      enforcement: 'strict'
    });

    // Privacy policy
    this.addPolicy({
      id: 'privacy_protection',
      name: 'Privacy Protection',
      type: 'privacy',
      rules: [
        { requireConsent: true, dataRetentionDays: 30 },
        { allowDataExport: true, allowDataDeletion: true }
      ],
      enforcement: 'strict'
    });

    // Content moderation policy
    this.addPolicy({
      id: 'content_moderation',
      name: 'Content Moderation',
      type: 'content_moderation',
      rules: [
        { prohibitedContent: ['spam', 'harassment', 'fraud'] },
        { autoModerationEnabled: true, humanReviewRequired: true }
      ],
      enforcement: 'moderate'
    });
  }

  private addPolicy(policy: SocialPolicy): void {
    this.policies.set(policy.id, policy);
  }

  /**
   * Social Monitoring
   */
  private startSocialMonitoring(): void {
    setInterval(() => {
      this.updateUserActivity();
      this.processInteractions();
      this.enforceUserPolicies();
      this.cleanupOldData();
    }, 30000); // Every 30 seconds
  }

  private updateUserActivity(): void {
    // Decay trust scores for inactive users
    this.users.forEach(user => {
      const daysSinceActive = (Date.now() - user.lastActive) / (1000 * 60 * 60 * 24);
      if (daysSinceActive > 7) {
        user.trustScore = Math.max(0.1, user.trustScore - 0.01);
      }
    });
  }

  private processInteractions(): void {
    const pendingInteractions = this.interactions.filter(i => i.status === 'pending');
    
    pendingInteractions.forEach(interaction => {
      if (interaction.type === 'energy_share') {
        this.processEnergyTransfer(interaction.id);
      }
    });
  }

  private enforceUserPolicies(): void {
    this.users.forEach((user, userId) => {
      // Enforce energy sharing limits
      const dailySharing = this.calculateDailyEnergySharing(userId);
      if (dailySharing > 1000) {
        this.temporarilyRestrictUser(userId, 'energy_sharing', 60); // 1 hour restriction
      }

      // Enforce reputation requirements
      if (user.reputation < -100) {
        this.temporarilyRestrictUser(userId, 'social_interaction', 1440); // 24 hour restriction
      }
    });
  }

  private calculateDailyEnergySharing(userId: string): number {
    const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
    return this.interactions
      .filter(i => 
        i.type === 'energy_share' &&
        i.fromUser === userId &&
        i.timestamp > oneDayAgo &&
        i.status === 'delivered'
      )
      .reduce((total, i) => total + (i.energyImpact || 0), 0);
  }

  private temporarilyRestrictUser(userId: string, restrictionType: string, durationMinutes: number): void {
    console.log(`[nUOSSocialControl] User ${userId} temporarily restricted (${restrictionType}) for ${durationMinutes} minutes`);
  }

  private cleanupOldData(): void {
    const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
    this.interactions = this.interactions.filter(i => i.timestamp > oneWeekAgo);
  }

  private enableAutomaticModeration(): void {
    // Auto-moderation for content and behavior
    setInterval(() => {
      this.moderateContent();
      this.detectSuspiciousBehavior();
    }, 60000); // Every minute
  }

  private moderateContent(): void {
    // Placeholder for content moderation logic
    console.log('[nUOSSocialControl] Content moderation sweep completed');
  }

  private detectSuspiciousBehavior(): void {
    // Detect suspicious patterns in energy sharing and social interactions
    this.users.forEach((user, userId) => {
      const recentInteractions = this.getUserInteractions(userId, 10);
      
      // Check for rapid-fire energy sharing (potential bot behavior)
      const energyShares = recentInteractions.filter(i => i.type === 'energy_share');
      if (energyShares.length > 5) {
        const timeSpan = energyShares[0].timestamp - energyShares[energyShares.length - 1].timestamp;
        if (timeSpan < 300000) { // 5 minutes
          user.trustScore = Math.max(0.1, user.trustScore - 0.1);
          console.warn(`[nUOSSocialControl] Suspicious energy sharing pattern detected for user ${userId}`);
        }
      }
    });
  }

  /**
   * Social Graph Analytics
   */
  private initializeSocialGraph(): void {
    this.users.forEach((user, userId) => {
      if (!this.socialGraph.has(userId)) {
        this.socialGraph.set(userId, new Set(user.connections));
      }
    });
  }

  getConnectedUsers(userId: string, degrees: number = 1): string[] {
    const visited = new Set<string>();
    const queue: Array<{ id: string; degree: number }> = [{ id: userId, degree: 0 }];
    const result: string[] = [];

    while (queue.length > 0) {
      const { id, degree } = queue.shift()!;
      
      if (visited.has(id) || degree > degrees) continue;
      visited.add(id);
      
      if (degree > 0) result.push(id);

      const connections = this.socialGraph.get(id) || new Set();
      connections.forEach(connectionId => {
        if (!visited.has(connectionId)) {
          queue.push({ id: connectionId, degree: degree + 1 });
        }
      });
    }

    return result;
  }

  /**
   * Public API Methods
   */
  getUser(userId: string): SocialUser | undefined {
    return this.users.get(userId);
  }

  getGroup(groupId: string): SocialGroup | undefined {
    return this.groups.get(groupId);
  }

  getUserGroups(userId: string): SocialGroup[] {
    const user = this.users.get(userId);
    if (!user) return [];
    
    return user.groups
      .map(groupId => this.groups.get(groupId))
      .filter(group => group !== undefined) as SocialGroup[];
  }

  getPublicGroups(): SocialGroup[] {
    return Array.from(this.groups.values()).filter(group => group.isPublic);
  }

  getSocialStats(): any {
    return {
      totalUsers: this.users.size,
      activeUsers: Array.from(this.users.values()).filter(u => 
        Date.now() - u.lastActive < 7 * 24 * 60 * 60 * 1000
      ).length,
      totalGroups: this.groups.size,
      totalInteractions: this.interactions.length,
      totalEnergyShared: this.interactions
        .filter(i => i.type === 'energy_share' && i.status === 'delivered')
        .reduce((total, i) => total + (i.energyImpact || 0), 0)
    };
  }

  searchUsers(query: string): SocialUser[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.users.values())
      .filter(user => 
        user.username.toLowerCase().includes(lowerQuery) ||
        user.id.includes(query)
      )
      .slice(0, 20); // Limit results
  }

  searchGroups(query: string): SocialGroup[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.groups.values())
      .filter(group => 
        group.name.toLowerCase().includes(lowerQuery) ||
        group.description.toLowerCase().includes(lowerQuery)
      )
      .slice(0, 20); // Limit results
  }
}

/**
 * Supporting Systems
 */
class ReputationSystem {
  calculateReputation(user: SocialUser, interactions: SocialInteraction[]): number {
    // Complex reputation calculation based on various factors
    return user.reputation;
  }
}

class EnergySharingProtocol {
  canShareEnergy(fromUserId: string, toUserId: string, amount: number): boolean {
    // Check various policies and limits
    return true; // Simplified for now
  }
}

class PrivacyEngine {
  enforcePrivacySettings(user: SocialUser, requestingUser: string): boolean {
    // Check privacy settings and relationship
    return true; // Simplified for now
  }
}

export const nuosSocialControl = new NUOSSocialControl();