import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface SimpleNuvaState {
  umatter: number;
  tru: number;
  nuva: number;
  batteryLevel: number;
  isCharging: boolean;
  lastUpdate: number;
  isConnected: boolean;
  lastConnectionTime: number;
  
  // Actions
  setUser: (user: any) => void;
  setBatteryLevel: (level: number) => void;
  setIsCharging: (charging: boolean) => void;
  addUMatter: (amount: number) => void;
  spendTrU: (amount: number) => void;
  convertUMatterToTrU: (umatterAmount: number) => void;
  convertUMatterToNUva: (umatterAmount: number) => void;
  connectWebSocket: () => void;
  disconnectWebSocket: () => void;
}

let ws: WebSocket | null = null;

export const useSimpleNuvaStore = create<SimpleNuvaState>()(
  persist(
    (set, get) => ({
      umatter: 0,
      tru: 0,
      nuva: 0,
      batteryLevel: 0.75,
      isCharging: false,
      lastUpdate: Date.now(),
      isConnected: false,
      lastConnectionTime: 0,

      setUser: (user: any) => {},
      setBatteryLevel: (level: number) => set({ batteryLevel: level }),
      setIsCharging: (charging: boolean) => set({ isCharging: charging }),
      addUMatter: (amount: number) => set(state => ({ umatter: state.umatter + amount })),
      spendTrU: (amount: number) => set(state => ({ tru: Math.max(0, state.tru - amount) })),
      convertUMatterToTrU: (umatterAmount: number) => {
        const state = get();
        if (state.umatter >= umatterAmount) {
          set({
            umatter: state.umatter - umatterAmount,
            tru: state.tru + umatterAmount
          });
        }
      },
      convertUMatterToNUva: (umatterAmount: number) => {
        const state = get();
        if (state.umatter >= umatterAmount) {
          set({
            umatter: state.umatter - umatterAmount,
            nuva: state.nuva + umatterAmount
          });
        }
      },

      connectWebSocket: () => {
        if (ws?.readyState === WebSocket.OPEN) return;

        try {
          const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
          const host = window.location.host;
          const wsUrl = `${protocol}//${host}/nu-websocket?token=${get().user?.id || 'anonymous'}`;
          
          console.log('[SimpleNuvaStore] Connecting to WebSocket:', wsUrl);
          ws = new WebSocket(wsUrl);

          ws.onopen = () => {
            console.log('[SimpleNuvaStore] WebSocket connected successfully');
            set({ isConnected: true, lastConnectionTime: Date.now() });
            
            // Send current authentic battery data to server if available
            if ('getBattery' in navigator) {
              navigator.getBattery().then(battery => {
                ws.send(JSON.stringify({
                  type: 'battery_update',
                  level: Math.round(battery.level * 100),
                  charging: battery.charging,
                  timestamp: Date.now(),
                  source: 'REAL_BATTERY_API'
                }));
                
                // Set up real-time battery monitoring
                battery.addEventListener('levelchange', () => {
                  ws.send(JSON.stringify({
                    type: 'battery_update',
                    level: Math.round(battery.level * 100),
                    charging: battery.charging,
                    timestamp: Date.now(),
                    source: 'REAL_BATTERY_API'
                  }));
                });
                
                battery.addEventListener('chargingchange', () => {
                  ws.send(JSON.stringify({
                    type: 'battery_update',
                    level: Math.round(battery.level * 100),
                    charging: battery.charging,
                    timestamp: Date.now(),
                    source: 'REAL_BATTERY_API'
                  }));
                });
              }).catch(error => {
                console.log('[SimpleNuvaStore] Battery API not available:', error);
              });
            }
          };

          ws.onmessage = (event) => {
            try {
              const data = JSON.parse(event.data);
              console.log('[SimpleNuvaStore] WebSocket message received:', data.type);
              
              if (data.type === 'connection_established') {
                console.log('[SimpleNuvaStore] Connection confirmed by server');
                set({ isConnected: true, lastConnectionTime: Date.now() });
              } else if (data.type === 'real_time_update') {
                // Only update if we have authentic battery data from server
                const updates: any = {
                  umatter: state.umatter + (data.umatter || 0),
                  lastUpdate: data.timestamp,
                  isConnected: true
                };
                
                // Only update battery if it's authentic data (not null/undefined)
                if (data.battery !== null && data.battery !== undefined) {
                  updates.batteryLevel = data.battery / 100;
                  console.log('[SimpleNuvaStore] Received authentic battery update:', data.battery + '%');
                }
                
                if (data.charging !== null && data.charging !== undefined) {
                  updates.isCharging = data.charging;
                }
                
                set(state => ({ ...state, ...updates }));
              } else if (data.type === 'battery_sync') {
                // Handle authentic battery sync from other clients
                set(state => ({
                  ...state,
                  batteryLevel: data.level / 100,
                  isCharging: data.charging,
                  lastUpdate: data.timestamp
                }));
                console.log('[SimpleNuvaStore] Battery synced from authentic device:', data.level + '%');
              }
            } catch (error) {
              console.error('[SimpleNuvaStore] WebSocket message parse error:', error);
            }
          };

          ws.onclose = (event) => {
            console.log('[SimpleNuvaStore] WebSocket disconnected:', { code: event.code, reason: event.reason });
            set({ isConnected: false });
            
            // Intelligent reconnection - only if not intentionally closed
            if (event.code !== 1000) {
              console.log('[SimpleNuvaStore] Attempting reconnection in 5 seconds...');
              setTimeout(() => {
                if (!get().isConnected) {
                  get().connectWebSocket();
                }
              }, 5000);
            }
          };

          ws.onerror = (error) => {
            console.error('[SimpleNuvaStore] WebSocket error:', error);
            set({ isConnected: false });
          };
        } catch (error) {
          console.error('[SimpleNuvaStore] WebSocket connection failed:', error);
          set({ isConnected: false });
        }
      },

      disconnectWebSocket: () => {
        if (ws) {
          ws.close(1000, 'Intentional disconnect');
          ws = null;
        }
        set({ isConnected: false });
      }
    }),
    {
      name: 'simple-nuva-energy-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);