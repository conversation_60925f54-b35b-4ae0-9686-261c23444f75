import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface EnergyMetrics {
  neuralPowerWatts: number;
  batteryLevel: number;
  isCharging: boolean;
  joyLevel: number;
  stressLevel: number;
  currentUMatter: number;
  currentTrU: number;
  currentNUva: number;
  dailyDataMB: number;
  energyEfficiency: number;
}

interface EnergyPackage {
  id: string;
  type: 'sleep' | 'active' | 'joy' | 'mixed';
  dataMB: number;
  umatterRequired: number;
  truValue: number;
  nuvaValue: number;
  usdValue: number;
  duration: string;
  created: Date;
}

interface DeviceConnection {
  id: string;
  name: string;
  type: 'smartphone' | 'bluetooth' | 'iot' | 'wearable';
  batteryLevel?: number;
  isConnected: boolean;
  lastSync: Date;
}

interface NuvaEnergyState {
  // Energy metrics
  metrics: EnergyMetrics;

  // Energy packages
  packages: EnergyPackage[];

  // Connected devices
  devices: DeviceConnection[];

  // Bot status
  bots: {
    gearTick: { active: boolean; lastUpdate: Date };
    drain: { active: boolean; totalTracked: number };
    trade: { active: boolean; autoTrade: boolean };
  };

  // Global energy network stats
  networkStats: {
    totalUsers: number;
    dailyUMatter: number;
    marketValue: number;
    energyEfficiencyGlobal: number;
  };

  // WebSocket connection state
  isConnected: boolean;
  lastConnectionTime: number;

  // Actions
  updateMetrics: (metrics: Partial<EnergyMetrics>) => void;
  addPackage: (pkg: EnergyPackage) => void;
  removePackage: (id: string) => void;
  connectDevice: (device: DeviceConnection) => void;
  disconnectDevice: (id: string) => void;
  updateBotStatus: (bot: keyof NuvaEnergyState['bots'], status: Partial<NuvaEnergyState['bots'][keyof NuvaEnergyState['bots']]>) => void;
  updateNetworkStats: (stats: Partial<NuvaEnergyState['networkStats']>) => void;

  // Energy conversion functions
  calculateEnergyConversion: (batteryLevel: number, isActive: boolean, joyLevel: number) => {
    umatter: number;
    tru: number;
    nuva: number;
    dataMB: number;
    dataValue: number;
  };

  // Real-time synchronization
  startRealTimeSync: () => void;
  stopRealTimeSync: () => void;
  setUser: (user: any) => void;
  setBatteryLevel: (level: number) => void;
  setIsCharging: (charging: boolean) => void;
  addUMatter: (amount: number) => void;
  spendTrU: (amount: number) => void;
  convertUMatterToTrU: (umatterAmount: number) => void;
  convertUMatterToNUva: (umatterAmount: number) => void;
  connectWebSocket: () => void;
  disconnectWebSocket: () => void;
}

let ws: WebSocket | null = null;

export const useNuvaStore = create<NuvaEnergyState>()(
  persist(
    (set, get) => ({
      metrics: {
        neuralPowerWatts: 20,
        batteryLevel: 85,
        isCharging: false,
        joyLevel: 0.65,
        stressLevel: 0.3,
        currentUMatter: 12.5,
        currentTrU: 1.25,
        currentNUva: 12.5,
        dailyDataMB: 28,
        energyEfficiency: 0.74
      },

      packages: [],
      devices: [],

      bots: {
        gearTick: { active: true, lastUpdate: new Date() },
        drain: { active: true, totalTracked: 0 },
        trade: { active: false, autoTrade: false }
      },

      networkStats: {
        totalUsers: 5_000_000,
        dailyUMatter: 3_500_000_000,
        marketValue: 98_000_000,
        energyEfficiencyGlobal: 0.67
      },

      isConnected: false,
      lastConnectionTime: 0,

      updateMetrics: (newMetrics) => set((state) => ({
        metrics: { ...state.metrics, ...newMetrics }
      })),

      addPackage: (pkg) => set((state) => ({
        packages: [...state.packages, pkg]
      })),

      removePackage: (id) => set((state) => ({
        packages: state.packages.filter(p => p.id !== id)
      })),

      connectDevice: (device) => set((state) => ({
        devices: [...state.devices.filter(d => d.id !== device.id), device]
      })),

      disconnectDevice: (id) => set((state) => ({
        devices: state.devices.filter(d => d.id !== id)
      })),

      updateBotStatus: (bot, status) => set((state) => ({
        bots: {
          ...state.bots,
          [bot]: { ...state.bots[bot], ...status }
        }
      })),

      updateNetworkStats: (stats) => set((state) => ({
        networkStats: { ...state.networkStats, ...stats }
      })),

      calculateEnergyConversion: (batteryLevel, isActive, joyLevel) => {
        // Scientific energy conversion based on 20W brain power
        const baseUM = isActive ? 0.2 : 0.5; // Active vs sleep UM generation
        const joyBoost = 1 + (joyLevel * 0.15); // Up to 15% boost for high joy
        const stressReduction = batteryLevel < 20 ? 0.6 : 1; // Low battery = stress
        const umatter = baseUM * joyBoost * stressReduction;

        // Token conversions
        const tru = umatter * 0.1; // 1 UM = 0.1 trU
        const nuva = umatter * 1; // 1 UM = 1 nUva

        // Data generation
        const dataMB = isActive ? 20 : 8; // MB based on activity
        const dataValue = dataMB * 0.0007; // $0.0007/MB energy-based pricing

        return { umatter, tru, nuva, dataMB, dataValue };
      },

      startRealTimeSync: () => {
        // Start real-time energy monitoring
        const interval = setInterval(() => {
          const state = get();
          const { metrics } = state;

          // Simulate real-time battery changes
          const batteryChange = metrics.isCharging ? 
            Math.random() * 2 : -Math.random() * 0.5;
          const newBatteryLevel = Math.max(0, Math.min(100, 
            metrics.batteryLevel + batteryChange));

          // Simulate neural activity fluctuations
          const neuralChange = (Math.random() - 0.5) * 0.1;
          const newNeuralPower = Math.max(18, Math.min(25, 
            metrics.neuralPowerWatts + neuralChange));

          // Calculate new energy conversion
          const conversion = state.calculateEnergyConversion(
            newBatteryLevel, 
            newNeuralPower > 21, 
            metrics.joyLevel
          );

          // Update metrics
          state.updateMetrics({
            batteryLevel: newBatteryLevel,
            neuralPowerWatts: newNeuralPower,
            currentUMatter: metrics.currentUMatter + conversion.umatter * 0.01,
            currentTrU: metrics.currentTrU + conversion.tru * 0.01,
            currentNUva: metrics.currentNUva + conversion.nuva * 0.01,
            dailyDataMB: metrics.dailyDataMB + conversion.dataMB * 0.01
          });

          // Update bot statuses
          state.updateBotStatus('gearTick', { lastUpdate: new Date() });
          state.updateBotStatus('drain', { 
            totalTracked: state.bots.drain.totalTracked + conversion.dataMB * 0.01 
          });

        }, 5000); // Update every 5 seconds

        // Store interval ID for cleanup
        (window as any).nuvaEnergyInterval = interval;
      },

      stopRealTimeSync: () => {
        if ((window as any).nuvaEnergyInterval) {
          clearInterval((window as any).nuvaEnergyInterval);
          delete (window as any).nuvaEnergyInterval;
        }
      },
      setUser: (user: any) => {},
      setBatteryLevel: (level: number) => {},
      setIsCharging: (charging: boolean) => {},
      addUMatter: (amount: number) => {},
      spendTrU: (amount: number) => {},
      convertUMatterToTrU: (umatterAmount: number) => {
        const state = get();
        if (state.umatter >= umatterAmount) {
          set({
            umatter: state.umatter - umatterAmount,
            tru: state.tru + umatterAmount
          });
        }
      },
      convertUMatterToNUva: (umatterAmount: number) => {
        const state = get();
        if (state.umatter >= umatterAmount) {
          set({
            umatter: state.umatter - umatterAmount,
            nuva: state.nuva + umatterAmount
          });
        }
      },

      connectWebSocket: () => {
        // Close existing connection if any
        if (ws) {
          ws.close();
          ws = null;
        }

        try {
          const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
          const host = window.location.host;
          const wsUrl = `${protocol}//${host}/nu-websocket?token=${get().user?.id || 'anonymous'}`;
          
          console.log('[nUStore] Connecting to WebSocket:', wsUrl);
          ws = new WebSocket(wsUrl);

          ws.onopen = () => {
            console.log('[nUStore] WebSocket connected successfully');
            set({ isConnected: true, lastConnectionTime: Date.now() });
            
            // Subscribe to energy updates
            ws?.send(JSON.stringify({
              type: 'subscribe',
              payload: { channel: 'energy-updates' }
            }));

            // Subscribe to nUmentum feed
            ws?.send(JSON.stringify({
              type: 'subscribe', 
              payload: { channel: 'numentum-feed' }
            }));
          };
        } catch (error) {
          console.error('[nUStore] WebSocket connection failed:', error);
          set({ isConnected: false });
        }

        ws.onmessage = (event) => {
          const message = JSON.parse(event.data);

          switch (message.type) {
            case 'energy-pool-update':
              // Update energy pool data in state
              break;

            case 'numentum-update':
              // Handle nUmentum updates
              set(state => ({
                ...state,
                numentumMultiplier: message.data.multiplier || state.numentumMultiplier
              }));
              break;

            case 'battery-sync':
              set(state => ({
                ...state,
                batteryLevel: message.data.level,
                isCharging: message.data.charging
              }));
              break;

            case 'umatter-earned':
              set(state => ({
                ...state,
                umatter: state.umatter + message.data.amount
              }));
              break;
          }
        };

        ws.onclose = () => {
          console.log('WebSocket disconnected');
          set({ isConnected: false });
          // Attempt reconnection after 5 seconds
          setTimeout(() => get().connectWebSocket(), 5000);
        };

        ws.onerror = (error) => {
          console.error('WebSocket error:', error);
          set({ isConnected: false });
        };
      },

      disconnectWebSocket: () => {
        if (ws) {
          ws.close();
          ws = null;
        }
        set({ isConnected: false });
      }
    }),
    {
      name: 'nuva-energy-storage',
      partialize: (state) => ({
        metrics: state.metrics,
        packages: state.packages,
        devices: state.devices,
        bots: state.bots,
        networkStats: state.networkStats
      })
    }
  )
);