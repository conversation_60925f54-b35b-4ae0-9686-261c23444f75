/**
 * IPFS Storage - Decentralized storage for nU Universe
 * Integrated from attached_assets/IPFSStorage_1749560635069.ts
 */

interface StorageNode {
  id: string;
  multiaddr: string;
  isActive: boolean;
  capacity: number;
  used: number;
  latency: number;
  trustScore: number;
}

interface StoredData {
  id: string;
  hash: string;
  category: string;
  size: number;
  timestamp: number;
  replicas: number;
  encrypted: boolean;
  metadata: any;
}

interface EnergyPoolData {
  poolId: string;
  participants: string[];
  totalEnergy: number;
  distribution: Record<string, number>;
  rules: any;
  created: number;
  lastUpdate: number;
}

interface EnergyTransferData {
  transferId: string;
  fromUser: string;
  toUser: string;
  amount: number;
  fee: number;
  status: 'pending' | 'completed' | 'failed';
  timestamp: number;
  signature: string;
}

class IPFSStorage {
  private nodeId: string;
  private initialized: boolean = false;
  private fallbackStorage: boolean = false;
  private storageNodes: Map<string, StorageNode> = new Map();
  private localStorage: Map<string, StoredData> = new Map();
  private encryptionKey: CryptoKey | null = null;
  private replicationFactor: number = 3;

  constructor() {
    this.nodeId = this.generateDeviceNodeId();
    // Don't auto-initialize to prevent duplicate instances
  }

  /**
   * Initialize IPFS storage with graceful fallback
   */
  public async initialize(): Promise<boolean> {
    // Prevent duplicate initialization
    if (this.initialized) {
      return true;
    }

    console.log('[IPFSStorage] Initializing decentralized storage...');
    
    try {
      // Try to initialize IPFS connection
      const ipfsSuccess = await this.tryIPFSInit();
      
      if (!ipfsSuccess) {
        this.fallbackStorage = true;
      }
      
      // Initialize encryption
      await this.initializeEncryption();
      
      // Setup storage nodes
      this.initializeStorageNodes();
      
      // Load existing data
      this.loadLocalStorage();
      
      this.initialized = true;
      console.log(`[IPFSStorage] Storage initialized (${this.fallbackStorage ? 'Local' : 'IPFS'} mode)`);
      
      return true;
    } catch (error) {
      console.error('[IPFSStorage] Initialization failed:', error);
      this.fallbackStorage = true;
      this.initialized = true;
      return false;
    }
  }

  private async tryIPFSInit(): Promise<boolean> {
    // In a real implementation, this would connect to IPFS
    // For now, we'll simulate IPFS availability
    return new Promise(resolve => {
      setTimeout(() => {
        // Simulate IPFS connection attempt
        const hasIPFS = Math.random() > 0.7; // 30% chance of IPFS being available
        resolve(hasIPFS);
      }, 1000);
    });
  }

  private async initializeEncryption(): Promise<void> {
    try {
      // Generate or retrieve encryption key
      const savedKey = localStorage.getItem('ipfs_encryption_key');
      
      if (savedKey) {
        // Import existing key
        const keyData = JSON.parse(savedKey);
        this.encryptionKey = await crypto.subtle.importKey(
          'raw',
          new Uint8Array(keyData),
          'AES-GCM',
          false,
          ['encrypt', 'decrypt']
        );
      } else {
        // Generate new key
        this.encryptionKey = await crypto.subtle.generateKey(
          { name: 'AES-GCM', length: 256 },
          true,
          ['encrypt', 'decrypt']
        );
        
        // Export and save key
        const exportedKey = await crypto.subtle.exportKey('raw', this.encryptionKey);
        localStorage.setItem('ipfs_encryption_key', JSON.stringify(Array.from(new Uint8Array(exportedKey))));
      }
      
      console.log('[IPFSStorage] Encryption initialized');
    } catch (error) {
      console.warn('[IPFSStorage] Encryption setup failed, using plaintext');
    }
  }

  private initializeStorageNodes(): void {
    // Initialize network of storage nodes
    const nodeConfigs = [
      { id: 'node_primary', multiaddr: '/ip4/127.0.0.1/tcp/4001', capacity: 1000 },
      { id: 'node_backup', multiaddr: '/ip4/127.0.0.1/tcp/4002', capacity: 500 },
      { id: 'node_distributed', multiaddr: '/ip4/127.0.0.1/tcp/4003', capacity: 750 }
    ];

    nodeConfigs.forEach(config => {
      const node: StorageNode = {
        id: config.id,
        multiaddr: config.multiaddr,
        isActive: Math.random() > 0.2, // 80% uptime
        capacity: config.capacity,
        used: Math.random() * config.capacity * 0.5,
        latency: 50 + Math.random() * 100,
        trustScore: 0.8 + Math.random() * 0.2
      };
      
      this.storageNodes.set(node.id, node);
    });
  }

  private loadLocalStorage(): void {
    const stored = localStorage.getItem('ipfs_local_data');
    if (stored) {
      try {
        const data = JSON.parse(stored);
        Object.entries(data).forEach(([key, value]) => {
          this.localStorage.set(key, value as StoredData);
        });
      } catch (error) {
        console.warn('[IPFSStorage] Failed to load local storage data');
      }
    }
  }

  private saveLocalStorage(): void {
    const data = Object.fromEntries(this.localStorage);
    localStorage.setItem('ipfs_local_data', JSON.stringify(data));
  }

  /**
   * Generate unique device node ID from hardware characteristics
   */
  private generateDeviceNodeId(): string {
    const factors = [
      navigator.userAgent,
      navigator.hardwareConcurrency || 4,
      screen.width,
      screen.height,
      navigator.language,
      new Date().getTimezoneOffset()
    ];
    
    const combined = factors.join('|');
    return `node_${this.simpleHash(combined)}`;
  }

  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  /**
   * Generate device signature for data authenticity
   */
  private generateDeviceSignature(): string {
    const timestamp = Date.now();
    const nodeData = `${this.nodeId}:${timestamp}`;
    return `sig_${this.simpleHash(nodeData)}`;
  }

  /**
   * Core storage operations
   */
  public async storeData(key: string, data: any, category: string = 'general'): Promise<string> {
    if (!this.initialized) {
      throw new Error('IPFSStorage not initialized');
    }

    try {
      // Prepare data for storage
      const dataStr = JSON.stringify(data);
      const encrypted = this.encryptionKey ? await this.encryptData(dataStr) : dataStr;
      
      // Generate content hash
      const hash = this.generateContentHash(encrypted);
      
      // Create storage record
      const storedData: StoredData = {
        id: key,
        hash,
        category,
        size: dataStr.length,
        timestamp: Date.now(),
        replicas: this.fallbackStorage ? 1 : this.replicationFactor,
        encrypted: !!this.encryptionKey,
        metadata: {
          nodeId: this.nodeId,
          signature: this.generateDeviceSignature()
        }
      };

      if (this.fallbackStorage) {
        // Store locally
        this.localStorage.set(key, storedData);
        localStorage.setItem(`ipfs_data_${key}`, encrypted);
        this.saveLocalStorage();
      } else {
        // Store on IPFS network
        await this.storeOnIPFS(key, encrypted, storedData);
      }

      console.log(`[IPFSStorage] Stored ${key} (${storedData.size} bytes) with hash ${hash}`);
      return hash;
    } catch (error) {
      console.error('[IPFSStorage] Store failed:', error);
      throw error;
    }
  }

  public async retrieveData(key: string, category: string = 'general'): Promise<any> {
    if (!this.initialized) {
      throw new Error('IPFSStorage not initialized');
    }

    try {
      let encrypted: string;
      
      if (this.fallbackStorage) {
        // Retrieve from local storage
        encrypted = localStorage.getItem(`ipfs_data_${key}`) || '';
        if (!encrypted) {
          throw new Error(`Data not found for key: ${key}`);
        }
      } else {
        // Retrieve from IPFS
        encrypted = await this.retrieveFromIPFS(key);
      }

      // Decrypt if needed
      const dataStr = this.encryptionKey ? await this.decryptData(encrypted) : encrypted;
      
      console.log(`[IPFSStorage] Retrieved ${key} from ${this.fallbackStorage ? 'local' : 'IPFS'} storage`);
      return JSON.parse(dataStr);
    } catch (error) {
      console.error('[IPFSStorage] Retrieve failed:', error);
      return null;
    }
  }

  /**
   * Energy-specific storage methods
   */
  public async storeEnergyPool(poolId: string, poolData: EnergyPoolData): Promise<string> {
    return this.storeData(`energy_pool_${poolId}`, poolData, 'energy_pools');
  }

  public async retrieveEnergyPool(poolId: string): Promise<EnergyPoolData | null> {
    return this.retrieveData(`energy_pool_${poolId}`, 'energy_pools');
  }

  public async storeEnergyTransfer(transferId: string, transferData: EnergyTransferData): Promise<string> {
    return this.storeData(`energy_transfer_${transferId}`, transferData, 'energy_transfers');
  }

  public async retrieveEnergyTransfer(transferId: string): Promise<EnergyTransferData | null> {
    return this.retrieveData(`energy_transfer_${transferId}`, 'energy_transfers');
  }

  public async storeBalances(balances: any): Promise<string> {
    return this.storeData(`balances_${this.nodeId}`, balances, 'user_balances');
  }

  public async retrieveBalances(): Promise<any> {
    return this.retrieveData(`balances_${this.nodeId}`, 'user_balances');
  }

  public async storeCommunityPools(pools: any[]): Promise<string> {
    return this.storeData('community_pools', pools, 'community');
  }

  public async retrieveCommunityPools(): Promise<any[]> {
    const pools = await this.retrieveData('community_pools', 'community');
    return pools || [];
  }

  public async storeRelationshipPools(pools: any[]): Promise<string> {
    return this.storeData('relationship_pools', pools, 'relationships');
  }

  public async retrieveRelationshipPools(): Promise<any[]> {
    const pools = await this.retrieveData('relationship_pools', 'relationships');
    return pools || [];
  }

  /**
   * Encryption/Decryption
   */
  private async encryptData(data: string): Promise<string> {
    if (!this.encryptionKey) return data;

    try {
      const encoder = new TextEncoder();
      const dataBytes = encoder.encode(data);
      const iv = crypto.getRandomValues(new Uint8Array(12));
      
      const encrypted = await crypto.subtle.encrypt(
        { name: 'AES-GCM', iv },
        this.encryptionKey,
        dataBytes
      );

      // Combine IV and encrypted data
      const combined = new Uint8Array(iv.length + encrypted.byteLength);
      combined.set(iv);
      combined.set(new Uint8Array(encrypted), iv.length);
      
      return btoa(String.fromCharCode(...combined));
    } catch (error) {
      console.error('[IPFSStorage] Encryption failed:', error);
      return data;
    }
  }

  private async decryptData(encryptedData: string): Promise<string> {
    if (!this.encryptionKey) return encryptedData;

    try {
      const combined = new Uint8Array(atob(encryptedData).split('').map(c => c.charCodeAt(0)));
      const iv = combined.slice(0, 12);
      const encrypted = combined.slice(12);

      const decrypted = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv },
        this.encryptionKey,
        encrypted
      );

      const decoder = new TextDecoder();
      return decoder.decode(decrypted);
    } catch (error) {
      console.error('[IPFSStorage] Decryption failed:', error);
      return encryptedData;
    }
  }

  /**
   * IPFS Network Operations
   */
  private async storeOnIPFS(key: string, data: string, metadata: StoredData): Promise<void> {
    // Simulate IPFS storage across multiple nodes
    const availableNodes = Array.from(this.storageNodes.values())
      .filter(node => node.isActive && (node.capacity - node.used) > data.length)
      .sort((a, b) => a.latency - b.latency);

    const replicationCount = Math.min(this.replicationFactor, availableNodes.length);
    
    for (let i = 0; i < replicationCount; i++) {
      const node = availableNodes[i];
      
      // Simulate storage on node
      await new Promise(resolve => setTimeout(resolve, node.latency));
      
      node.used += data.length;
      console.log(`[IPFSStorage] Replicated to node ${node.id}`);
    }

    this.localStorage.set(key, metadata);
    this.saveLocalStorage();
  }

  private async retrieveFromIPFS(key: string): Promise<string> {
    const metadata = this.localStorage.get(key);
    if (!metadata) {
      throw new Error(`Metadata not found for key: ${key}`);
    }

    // Find fastest available node with the data
    const availableNodes = Array.from(this.storageNodes.values())
      .filter(node => node.isActive)
      .sort((a, b) => a.latency - b.latency);

    if (availableNodes.length === 0) {
      throw new Error('No storage nodes available');
    }

    const fastestNode = availableNodes[0];
    
    // Simulate retrieval
    await new Promise(resolve => setTimeout(resolve, fastestNode.latency));
    
    // Return simulated data
    return localStorage.getItem(`ipfs_data_${key}`) || '';
  }

  /**
   * Utility methods
   */
  private generateContentHash(data: string): string {
    return `Qm${this.simpleHash(data)}${this.simpleHash(data.length.toString())}`;
  }

  public async listStoredData(category: string): Promise<string[]> {
    return Array.from(this.localStorage.values())
      .filter(data => data.category === category)
      .map(data => data.id);
  }

  public async clearAllData(): Promise<boolean> {
    try {
      this.localStorage.clear();
      
      // Clear localStorage items
      Object.keys(localStorage).forEach(key => {
        if (key.startsWith('ipfs_data_')) {
          localStorage.removeItem(key);
        }
      });
      
      this.saveLocalStorage();
      console.log('[IPFSStorage] All data cleared');
      return true;
    } catch (error) {
      console.error('[IPFSStorage] Clear failed:', error);
      return false;
    }
  }

  public async getStorageStats(): Promise<any> {
    const totalSize = Array.from(this.localStorage.values())
      .reduce((sum, data) => sum + data.size, 0);
    
    const categoryStats = Array.from(this.localStorage.values())
      .reduce((stats, data) => {
        stats[data.category] = (stats[data.category] || 0) + 1;
        return stats;
      }, {} as Record<string, number>);

    return {
      nodeId: this.nodeId,
      initialized: this.initialized,
      fallbackMode: this.fallbackStorage,
      totalItems: this.localStorage.size,
      totalSize,
      categoryStats,
      storageNodes: this.storageNodes.size,
      activeNodes: Array.from(this.storageNodes.values()).filter(n => n.isActive).length,
      replicationFactor: this.replicationFactor,
      encryptionEnabled: !!this.encryptionKey
    };
  }

  public isReady(): boolean {
    return this.initialized;
  }

  public getNodeId(): string {
    return this.nodeId;
  }

  public getStorageNodes(): StorageNode[] {
    return Array.from(this.storageNodes.values());
  }

  public getLocalDataCount(): number {
    return this.localStorage.size;
  }
}

export const ipfsStorage = new IPFSStorage();