/**
 * WebSocket Manager - Stable connection management for nU Universe
 * Handles connection, reconnection, and message routing
 */

interface WebSocketMessage {
  type: string;
  timestamp: number;
  [key: string]: any;
}

class WebSocketManager {
  private ws: WebSocket | null = null;
  private isConnecting = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private listeners: Set<(message: WebSocketMessage) => void> = new Set();
  private connectionState: 'disconnected' | 'connecting' | 'connected' = 'disconnected';

  constructor() {
    this.connect();
  }

  private getWebSocketUrl(): string {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    return `${protocol}//${host}/nu-websocket`;
  }

  async connect(): Promise<void> {
    if (this.isConnecting || this.connectionState === 'connected') {
      return;
    }

    this.isConnecting = true;
    this.connectionState = 'connecting';
    
    try {
      // Clean up existing connection
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }

      const wsUrl = this.getWebSocketUrl();
      console.log('[WebSocketManager] Connecting to:', wsUrl);
      
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('[WebSocketManager] Connected successfully');
        this.connectionState = 'connected';
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        
        // Clear any pending reconnection
        if (this.reconnectTimeout) {
          clearTimeout(this.reconnectTimeout);
          this.reconnectTimeout = null;
        }

        this.notifyListeners({
          type: 'connection_status',
          timestamp: Date.now(),
          status: 'connected'
        });
      };

      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          this.notifyListeners(message);
        } catch (error) {
          console.error('[WebSocketManager] Message parse error:', error);
        }
      };

      this.ws.onclose = (event) => {
        console.log('[WebSocketManager] Connection closed:', { 
          code: event.code, 
          reason: event.reason,
          wasClean: event.wasClean 
        });
        
        this.connectionState = 'disconnected';
        this.isConnecting = false;
        this.ws = null;

        this.notifyListeners({
          type: 'connection_status',
          timestamp: Date.now(),
          status: 'disconnected'
        });

        // Attempt reconnection if not intentionally closed
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
          this.scheduleReconnect();
        }
      };

      this.ws.onerror = (error) => {
        console.error('[WebSocketManager] Connection error:', error);
        this.connectionState = 'disconnected';
        this.isConnecting = false;
      };

    } catch (error) {
      console.error('[WebSocketManager] Connection failed:', error);
      this.connectionState = 'disconnected';
      this.isConnecting = false;
      this.scheduleReconnect();
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    this.reconnectAttempts++;
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    
    console.log(`[WebSocketManager] Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    this.reconnectTimeout = setTimeout(() => {
      this.connect();
    }, delay);
  }

  private notifyListeners(message: WebSocketMessage): void {
    this.listeners.forEach(listener => {
      try {
        listener(message);
      } catch (error) {
        console.error('[WebSocketManager] Listener error:', error);
      }
    });
  }

  send(message: any): boolean {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error('[WebSocketManager] Send error:', error);
        return false;
      }
    }
    return false;
  }

  subscribe(listener: (message: WebSocketMessage) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  getConnectionState(): string {
    return this.connectionState;
  }

  isConnected(): boolean {
    return this.connectionState === 'connected';
  }

  disconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.ws) {
      this.ws.close(1000, 'Intentional disconnect');
      this.ws = null;
    }

    this.connectionState = 'disconnected';
    this.isConnecting = false;
    this.reconnectAttempts = this.maxReconnectAttempts; // Prevent further reconnection
  }
}

export const webSocketManager = new WebSocketManager();