/**
 * SpUnder Integration - Connects original and advanced SpUnder systems
 */

import { spUnderBot } from './spunder-bot';
import { advancedSpUnderBot } from './advanced-spunder-bot';

class SpUnderIntegration {
  private initialized: boolean = false;

  constructor() {
    this.initialize();
  }

  private async initialize(): Promise<void> {
    if (this.initialized) return;

    console.log('[SpUnderIntegration] Connecting original and advanced SpUnder systems...');

    // Connect the systems to work together
    this.connectSystems();
    
    // Start autonomous operations
    this.startAutonomousOperations();

    this.initialized = true;
    console.log('[SpUnderIntegration] SpUnder integration complete');
  }

  private connectSystems(): void {
    // Original SpUnder handles real-time diagnostics and repairs
    // Advanced SpUnder handles architecture and development tasks
    
    // When original SpUnder detects issues, escalate complex ones to advanced system
    const originalGetSystemStatus = spUnderBot.getSystemStatus.bind(spUnderBot);
    spUnderBot.getSystemStatus = () => {
      const status = originalGetSystemStatus();
      
      // If there are critical issues, create advanced tasks
      if (status.systemIssues > 3) {
        this.escalateToAdvancedSystem(status);
      }
      
      return status;
    };
  }

  private async escalateToAdvancedSystem(status: any): Promise<void> {
    console.log('[SpUnderIntegration] Escalating complex issues to Advanced SpUnder...');
    
    // Create architectural rebuild task for complex issues
    await advancedSpUnderBot.createCustomTask(
      'System Architecture Analysis and Rebuild',
      [
        'Analyze current system architecture',
        'Identify structural issues',
        'Implement architectural improvements',
        'Optimize system performance'
      ],
      'architecture_rebuild'
    );
  }

  private startAutonomousOperations(): void {
    // Advanced SpUnder suggests improvements, original SpUnder implements them
    setInterval(async () => {
      try {
        const suggestions = await advancedSpUnderBot.suggestImprovements();
        
        if (suggestions.length > 0) {
          console.log(`[SpUnderIntegration] Processing ${suggestions.length} improvement suggestions`);
          
          // Create improvement tasks
          for (const suggestion of suggestions.slice(0, 2)) { // Limit to 2 at a time
            await advancedSpUnderBot.createCustomTask(
              `Implement: ${suggestion}`,
              [suggestion, 'Test implementation', 'Deploy changes'],
              'ui_enhancement'
            );
          }
        }
      } catch (error) {
        console.error('[SpUnderIntegration] Autonomous operation failed:', error);
      }
    }, 600000); // Every 10 minutes
  }

  public getIntegratedStatus(): any {
    const originalStatus = spUnderBot.getSystemStatus();
    const advancedStatus = advancedSpUnderBot.getSystemStatus();
    
    return {
      original: originalStatus,
      advanced: advancedStatus,
      integrated: {
        totalTasks: originalStatus.tasksProcessed + advancedStatus.activeTasks,
        totalIssues: originalStatus.systemIssues,
        autonomousMode: advancedStatus.autonomousMode,
        learningActive: advancedStatus.isLearning
      }
    };
  }

  public async requestIntegratedFeature(description: string): Promise<void> {
    // Use advanced system for complex features
    await advancedSpUnderBot.requestFeature(description);
    
    // Use original system for immediate fixes
    await spUnderBot.forceSystemRepair();
  }
}

export const spUnderIntegration = new SpUnderIntegration();