/**
 * Optimized API Client with Caching and Batch Requests
 * Reduces API calls and improves performance
 */

import { performanceOptimizer } from './performance-optimizer';

interface ApiConfig {
  baseUrl?: string;
  timeout?: number;
  retryAttempts?: number;
  cacheTimeout?: number;
}

class OptimizedApiClient {
  private config: Required<ApiConfig>;
  private abortControllers = new Map<string, AbortController>();

  constructor(config: ApiConfig = {}) {
    this.config = {
      baseUrl: '',
      timeout: 10000,
      retryAttempts: 3,
      cacheTimeout: 30000,
      ...config
    };
  }

  /**
   * Cached GET request with automatic retry
   */
  async get<T>(endpoint: string, options: { 
    cache?: boolean; 
    cacheTimeout?: number;
    priority?: 'high' | 'normal' | 'low';
  } = {}): Promise<T> {
    const { cache = true, cacheTimeout = this.config.cacheTimeout, priority } = options;
    const url = `${this.config.baseUrl}${endpoint}`;
    const cacheKey = `GET:${url}`;

    // Cancel previous request if exists
    this.cancelRequest(cacheKey);

    // Use cached data if available and cache is enabled
    if (cache) {
      try {
        return await performanceOptimizer.cachedFetch<T>(url, cacheTimeout);
      } catch (error) {
        // Fall through to regular fetch with retry
      }
    }

    return this.fetchWithRetry<T>(url, {
      method: 'GET',
      priority: priority === 'normal' ? 'high' : priority,
      cacheKey
    });
  }

  /**
   * POST request with retry logic
   */
  async post<T>(endpoint: string, data: any, options: {
    priority?: 'high' | 'normal' | 'low';
  } = {}): Promise<T> {
    const { priority } = options;
    const url = `${this.config.baseUrl}${endpoint}`;
    const cacheKey = `POST:${url}:${Date.now()}`;

    return this.fetchWithRetry<T>(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
      priority: priority === 'normal' ? 'high' : priority,
      cacheKey
    });
  }

  /**
   * Batch multiple GET requests
   */
  async batchGet<T>(endpoints: string[]): Promise<T[]> {
    const promises = endpoints.map(endpoint => 
      performanceOptimizer.batchRequest<T>(`${this.config.baseUrl}${endpoint}`)
    );
    
    return Promise.all(promises);
  }

  /**
   * Fetch with automatic retry and timeout
   */
  private async fetchWithRetry<T>(
    url: string,
    options: RequestInit & {
      priority?: 'high' | 'low';
      cacheKey?: string;
    }
  ): Promise<T> {
    const { priority, cacheKey, ...fetchOptions } = options;
    let lastError: Error;

    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      try {
        // Create abort controller for this request
        const controller = new AbortController();
        if (cacheKey) {
          this.abortControllers.set(cacheKey, controller);
        }

        // Set timeout
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

        const response = await fetch(url, {
          ...fetchOptions,
          signal: controller.signal
        });

        clearTimeout(timeoutId);
        
        if (cacheKey) {
          this.abortControllers.delete(cacheKey);
        }

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        return data;

      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on abort or non-network errors
        if (error instanceof Error && (
          error.name === 'AbortError' ||
          error.message.includes('AbortError') ||
          !this.isRetryableError(error)
        )) {
          break;
        }

        // Wait before retry (exponential backoff)
        if (attempt < this.config.retryAttempts) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError!;
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: Error): boolean {
    const retryableErrors = [
      'fetch',
      'network',
      'timeout',
      'connection',
      'ECONNREFUSED',
      'ETIMEDOUT'
    ];

    return retryableErrors.some(keyword => 
      error.message.toLowerCase().includes(keyword) ||
      error.name.toLowerCase().includes(keyword)
    );
  }

  /**
   * Cancel a specific request
   */
  private cancelRequest(cacheKey: string) {
    const controller = this.abortControllers.get(cacheKey);
    if (controller) {
      controller.abort();
      this.abortControllers.delete(cacheKey);
    }
  }

  /**
   * Cancel all pending requests
   */
  cancelAllRequests() {
    this.abortControllers.forEach(controller => controller.abort());
    this.abortControllers.clear();
  }

  /**
   * Get client statistics
   */
  getStats() {
    return {
      pendingRequests: this.abortControllers.size,
      cacheStats: performanceOptimizer.getCacheStats()
    };
  }
}

// Create optimized API client instance
export const apiClient = new OptimizedApiClient({
  timeout: 8000,
  retryAttempts: 2,
  cacheTimeout: 15000 // 15 second cache for better performance
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  apiClient.cancelAllRequests();
});