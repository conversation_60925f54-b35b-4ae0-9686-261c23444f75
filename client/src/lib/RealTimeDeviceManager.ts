
/**
 * Real-Time Device Manager
 * Connects to actual hardware devices and generates authentic UMatter
 */

export interface DeviceInfo {
  id: string;
  name: string;
  type: 'phone' | 'laptop' | 'tablet' | 'bluetooth' | 'usb' | 'iot';
  battery: number;
  isCharging: boolean;
  isConnected: boolean;
  lastSync: number;
  capabilities: string[];
  realHardware: boolean;
}

export interface EnergyMetrics {
  batteryDrain: number; // Wh consumed
  cpuUsage: number; // Percentage
  memoryUsage: number; // MB
  networkActivity: number; // KB/s
  screenBrightness: number; // 0-100
  umatterGenerated: number;
}

class RealTimeDeviceManager {
  private devices: Map<string, DeviceInfo> = new Map();
  private currentDevice: DeviceInfo | null = null;
  private energyMetrics: EnergyMetrics = {
    batteryDrain: 0,
    cpuUsage: 0,
    memoryUsage: 0,
    networkActivity: 0,
    screenBrightness: 50,
    umatterGenerated: 0
  };
  private listeners: Set<(devices: DeviceInfo[], metrics?: any) => void> = new Set();
  private batteryAPI: any = null;
  private isMonitoring = false;
  private monitoringInterval: number | null = null;
  private lastKnownBatteryLevel: number | null = null;

  constructor() {
    this.initializeRealDeviceAPIs();
  }

  /**
   * Initialize real device APIs
   */
  private async initializeRealDeviceAPIs(): Promise<void> {
    console.log('[RealTimeDeviceManager] Initializing real device APIs...');
    
    // Initialize Battery API
    await this.initializeBatteryAPI();
    
    // Initialize current device
    this.addCurrentDevice();
    
    // Initialize USB API
    await this.initializeUSBAPI();
    
    // Initialize Bluetooth API
    await this.initializeBluetoothAPI();
    
    // Initialize WebRTC for peer-to-peer device discovery
    await this.initializeWebRTC();
    
    // Start real-time monitoring
    this.startRealTimeMonitoring();
  }

  /**
   * Initialize Battery API for real battery data
   */
  private async initializeBatteryAPI(): Promise<void> {
    try {
      // @ts-ignore - Battery API
      this.batteryAPI = await navigator.getBattery?.();
      
      if (this.batteryAPI) {
        console.log('[RealTimeDeviceManager] Battery API connected');
        
        // Listen for battery events
        this.batteryAPI.addEventListener('chargingchange', this.handleBatteryChange.bind(this));
        this.batteryAPI.addEventListener('levelchange', this.handleBatteryChange.bind(this));
        this.batteryAPI.addEventListener('chargingtimechange', this.handleBatteryChange.bind(this));
        this.batteryAPI.addEventListener('dischargingtimechange', this.handleBatteryChange.bind(this));
        
        return;
      }
    } catch (error) {
      console.warn('[RealTimeDeviceManager] Battery API not available:', error);
    }
    
    // Fallback: estimate battery from performance metrics
    console.log('[RealTimeDeviceManager] Using performance-based battery estimation');
  }

  /**
   * Initialize battery drain monitoring and UMatter generation
   */
  private async initializeBatteryDrain(): Promise<void> {
    try {
      // Import and start the real battery drain system
      const { realBatteryDrain } = await import('./realBatteryDrain');
      
      // Subscribe to battery drain events
      realBatteryDrain.subscribe((metrics) => {
        console.log('[RealTimeDeviceManager] Battery drain detected:', metrics);
        
        // Update device metrics with real battery drain
        if (this.currentDevice) {
          this.currentDevice.battery = Math.max(0, this.currentDevice.battery - (metrics.drainRate / 60));
          
          // Notify listeners of UMatter generation
          this.listeners.forEach(callback => {
            callback([this.currentDevice!], {
              type: 'ubit_generation',
              amount: metrics.energyGenerated,
              source: 'battery_drain'
            });
          });
        }
      });
      
      // Start monitoring battery drain
      realBatteryDrain.startMonitoring();
      console.log('[RealTimeDeviceManager] Real battery drain monitoring initialized');
      
    } catch (error) {
      console.error('[RealTimeDeviceManager] Failed to initialize battery drain monitoring:', error);
    }
  }

  /**
   * Initialize USB API for hardware device detection
   */
  private async initializeUSBAPI(): Promise<void> {
    if (!('usb' in navigator)) {
      console.log('[RealTimeDeviceManager] WebUSB not supported');
      return;
    }

    try {
      // @ts-ignore - WebUSB API
      const devices = await navigator.usb.getDevices();
      
      devices.forEach((usbDevice: any) => {
        this.addUSBDevice(usbDevice);
      });

      // Listen for USB connect/disconnect
      // @ts-ignore
      navigator.usb.addEventListener('connect', (event: any) => {
        console.log('[RealTimeDeviceManager] USB device connected:', event.device);
        this.addUSBDevice(event.device);
      });

      // @ts-ignore
      navigator.usb.addEventListener('disconnect', (event: any) => {
        console.log('[RealTimeDeviceManager] USB device disconnected:', event.device);
        this.removeDevice(`usb-${event.device.vendorId}-${event.device.productId}`);
      });

      console.log(`[RealTimeDeviceManager] USB API initialized with ${devices.length} devices`);
    } catch (error) {
      console.warn('[RealTimeDeviceManager] USB API error:', error);
    }
  }

  /**
   * Initialize Bluetooth API for wireless device detection
   */
  private async initializeBluetoothAPI(): Promise<void> {
    if (!('bluetooth' in navigator)) {
      console.log('[RealTimeDeviceManager] Web Bluetooth not supported');
      return;
    }

    try {
      console.log('[RealTimeDeviceManager] Bluetooth API available');
      
      // Note: Bluetooth requires user interaction to scan
      // We'll provide a method for user-initiated scanning
    } catch (error) {
      console.warn('[RealTimeDeviceManager] Bluetooth API error:', error);
    }
  }

  /**
   * Initialize WebRTC for peer-to-peer device discovery
   */
  private async initializeWebRTC(): Promise<void> {
    try {
      // Create peer connection for device mesh networking
      const config = {
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' },
          { urls: 'stun:stun1.l.google.com:19302' }
        ]
      };
      
      const peerConnection = new RTCPeerConnection(config);
      
      // Set up data channel for device communication
      const dataChannel = peerConnection.createDataChannel('device-sync', {
        ordered: true
      });
      
      dataChannel.onopen = () => {
        console.log('[RealTimeDeviceManager] P2P data channel opened');
      };
      
      dataChannel.onmessage = (event) => {
        this.handleP2PMessage(JSON.parse(event.data));
      };
      
      console.log('[RealTimeDeviceManager] WebRTC P2P initialized');
    } catch (error) {
      console.warn('[RealTimeDeviceManager] WebRTC initialization failed:', error);
    }
  }

  /**
   * Add current device using real hardware data
   */
  private addCurrentDevice(): void {
    const deviceInfo: DeviceInfo = {
      id: this.generateDeviceId(),
      name: this.detectDeviceName(),
      type: this.detectDeviceType(),
      battery: this.getCurrentBatteryLevel(),
      isCharging: this.getCurrentChargingStatus(),
      isConnected: true,
      lastSync: Date.now(),
      capabilities: this.detectDeviceCapabilities(),
      realHardware: true
    };
    
    this.devices.set(deviceInfo.id, deviceInfo);
    console.log('[RealTimeDeviceManager] Current device added:', deviceInfo);
    this.notifyListeners();
  }

  /**
   * Add USB device
   */
  private addUSBDevice(usbDevice: any): void {
    const deviceInfo: DeviceInfo = {
      id: `usb-${usbDevice.vendorId}-${usbDevice.productId}`,
      name: usbDevice.productName || `USB Device (${usbDevice.vendorId}:${usbDevice.productId})`,
      type: 'usb',
      battery: 100, // USB devices are externally powered
      isCharging: false,
      isConnected: true,
      lastSync: Date.now(),
      capabilities: ['data_transfer', 'power_delivery'],
      realHardware: true
    };
    
    this.devices.set(deviceInfo.id, deviceInfo);
    console.log('[RealTimeDeviceManager] USB device added:', deviceInfo);
    this.notifyListeners();
  }

  /**
   * Scan for Bluetooth devices (requires user interaction)
   */
  async scanBluetoothDevices(): Promise<DeviceInfo[]> {
    if (!('bluetooth' in navigator)) {
      throw new Error('Bluetooth not supported');
    }

    try {
      // @ts-ignore
      const device = await navigator.bluetooth.requestDevice({
        acceptAllDevices: true,
        optionalServices: ['battery_service', 'device_information']
      });

      const deviceInfo: DeviceInfo = {
        id: `bluetooth-${device.id}`,
        name: device.name || 'Bluetooth Device',
        type: 'bluetooth',
        battery: await this.getBluetoothBatteryLevel(device),
        isCharging: false,
        isConnected: device.gatt?.connected || false,
        lastSync: Date.now(),
        capabilities: ['wireless_sync', 'battery_service'],
        realHardware: true
      };

      this.devices.set(deviceInfo.id, deviceInfo);
      console.log('[RealTimeDeviceManager] Bluetooth device added:', deviceInfo);
      this.notifyListeners();
      
      return [deviceInfo];
    } catch (error) {
      console.error('[RealTimeDeviceManager] Bluetooth scan failed:', error);
      throw error;
    }
  }

  /**
   * Get Bluetooth device battery level
   */
  private async getBluetoothBatteryLevel(device: any): Promise<number> {
    try {
      if (device.gatt) {
        await device.gatt.connect();
        const service = await device.gatt.getPrimaryService('battery_service');
        const characteristic = await service.getCharacteristic('battery_level');
        const value = await characteristic.readValue();
        return value.getUint8(0);
      }
    } catch (error) {
      console.warn('[RealTimeDeviceManager] Could not read Bluetooth battery:', error);
    }
    return 50; // Default
  }

  /**
   * Start real-time monitoring of device metrics
   */
  private startRealTimeMonitoring(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    console.log('[RealTimeDeviceManager] Starting real-time monitoring...');
    
    // Monitor hardware metrics every 30 seconds (display only)
    this.monitoringInterval = setInterval(() => {
      this.updateEnergyMetrics();
      this.updateDeviceStates();
      this.monitorHardwareMetricsOnly(); // No UMatter generation
    }, 30000) as any;
  }

  /**
   * Update energy metrics using real hardware data
   */
  private updateEnergyMetrics(): void {
    // Get real battery data
    const batteryLevel = this.getCurrentBatteryLevel();
    const isCharging = this.getCurrentChargingStatus();
    
    // Calculate battery drain rate
    const previousBattery = this.energyMetrics.batteryDrain;
    if (!isCharging && batteryLevel < 100) {
      // Estimate drain based on battery level change
      this.energyMetrics.batteryDrain = (100 - batteryLevel) * 0.148; // Assume 14.8Wh total capacity
    }
    
    // Get performance metrics
    // @ts-ignore
    if (performance.memory) {
      // @ts-ignore
      this.energyMetrics.memoryUsage = performance.memory.usedJSHeapSize / (1024 * 1024);
    }
    
    // Estimate CPU usage from performance timing
    const navTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navTiming) {
      this.energyMetrics.cpuUsage = Math.min(100, (navTiming.loadEventEnd - navTiming.fetchStart) / 100);
    }
    
    // Get network activity
    const resourceEntries = performance.getEntriesByType('resource');
    if (resourceEntries.length > 0) {
      const recentEntries = resourceEntries.slice(-10);
      this.energyMetrics.networkActivity = recentEntries.reduce((sum, entry) => {
        return sum + ((entry as any).transferSize || 0);
      }, 0) / (1024 * 10); // KB/s estimate
    }
  }

  /**
   * Update device states with real data
   */
  private updateDeviceStates(): void {
    this.devices.forEach((device, id) => {
      if (device.id === this.generateDeviceId()) {
        // Update current device
        device.battery = this.getCurrentBatteryLevel();
        device.isCharging = this.getCurrentChargingStatus();
        device.lastSync = Date.now();
      }
      
      // Update connection status for USB/Bluetooth devices
      if (device.type === 'usb' || device.type === 'bluetooth') {
        // Check if device is still connected (simplified check)
        device.isConnected = Date.now() - device.lastSync < 30000;
      }
    });
    
    this.notifyListeners();
  }

  /**
   * REMOVED - UMatter generation moved to extension-only system
   * Real hardware monitoring continues for metrics only
   */
  private monitorHardwareMetricsOnly(): void {
    // Only monitor for display purposes, no UMatter generation
    const batteryDrain = this.energyMetrics.batteryDrain;
    const cpuUsage = this.energyMetrics.cpuUsage;
    const memoryUsage = this.energyMetrics.memoryUsage;
    
    console.log('[RealTimeDeviceManager] Hardware metrics updated:', {
      batteryDrain,
      cpuUsage,
      memoryUsage,
      networkActivity: this.energyMetrics.networkActivity
    });
    
    // UMatter generation only happens through real extension activity
  }

  /**
   * Get current battery level from real API
   */
  private getCurrentBatteryLevel(): number {
    if (this.batteryAPI) {
      return Math.round(this.batteryAPI.level * 100);
    }
    
    // Fallback: estimate from performance
    return Math.max(20, 100 - (performance.now() / 100000) % 80);
  }

  /**
   * Get current charging status from real API
   */
  private getCurrentChargingStatus(): boolean {
    if (this.batteryAPI) {
      return this.batteryAPI.charging;
    }
    
    // Fallback: random estimation
    return Math.random() > 0.7;
  }

  /**
   * Handle battery change events
   */
  private handleBatteryChange(): void {
    console.log('[RealTimeDeviceManager] Battery status changed');
    this.updateDeviceStates();
  }

  /**
   * Handle P2P messages from other devices
   */
  private handleP2PMessage(message: any): void {
    if (message.type === 'device_sync') {
      const deviceInfo: DeviceInfo = message.device;
      deviceInfo.realHardware = true;
      this.devices.set(deviceInfo.id, deviceInfo);
      console.log('[RealTimeDeviceManager] P2P device synced:', deviceInfo);
      this.notifyListeners();
    }
  }

  /**
   * Detect device name from user agent
   */
  private detectDeviceName(): string {
    const ua = navigator.userAgent;
    
    if (ua.includes('MacBook')) return 'MacBook';
    if (ua.includes('iPad')) return 'iPad';
    if (ua.includes('Android')) return 'Android Device';
    if (ua.includes('Mac')) return 'MacBook';
    if (ua.includes('Windows')) return 'Windows PC';
    if (ua.includes('Linux')) return 'Linux Device';
    
    return 'Unknown Device';
  }

  /**
   * Detect device type from user agent
   */
  private detectDeviceType(): 'phone' | 'laptop' | 'tablet' | 'bluetooth' | 'usb' | 'iot' {
    const ua = navigator.userAgent;
    
    if (ua.includes('MacBook') || ua.includes('Android')) return 'phone';
    if (ua.includes('iPad') || ua.includes('Tablet')) return 'tablet';
    
    return 'laptop';
  }

  /**
   * Detect device capabilities
   */
  private detectDeviceCapabilities(): string[] {
    const capabilities = ['energy_generation', 'umatter_sync'];
    
    if (this.batteryAPI) capabilities.push('battery_monitoring');
    if ('usb' in navigator) capabilities.push('usb_devices');
    if ('bluetooth' in navigator) capabilities.push('bluetooth_sync');
    if ('serviceWorker' in navigator) capabilities.push('background_sync');
    
    return capabilities;
  }

  /**
   * Generate unique device ID
   */
  private generateDeviceId(): string {
    // Use a combination of screen resolution, timezone, and other fingerprints
    const screen = `${window.screen.width}x${window.screen.height}`;
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const lang = navigator.language;
    
    return `device-${btoa(screen + timezone + lang).slice(0, 12)}`;
  }

  /**
   * Remove device
   */
  private removeDevice(deviceId: string): void {
    if (this.devices.delete(deviceId)) {
      console.log('[RealTimeDeviceManager] Device removed:', deviceId);
      this.notifyListeners();
    }
  }

  /**
   * Subscribe to device updates
   */
  subscribe(callback: (devices: DeviceInfo[]) => void): () => void {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  /**
   * Notify listeners
   */
  private notifyListeners(): void {
    const devices = Array.from(this.devices.values());
    this.listeners.forEach(callback => callback(devices));
  }

  /**
   * Public methods
   */
  getDevices(): DeviceInfo[] {
    return Array.from(this.devices.values());
  }

  getEnergyMetrics(): EnergyMetrics {
    return { ...this.energyMetrics };
  }

  async discoverDevices(): Promise<DeviceInfo[]> {
    console.log('[RealTimeDeviceManager] Starting device discovery...');
    
    // Try to discover new devices
    const discoveredDevices: DeviceInfo[] = [];
    
    // Try Bluetooth scan (requires user interaction)
    try {
      const bluetoothDevices = await this.scanBluetoothDevices();
      discoveredDevices.push(...bluetoothDevices);
    } catch (error) {
      console.log('[RealTimeDeviceManager] Bluetooth discovery skipped:', (error as Error).message);
    }
    
    return discoveredDevices;
  }

  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
    console.log('[RealTimeDeviceManager] Monitoring stopped');
  }
}

export const realTimeDeviceManager = new RealTimeDeviceManager();
export default realTimeDeviceManager;
