/**
 * SBU Token Manager - Advanced Energy Storage Units
 * Integrated from attached_assets/SBUTokenManager_1750797491536.ts
 */

interface SBUToken {
  id: string;
  type: 'kinetic' | 'thermal' | 'photonic' | 'neural' | 'quantum';
  energy: number;
  efficiency: number;
  purity: number;
  resonance: number;
  timestamp: number;
  sourceDevice: string;
  compressionRatio: number;
}

interface ExternalBatteryReading {
  deviceId: string;
  batteryLevel: number;
  voltage: number;
  current: number;
  temperature: number;
  isCharging: boolean;
  chargingRate: number;
  kineticActivity: number;
  thermalGradient: number;
  timestamp: number;
}

class SBUTokenManager {
  private sbuTokens: SBUToken[] = [];
  private externalBatteries: Map<string, ExternalBatteryReading> = new Map();
  private kineticThreshold = 0.5; // Minimum movement to generate kinetic energy
  private thermalThreshold = 2.0; // Minimum temperature change
  
  constructor() {
    this.initializeExternalBatteryDetection();
    this.startKineticEnergyHarvesting();
    console.log('[SBUTokenManager] Advanced energy storage units initialized');
  }

  /**
   * Generate SBU token from accumulated energy
   */
  generateSBUToken(energyAmount: number, sourceType: string, deviceData: any): SBUToken {
    const efficiency = this.calculateEnergyEfficiency(deviceData);
    const purity = this.calculateEnergyPurity(sourceType, deviceData);
    const resonance = this.calculateQuantumResonance(energyAmount, efficiency);
    const compressionRatio = this.calculateCompressionRatio(energyAmount, purity);

    const sbuToken: SBUToken = {
      id: `sbu_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: this.determineEnergyType(sourceType, deviceData),
      energy: energyAmount,
      efficiency,
      purity,
      resonance,
      timestamp: Date.now(),
      sourceDevice: deviceData.deviceId || 'unknown',
      compressionRatio
    };

    this.sbuTokens.push(sbuToken);
    console.log(`[SBUTokenManager] Generated SBU token:`, {
      type: sbuToken.type,
      energy: sbuToken.energy.toFixed(6),
      efficiency: sbuToken.efficiency.toFixed(2),
      purity: sbuToken.purity.toFixed(2)
    });

    return sbuToken;
  }

  /**
   * External battery detection for kinetic energy harvesting
   */
  private initializeExternalBatteryDetection() {
    // Check for external battery APIs
    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        this.monitorExternalBattery('primary', battery);
      });
    }

    // Check for multiple battery sources
    this.detectMultipleBatterySources();
  }

  private detectMultipleBatterySources() {
    // Detect external power banks, wireless chargers, etc.
    const powerSources = ['usb', 'wireless', 'solar', 'kinetic'];
    
    powerSources.forEach(source => {
      this.simulateExternalBatteryReading(source);
    });
  }

  private simulateExternalBatteryReading(sourceId: string) {
    const reading: ExternalBatteryReading = {
      deviceId: sourceId,
      batteryLevel: 85 + Math.random() * 15,
      voltage: 3.7 + Math.random() * 0.5,
      current: 0.5 + Math.random() * 1.5,
      temperature: 20 + Math.random() * 15,
      isCharging: Math.random() > 0.5,
      chargingRate: Math.random() * 10,
      kineticActivity: Math.random() * 5,
      thermalGradient: (Math.random() - 0.5) * 4,
      timestamp: Date.now()
    };

    this.externalBatteries.set(sourceId, reading);
  }

  private monitorExternalBattery(deviceId: string, battery: any) {
    const updateReading = () => {
      const reading: ExternalBatteryReading = {
        deviceId,
        batteryLevel: battery.level * 100,
        voltage: 3.7, // Estimated
        current: battery.charging ? 2.0 : -0.5,
        temperature: 25, // Estimated
        isCharging: battery.charging,
        chargingRate: battery.charging ? 5 : 0,
        kineticActivity: this.detectKineticActivity(),
        thermalGradient: this.detectThermalGradient(),
        timestamp: Date.now()
      };

      this.externalBatteries.set(deviceId, reading);
      this.processKineticEnergyHarvesting(reading);
    };

    battery.addEventListener('chargingchange', updateReading);
    battery.addEventListener('levelchange', updateReading);
    updateReading();
  }

  /**
   * Kinetic energy harvesting from device movement
   */
  private startKineticEnergyHarvesting() {
    if ('DeviceMotionEvent' in window) {
      window.addEventListener('devicemotion', (event) => {
        this.processDeviceMotion(event);
      });
    }

    if ('DeviceOrientationEvent' in window) {
      window.addEventListener('deviceorientation', (event) => {
        this.processDeviceOrientation(event);
      });
    }
  }

  private processDeviceMotion(event: DeviceMotionEvent) {
    if (!event.acceleration) return;

    const { x, y, z } = event.acceleration;
    const kineticMagnitude = Math.sqrt((x || 0) ** 2 + (y || 0) ** 2 + (z || 0) ** 2);

    if (kineticMagnitude > this.kineticThreshold) {
      const kineticEnergy = kineticMagnitude * 0.01; // Convert to energy units
      this.harvestKineticEnergy(kineticEnergy, 'motion', event);
    }
  }

  private harvestKineticEnergy(energy: number, source: string, eventData: any) {
    const sbuToken = this.generateSBUToken(energy, `kinetic_${source}`, {
      deviceId: 'kinetic_harvester',
      motionData: eventData,
      harvestingType: 'kinetic'
    });

    // Send to energy batching system
    this.sendToBatchingSystem(sbuToken);
  }

  private sendToBatchingSystem(sbuToken: SBUToken) {
    // Integrate with existing energy sync controller
    import('./energy-sync-controller').then(({ energySyncController }) => {
      energySyncController.addEnergy(`sbu_${sbuToken.type}`, sbuToken.energy, {
        sbuToken,
        efficiency: sbuToken.efficiency,
        purity: sbuToken.purity,
        resonance: sbuToken.resonance
      });
    }).catch(console.error);
  }

  /**
   * Energy calculation methods
   */
  private calculateEnergyEfficiency(deviceData: any): number {
    const baseEfficiency = 0.85;
    const batteryBonus = deviceData.batteryLevel > 0.8 ? 0.1 : 0;
    const chargingBonus = deviceData.isCharging ? 0.05 : 0;
    return Math.min(baseEfficiency + batteryBonus + chargingBonus, 1.0);
  }

  private calculateEnergyPurity(sourceType: string, deviceData: any): number {
    const typeMultipliers = {
      kinetic: 0.9,
      thermal: 0.8,
      photonic: 0.95,
      neural: 0.85,
      quantum: 1.0
    };
    
    const baseType = sourceType.split('_')[0] as keyof typeof typeMultipliers;
    return (typeMultipliers[baseType] || 0.8) * (0.8 + Math.random() * 0.2);
  }

  private calculateQuantumResonance(energy: number, efficiency: number): number {
    return energy * efficiency * (0.7 + Math.random() * 0.3);
  }

  private calculateCompressionRatio(energy: number, purity: number): number {
    return energy * purity * 10; // 10:1 compression typical
  }

  private determineEnergyType(sourceType: string, deviceData: any): SBUToken['type'] {
    if (sourceType.includes('kinetic')) return 'kinetic';
    if (sourceType.includes('thermal')) return 'thermal';
    if (sourceType.includes('photonic')) return 'photonic';
    if (sourceType.includes('neural')) return 'neural';
    if (sourceType.includes('quantum')) return 'quantum';
    return 'kinetic'; // Default
  }

  private detectKineticActivity(): number {
    return Math.random() * 5; // Simulated kinetic activity
  }

  private detectThermalGradient(): number {
    return (Math.random() - 0.5) * 4; // -2 to +2 degree gradient
  }

  private processKineticEnergyHarvesting(reading: ExternalBatteryReading) {
    if (reading.kineticActivity > this.kineticThreshold) {
      const harvestedEnergy = reading.kineticActivity * 0.1;
      this.harvestKineticEnergy(harvestedEnergy, 'external_battery', reading);
    }

    if (Math.abs(reading.thermalGradient) > this.thermalThreshold) {
      const thermalEnergy = Math.abs(reading.thermalGradient) * 0.05;
      this.harvestThermalEnergy(thermalEnergy, reading);
    }
  }

  private harvestThermalEnergy(energy: number, reading: ExternalBatteryReading) {
    const sbuToken = this.generateSBUToken(energy, 'thermal_gradient', {
      deviceId: reading.deviceId,
      thermalData: reading,
      harvestingType: 'thermal'
    });

    this.sendToBatchingSystem(sbuToken);
  }

  private processDeviceOrientation(event: DeviceOrientationEvent) {
    // Process orientation changes for additional energy harvesting
    const orientationEnergy = Math.abs(event.alpha || 0) * 0.001;
    if (orientationEnergy > 0.01) {
      this.harvestKineticEnergy(orientationEnergy, 'orientation', event);
    }
  }

  /**
   * Public API methods
   */
  getSBUTokens(): SBUToken[] {
    return [...this.sbuTokens];
  }

  getSBUTokensByType(type: SBUToken['type']): SBUToken[] {
    return this.sbuTokens.filter(token => token.type === type);
  }

  getTotalSBUEnergy(): number {
    return this.sbuTokens.reduce((total, token) => total + token.energy, 0);
  }

  getExternalBatteryReadings(): ExternalBatteryReading[] {
    return Array.from(this.externalBatteries.values());
  }
}

export const sbuTokenManager = new SBUTokenManager();