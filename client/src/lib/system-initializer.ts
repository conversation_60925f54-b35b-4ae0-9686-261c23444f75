/**
 * System Initializer - Complete nU Universe system startup
 * Integrates all provided systems: nUCore, nUOS, SpUnderBot, GhostBot, etc.
 */

import { nuCore } from './nu-core';
import { spUnderBot } from './spunder-bot';
import { nuosSocialControl } from './nuos-social-control';
import { energyGhost, helpGhost, dataGhost } from './ghost-bot';
import { iotManager } from './iot-manager';
import { ipfsStorage } from './ipfs-storage';
import { metaAIOrchestrator } from './meta-ai-orchestrator';
import { realWorldEnergyAPI } from './real-world-energy-api';
import { sbuTokenManager } from './sbu-token-manager';
import { walletFix } from './wallet-fix';
import { advancedSpUnderBot } from './advanced-spunder-bot';
import { spUnderIntegration } from './spunder-integration';

class SystemInitializer {
  private initialized: boolean = false;
  private systems: Map<string, any> = new Map();
  private initializationOrder: string[] = [
    'ipfsStorage',
    'nuCore',
    'spUnderBot',
    'nuosSocialControl',
    'ghostBots',
    'iotManager',
    'metaAI',
    'realWorldEnergy',
    'sbuTokens'
  ];

  async initializeAllSystems(): Promise<void> {
    if (this.initialized) {
      console.log('[SystemInitializer] Systems already initialized');
      return;
    }

    console.log('[SystemInitializer] Starting complete nU Universe system initialization...');

    try {
      // Initialize systems in order
      for (const systemName of this.initializationOrder) {
        await this.initializeSystem(systemName);
      }

      // Start SpUnderBot system diagnostics and repairs
      // await this.startSystemDiagnostics(); // SpUnderBot disabled

      // Enable all system integrations
      await this.enableSystemIntegrations();

      this.initialized = true;
      console.log('[SystemInitializer] Complete system initialization successful');

      // Trigger wallet updates and button functionality
      this.activateUIFunctionality();

    } catch (error) {
      console.error('[SystemInitializer] System initialization failed:', error);
      throw error;
    }
  }

  private async initializeSystem(systemName: string): Promise<void> {
    console.log(`[SystemInitializer] Initializing ${systemName}...`);

    switch (systemName) {
      case 'ipfsStorage':
        await ipfsStorage.initialize();
        this.systems.set('ipfsStorage', ipfsStorage);
        break;

      case 'nuCore':
        // nU Core auto-initializes
        this.systems.set('nuCore', nuCore);
        break;

      case 'spUnderBot':
        // SpUnderBot starts automatically and fixes issues
        // this.systems.set('spUnderBot', spUnderBot);
        // this.systems.set('advancedSpUnderBot', advancedSpUnderBot);
        // this.systems.set('spUnderIntegration', spUnderIntegration);
        console.log('[SystemInitializer] SpUnderBot initialization skipped.');
        break;

      case 'nuosSocialControl':
        this.systems.set('nuosSocialControl', nuosSocialControl);
        break;

      case 'ghostBots':
        this.systems.set('energyGhost', energyGhost);
        this.systems.set('helpGhost', helpGhost);
        this.systems.set('dataGhost', dataGhost);
        break;

      case 'iotManager':
        await iotManager.startDiscovery();
        this.systems.set('iotManager', iotManager);
        break;

      case 'metaAI':
        this.systems.set('metaAI', metaAIOrchestrator);
        break;

      case 'realWorldEnergy':
        this.systems.set('realWorldEnergy', realWorldEnergyAPI);
        break;

      case 'sbuTokens':
        this.systems.set('sbuTokens', sbuTokenManager);
        break;
    }
  }

  private async startSystemDiagnostics(): Promise<void> {
    console.log('[SystemInitializer] Starting SpUnderBot system diagnostics...');

    // SpUnderBot will automatically detect and fix issues
    // const systemStatus = spUnderBot.getSystemStatus();
    // console.log('[SystemInitializer] SpUnderBot status:', systemStatus);

    // Force system repair to fix wallet, extension, and button issues
    // await spUnderBot.forceSystemRepair();

    console.log('[SystemInitializer] SpUnderBot system diagnostics skipped.');
  }

  private async enableSystemIntegrations(): Promise<void> {
    console.log('[SystemInitializer] Enabling system integrations...');

    // Connect ghost bots to energy optimization
    energyGhost.assignTask({
      id: 'startup_optimization',
      type: 'energy_optimization',
      priority: 'high',
      parameters: { 
        targetEfficiency: 0.95,
        enableRealTimeUpdates: true,
        fixWalletUpdates: true
      },
      assignedBot: '',
      status: 'pending',
      createdAt: Date.now()
    });

    // Connect IoT manager to energy system
    iotManager.subscribe((devices) => {
      console.log(`[SystemInitializer] IoT devices updated: ${devices.length} devices`);
    });

    // Connect AI orchestrator to system optimization
    await metaAIOrchestrator.submitTask('optimization', {
      systemOptimization: true,
      targetSystems: ['wallet', 'extension', 'energy', 'buttons']
    }, { priority: 'critical' });
  }

  private activateUIFunctionality(): void {
    console.log('[SystemInitializer] Activating UI functionality...');

    // Fix wallet updates
    this.fixWalletUpdates();

    // Fix extension download
    this.fixExtensionDownload();

    // Fix button functionality
    this.fixButtonFunctionality();

    // Enable energy generation
    this.enableEnergyGeneration();
  }

  private fixWalletUpdates(): void {
    console.log('[SystemInitializer] Fixing wallet updates...');

    // Wallet fix is already initialized and handles updates automatically
    walletFix.forceUpdate();
  }

  private fixExtensionDownload(): void {
    console.log('[SystemInitializer] Fixing extension download...');

    // Find and fix extension download buttons
    const checkAndFixExtension = () => {
      const extensionButtons = document.querySelectorAll('button, a');

      extensionButtons.forEach(button => {
        if (button.textContent?.includes('Download Extension') || 
            button.textContent?.includes('Install Extension')) {

          button.addEventListener('click', async (event) => {
            event.preventDefault();

            try {
              console.log('[SystemInitializer] Extension download triggered');

              const response = await fetch('/api/extension/download');

              if (response.ok) {
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'nu-universe-quantum-extension.zip';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);

                console.log('[SystemInitializer] Extension download successful');
              } else {
                console.error('[SystemInitializer] Extension download failed:', response.status);
              }
            } catch (error) {
              console.error('[SystemInitializer] Extension download error:', error);
            }
          });
        }
      });
    };

    // Check immediately and on DOM changes
    checkAndFixExtension();

    // Set up mutation observer for dynamically added buttons
    const observer = new MutationObserver(checkAndFixExtension);
    observer.observe(document.body, { childList: true, subtree: true });
  }

  private fixButtonFunctionality(): void {
    console.log('[SystemInitializer] Fixing button functionality...');

    const enhanceButtons = () => {
      const buttons = document.querySelectorAll('button');

      buttons.forEach(button => {
        // Energy generation buttons
        if (button.textContent?.includes('Generate') || 
            button.textContent?.includes('Energy') ||
            button.textContent?.includes('Sync') ||
            button.textContent?.includes('Update')) {

          // Remove disabled state
          button.removeAttribute('disabled');
          button.classList.remove('disabled');

          // Add working click handler
          button.addEventListener('click', (event) => {
            console.log('[SystemInitializer] Button activated:', button.textContent);

            if (button.textContent?.includes('Generate')) {
              this.triggerEnergyGeneration();
            } else if (button.textContent?.includes('Sync')) {
              this.triggerDeviceSync();
            } else if (button.textContent?.includes('Update')) {
              this.triggerWalletUpdate();
            }
          });

          // Visual feedback that button is working
          button.style.opacity = '1';
          button.style.cursor = 'pointer';
        }
      });
    };

    enhanceButtons();

    // Re-enhance buttons when DOM changes
    const observer = new MutationObserver(enhanceButtons);
    observer.observe(document.body, { childList: true, subtree: true });
  }

  private enableEnergyGeneration(): void {
    console.log('[SystemInitializer] Enabling energy generation...');

    // Connect to energy sync controller
    import('./energy-sync-controller').then(({ energySyncController }) => {
      // Enable energy generation
      energySyncController.addEnergy('system_startup', 10.0, {
        source: 'system_initializer',
        type: 'startup_bonus',
        timestamp: Date.now()
      });

      console.log('[SystemInitializer] Energy generation enabled');
    }).catch(console.error);
  }

  private triggerEnergyGeneration(): void {
    console.log('[SystemInitializer] Manual energy generation triggered');

    import('./energy-sync-controller').then(({ energySyncController }) => {
      energySyncController.addEnergy('manual_generation', 5.0, {
        source: 'manual_button',
        type: 'user_interaction',
        timestamp: Date.now()
      });
    }).catch(console.error);
  }

  private triggerDeviceSync(): void {
    console.log('[SystemInitializer] Device sync triggered');

    // Trigger device discovery
    iotManager.startDiscovery();

    // Force device update event
    window.dispatchEvent(new CustomEvent('deviceSyncTriggered', {
      detail: { source: 'manual_sync' }
    }));
  }

  private triggerWalletUpdate(): void {
    console.log('[SystemInitializer] Wallet update triggered');
    this.fixWalletUpdates();
  }

  // Public API
  getSystemStatus(): any {
    return {
      initialized: this.initialized,
      systems: Array.from(this.systems.keys()),
      spUnderBotStatus: spUnderBot.getSystemStatus(),
      nuCoreMetrics: nuCore.getNetworkMetrics(),
      socialControlStats: nuosSocialControl.getSocialStats()
    };
  }

  getSystem(name: string): any {
    return this.systems.get(name);
  }

  async reinitialize(): Promise<void> {
    this.initialized = false;
    this.systems.clear();
    await this.initializeAllSystems();
  }
}

export const systemInitializer = new SystemInitializer();

// Auto-initialize when loaded
systemInitializer.initializeAllSystems().catch(console.error);