
export class ServerLogReceiver {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor() {
    this.connect();
  }

  private connect(): void {
    try {
      const wsUrl = `ws://${window.location.hostname}:${window.location.port}/ws`;
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('[ServerLogReceiver] Connected to server logs');
        this.reconnectAttempts = 0;
      };

      this.ws.onmessage = (event) => {
        try {
          const logData = JSON.parse(event.data);
          
          if (logData.type === 'server_log') {
            this.displayServerLog(logData);
          }
        } catch (error) {
          // Ignore non-JSON messages
        }
      };

      this.ws.onclose = () => {
        console.log('[ServerLogReceiver] Disconnected from server logs');
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('[ServerLogReceiver] WebSocket error:', error);
      };

    } catch (error) {
      console.error('[ServerLogReceiver] Failed to connect:', error);
      this.attemptReconnect();
    }
  }

  private displayServerLog(logData: any): void {
    const message = `[SERVER ${logData.level.toUpperCase()}] ${logData.source ? `[${logData.source}] ` : ''}${logData.message}`;
    
    switch (logData.level) {
      case 'error':
        console.error(message, logData.metadata || '');
        break;
      case 'warn':
        console.warn(message, logData.metadata || '');
        break;
      case 'debug':
        console.debug(message, logData.metadata || '');
        break;
      default:
        console.log(message, logData.metadata || '');
    }
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`[ServerLogReceiver] Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect();
      }, 1000 * this.reconnectAttempts);
    }
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

// Auto-start in development
if (process.env.NODE_ENV === 'development') {
  const serverLogReceiver = new ServerLogReceiver();
  
  // Store globally for cleanup
  (window as any).serverLogReceiver = serverLogReceiver;
}
