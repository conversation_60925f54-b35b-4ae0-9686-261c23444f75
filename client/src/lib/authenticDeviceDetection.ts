// Authentic Device Detection System
export interface DeviceInfo {
  id: string;
  name: string;
  type: 'phone' | 'tablet' | 'laptop' | 'desktop' | 'iot' | 'wearable';
  os: string;
  browser: string;
  userAgent: string;
  platform: string;
  screenResolution: string;
  batteryLevel?: number;
  networkType: string;
  isOnline: boolean;
  lastSeen: Date;
}

export interface HardwareSpecs {
  cores: number;
  memory: number;
  storage: number;
  gpu?: string;
  sensors: string[];
  capabilities: string[];
}

export interface NetworkInfo {
  type: string;
  speed: number;
  latency: number;
  isStable: boolean;
}

class AuthenticDeviceDetection {
  private detectedDevices: Map<string, DeviceInfo> = new Map();
  private currentDevice: DeviceInfo | null = null;

  async detectCurrentDevice(): Promise<DeviceInfo> {
    const deviceInfo: DeviceInfo = {
      id: this.generateDeviceId(),
      name: this.getDeviceName(),
      type: this.getDeviceType(),
      os: this.getOperatingSystem(),
      browser: this.getBrowserInfo(),
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      screenResolution: `${screen.width}x${screen.height}`,
      networkType: this.getNetworkType(),
      isOnline: navigator.onLine,
      lastSeen: new Date()
    };

    // Get battery level if available
    if ('getBattery' in navigator) {
      try {
        const battery = await (navigator as any).getBattery();
        deviceInfo.batteryLevel = Math.floor(battery.level * 100);
      } catch (error) {
        console.log('Battery API not available');
      }
    }

    this.currentDevice = deviceInfo;
    this.detectedDevices.set(deviceInfo.id, deviceInfo);
    
    return deviceInfo;
  }

  private generateDeviceId(): string {
    // Generate unique device ID based on hardware fingerprint
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Device fingerprint', 2, 2);
    }
    
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL()
    ].join('|');
    
    return btoa(fingerprint).substring(0, 16);
  }

  private getDeviceName(): string {
    const ua = navigator.userAgent;
    if (ua.includes('iPhone')) return 'iPhone';
    if (ua.includes('iPad')) return 'iPad';
    if (ua.includes('Android')) return 'Android Device';
    if (ua.includes('Mac')) return 'Mac';
    if (ua.includes('Windows')) return 'Windows PC';
    if (ua.includes('Linux')) return 'Linux Device';
    return 'Unknown Device';
  }

  private getDeviceType(): DeviceInfo['type'] {
    const ua = navigator.userAgent;
    if (ua.includes('Mobile') || ua.includes('iPhone') || ua.includes('Android')) {
      return 'phone';
    }
    if (ua.includes('iPad') || ua.includes('Tablet')) {
      return 'tablet';
    }
    if (ua.includes('Mac') || ua.includes('Windows') || ua.includes('Linux')) {
      return screen.width > 1024 ? 'desktop' : 'laptop';
    }
    return 'desktop';
  }

  private getOperatingSystem(): string {
    const ua = navigator.userAgent;
    if (ua.includes('Windows')) return 'Windows';
    if (ua.includes('Mac OS')) return 'macOS';
    if (ua.includes('Linux')) return 'Linux';
    if (ua.includes('Android')) return 'Android';
    if (ua.includes('iOS')) return 'iOS';
    return 'Unknown';
  }

  private getBrowserInfo(): string {
    const ua = navigator.userAgent;
    if (ua.includes('Chrome')) return 'Chrome';
    if (ua.includes('Firefox')) return 'Firefox';
    if (ua.includes('Safari')) return 'Safari';
    if (ua.includes('Edge')) return 'Edge';
    return 'Unknown';
  }

  private getNetworkType(): string {
    const connection = (navigator as any).connection;
    if (connection) {
      return connection.effectiveType || connection.type || 'unknown';
    }
    return 'unknown';
  }

  async getHardwareSpecs(): Promise<HardwareSpecs> {
    return {
      cores: navigator.hardwareConcurrency || 1,
      memory: (navigator as any).deviceMemory || 0,
      storage: 0, // Would need to be calculated
      sensors: this.getAvailableSensors(),
      capabilities: this.getDeviceCapabilities()
    };
  }

  private getAvailableSensors(): string[] {
    const sensors: string[] = [];
    if ('geolocation' in navigator) sensors.push('GPS');
    if ('accelerometer' in window) sensors.push('Accelerometer');
    if ('gyroscope' in window) sensors.push('Gyroscope');
    if ('magnetometer' in window) sensors.push('Magnetometer');
    return sensors;
  }

  private getDeviceCapabilities(): string[] {
    const capabilities: string[] = [];
    if ('serviceWorker' in navigator) capabilities.push('ServiceWorker');
    if ('webgl' in window) capabilities.push('WebGL');
    if ('webrtc' in window) capabilities.push('WebRTC');
    if ('bluetooth' in navigator) capabilities.push('Bluetooth');
    return capabilities;
  }

  getCurrentDevice(): DeviceInfo | null {
    return this.currentDevice;
  }

  getAllDetectedDevices(): DeviceInfo[] {
    return Array.from(this.detectedDevices.values());
  }

  async getNetworkInfo(): Promise<NetworkInfo> {
    const connection = (navigator as any).connection;
    return {
      type: connection?.effectiveType || 'unknown',
      speed: connection?.downlink || 0,
      latency: connection?.rtt || 0,
      isStable: navigator.onLine
    };
  }
}

export const authenticDeviceDetection = new AuthenticDeviceDetection();
