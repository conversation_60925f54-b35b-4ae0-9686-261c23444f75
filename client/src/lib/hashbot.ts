/**
 * HashBot - Cryptographic Utilities for nU Web
 * Provides encryption, hashing, and integrity verification
 */

export interface HashBotConfig {
  algorithm: 'SHA-256' | 'SHA-512' | 'blake2b';
  keyDerivation: 'PBKDF2' | 'scrypt' | 'argon2';
  iterations: number;
  saltLength: number;
}

export interface EncryptedData {
  data: string;
  hash: string;
  salt: string;
  iv: string;
  timestamp: number;
  algorithm: string;
}

export interface VerificationResult {
  valid: boolean;
  hash: string;
  timestamp: number;
  algorithm: string;
}

export class HashBot {
  private config: HashBotConfig;
  private encoder: TextEncoder;
  private decoder: TextDecoder;

  constructor(config: Partial<HashBotConfig> = {}) {
    this.config = {
      algorithm: 'SHA-256',
      keyDerivation: 'PBKDF2',
      iterations: 100000,
      saltLength: 32,
      ...config
    };

    this.encoder = new TextEncoder();
    this.decoder = new TextDecoder();
  }

  /**
   * Generate a cryptographic hash of data
   */
  async hash(data: string | ArrayBuffer): Promise<string> {
    const buffer = typeof data === 'string' ? this.encoder.encode(data) : data;
    
    const hashBuffer = await crypto.subtle.digest(this.config.algorithm, buffer);
    return this.arrayBufferToHex(hashBuffer);
  }

  /**
   * Generate a salt for cryptographic operations
   */
  generateSalt(): string {
    const saltArray = new Uint8Array(this.config.saltLength);
    crypto.getRandomValues(saltArray);
    return this.arrayBufferToHex(saltArray);
  }

  /**
   * Generate an initialization vector (IV)
   */
  generateIV(): string {
    const ivArray = new Uint8Array(16); // 128-bit IV for AES
    crypto.getRandomValues(ivArray);
    return this.arrayBufferToHex(ivArray);
  }

  /**
   * Derive a key from a password using PBKDF2
   */
  async deriveKey(password: string, salt: string): Promise<CryptoKey> {
    const passwordBuffer = this.encoder.encode(password);
    const saltBuffer = this.hexToArrayBuffer(salt);

    const baseKey = await crypto.subtle.importKey(
      'raw',
      passwordBuffer,
      'PBKDF2',
      false,
      ['deriveKey']
    );

    return await crypto.subtle.deriveKey(
      {
        name: 'PBKDF2',
        salt: saltBuffer,
        iterations: this.config.iterations,
        hash: this.config.algorithm
      },
      baseKey,
      { name: 'AES-GCM', length: 256 },
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * Encrypt data using AES-GCM
   */
  async encrypt(data: string, password: string): Promise<EncryptedData> {
    const salt = this.generateSalt();
    const iv = this.generateIV();
    const key = await this.deriveKey(password, salt);

    const dataBuffer = this.encoder.encode(data);
    const ivBuffer = this.hexToArrayBuffer(iv);

    const encryptedBuffer = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv: ivBuffer },
      key,
      dataBuffer
    );

    const encryptedData = this.arrayBufferToHex(encryptedBuffer);
    const hash = await this.hash(data);

    return {
      data: encryptedData,
      hash,
      salt,
      iv,
      timestamp: Date.now(),
      algorithm: this.config.algorithm
    };
  }

  /**
   * Decrypt data using AES-GCM
   */
  async decrypt(encryptedData: EncryptedData, password: string): Promise<string> {
    const key = await this.deriveKey(password, encryptedData.salt);
    const encryptedBuffer = this.hexToArrayBuffer(encryptedData.data);
    const ivBuffer = this.hexToArrayBuffer(encryptedData.iv);

    try {
      const decryptedBuffer = await crypto.subtle.decrypt(
        { name: 'AES-GCM', iv: ivBuffer },
        key,
        encryptedBuffer
      );

      return this.decoder.decode(decryptedBuffer);
    } catch (error) {
      throw new Error('Decryption failed: Invalid password or corrupted data');
    }
  }

  /**
   * Verify data integrity using hash comparison
   */
  async verify(data: string, expectedHash: string): Promise<VerificationResult> {
    const computedHash = await this.hash(data);
    
    return {
      valid: computedHash === expectedHash,
      hash: computedHash,
      timestamp: Date.now(),
      algorithm: this.config.algorithm
    };
  }

  /**
   * Generate a Merkle tree root from an array of data
   */
  async generateMerkleRoot(dataArray: string[]): Promise<string> {
    if (dataArray.length === 0) return '';
    
    let hashes = await Promise.all(dataArray.map(data => this.hash(data)));
    
    while (hashes.length > 1) {
      const newHashes: string[] = [];
      for (let i = 0; i < hashes.length; i += 2) {
        const left = hashes[i];
        const right = hashes[i + 1] || left; // Handle odd number of hashes
        const combined = left + right;
        newHashes.push(await this.hash(combined));
      }
      hashes = newHashes;
    }
    
    return hashes[0];
  }

  /**
   * Generate a digital signature using HMAC
   */
  async sign(data: string, secret: string): Promise<string> {
    const key = await crypto.subtle.importKey(
      'raw',
      this.encoder.encode(secret),
      { name: 'HMAC', hash: this.config.algorithm },
      false,
      ['sign']
    );

    const signature = await crypto.subtle.sign(
      'HMAC',
      key,
      this.encoder.encode(data)
    );

    return this.arrayBufferToHex(signature);
  }

  /**
   * Verify a digital signature using HMAC
   */
  async verifySignature(data: string, signature: string, secret: string): Promise<boolean> {
    const key = await crypto.subtle.importKey(
      'raw',
      this.encoder.encode(secret),
      { name: 'HMAC', hash: this.config.algorithm },
      false,
      ['verify']
    );

    const signatureBuffer = this.hexToArrayBuffer(signature);

    return await crypto.subtle.verify(
      'HMAC',
      key,
      signatureBuffer,
      this.encoder.encode(data)
    );
  }

  /**
   * Generate a secure random token
   */
  generateToken(length: number = 32): string {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return this.arrayBufferToHex(array);
  }

  /**
   * Generate a DID (Decentralized Identifier)
   */
  async generateDID(publicKey: string, method: string = 'nu'): Promise<string> {
    const keyHash = await this.hash(publicKey);
    return `did:${method}:${keyHash.substring(0, 32)}`;
  }

  /**
   * Convert ArrayBuffer to hex string
   */
  private arrayBufferToHex(buffer: ArrayBuffer): string {
    const byteArray = new Uint8Array(buffer);
    return Array.from(byteArray)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('');
  }

  /**
   * Convert hex string to ArrayBuffer
   */
  private hexToArrayBuffer(hex: string): ArrayBuffer {
    const bytes = new Uint8Array(hex.length / 2);
    for (let i = 0; i < hex.length; i += 2) {
      bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
    }
    return bytes.buffer;
  }

  /**
   * Secure comparison of two strings (timing-safe)
   */
  secureCompare(a: string, b: string): boolean {
    if (a.length !== b.length) return false;
    
    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }
    
    return result === 0;
  }

  /**
   * Generate a key pair for asymmetric encryption
   */
  async generateKeyPair(): Promise<{
    publicKey: string;
    privateKey: string;
  }> {
    const keyPair = await crypto.subtle.generateKey(
      {
        name: 'RSA-OAEP',
        modulusLength: 2048,
        publicExponent: new Uint8Array([1, 0, 1]),
        hash: this.config.algorithm
      },
      true,
      ['encrypt', 'decrypt']
    );

    const publicKey = await crypto.subtle.exportKey('spki', keyPair.publicKey);
    const privateKey = await crypto.subtle.exportKey('pkcs8', keyPair.privateKey);

    return {
      publicKey: this.arrayBufferToHex(publicKey),
      privateKey: this.arrayBufferToHex(privateKey)
    };
  }
}

// Global HashBot instance
export const hashBot = new HashBot();
