/**
 * Mobile Device Manager - Authentic Mobile Integration
 * Handles real mobile device APIs and native functionality
 */

interface MobileDeviceCapabilities {
  battery: boolean;
  deviceMotion: boolean;
  deviceOrientation: boolean;
  geolocation: boolean;
  camera: boolean;
  microphone: boolean;
  bluetooth: boolean;
  nfc: boolean;
  vibration: boolean;
  wakeLock: boolean;
  share: boolean;
}

interface MobileMetrics {
  batteryLevel: number;
  isCharging: boolean;
  touchInteractions: number;
  deviceMotion: {
    acceleration: { x: number; y: number; z: number };
    rotationRate: { alpha: number; beta: number; gamma: number };
  };
  orientation: { alpha: number; beta: number; gamma: number };
  networkConnection: {
    effectiveType: string;
    downlink: number;
    rtt: number;
  };
  memoryInfo: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
}

class MobileDeviceManager {
  private capabilities: MobileDeviceCapabilities;
  private metrics: MobileMetrics;
  private listeners: Set<(metrics: MobileMetrics) => void> = new Set();
  private isInitialized = false;
  private touchCount = 0;
  private lastTouchTime = 0;
  
  constructor() {
    this.capabilities = this.detectCapabilities();
    this.metrics = this.initializeMetrics();
    this.initialize();
  }

  private detectCapabilities(): MobileDeviceCapabilities {
    return {
      battery: 'getBattery' in navigator,
      deviceMotion: 'DeviceMotionEvent' in window,
      deviceOrientation: 'DeviceOrientationEvent' in window,
      geolocation: 'geolocation' in navigator,
      camera: 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices,
      microphone: 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices,
      bluetooth: 'bluetooth' in navigator,
      nfc: 'nfc' in navigator,
      vibration: 'vibrate' in navigator,
      wakeLock: 'wakeLock' in navigator,
      share: 'share' in navigator
    };
  }

  private initializeMetrics(): MobileMetrics {
    return {
      batteryLevel: 0,
      isCharging: false,
      touchInteractions: 0,
      deviceMotion: {
        acceleration: { x: 0, y: 0, z: 0 },
        rotationRate: { alpha: 0, beta: 0, gamma: 0 }
      },
      orientation: { alpha: 0, beta: 0, gamma: 0 },
      networkConnection: {
        effectiveType: '4g',
        downlink: 10,
        rtt: 100
      },
      memoryInfo: {
        usedJSHeapSize: 0,
        totalJSHeapSize: 0,
        jsHeapSizeLimit: 0
      }
    };
  }

  private async initialize() {
    if (this.isInitialized) return;

    console.log('[MobileDeviceManager] Initializing with capabilities:', this.capabilities);

    // Initialize battery monitoring
    if (this.capabilities.battery) {
      await this.initializeBattery();
    }

    // Initialize motion tracking
    if (this.capabilities.deviceMotion) {
      this.initializeMotionTracking();
    }

    // Initialize orientation tracking
    if (this.capabilities.deviceOrientation) {
      this.initializeOrientationTracking();
    }

    // Initialize touch tracking
    this.initializeTouchTracking();

    // Initialize network monitoring
    this.initializeNetworkMonitoring();

    // Initialize memory monitoring
    this.initializeMemoryMonitoring();

    // Start real-time updates
    this.startRealtimeUpdates();

    this.isInitialized = true;
    console.log('[MobileDeviceManager] Initialization complete');
  }

  private async initializeBattery() {
    try {
      const battery = await (navigator as any).getBattery();
      
      const updateBattery = () => {
        this.metrics.batteryLevel = battery.level;
        this.metrics.isCharging = battery.charging;
        this.notifyListeners();
      };

      battery.addEventListener('chargingchange', updateBattery);
      battery.addEventListener('levelchange', updateBattery);
      
      // Initial update
      updateBattery();
      
      console.log('[MobileDeviceManager] Battery API connected:', {
        level: battery.level,
        charging: battery.charging
      });
    } catch (error) {
      console.log('[MobileDeviceManager] Battery API not available');
    }
  }

  private initializeMotionTracking() {
    // Request permission for iOS devices
    if (typeof (DeviceMotionEvent as any).requestPermission === 'function') {
      (DeviceMotionEvent as any).requestPermission()
        .then((permissionState: string) => {
          if (permissionState === 'granted') {
            this.setupMotionListeners();
          }
        })
        .catch(console.error);
    } else {
      this.setupMotionListeners();
    }
  }

  private setupMotionListeners() {
    window.addEventListener('devicemotion', (event) => {
      if (event.acceleration) {
        this.metrics.deviceMotion.acceleration = {
          x: event.acceleration.x || 0,
          y: event.acceleration.y || 0,
          z: event.acceleration.z || 0
        };
      }
      
      if (event.rotationRate) {
        this.metrics.deviceMotion.rotationRate = {
          alpha: event.rotationRate.alpha || 0,
          beta: event.rotationRate.beta || 0,
          gamma: event.rotationRate.gamma || 0
        };
      }
      
      this.notifyListeners();
    });

    console.log('[MobileDeviceManager] Motion tracking initialized');
  }

  private initializeOrientationTracking() {
    // Request permission for iOS devices
    if (typeof (DeviceOrientationEvent as any).requestPermission === 'function') {
      (DeviceOrientationEvent as any).requestPermission()
        .then((permissionState: string) => {
          if (permissionState === 'granted') {
            this.setupOrientationListeners();
          }
        })
        .catch(console.error);
    } else {
      this.setupOrientationListeners();
    }
  }

  private setupOrientationListeners() {
    window.addEventListener('deviceorientation', (event) => {
      this.metrics.orientation = {
        alpha: event.alpha || 0,
        beta: event.beta || 0,
        gamma: event.gamma || 0
      };
      this.notifyListeners();
    });

    console.log('[MobileDeviceManager] Orientation tracking initialized');
  }

  private initializeTouchTracking() {
    const touchEvents = ['touchstart', 'touchmove', 'touchend'];
    
    touchEvents.forEach(eventType => {
      document.addEventListener(eventType, (event) => {
        const now = Date.now();
        
        // Count unique touch interactions (throttle to avoid spam)
        if (now - this.lastTouchTime > 100) {
          this.touchCount++;
          this.metrics.touchInteractions = this.touchCount;
          this.lastTouchTime = now;
          this.notifyListeners();
        }
      }, { passive: true });
    });

    console.log('[MobileDeviceManager] Touch tracking initialized');
  }

  private initializeNetworkMonitoring() {
    const connection = (navigator as any).connection || 
                     (navigator as any).mozConnection || 
                     (navigator as any).webkitConnection;

    if (connection) {
      const updateConnection = () => {
        this.metrics.networkConnection = {
          effectiveType: connection.effectiveType || '4g',
          downlink: connection.downlink || 10,
          rtt: connection.rtt || 100
        };
        this.notifyListeners();
      };

      connection.addEventListener('change', updateConnection);
      updateConnection(); // Initial update

      console.log('[MobileDeviceManager] Network monitoring initialized');
    }
  }

  private initializeMemoryMonitoring() {
    if ('memory' in performance) {
      const updateMemory = () => {
        const memInfo = (performance as any).memory;
        this.metrics.memoryInfo = {
          usedJSHeapSize: memInfo.usedJSHeapSize || 0,
          totalJSHeapSize: memInfo.totalJSHeapSize || 0,
          jsHeapSizeLimit: memInfo.jsHeapSizeLimit || 0
        };
        this.notifyListeners();
      };

      setInterval(updateMemory, 5000); // Update every 5 seconds
      updateMemory(); // Initial update

      console.log('[MobileDeviceManager] Memory monitoring initialized');
    }
  }

  private startRealtimeUpdates() {
    // Send periodic updates every 30 seconds to reduce system load
    setInterval(() => {
      this.notifyListeners();
    }, 30000);
  }

  public subscribe(callback: (metrics: MobileMetrics) => void): () => void {
    this.listeners.add(callback);
    
    // Send initial data
    callback(this.metrics);
    
    return () => {
      this.listeners.delete(callback);
    };
  }

  private notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener(this.metrics);
      } catch (error) {
        console.error('[MobileDeviceManager] Listener error:', error);
      }
    });
  }

  public getCapabilities(): MobileDeviceCapabilities {
    return { ...this.capabilities };
  }

  public getCurrentMetrics(): MobileMetrics {
    return { ...this.metrics };
  }

  public async requestPermissions(): Promise<void> {
    const requests: Promise<any>[] = [];

    // Request device motion permission (iOS)
    if (this.capabilities.deviceMotion && 
        typeof (DeviceMotionEvent as any).requestPermission === 'function') {
      requests.push((DeviceMotionEvent as any).requestPermission());
    }

    // Request device orientation permission (iOS)
    if (this.capabilities.deviceOrientation && 
        typeof (DeviceOrientationEvent as any).requestPermission === 'function') {
      requests.push((DeviceOrientationEvent as any).requestPermission());
    }

    try {
      await Promise.all(requests);
      console.log('[MobileDeviceManager] All permissions granted');
    } catch (error) {
      console.log('[MobileDeviceManager] Some permissions denied');
    }
  }

  public async vibrate(pattern: number | number[]): Promise<void> {
    if (this.capabilities.vibration) {
      navigator.vibrate(pattern);
    }
  }

  public async share(data: ShareData): Promise<void> {
    if (this.capabilities.share) {
      await navigator.share(data);
    } else {
      throw new Error('Share API not supported');
    }
  }

  public isReady(): boolean {
    return this.isInitialized;
  }

  public calculateUMatterFromMobile(): number {
    // Calculate UMatter based on authentic mobile interactions
    const touchEnergy = this.metrics.touchInteractions * 0.01; // 0.01 UMatter per touch
    
    // Motion energy from device movement
    const motionMagnitude = Math.sqrt(
      this.metrics.deviceMotion.acceleration.x ** 2 +
      this.metrics.deviceMotion.acceleration.y ** 2 +
      this.metrics.deviceMotion.acceleration.z ** 2
    );
    const motionEnergy = motionMagnitude * 0.005;
    
    // Battery charging bonus
    const chargingBonus = this.metrics.isCharging ? 1.2 : 1.0;
    
    // Network activity bonus
    const networkBonus = this.metrics.networkConnection.downlink > 5 ? 1.1 : 1.0;
    
    return (touchEnergy + motionEnergy) * chargingBonus * networkBonus;
  }
}

export const mobileDeviceManager = new MobileDeviceManager();