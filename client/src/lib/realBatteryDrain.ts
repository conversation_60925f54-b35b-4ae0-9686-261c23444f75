/**
 * REAL BATTERY DRAIN SYSTEM
 * Authentic battery monitoring and UMatter generation based on actual device usage
 */

interface BatteryInfo {
  level: number;
  charging: boolean;
  chargingTime?: number;
  dischargingTime?: number;
}

interface DrainMetrics {
  drainRate: number; // % per hour
  energyGenerated: number; // Ubits generated
  efficiency: number; // conversion efficiency
  timeActive: number; // milliseconds
}

class RealBatteryDrain {
  private battery: any = null;
  private lastLevel: number = 100;
  private lastUpdate: number = Date.now();
  private drainHistory: DrainMetrics[] = [];
  private isMonitoring: boolean = false;
  private updateInterval: ReturnType<typeof setInterval> | null = null;
  private listeners: Set<(metrics: DrainMetrics) => void> = new Set();

  constructor() {
    this.initializeBatteryAPI();
  }

  /**
   * Initialize the Battery API for real monitoring
   */
  private async initializeBatteryAPI(): Promise<void> {
    try {
      // Check if Battery API is available
      if ('getBattery' in navigator) {
        this.battery = await (navigator as any).getBattery();
        this.setupBatteryListeners();
        this.log('Battery API initialized successfully');
      } else if ('battery' in navigator) {
        this.battery = (navigator as any).battery;
        this.setupBatteryListeners();
        this.log('Legacy battery API initialized');
      } else {
        this.log('Battery API not available, using performance-based estimation', 'warn');
        this.initializePerformanceMonitoring();
      }
    } catch (error) {
      this.log(`Battery API initialization failed: ${error}`, 'error');
      this.initializePerformanceMonitoring();
    }
  }

  /**
   * Setup event listeners for battery changes
   */
  private setupBatteryListeners(): void {
    if (!this.battery) return;

    // Listen for battery level changes
    this.battery.addEventListener('levelchange', () => {
      this.handleBatteryChange();
    });

    // Listen for charging state changes
    this.battery.addEventListener('chargingchange', () => {
      this.handleChargingChange();
    });

    // Initial reading
    this.lastLevel = this.battery.level * 100;
    this.lastUpdate = Date.now();
  }

  /**
   * Handle actual battery level changes
   */
  private handleBatteryChange(): void {
    if (!this.battery || !this.isMonitoring) return;

    const currentLevel = this.battery.level * 100;
    const currentTime = Date.now();
    const timeDiff = currentTime - this.lastUpdate;

    // Only process if battery has actually drained (not charging)
    if (currentLevel < this.lastLevel && !this.battery.charging) {
      const drainAmount = this.lastLevel - currentLevel;
      const drainRate = (drainAmount / timeDiff) * 3600000; // % per hour

      const metrics: DrainMetrics = {
        drainRate,
        energyGenerated: this.calculateUbitsFromDrain(drainAmount),
        efficiency: this.calculateEfficiency(drainRate),
        timeActive: timeDiff
      };

      this.drainHistory.push(metrics);
      this.notifyListeners(metrics);
      this.log(`Battery drained ${drainAmount.toFixed(2)}% → Generated ${metrics.energyGenerated} Ubits`);
    }

    this.lastLevel = currentLevel;
    this.lastUpdate = currentTime;
  }

  /**
   * Handle charging state changes
   */
  private handleChargingChange(): void {
    if (!this.battery) return;

    if (this.battery.charging) {
      this.log('Device charging - pausing UMatter generation');
    } else {
      this.log('Device unplugged - resuming UMatter generation');
    }
  }

  /**
   * Initialize performance-based monitoring as fallback
   */
  private initializePerformanceMonitoring(): void {
    // Monitor CPU usage, memory usage, and network activity as proxy for battery drain
    let lastPerformance = performance.now();
    
    this.updateInterval = setInterval(() => {
      if (!this.isMonitoring) return;

      const currentPerformance = performance.now();
      const timeDiff = currentPerformance - lastPerformance;
      
      // Estimate battery drain based on system activity
      const estimatedDrain = this.estimateDrainFromPerformance(timeDiff);
      
      if (estimatedDrain > 0) {
        const metrics: DrainMetrics = {
          drainRate: estimatedDrain,
          energyGenerated: this.calculateUbitsFromDrain(estimatedDrain),
          efficiency: 0.85, // Estimated efficiency
          timeActive: timeDiff
        };

        this.drainHistory.push(metrics);
        this.notifyListeners(metrics);
      }

      lastPerformance = currentPerformance;
    }, 5000); // Check every 5 seconds
  }

  /**
   * Estimate battery drain from performance metrics
   */
  private estimateDrainFromPerformance(timeDiff: number): number {
    try {
      // Use memory info if available
      const memInfo = (performance as any).memory;
      let activityFactor = 1.0;

      if (memInfo) {
        const memoryUsage = memInfo.usedJSHeapSize / memInfo.totalJSHeapSize;
        activityFactor = 0.5 + (memoryUsage * 1.5); // Scale between 0.5 and 2.0
      }

      // Base drain rate: ~0.1% per minute for typical web browsing
      const baseDrainRate = 0.1 / 60000; // % per millisecond
      const estimatedDrain = baseDrainRate * timeDiff * activityFactor;

      return Math.min(estimatedDrain, 0.5); // Cap at 0.5% per measurement
    } catch (error) {
      return 0.02; // Default minimal drain
    }
  }

  /**
   * Calculate Ubits generated from battery drain
   * AUTHENTIC formula: 1% battery = 10,000 Ubits (user specification)
   */
  private calculateUbitsFromDrain(drainPercent: number): number {
    const AUTHENTIC_RATE = 10000; // Exact rate specified by user
    const efficiency = this.getCurrentEfficiency();
    
    // Real calculation with authentic conversion rate
    const ubits = Math.floor(drainPercent * AUTHENTIC_RATE * efficiency);
    
    console.log(`[AUTHENTIC BATTERY DRAIN] ${drainPercent.toFixed(3)}% → ${ubits} Ubits (Rate: ${AUTHENTIC_RATE}/%, Efficiency: ${efficiency.toFixed(2)})`);
    
    return ubits;
  }

  /**
   * Calculate conversion efficiency based on drain rate
   */
  private calculateEfficiency(drainRate: number): number {
    // Higher drain rates are more efficient (more intensive work = more UMatter)
    // Efficiency ranges from 0.6 to 1.2
    const baseEfficiency = 0.8;
    const rateBonus = Math.min(drainRate / 10, 0.4); // Up to 40% bonus for high drain rates
    
    return baseEfficiency + rateBonus;
  }

  /**
   * Get current conversion efficiency
   */
  private getCurrentEfficiency(): number {
    if (this.drainHistory.length === 0) return 0.8;
    
    // Average efficiency from recent history
    const recentHistory = this.drainHistory.slice(-10);
    const avgEfficiency = recentHistory.reduce((sum, metrics) => sum + metrics.efficiency, 0) / recentHistory.length;
    
    return avgEfficiency;
  }

  /**
   * Start monitoring battery drain
   */
  public startMonitoring(): void {
    this.isMonitoring = true;
    this.log('Real battery drain monitoring started');
  }

  /**
   * Stop monitoring battery drain
   */
  public stopMonitoring(): void {
    this.isMonitoring = false;
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
    this.log('Battery drain monitoring stopped');
  }

  /**
   * Get current battery status
   */
  public getCurrentBatteryInfo(): BatteryInfo | null {
    if (!this.battery) return null;

    return {
      level: this.battery.level * 100,
      charging: this.battery.charging,
      chargingTime: this.battery.chargingTime,
      dischargingTime: this.battery.dischargingTime
    };
  }

  /**
   * Get total Ubits generated today
   */
  public getTodayGeneration(): number {
    const today = new Date().toDateString();
    const todayHistory = this.drainHistory.filter(metrics => 
      new Date(this.lastUpdate - metrics.timeActive).toDateString() === today
    );
    
    return todayHistory.reduce((total, metrics) => total + metrics.energyGenerated, 0);
  }

  /**
   * Get drain statistics
   */
  public getDrainStats(): {
    totalGenerated: number;
    avgEfficiency: number;
    totalDrainTime: number;
    avgDrainRate: number;
  } {
    if (this.drainHistory.length === 0) {
      return {
        totalGenerated: 0,
        avgEfficiency: 0.8,
        totalDrainTime: 0,
        avgDrainRate: 0
      };
    }

    const totalGenerated = this.drainHistory.reduce((sum, metrics) => sum + metrics.energyGenerated, 0);
    const avgEfficiency = this.drainHistory.reduce((sum, metrics) => sum + metrics.efficiency, 0) / this.drainHistory.length;
    const totalDrainTime = this.drainHistory.reduce((sum, metrics) => sum + metrics.timeActive, 0);
    const avgDrainRate = this.drainHistory.reduce((sum, metrics) => sum + metrics.drainRate, 0) / this.drainHistory.length;

    return {
      totalGenerated,
      avgEfficiency,
      totalDrainTime,
      avgDrainRate
    };
  }

  /**
   * Subscribe to battery drain updates
   */
  public subscribe(callback: (metrics: DrainMetrics) => void): () => void {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  /**
   * Notify all listeners of new drain metrics
   */
  private notifyListeners(metrics: DrainMetrics): void {
    this.listeners.forEach(callback => {
      try {
        callback(metrics);
      } catch (error) {
        this.log(`Error in battery drain listener: ${error}`, 'error');
      }
    });
  }

  /**
   * Force a manual Ubit generation (for testing or manual claiming)
   */
  public manualGeneration(amount: number = 100): number {
    const metrics: DrainMetrics = {
      drainRate: 1.0, // Manual generation
      energyGenerated: amount,
      efficiency: 1.0,
      timeActive: 0
    };

    this.drainHistory.push(metrics);
    this.notifyListeners(metrics);
    this.log(`Manual generation: ${amount} Ubits`);

    return amount;
  }

  /**
   * Logging utility
   */
  private log(message: string, level: 'info' | 'warn' | 'error' = 'info'): void {
    const timestamp = new Date().toISOString();
    console[level](`[RealBatteryDrain] ${timestamp}: ${message}`);
  }
}

// Create singleton instance
export const realBatteryDrain = new RealBatteryDrain();