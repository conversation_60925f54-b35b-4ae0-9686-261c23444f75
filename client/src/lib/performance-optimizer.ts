/**
 * Performance Optimizer for nU Universe
 * Implements caching, lazy loading, and efficient data management
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

class PerformanceOptimizer {
  private cache = new Map<string, CacheEntry<any>>();
  private updateIntervals = new Map<string, number>();
  private subscriptions = new Set<() => void>();

  /**
   * API Response Caching with TTL
   */
  async cachedFetch<T>(url: string, ttl: number = 30000): Promise<T> {
    const cached = this.cache.get(url);
    
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }

    try {
      const response = await fetch(url);
      const data = await response.json();
      
      this.cache.set(url, {
        data,
        timestamp: Date.now(),
        ttl
      });
      
      return data;
    } catch (error) {
      // Return cached data if available during network failure
      if (cached) {
        console.warn(`[PerformanceOptimizer] Using stale cache for ${url}:`, error);
        return cached.data;
      }
      throw error;
    }
  }

  /**
   * Optimized Update Intervals
   */
  setOptimizedInterval(key: string, callback: () => void, interval: number): () => void {
    // Clear existing interval
    const existing = this.updateIntervals.get(key);
    if (existing) {
      clearInterval(existing);
    }

    // Set new optimized interval
    const id = setInterval(callback, interval);
    this.updateIntervals.set(key, id);

    // Return cleanup function
    const cleanup = () => {
      clearInterval(id);
      this.updateIntervals.delete(key);
    };
    
    this.subscriptions.add(cleanup);
    return cleanup;
  }

  /**
   * Batch API Requests
   */
  private requestQueue: Array<{ url: string; resolve: Function; reject: Function }> = [];
  private processingQueue = false;

  async batchRequest<T>(url: string): Promise<T> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ url, resolve, reject });
      
      if (!this.processingQueue) {
        this.processBatchRequests();
      }
    });
  }

  private async processBatchRequests() {
    this.processingQueue = true;
    
    // Group similar requests
    const grouped = new Map<string, Array<{ resolve: Function; reject: Function }>>();
    
    this.requestQueue.forEach(({ url, resolve, reject }) => {
      if (!grouped.has(url)) {
        grouped.set(url, []);
      }
      grouped.get(url)!.push({ resolve, reject });
    });

    this.requestQueue = [];

    // Process unique requests
    const promises = Array.from(grouped.entries()).map(async ([url, handlers]) => {
      try {
        const data = await this.cachedFetch(url);
        handlers.forEach(({ resolve }) => resolve(data));
      } catch (error) {
        handlers.forEach(({ reject }) => reject(error));
      }
    });

    await Promise.all(promises);
    this.processingQueue = false;

    // Process any new requests that came in
    if (this.requestQueue.length > 0) {
      setTimeout(() => this.processBatchRequests(), 10);
    }
  }

  /**
   * Memory Management
   */
  cleanup() {
    // Clear all intervals
    this.updateIntervals.forEach(id => clearInterval(id));
    this.updateIntervals.clear();

    // Clear cache older than 5 minutes
    const fiveMinutesAgo = Date.now() - 300000;
    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < fiveMinutesAgo) {
        this.cache.delete(key);
      }
    }

    // Clean up subscriptions
    this.subscriptions.forEach(cleanup => cleanup());
    this.subscriptions.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      cacheSize: this.cache.size,
      activeIntervals: this.updateIntervals.size,
      activeSubscriptions: this.subscriptions.size,
      memoryUsage: this.calculateMemoryUsage()
    };
  }

  private calculateMemoryUsage(): number {
    let totalSize = 0;
    this.cache.forEach(entry => {
      totalSize += JSON.stringify(entry.data).length;
    });
    return totalSize;
  }
}

export const performanceOptimizer = new PerformanceOptimizer();

// Auto-cleanup every 5 minutes
setInterval(() => {
  performanceOptimizer.cleanup();
}, 300000);