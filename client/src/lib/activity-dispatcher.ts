/**
 * Activity Dispatcher - Enhanced real-time activity tracking
 * Provides visual cues and function monitoring like Cursor/VS Code
 */

export interface ActivityEvent {
  type: 'function_call' | 'system_analysis' | 'code_generation' | 'file_edit' | 'repair' | 'optimization' | 'learning' | 'diagnosis';
  title: string;
  description: string;
  details?: string;
  status: 'starting' | 'in_progress' | 'completed' | 'failed' | 'warning';
  metadata?: any;
  functionName?: string;
  duration?: number;
  file?: string;
  lines?: [number, number];
}

class ActivityDispatcher {
  private listeners: Set<(event: ActivityEvent) => void> = new Set();
  private functionCallQueue: Map<string, ActivityEvent> = new Map();

  constructor() {
    this.initializeMonitoring();
  }

  private initializeMonitoring() {
    // Override console methods to capture SpUnder activities
    this.setupConsoleInterception();
    
    // Monitor function executions
    this.setupFunctionMonitoring();
    
    // Start periodic system checks
    this.startPeriodicChecks();
    
    // Initialize with welcome message
    setTimeout(() => {
      this.dispatch({
        type: 'system_analysis',
        title: 'Activity Dispatcher Initialized',
        description: 'Real-time function monitoring and visual feedback system active',
        status: 'completed',
        functionName: 'initializeDispatcher',
        details: 'All system activities, function calls, and AI operations are now being tracked'
      });
    }, 500);
  }

  private setupConsoleInterception() {
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalError = console.error;

    console.log = (...args) => {
      originalLog.apply(console, args);
      this.parseConsoleActivity('log', args);
    };

    console.warn = (...args) => {
      originalWarn.apply(console, args);
      this.parseConsoleActivity('warn', args);
    };

    console.error = (...args) => {
      originalError.apply(console, args);
      this.parseConsoleActivity('error', args);
    };
  }

  private setupFunctionMonitoring() {
    // Monitor key system functions
    if (typeof window !== 'undefined') {
      // Track function calls on window object
      this.wrapFunctionCalls();
    }
  }

  private wrapFunctionCalls() {
    // Wrap common system functions to track their execution
    const functionsToTrack = [
      'fetch',
      'XMLHttpRequest'
    ];

    // Override fetch to track API calls
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const startTime = performance.now();
      const url = args[0]?.toString() || 'unknown';
      
      this.dispatch({
        type: 'function_call',
        title: 'API Call',
        description: `Calling ${url}`,
        status: 'starting',
        functionName: 'fetch',
        metadata: { url }
      });

      try {
        const result = await originalFetch.apply(window, args);
        const duration = performance.now() - startTime;
        
        this.dispatch({
          type: 'function_call',
          title: 'API Call Complete',
          description: `${url} - ${result.status} ${result.statusText}`,
          status: result.ok ? 'completed' : 'failed',
          functionName: 'fetch',
          duration,
          metadata: { url, status: result.status }
        });

        return result;
      } catch (error) {
        const duration = performance.now() - startTime;
        
        this.dispatch({
          type: 'function_call',
          title: 'API Call Failed',
          description: `${url} - ${error.message}`,
          status: 'failed',
          functionName: 'fetch',
          duration,
          metadata: { url, error: error.message }
        });

        throw error;
      }
    };
  }

  private parseConsoleActivity(level: string, args: any[]) {
    if (!args[0] || typeof args[0] !== 'string') return;

    const message = args[0];

    // Parse SpUnder Bot activities
    if (message.includes('[SpUnderBot]')) {
      this.parseSpUnderBotActivity(message, args);
    } else if (message.includes('[AdvancedSpUnderBot]')) {
      this.parseAdvancedBotActivity(message, args);
    } else if (message.includes('[AuthenticDeviceManager]')) {
      this.parseDeviceActivity(message, args);
    } else if (message.includes('[EnergySyncController]')) {
      this.parseEnergyActivity(message, args);
    } else if (message.includes('[WalletFix]')) {
      this.parseWalletActivity(message, args);
    } else if (message.includes('[SystemInitializer]')) {
      this.parseSystemActivity(message, args);
    }
  }

  private parseSpUnderBotActivity(message: string, args: any[]) {
    if (message.includes('Running comprehensive system diagnostics')) {
      this.dispatch({
        type: 'diagnosis',
        title: 'System Diagnostics Started',
        description: 'Running comprehensive health check',
        status: 'in_progress',
        functionName: 'runDiagnostics',
        details: 'Scanning wallet, extension, buttons, energy generation, and API endpoints'
      });
    } else if (message.includes('Executing repair task')) {
      const taskType = message.match(/task: (\w+)/)?.[1];
      this.dispatch({
        type: 'repair',
        title: `Repair Task: ${taskType}`,
        description: `Executing automated repair for ${taskType?.replace('_', ' ')}`,
        status: 'in_progress',
        functionName: 'executeRepair',
        metadata: { taskType }
      });
    } else if (message.includes('Repair task completed')) {
      const taskType = message.match(/completed: (\w+)/)?.[1];
      this.dispatch({
        type: 'repair',
        title: 'Repair Completed',
        description: `Successfully repaired ${taskType?.replace('_', ' ')} system`,
        status: 'completed',
        functionName: 'executeRepair',
        metadata: { taskType }
      });
    } else if (message.includes('Issue reported')) {
      const issueType = message.match(/: (\w+) -/)?.[1];
      const issueDescription = message.split(' - ')[1];
      this.dispatch({
        type: 'diagnosis',
        title: 'Issue Detected',
        description: `Found ${issueType?.replace('_', ' ')} issue`,
        status: 'warning',
        functionName: 'reportIssue',
        details: issueDescription,
        metadata: { issueType }
      });
    }
  }

  private parseAdvancedBotActivity(message: string, args: any[]) {
    if (message.includes('Autonomous system butler initialized')) {
      this.dispatch({
        type: 'system_analysis',
        title: 'Advanced AI Butler Online',
        description: 'Autonomous development capabilities activated',
        status: 'completed',
        functionName: 'initializeAI',
        details: 'Code generation, architecture analysis, and learning engine are now active'
      });
    } else if (message.includes('Generating code')) {
      this.dispatch({
        type: 'code_generation',
        title: 'Code Generation',
        description: 'AI is generating new code',
        status: 'in_progress',
        functionName: 'generateCode'
      });
    } else if (message.includes('Learning from')) {
      this.dispatch({
        type: 'learning',
        title: 'AI Learning',
        description: 'System is learning from new patterns',
        status: 'in_progress',
        functionName: 'learnFromData'
      });
    }
  }

  private parseDeviceActivity(message: string, args: any[]) {
    if (message.includes('AUTHENTIC MacBook METRICS')) {
      const metrics = args[1];
      this.dispatch({
        type: 'function_call',
        title: 'Device Metrics Updated',
        description: `Battery: ${metrics?.battery}, Memory: ${metrics?.memory}`,
        status: 'completed',
        functionName: 'updateDeviceMetrics',
        metadata: metrics
      });
    } else if (message.includes('UMatter generated')) {
      const amount = args[1];
      this.dispatch({
        type: 'optimization',
        title: 'Energy Generated',
        description: `Generated ${amount?.toFixed?.(3) || amount} UMatter`,
        status: 'completed',
        functionName: 'generateEnergy',
        metadata: { amount }
      });
    }
  }

  private parseEnergyActivity(message: string, args: any[]) {
    if (message.includes('Batch processed successfully')) {
      const data = args[1];
      this.dispatch({
        type: 'optimization',
        title: 'Energy Batch Processed',
        description: `Processed ${data?.batchSize || 'multiple'} energy transactions`,
        status: 'completed',
        functionName: 'processBatch',
        details: `Generated ${data?.totalAmount?.toFixed?.(6) || 'N/A'} UMatter`,
        metadata: data
      });
    }
  }

  private parseWalletActivity(message: string, args: any[]) {
    if (message.includes('Wallet updated')) {
      const balance = args[1];
      this.dispatch({
        type: 'function_call',
        title: 'Wallet Updated',
        description: `Balance: ${balance?.toFixed?.(2) || balance} UMatter`,
        status: 'completed',
        functionName: 'updateWallet',
        metadata: { balance }
      });
    }
  }

  private parseSystemActivity(message: string, args: any[]) {
    if (message.includes('system initialization')) {
      this.dispatch({
        type: 'system_analysis',
        title: 'System Initialization',
        description: 'Initializing nU Universe components',
        status: 'in_progress',
        functionName: 'initializeSystem'
      });
    }
  }

  private startPeriodicChecks() {
    // Simulate periodic AI activities
    setInterval(() => {
      this.dispatch({
        type: 'system_analysis',
        title: 'Health Check',
        description: 'Routine system health monitoring',
        status: 'completed',
        functionName: 'healthCheck'
      });
    }, 30000); // Every 30 seconds
  }

  public dispatch(event: Omit<ActivityEvent, 'timestamp'>) {
    const fullEvent = {
      ...event,
      timestamp: Date.now()
    };

    // Dispatch to custom event system
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('spunder-activity', {
        detail: fullEvent
      }));
    }

    // Notify direct listeners
    this.listeners.forEach(listener => listener(fullEvent));
  }

  public subscribe(listener: (event: ActivityEvent) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  // Public methods for manual activity tracking
  public trackFunction(name: string, description: string) {
    this.dispatch({
      type: 'function_call',
      title: `Function: ${name}`,
      description,
      status: 'starting',
      functionName: name
    });
  }

  public trackFileEdit(file: string, lines?: [number, number]) {
    this.dispatch({
      type: 'file_edit',
      title: 'File Modified',
      description: `Editing ${file}`,
      status: 'in_progress',
      file,
      lines
    });
  }

  public trackCodeGeneration(description: string) {
    this.dispatch({
      type: 'code_generation',
      title: 'Code Generation',
      description,
      status: 'starting',
      functionName: 'generateCode'
    });
  }
}

// Global instance
export const activityDispatcher = new ActivityDispatcher();

// Helper functions for easy tracking
export const trackActivity = (event: Omit<ActivityEvent, 'timestamp'>) => {
  activityDispatcher.dispatch(event);
};

export const trackFunction = (name: string, description: string) => {
  activityDispatcher.trackFunction(name, description);
};

export const trackFileEdit = (file: string, lines?: [number, number]) => {
  activityDispatcher.trackFileEdit(file, lines);
};

export const trackCodeGeneration = (description: string) => {
  activityDispatcher.trackCodeGeneration(description);
};