/**
 * SpUnder - Silent Privacy Underground
 * Cryptographic interaction tracking with DID-based authentication
 */

export interface SpUnderConfig {
  encryptionLevel: 1 | 2 | 3 | 4 | 5;
  anonymousMode: boolean;
  didAuthentication: boolean;
  silentTracking: boolean;
}

export interface SpUnderInteraction {
  id: string;
  type: 'click' | 'scroll' | 'focus' | 'navigation' | 'api_call';
  element?: string;
  timestamp: number;
  metadata: Record<string, any>;
  encrypted: boolean;
  hash: string;
}

export interface SpUnderSession {
  sessionId: string;
  userId?: string;
  startTime: number;
  interactions: SpUnderInteraction[];
  config: SpUnderConfig;
  merkleRoot?: string;
}

export class SpUnder {
  private config: SpUnderConfig;
  private currentSession: SpUnderSession | null = null;
  private interactionBuffer: SpUnderInteraction[] = [];
  private readonly BUFFER_SIZE = 50;

  constructor(config: Partial<SpUnderConfig> = {}) {
    this.config = {
      encryptionLevel: 3,
      anonymousMode: false,
      didAuthentication: true,
      silentTracking: true,
      ...config
    };

    if (typeof window !== 'undefined' && this.config.silentTracking) {
      this.initializeSilentTracking();
    }
  }

  /**
   * Start a new SpUnder tracking session
   */
  async startSession(userId?: string): Promise<string> {
    const sessionId = this.generateSessionId();
    
    this.currentSession = {
      sessionId,
      userId: this.config.anonymousMode ? undefined : userId,
      startTime: Date.now(),
      interactions: [],
      config: { ...this.config }
    };

    // Store session in localStorage for persistence
    if (typeof window !== 'undefined') {
      localStorage.setItem('spunder_session', JSON.stringify(this.currentSession));
    }

    return sessionId;
  }

  /**
   * Track an interaction event
   */
  trackInteraction(
    type: SpUnderInteraction['type'],
    element?: string,
    metadata: Record<string, any> = {}
  ): SpUnderInteraction {
    const interaction: SpUnderInteraction = {
      id: this.generateInteractionId(),
      type,
      element,
      timestamp: Date.now(),
      metadata: {
        ...metadata,
        userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : '',
        url: typeof window !== 'undefined' ? window.location.href : '',
        sessionId: this.currentSession?.sessionId
      },
      encrypted: this.config.encryptionLevel > 1,
      hash: ''
    };

    // Encrypt interaction if required
    if (interaction.encrypted) {
      interaction.hash = this.encryptInteraction(interaction);
    }

    // Add to current session
    if (this.currentSession) {
      this.currentSession.interactions.push(interaction);
    }

    // Add to buffer for batch processing
    this.interactionBuffer.push(interaction);

    // Process buffer if full
    if (this.interactionBuffer.length >= this.BUFFER_SIZE) {
      this.processInteractionBuffer();
    }

    return interaction;
  }

  /**
   * Initialize silent tracking of user interactions
   */
  private initializeSilentTracking(): void {
    if (typeof window === 'undefined') return;

    // Track clicks
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      this.trackInteraction('click', this.getElementSelector(target), {
        x: event.clientX,
        y: event.clientY,
        button: event.button
      });
    }, { passive: true });

    // Track scrolling
    let scrollTimeout: NodeJS.Timeout;
    document.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        this.trackInteraction('scroll', '', {
          scrollY: window.scrollY,
          scrollX: window.scrollX
        });
      }, 100);
    }, { passive: true });

    // Track focus changes
    document.addEventListener('focusin', (event) => {
      const target = event.target as HTMLElement;
      this.trackInteraction('focus', this.getElementSelector(target));
    }, { passive: true });

    // Track navigation
    const originalPushState = history.pushState;
    const originalReplaceState = history.replaceState;

    history.pushState = function(...args) {
      originalPushState.apply(this, args);
      window.dispatchEvent(new Event('spunder-navigation'));
    };

    history.replaceState = function(...args) {
      originalReplaceState.apply(this, args);
      window.dispatchEvent(new Event('spunder-navigation'));
    };

    window.addEventListener('popstate', () => {
      window.dispatchEvent(new Event('spunder-navigation'));
    });

    window.addEventListener('spunder-navigation', () => {
      this.trackInteraction('navigation', '', {
        url: window.location.href,
        referrer: document.referrer
      });
    });
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 9);
    return `spunder_${timestamp}_${random}`;
  }

  /**
   * Generate a unique interaction ID
   */
  private generateInteractionId(): string {
    return `int_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get a CSS selector for an element
   */
  private getElementSelector(element: HTMLElement): string {
    if (element.id) {
      return `#${element.id}`;
    }
    
    if (element.className && typeof element.className === 'string') {
      return `.${element.className.split(' ').join('.')}`;
    }
    
    return element.tagName.toLowerCase();
  }

  /**
   * Encrypt interaction data using SpUnder algorithm
   */
  private encryptInteraction(interaction: SpUnderInteraction): string {
    // Simple hash-based encryption for demo
    // In production, use proper cryptographic libraries
    const data = JSON.stringify({
      id: interaction.id,
      type: interaction.type,
      timestamp: interaction.timestamp,
      sessionId: this.currentSession?.sessionId
    });
    
    return this.simpleHash(data + this.config.encryptionLevel.toString());
  }

  /**
   * Simple hash function for demonstration
   */
  private simpleHash(str: string): string {
    let hash = 0;
    if (str.length === 0) return hash.toString();
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    
    return Math.abs(hash).toString(16);
  }

  /**
   * Process buffered interactions
   */
  private async processInteractionBuffer(): Promise<void> {
    if (this.interactionBuffer.length === 0) return;

    const interactions = [...this.interactionBuffer];
    this.interactionBuffer = [];

    // Generate Merkle tree root for integrity verification
    const merkleRoot = this.generateMerkleRoot(interactions);
    
    if (this.currentSession) {
      this.currentSession.merkleRoot = merkleRoot;
    }

    // Send to server (if not in anonymous mode)
    if (!this.config.anonymousMode && this.currentSession?.userId) {
      try {
        await this.sendInteractionsToServer(interactions);
      } catch (error) {
        console.warn('[SpUnder] Failed to send interactions to server:', error);
        // Re-add to buffer for retry
        this.interactionBuffer.unshift(...interactions);
      }
    }
  }

  /**
   * Generate Merkle tree root for interaction integrity
   */
  private generateMerkleRoot(interactions: SpUnderInteraction[]): string {
    if (interactions.length === 0) return '';
    
    let hashes = interactions.map(i => i.hash || this.simpleHash(i.id));
    
    while (hashes.length > 1) {
      const newHashes: string[] = [];
      for (let i = 0; i < hashes.length; i += 2) {
        const left = hashes[i];
        const right = hashes[i + 1] || left;
        newHashes.push(this.simpleHash(left + right));
      }
      hashes = newHashes;
    }
    
    return hashes[0];
  }

  /**
   * Send interactions to server
   */
  private async sendInteractionsToServer(interactions: SpUnderInteraction[]): Promise<void> {
    const response = await fetch('/api/spunder/interactions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        sessionId: this.currentSession?.sessionId,
        interactions,
        merkleRoot: this.currentSession?.merkleRoot
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to send interactions: ${response.statusText}`);
    }
  }

  /**
   * Get current session data
   */
  getCurrentSession(): SpUnderSession | null {
    return this.currentSession;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<SpUnderConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (this.currentSession) {
      this.currentSession.config = { ...this.config };
    }
  }

  /**
   * End current session
   */
  async endSession(): Promise<void> {
    if (!this.currentSession) return;

    // Process any remaining interactions
    await this.processInteractionBuffer();

    // Clear session data
    this.currentSession = null;
    
    if (typeof window !== 'undefined') {
      localStorage.removeItem('spunder_session');
    }
  }

  /**
   * Get interaction statistics
   */
  getSessionStats(): {
    totalInteractions: number;
    interactionTypes: Record<string, number>;
    sessionDuration: number;
    encryptedInteractions: number;
  } {
    if (!this.currentSession) {
      return {
        totalInteractions: 0,
        interactionTypes: {},
        sessionDuration: 0,
        encryptedInteractions: 0
      };
    }

    const { interactions, startTime } = this.currentSession;
    const interactionTypes: Record<string, number> = {};
    let encryptedInteractions = 0;

    interactions.forEach(interaction => {
      interactionTypes[interaction.type] = (interactionTypes[interaction.type] || 0) + 1;
      if (interaction.encrypted) encryptedInteractions++;
    });

    return {
      totalInteractions: interactions.length,
      interactionTypes,
      sessionDuration: Date.now() - startTime,
      encryptedInteractions
    };
  }
}

// Global SpUnder instance
export const spunder = new SpUnder();
