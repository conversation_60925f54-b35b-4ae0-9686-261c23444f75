/**
 * Wallet Fix - Ensures wallet updates work properly
 */

class WalletFix {
  private updateInterval: NodeJS.Timeout | null = null;
  private isUpdating: boolean = false;

  constructor() {
    this.initialize();
    console.log('[WalletFix] Wallet update system initialized');
  }

  private initialize(): void {
    // Start periodic wallet updates
    this.startPeriodicUpdates();
    
    // Listen for manual update triggers
    this.setupEventListeners();
    
    // Force initial update
    this.forceUpdate();
  }

  private startPeriodicUpdates(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }

    this.updateInterval = setInterval(() => {
      this.updateWalletData();
    }, 5000); // Update every 5 seconds
  }

  private setupEventListeners(): void {
    // Listen for wallet update events
    window.addEventListener('walletUpdateRequired', () => {
      this.forceUpdate();
    });

    // Listen for energy generation events
    window.addEventListener('energyGenerated', (event: any) => {
      setTimeout(() => this.updateWalletData(), 1000); // Update 1 second after energy generation
    });
  }

  private async updateWalletData(): Promise<void> {
    if (this.isUpdating) return;
    
    this.isUpdating = true;

    try {
      // Fetch latest banking balance
      const bankingResponse = await fetch('/api/banking/balance', {
        headers: { 'Cache-Control': 'no-cache' }
      });

      if (bankingResponse.ok) {
        const bankingData = await bankingResponse.json();
        
        // Fetch wallet balance
        const walletResponse = await fetch('/api/wallet/balance', {
          headers: { 'Cache-Control': 'no-cache' }
        });

        if (walletResponse.ok) {
          const walletData = await walletResponse.json();
          
          // Combine data and dispatch update
          const combinedBalance = {
            umatter: bankingData.umatterBalance || walletData.umatter || 0,
            tru: bankingData.truBalance || walletData.tru || 0,
            nuva: bankingData.nuvaBalance || walletData.nuva || 0,
            inurtia: bankingData.inurtiaBalance || walletData.inurtia || 0,
            ubits: bankingData.ubitsBalance || walletData.ubits || 0,
            totalEnergyGenerated: bankingData.totalEnergyGenerated || 0,
            lastUpdate: Date.now()
          };

          // Dispatch wallet update event
          window.dispatchEvent(new CustomEvent('walletDataUpdated', {
            detail: combinedBalance
          }));

          // Update UI elements directly
          this.updateUIElements(combinedBalance);
          
          console.log('[WalletFix] Wallet updated:', combinedBalance.umatter, 'UMatter');
        }
      }
    } catch (error) {
      console.error('[WalletFix] Update failed:', error);
    } finally {
      this.isUpdating = false;
    }
  }

  private updateUIElements(balance: any): void {
    // Find and update wallet balance displays
    const balanceElements = document.querySelectorAll('[data-balance-type]');
    
    balanceElements.forEach(element => {
      const balanceType = element.getAttribute('data-balance-type');
      
      if (balanceType && balance[balanceType] !== undefined) {
        const value = typeof balance[balanceType] === 'number' 
          ? balance[balanceType].toFixed(2) 
          : balance[balanceType];
        
        if (element.textContent !== value) {
          element.textContent = value;
          
          // Add visual feedback for updates
          element.classList.add('balance-updated');
          setTimeout(() => {
            element.classList.remove('balance-updated');
          }, 1000);
        }
      }
    });

    // Update any generic balance displays
    const genericBalanceElements = document.querySelectorAll('.wallet-balance, .umatter-balance, .energy-balance');
    
    genericBalanceElements.forEach(element => {
      if (element.textContent && !element.textContent.includes(balance.umatter.toFixed(2))) {
        element.textContent = `${balance.umatter.toFixed(2)} UMatter`;
      }
    });
  }

  public forceUpdate(): void {
    console.log('[WalletFix] Force update triggered');
    this.updateWalletData();
  }

  public stop(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }
}

// Add CSS for balance update animation
const style = document.createElement('style');
style.textContent = `
  .balance-updated {
    animation: balanceFlash 1s ease-in-out;
  }
  
  @keyframes balanceFlash {
    0% { background-color: rgba(34, 197, 94, 0.2); }
    50% { background-color: rgba(34, 197, 94, 0.4); }
    100% { background-color: transparent; }
  }
`;
document.head.appendChild(style);

// Initialize wallet fix
export const walletFix = new WalletFix();