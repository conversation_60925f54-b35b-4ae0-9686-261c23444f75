/**
 * Real Hardware Connector - Direct connection to Node.js hardware APIs
 * Replaces all simulated energy with authentic MacBook hardware metrics
 */

interface RealHardwareEnergy {
  umatterGenerated: number;
  source: string;
  authentic: boolean;
  metrics: {
    cpuUsage: number;
    memoryUsage: number;
    powerConsumption: number;
  };
}

export class RealHardwareConnector {
  private static instance: RealHardwareConnector;
  private isConnected = false;
  
  static getInstance(): RealHardwareConnector {
    if (!RealHardwareConnector.instance) {
      RealHardwareConnector.instance = new RealHardwareConnector();
    }
    return RealHardwareConnector.instance;
  }

  /**
   * Connect directly to server's real Node.js hardware APIs
   */
  async connectToRealHardware(): Promise<RealHardwareEnergy | null> {
    try {
      const response = await fetch('/api/energy/real-hardware-metrics', {
        method: 'GET'
      });

      if (!response.ok) {
        // Return null instead of throwing to prevent cascade failures
        console.log(`[RealHardwareConnector] Hardware API unavailable: ${response.status}`);
        this.isConnected = false;
        return null;
      }

      const realData = await response.json();
      this.isConnected = true;

      console.log(`[RealHardwareConnector] AUTHENTIC: ${realData.authenticEnergy.toFixed(6)} UMatter from real Node.js hardware`);
      console.log(`[RealHardwareConnector] Real metrics: CPU ${realData.metrics.cpuUsage.toFixed(2)}%, Memory ${realData.metrics.memoryUsage.toFixed(1)}MB, Power ${realData.metrics.powerConsumption.toFixed(2)}W`);
      
      return {
        umatterGenerated: realData.authenticEnergy,
        source: realData.source,
        authentic: realData.authentic,
        metrics: realData.metrics
      };
    } catch (error) {
      console.log('[RealHardwareConnector] Failed to connect to real hardware:', {});
      this.isConnected = false;
      return null;
    }
  }

  /**
   * Start continuous real hardware energy generation
   */
  startRealHardwareGeneration(callback: (energy: RealHardwareEnergy) => void): void {
    const generateFromRealHardware = async () => {
      const realEnergy = await this.connectToRealHardware();
      if (realEnergy && realEnergy.authentic) {
        callback(realEnergy);
      }
    };

    // Generate energy from real hardware every 5 seconds
    setInterval(generateFromRealHardware, 5000);
    
    // Initial generation
    generateFromRealHardware();
  }

  isConnectedToRealHardware(): boolean {
    return this.isConnected;
  }
}

export const realHardwareConnector = RealHardwareConnector.getInstance();