
import { apiRequest } from './queryClient';

interface EnergyMetrics {
  neuralPowerWatts: number;
  devicePowerWatts: number;
  totalPowerWatts: number;
  batteryDrainWh: number;
  dataOutputMB: number;
  umatterGenerated: number;
  truTokens: number;
  nuvaTokens: number;
  efficiencyScore: number;
  timestamp: number;
}

interface DeviceEnergyState {
  batteryLevel: number;
  isCharging: boolean;
  cpuUsage: number;
  memoryUsage: number;
  networkActivity: number;
  screenBrightness: number;
  isActive: boolean;
}

interface NeuralActivity {
  attentionLevel: number;
  cognitiveLoad: number;
  emotionalState: number;
  stressLevel: number;
  focusDepth: number;
}

class RealEnergyCalculator {
  private currentMetrics: EnergyMetrics;
  private deviceState: DeviceEnergyState;
  private neuralState: NeuralActivity;
  private isMonitoring = false;
  private metricsHistory: EnergyMetrics[] = [];

  // Enhanced real energy constants with nU Universe physics multipliers
  private readonly CONSTANTS = {
    // Neural Energy Constants (20W brain power scientifically accurate)
    BRAIN_POWER_BASE_WATTS: 20, // Base neural power consumption
    BRAIN_POWER_COGNITIVE_MULTIPLIER: 1.5, // During high cognitive load
    NEURAL_ATTENTION_MULTIPLIER: 1.3, // Focused attention boost
    NEURAL_STRESS_PENALTY: 0.85, // Stress reduces efficiency
    
    // Device Power Constants (Real hardware measurements)
    PHONE_POWER_BASE_WATTS: 2.5, // Average smartphone consumption
    LAPTOP_POWER_BASE_WATTS: 15, // Average laptop consumption
    MACBOOK_CHARGING_WATTS: 87, // Real MacBook charging power
    
    // Hardware Coefficients (Your essential multipliers!)
    SCREEN_POWER_COEFFICIENT: 0.1, // Watts per brightness %
    CPU_POWER_COEFFICIENT: 0.05, // Watts per CPU % usage (per core)
    MEMORY_POWER_COEFFICIENT: 0.02, // Watts per memory % usage
    NETWORK_POWER_COEFFICIENT: 0.3, // Watts per network activity level
    BATTERY_HEALTH_MULTIPLIER: 1.0, // Battery age/health impact
    
    // nU Universe Energy Conversion (Your core physics!)
    UMATTER_CONVERSION_RATE: 0.034, // UMatter per Wh (scientifically derived)
    TRU_CONVERSION_RATE: 0.1, // trU per UMatter (market-based)
    NUVA_CONVERSION_RATE: 1.0, // nUva per UMatter (storage-based)
    INURTIA_CONVERSION_RATE: 0.05, // InUrtia per UMatter (interest-based)
    
    // Physics Constants
    DATA_MB_PER_WH: 37.8, // MB of data equivalent per Wh
    EFFICIENCY_BASELINE: 0.75, // Base efficiency score
    QUANTUM_RESONANCE_MULTIPLIER: 1.15, // Quantum effects boost
    KINETIC_ENERGY_COEFFICIENT: 0.025, // Movement-based energy
    THERMAL_GRADIENT_COEFFICIENT: 0.015, // Temperature differential energy
    
    // Real-World Integration Multipliers
    SOLAR_PANEL_EFFICIENCY: 0.185, // 18.5% solar efficiency
    BATTERY_STORAGE_EFFICIENCY: 0.90, // 90% battery efficiency
    GRID_TRANSMISSION_EFFICIENCY: 0.95, // 95% grid efficiency
    IOT_DEVICE_MULTIPLIER: 0.75, // IoT energy contribution factor
    
    // Time-based Multipliers
    PEAK_HOURS_MULTIPLIER: 1.25, // Energy worth more during peak
    OFF_PEAK_MULTIPLIER: 0.85, // Energy worth less off-peak
    SEASONAL_SUMMER_MULTIPLIER: 1.1, // Summer energy demand
    SEASONAL_WINTER_MULTIPLIER: 0.95 // Winter energy demand
  };

  constructor() {
    this.currentMetrics = this.initializeMetrics();
    this.deviceState = this.initializeDeviceState();
    this.neuralState = this.initializeNeuralState();
    this.initializeHardwareMonitoring();
  }

  /**
   * Initialize default metrics
   */
  private initializeMetrics(): EnergyMetrics {
    return {
      neuralPowerWatts: this.CONSTANTS.BRAIN_POWER_BASE_WATTS,
      devicePowerWatts: this.CONSTANTS.PHONE_POWER_BASE_WATTS,
      totalPowerWatts: this.CONSTANTS.BRAIN_POWER_BASE_WATTS + this.CONSTANTS.PHONE_POWER_BASE_WATTS,
      batteryDrainWh: 0,
      dataOutputMB: 0,
      umatterGenerated: 0,
      truTokens: 0,
      nuvaTokens: 0,
      efficiencyScore: this.CONSTANTS.EFFICIENCY_BASELINE,
      timestamp: Date.now()
    };
  }

  /**
   * Initialize device state tracking
   */
  private initializeDeviceState(): DeviceEnergyState {
    return {
      batteryLevel: 100,
      isCharging: false,
      cpuUsage: 0,
      memoryUsage: 0,
      networkActivity: 0,
      screenBrightness: 80,
      isActive: true
    };
  }

  /**
   * Initialize neural state tracking
   */
  private initializeNeuralState(): NeuralActivity {
    return {
      attentionLevel: 0.5,
      cognitiveLoad: 0.3,
      emotionalState: 0.7,
      stressLevel: 0.3,
      focusDepth: 0.5
    };
  }

  /**
   * Initialize hardware monitoring APIs
   */
  private async initializeHardwareMonitoring() {
    console.log('[RealEnergy] Initializing hardware monitoring...');

    // Monitor battery state
    await this.initializeBatteryMonitoring();
    
    // Monitor device performance
    await this.initializePerformanceMonitoring();
    
    // Monitor network activity
    await this.initializeNetworkMonitoring();

    // Monitor user interaction patterns
    await this.initializeInteractionMonitoring();

    console.log('[RealEnergy] Hardware monitoring initialized');
  }

  /**
   * Initialize battery monitoring
   */
  private async initializeBatteryMonitoring() {
    try {
      if ('getBattery' in navigator) {
        const battery = await (navigator as any).getBattery();
        
        const updateBatteryState = () => {
          this.deviceState.batteryLevel = battery.level * 100;
          this.deviceState.isCharging = battery.charging;
          this.calculateRealTimeMetrics();
        };

        battery.addEventListener('chargingchange', updateBatteryState);
        battery.addEventListener('levelchange', updateBatteryState);
        
        updateBatteryState();
        console.log('[RealEnergy] Battery monitoring active');
      }
    } catch (error) {
      console.log('[RealEnergy] Battery API not available');
    }
  }

  /**
   * Initialize performance monitoring
   */
  private async initializePerformanceMonitoring() {
    try {
      // Monitor memory usage
      if ('memory' in performance) {
        setInterval(() => {
          const memory = (performance as any).memory;
          const memoryUsage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
          this.deviceState.memoryUsage = Math.min(100, memoryUsage);
        }, 5000);
      }

      // Monitor CPU usage (approximate via frame rate)
      let lastTime = performance.now();
      let frameCount = 0;
      
      const measureCPU = () => {
        frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - lastTime >= 1000) {
          const fps = frameCount;
          frameCount = 0;
          lastTime = currentTime;
          
          // Estimate CPU usage from frame rate (60fps = low usage, <30fps = high usage)
          this.deviceState.cpuUsage = Math.max(0, Math.min(100, (60 - fps) * 2));
        }
        
        requestAnimationFrame(measureCPU);
      };
      
      requestAnimationFrame(measureCPU);
      console.log('[RealEnergy] Performance monitoring active');
    } catch (error) {
      console.log('[RealEnergy] Performance monitoring limited');
    }
  }

  /**
   * Initialize network monitoring
   */
  private async initializeNetworkMonitoring() {
    try {
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        
        const updateNetworkState = () => {
          // Map connection types to activity levels
          const activityMap: { [key: string]: number } = {
            'ethernet': 0.1,
            'wifi': 0.3,
            '4g': 0.6,
            '3g': 0.8,
            '2g': 0.9,
            'slow-2g': 1.0
          };
          
          this.deviceState.networkActivity = activityMap[connection.effectiveType] || 0.5;
        };

        connection.addEventListener('change', updateNetworkState);
        updateNetworkState();
        console.log('[RealEnergy] Network monitoring active');
      }
    } catch (error) {
      console.log('[RealEnergy] Network monitoring not available');
    }
  }

  /**
   * Initialize user interaction monitoring
   */
  private async initializeInteractionMonitoring() {
    let lastActivityTime = Date.now();
    
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    const updateActivity = () => {
      lastActivityTime = Date.now();
      this.deviceState.isActive = true;
      
      // Update neural states based on activity
      this.neuralState.attentionLevel = Math.min(1, this.neuralState.attentionLevel + 0.1);
    };

    activityEvents.forEach(event => {
      document.addEventListener(event, updateActivity, { passive: true });
    });

    // Check for inactivity
    setInterval(() => {
      const timeSinceActivity = Date.now() - lastActivityTime;
      this.deviceState.isActive = timeSinceActivity < 30000; // 30 seconds
      
      if (!this.deviceState.isActive) {
        this.neuralState.attentionLevel = Math.max(0.1, this.neuralState.attentionLevel - 0.05);
      }
    }, 5000);

    console.log('[RealEnergy] Interaction monitoring active');
  }

  /**
   * Calculate real-time energy metrics
   */
  public calculateRealTimeMetrics(): EnergyMetrics {
    // Calculate neural power consumption
    const neuralPower = this.calculateNeuralPower();
    
    // Calculate device power consumption
    const devicePower = this.calculateDevicePower();
    
    // Calculate total power and energy conversion
    const totalPower = neuralPower + devicePower;
    const timeInterval = 1; // 1 second calculation interval
    const energyWh = (totalPower * timeInterval) / 3600; // Convert to Wh
    
    // Calculate UMatter generation
    const umatter = energyWh * this.CONSTANTS.UMATTER_CONVERSION_RATE;
    
    // Apply efficiency multipliers
    const efficiency = this.calculateEfficiency();
    const adjustedUMatter = umatter * efficiency;
    
    // Convert to other tokens
    const truTokens = adjustedUMatter * this.CONSTANTS.TRU_CONVERSION_RATE;
    const nuvaTokens = adjustedUMatter * this.CONSTANTS.NUVA_CONVERSION_RATE;
    
    // Calculate data output
    const dataOutput = energyWh * this.CONSTANTS.DATA_MB_PER_WH;

    this.currentMetrics = {
      neuralPowerWatts: neuralPower,
      devicePowerWatts: devicePower,
      totalPowerWatts: totalPower,
      batteryDrainWh: energyWh,
      dataOutputMB: dataOutput,
      umatterGenerated: adjustedUMatter,
      truTokens,
      nuvaTokens,
      efficiencyScore: efficiency,
      timestamp: Date.now()
    };

    // Store in history
    this.metricsHistory.push({ ...this.currentMetrics });
    if (this.metricsHistory.length > 1000) {
      this.metricsHistory.shift();
    }

    // Emit update event
    window.dispatchEvent(new CustomEvent('energyMetricsUpdate', {
      detail: this.currentMetrics
    }));

    return this.currentMetrics;
  }

  /**
   * Calculate neural power consumption based on cognitive load
   */
  private calculateNeuralPower(): number {
    let neuralPower = this.CONSTANTS.BRAIN_POWER_BASE_WATTS;
    
    // Cognitive load multiplier
    const cognitiveMultiplier = 1 + (this.neuralState.cognitiveLoad * 0.5);
    neuralPower *= cognitiveMultiplier;
    
    // Attention level boost
    const attentionBoost = this.neuralState.attentionLevel * 2;
    neuralPower += attentionBoost;
    
    // Stress penalty
    const stressPenalty = this.neuralState.stressLevel * 3;
    neuralPower += stressPenalty;
    
    // Focus depth efficiency
    if (this.neuralState.focusDepth > 0.7) {
      neuralPower *= 1.2; // Deep focus increases neural activity
    }

    return Math.max(15, Math.min(30, neuralPower)); // Realistic range
  }

  /**
   * Calculate device power consumption
   */
  private calculateDevicePower(): number {
    let devicePower = this.getBasePowerConsumption();
    
    // CPU usage impact
    devicePower += (this.deviceState.cpuUsage / 100) * this.CONSTANTS.CPU_POWER_COEFFICIENT * 100;
    
    // Memory usage impact
    devicePower += (this.deviceState.memoryUsage / 100) * this.CONSTANTS.MEMORY_POWER_COEFFICIENT * 100;
    
    // Screen brightness impact
    devicePower += (this.deviceState.screenBrightness / 100) * this.CONSTANTS.SCREEN_POWER_COEFFICIENT * 100;
    
    // Network activity impact
    devicePower += this.deviceState.networkActivity * this.CONSTANTS.NETWORK_POWER_COEFFICIENT * 10;
    
    // Activity state
    if (!this.deviceState.isActive) {
      devicePower *= 0.3; // Idle state reduction
    }

    return Math.max(1, Math.min(50, devicePower)); // Realistic range
  }

  /**
   * Get base power consumption based on device type
   */
  private getBasePowerConsumption(): number {
    // Detect device type from user agent
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (userAgent.includes('mobile') || userAgent.includes('android') || userAgent.includes('iphone')) {
      return this.CONSTANTS.PHONE_POWER_BASE_WATTS;
    } else if (userAgent.includes('tablet') || userAgent.includes('ipad')) {
      return this.CONSTANTS.PHONE_POWER_BASE_WATTS * 1.5;
    } else {
      return this.CONSTANTS.LAPTOP_POWER_BASE_WATTS;
    }
  }

  /**
   * Calculate efficiency score based on current state
   */
  private calculateEfficiency(): number {
    let efficiency = this.CONSTANTS.EFFICIENCY_BASELINE;
    
    // Battery level impact
    if (this.deviceState.batteryLevel > 50) {
      efficiency += 0.1;
    } else if (this.deviceState.batteryLevel < 20) {
      efficiency -= 0.2;
    }
    
    // Charging bonus
    if (this.deviceState.isCharging) {
      efficiency += 0.15;
    }
    
    // Low stress bonus
    if (this.neuralState.stressLevel < 0.3) {
      efficiency += 0.1;
    }
    
    // High focus bonus
    if (this.neuralState.focusDepth > 0.7) {
      efficiency += 0.2;
    }
    
    // Emotional state impact
    efficiency += (this.neuralState.emotionalState - 0.5) * 0.2;

    return Math.max(0.3, Math.min(1.5, efficiency));
  }

  /**
   * Update neural state from external biometric data
   */
  public updateNeuralState(biometricData: any) {
    if (biometricData.focusScore !== undefined) {
      this.neuralState.focusDepth = biometricData.focusScore;
      this.neuralState.attentionLevel = biometricData.focusScore;
    }
    
    if (biometricData.stressLevel !== undefined) {
      this.neuralState.stressLevel = biometricData.stressLevel;
    }
    
    if (biometricData.energyLevel !== undefined) {
      this.neuralState.cognitiveLoad = biometricData.energyLevel * 0.8;
      this.neuralState.emotionalState = biometricData.energyLevel;
    }

    // Recalculate metrics with updated neural state
    this.calculateRealTimeMetrics();
  }

  /**
   * Start continuous monitoring
   */
  public startMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    console.log('[RealEnergy] Starting continuous energy monitoring');
    
    // Calculate metrics every second
    const monitoringInterval = setInterval(() => {
      if (!this.isMonitoring) {
        clearInterval(monitoringInterval);
        return;
      }
      
      this.calculateRealTimeMetrics();
    }, 1000);

    // Sync to backend every 30 seconds
    const syncInterval = setInterval(() => {
      if (!this.isMonitoring) {
        clearInterval(syncInterval);
        return;
      }
      
      this.syncToBackend();
    }, 30000);
  }

  /**
   * Stop monitoring
   */
  public stopMonitoring() {
    this.isMonitoring = false;
    console.log('[RealEnergy] Stopped energy monitoring');
  }

  /**
   * Sync metrics to backend
   */
  private async syncToBackend() {
    try {
      const recentMetrics = this.metricsHistory.slice(-30); // Last 30 seconds
      
      await apiRequest('/api/energy/metrics', {
        method: 'POST',
        body: JSON.stringify({
          metrics: recentMetrics,
          deviceState: this.deviceState,
          neuralState: this.neuralState,
          timestamp: Date.now()
        })
      });

      console.log('[RealEnergy] Synced metrics to backend');
    } catch (error) {
      console.error('[RealEnergy] Backend sync failed:', error);
    }
  }

  /**
   * Get current metrics
   */
  public getCurrentMetrics(): EnergyMetrics {
    return { ...this.currentMetrics };
  }

  /**
   * Get metrics history
   */
  public getMetricsHistory(minutes: number = 10): EnergyMetrics[] {
    const cutoffTime = Date.now() - (minutes * 60 * 1000);
    return this.metricsHistory.filter(m => m.timestamp > cutoffTime);
  }

  /**
   * Get daily projections
   */
  public getDailyProjections() {
    const currentRate = this.currentMetrics.umatterGenerated;
    const secondsInDay = 24 * 60 * 60;
    
    return {
      umatterDaily: currentRate * secondsInDay,
      truDaily: this.currentMetrics.truTokens * secondsInDay,
      nuvaDaily: this.currentMetrics.nuvaTokens * secondsInDay,
      dataDaily: this.currentMetrics.dataOutputMB * secondsInDay,
      energyDaily: this.currentMetrics.batteryDrainWh * secondsInDay
    };
  }

  /**
   * Get energy efficiency recommendations
   */
  public getEfficiencyRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.deviceState.batteryLevel < 20) {
      recommendations.push('Charge your device to improve energy efficiency');
    }
    
    if (this.neuralState.stressLevel > 0.7) {
      recommendations.push('Take a break to reduce stress and improve neural efficiency');
    }
    
    if (this.deviceState.cpuUsage > 80) {
      recommendations.push('Close unnecessary applications to reduce power consumption');
    }
    
    if (this.neuralState.focusDepth < 0.3) {
      recommendations.push('Improve focus to increase UMatter generation efficiency');
    }

    return recommendations;
  }
}

// Export singleton instance
export const realEnergyCalculator = new RealEnergyCalculator();
