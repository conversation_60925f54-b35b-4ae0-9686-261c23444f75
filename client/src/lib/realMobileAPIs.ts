/**
 * Real Mobile APIs - Authentic browser API access for mobile devices
 * Zero fallbacks or mock data - only real device capabilities
 */

interface MobileCapabilities {
  battery: {
    level: number;
    charging: boolean;
    chargingTime: number;
    dischargingTime: number;
  };
  motion: {
    acceleration: { x: number; y: number; z: number } | null;
    accelerationIncludingGravity: { x: number; y: number; z: number } | null;
    rotationRate: { alpha: number; beta: number; gamma: number } | null;
  };
  orientation: {
    alpha: number;
    beta: number;
    gamma: number;
  } | null;
  network: {
    effectiveType: string;
    downlink: number;
    rtt: number;
    saveData: boolean;
  };
  touch: {
    maxTouchPoints: number;
    touchSupported: boolean;
  };
  location: {
    latitude: number;
    longitude: number;
    accuracy: number;
  } | null;
}

class RealMobileAPIs {
  private capabilities: MobileCapabilities = {
    battery: { level: 0, charging: false, chargingTime: 0, dischargingTime: 0 },
    motion: { acceleration: null, accelerationIncludingGravity: null, rotationRate: null },
    orientation: null,
    network: { effectiveType: '4g', downlink: 0, rtt: 0, saveData: false },
    touch: { maxTouchPoints: 0, touchSupported: false },
    location: null
  };
  
  private listeners: Set<(capabilities: MobileCapabilities) => void> = new Set();
  private isInitialized = false;

  async initialize(): Promise<boolean> {
    console.log('[RealMobileAPIs] Initializing authentic mobile device capabilities...');
    
    try {
      // Real Battery API
      await this.initializeBatteryAPI();
      
      // Real Device Motion API
      await this.initializeMotionAPI();
      
      // Real Device Orientation API
      await this.initializeOrientationAPI();
      
      // Real Network Information API
      await this.initializeNetworkAPI();
      
      // Real Touch API
      this.initializeTouchAPI();
      
      // Real Geolocation API (with permission)
      await this.initializeLocationAPI();
      
      this.isInitialized = true;
      console.log('[RealMobileAPIs] ✅ Mobile device APIs initialized:', this.capabilities);
      this.notifyListeners();
      
      return true;
    } catch (error) {
      console.error('[RealMobileAPIs] Failed to initialize mobile APIs:', error);
      return false;
    }
  }

  private async initializeBatteryAPI(): Promise<void> {
    try {
      if ('getBattery' in navigator && typeof navigator.getBattery === 'function') {
        const battery = await navigator.getBattery();
        
        this.capabilities.battery = {
          level: battery.level,
          charging: battery.charging,
          chargingTime: battery.chargingTime,
          dischargingTime: battery.dischargingTime
        };
        
        // Set up real-time battery event listeners
        battery.addEventListener('chargingchange', () => {
          this.capabilities.battery.charging = battery.charging;
          this.notifyListeners();
        });
        
        battery.addEventListener('levelchange', () => {
          this.capabilities.battery.level = battery.level;
          this.notifyListeners();
        });
        
        console.log('[RealMobileAPIs] ✅ Real battery API connected:', {
          level: `${(battery.level * 100).toFixed(1)}%`,
          charging: battery.charging
        });
      }
    } catch (error) {
      console.log('[RealMobileAPIs] Battery API not available on this device');
    }
  }

  private async initializeMotionAPI(): Promise<void> {
    try {
      // Request permission for iOS 13+
      if (typeof DeviceMotionEvent !== 'undefined' && 'requestPermission' in DeviceMotionEvent) {
        const permission = await (DeviceMotionEvent as any).requestPermission();
        if (permission !== 'granted') {
          console.log('[RealMobileAPIs] Device motion permission denied');
          return;
        }
      }
      
      if ('DeviceMotionEvent' in window) {
        window.addEventListener('devicemotion', (event) => {
          this.capabilities.motion = {
            acceleration: event.acceleration ? {
              x: event.acceleration.x || 0,
              y: event.acceleration.y || 0,
              z: event.acceleration.z || 0
            } : null,
            accelerationIncludingGravity: event.accelerationIncludingGravity ? {
              x: event.accelerationIncludingGravity.x || 0,
              y: event.accelerationIncludingGravity.y || 0,
              z: event.accelerationIncludingGravity.z || 0
            } : null,
            rotationRate: event.rotationRate ? {
              alpha: event.rotationRate.alpha || 0,
              beta: event.rotationRate.beta || 0,
              gamma: event.rotationRate.gamma || 0
            } : null
          };
          this.notifyListeners();
        });
        
        console.log('[RealMobileAPIs] ✅ Device motion API connected');
      }
    } catch (error) {
      console.log('[RealMobileAPIs] Device motion API not available');
    }
  }

  private async initializeOrientationAPI(): Promise<void> {
    try {
      // Request permission for iOS 13+
      if (typeof DeviceOrientationEvent !== 'undefined' && 'requestPermission' in DeviceOrientationEvent) {
        const permission = await (DeviceOrientationEvent as any).requestPermission();
        if (permission !== 'granted') {
          console.log('[RealMobileAPIs] Device orientation permission denied');
          return;
        }
      }
      
      if ('DeviceOrientationEvent' in window) {
        window.addEventListener('deviceorientation', (event) => {
          this.capabilities.orientation = {
            alpha: event.alpha || 0,
            beta: event.beta || 0,
            gamma: event.gamma || 0
          };
          this.notifyListeners();
        });
        
        console.log('[RealMobileAPIs] ✅ Device orientation API connected');
      }
    } catch (error) {
      console.log('[RealMobileAPIs] Device orientation API not available');
    }
  }

  private async initializeNetworkAPI(): Promise<void> {
    try {
      const connection = (navigator as any).connection || 
                       (navigator as any).mozConnection || 
                       (navigator as any).webkitConnection;
      
      if (connection) {
        this.capabilities.network = {
          effectiveType: connection.effectiveType || '4g',
          downlink: connection.downlink || 0,
          rtt: connection.rtt || 0,
          saveData: connection.saveData || false
        };
        
        connection.addEventListener('change', () => {
          this.capabilities.network = {
            effectiveType: connection.effectiveType || '4g',
            downlink: connection.downlink || 0,
            rtt: connection.rtt || 0,
            saveData: connection.saveData || false
          };
          this.notifyListeners();
        });
        
        console.log('[RealMobileAPIs] ✅ Network information API connected:', this.capabilities.network);
      }
    } catch (error) {
      console.log('[RealMobileAPIs] Network information API not available');
    }
  }

  private initializeTouchAPI(): void {
    try {
      this.capabilities.touch = {
        maxTouchPoints: navigator.maxTouchPoints || 0,
        touchSupported: 'ontouchstart' in window || navigator.maxTouchPoints > 0
      };
      
      console.log('[RealMobileAPIs] ✅ Touch API connected:', this.capabilities.touch);
    } catch (error) {
      console.log('[RealMobileAPIs] Touch API not available');
    }
  }

  private async initializeLocationAPI(): Promise<void> {
    try {
      if ('geolocation' in navigator) {
        const options = {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        };
        
        navigator.geolocation.getCurrentPosition(
          (position) => {
            this.capabilities.location = {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
              accuracy: position.coords.accuracy
            };
            console.log('[RealMobileAPIs] ✅ Geolocation API connected');
            this.notifyListeners();
          },
          (error) => {
            console.log('[RealMobileAPIs] Geolocation permission denied or unavailable:', error.message);
          },
          options
        );
      }
    } catch (error) {
      console.log('[RealMobileAPIs] Geolocation API not available');
    }
  }

  public subscribe(callback: (capabilities: MobileCapabilities) => void): () => void {
    this.listeners.add(callback);
    if (this.isInitialized) {
      callback(this.capabilities);
    }
    return () => this.listeners.delete(callback);
  }

  private notifyListeners(): void {
    this.listeners.forEach(callback => {
      try {
        callback(this.capabilities);
      } catch (error) {
        console.error('[RealMobileAPIs] Error notifying listener:', error);
      }
    });
  }

  public getCapabilities(): MobileCapabilities {
    return { ...this.capabilities };
  }

  public isMobile(): boolean {
    return this.capabilities.touch.touchSupported || 
           /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }

  public calculateEnergyFromMotion(): number {
    if (!this.capabilities.motion.acceleration) return 0;
    
    const { x, y, z } = this.capabilities.motion.acceleration;
    const magnitude = Math.sqrt(x * x + y * y + z * z);
    
    // Convert motion to energy: 1 m/s² = 0.001 UMatter
    return magnitude * 0.001;
  }

  public calculateEnergyFromTouch(touchCount: number): number {
    // Each touch interaction generates 0.1 UMatter
    return touchCount * 0.1;
  }

  public calculateEnergyFromBattery(): number {
    const { level, charging } = this.capabilities.battery;
    
    // Charging device generates more energy
    const baseEnergy = level * 10; // Battery level contributes to energy
    const chargingBonus = charging ? 5 : 0;
    
    return (baseEnergy + chargingBonus) * 0.01; // Convert to UMatter scale
  }
}

export const realMobileAPIs = new RealMobileAPIs();