
/**
 * NUTShell Network - P2P Network Layer for nU Universe
 * Provides network connectivity for GhostBot and other services
 */

interface NetworkPeer {
  id: string;
  lastSeen: number;
  verified: boolean;
  capabilities: string[];
}

interface NetworkStatus {
  peers: number;
  networkHealth: number;
  lastUpdate: number;
}

class NUTShellNetwork {
  private peers: Map<string, NetworkPeer> = new Map();
  private isActive: boolean = false;
  private networkHealth: number = 0;
  private lastUpdate: number = 0;

  constructor() {
    this.initialize();
  }

  private async initialize() {
    console.log('[NUTShellNetwork] Initializing P2P network layer');
    this.isActive = true;
    this.updateNetworkHealth();
    
    // Start periodic health updates
    setInterval(() => {
      this.updateNetworkHealth();
    }, 30000);
  }

  private updateNetworkHealth() {
    // Calculate network health based on active peers and connectivity
    const activePeers = Array.from(this.peers.values())
      .filter(peer => Date.now() - peer.lastSeen < 60000).length;
    
    this.networkHealth = Math.min(1.0, activePeers / 10); // Optimal at 10+ peers
    this.lastUpdate = Date.now();
    
    console.log(`[NUTShellNetwork] Health: ${(this.networkHealth * 100).toFixed(1)}%, Peers: ${activePeers}`);
  }

  addPeer(peerId: string, capabilities: string[] = []): void {
    this.peers.set(peerId, {
      id: peerId,
      lastSeen: Date.now(),
      verified: true,
      capabilities
    });
    
    this.updateNetworkHealth();
    console.log(`[NUTShellNetwork] Added peer: ${peerId}`);
  }

  removePeer(peerId: string): void {
    this.peers.delete(peerId);
    this.updateNetworkHealth();
    console.log(`[NUTShellNetwork] Removed peer: ${peerId}`);
  }

  getNetworkStatus(): NetworkStatus {
    const activePeers = Array.from(this.peers.values())
      .filter(peer => Date.now() - peer.lastSeen < 60000).length;

    return {
      peers: activePeers,
      networkHealth: this.networkHealth,
      lastUpdate: this.lastUpdate
    };
  }

  getPeers(): NetworkPeer[] {
    return Array.from(this.peers.values())
      .filter(peer => Date.now() - peer.lastSeen < 60000);
  }

  isConnected(): boolean {
    return this.isActive && this.peers.size > 0;
  }

  broadcast(message: any): void {
    if (!this.isActive) return;

    console.log('[NUTShellNetwork] Broadcasting message to', this.peers.size, 'peers');
    // In a real implementation, this would use WebRTC or WebSocket connections
  }

  // Compatibility methods for GhostBot
  on(event: string, callback: (data: any) => void): void {
    // Event listener for network events
    console.log(`[NUTShellNetwork] Registered listener for: ${event}`);
  }

  emit(event: string, data: any): void {
    // Emit network events
    console.log(`[NUTShellNetwork] Emitted event: ${event}`);
  }
}

// Singleton instance
let nutshellNetworkInstance: NUTShellNetwork | null = null;

export function getNUTShellNetwork(): NUTShellNetwork {
  if (!nutshellNetworkInstance) {
    nutshellNetworkInstance = new NUTShellNetwork();
  }
  return nutshellNetworkInstance;
}

export { NUTShellNetwork };
export type { NetworkPeer, NetworkStatus };
/**
 * NUTShell Network - Decentralized Network Management
 * Handles peer-to-peer connections and network synchronization
 */

export interface NetworkPeer {
  id: string;
  address: string;
  lastSeen: number;
  reliability: number;
  capabilities: string[];
}

export interface NetworkStatus {
  connected: boolean;
  peerCount: number;
  networkHealth: number;
  lastSync: number;
}

class NUTShellNetwork {
  private peers: Map<string, NetworkPeer> = new Map();
  private isConnected: boolean = false;
  private networkHealth: number = 100;
  private lastSyncTime: number = 0;
  private syncInterval: number | null = null;

  constructor() {
    this.initialize();
  }

  private initialize() {
    console.log('[NUTShellNetwork] Initializing decentralized network');
    this.startNetworkMonitoring();
    this.isConnected = true;
  }

  /**
   * Start network monitoring and health checks
   */
  private startNetworkMonitoring() {
    this.syncInterval = window.setInterval(() => {
      this.performNetworkSync();
      this.updateNetworkHealth();
    }, 30000); // Sync every 30 seconds
  }

  /**
   * Perform network synchronization
   */
  private performNetworkSync() {
    this.lastSyncTime = Date.now();
    
    // Simulate peer discovery and sync
    this.discoveryPeers();
    
    console.log(`[NUTShellNetwork] Network sync completed. Peers: ${this.peers.size}`);
  }

  /**
   * Discover and add network peers
   */
  private discoveryPeers() {
    // Simulate discovering peers on the network
    const peerCount = 3 + Math.floor(Math.random() * 5); // 3-7 peers
    
    for (let i = 0; i < peerCount; i++) {
      const peerId = `peer_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
      const peer: NetworkPeer = {
        id: peerId,
        address: `192.168.1.${100 + i}:8080`,
        lastSeen: Date.now(),
        reliability: 0.8 + Math.random() * 0.2, // 80-100% reliability
        capabilities: ['energy_sync', 'data_exchange', 'quantum_processing']
      };
      
      this.peers.set(peerId, peer);
    }

    // Remove old peers (older than 5 minutes)
    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
    for (const [peerId, peer] of this.peers.entries()) {
      if (peer.lastSeen < fiveMinutesAgo) {
        this.peers.delete(peerId);
      }
    }
  }

  /**
   * Update network health based on peer status
   */
  private updateNetworkHealth() {
    const peerCount = this.peers.size;
    const avgReliability = Array.from(this.peers.values())
      .reduce((sum, peer) => sum + peer.reliability, 0) / Math.max(peerCount, 1);

    // Calculate health based on peer count and reliability
    let health = 0;
    
    if (peerCount > 0) {
      health = Math.min(100, (peerCount * 10) + (avgReliability * 50));
    }
    
    this.networkHealth = health;
  }

  /**
   * Get current network status
   */
  public getStatus(): NetworkStatus {
    return {
      connected: this.isConnected,
      peerCount: this.peers.size,
      networkHealth: this.networkHealth,
      lastSync: this.lastSyncTime
    };
  }

  /**
   * Get list of active peers
   */
  public getPeers(): NetworkPeer[] {
    return Array.from(this.peers.values());
  }

  /**
   * Connect to a specific peer
   */
  public async connectToPeer(address: string): Promise<boolean> {
    try {
      const peerId = `manual_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;
      const peer: NetworkPeer = {
        id: peerId,
        address,
        lastSeen: Date.now(),
        reliability: 0.9,
        capabilities: ['energy_sync', 'data_exchange']
      };
      
      this.peers.set(peerId, peer);
      console.log(`[NUTShellNetwork] Connected to peer: ${address}`);
      return true;
    } catch (error) {
      console.error(`[NUTShellNetwork] Failed to connect to peer: ${address}`, error);
      return false;
    }
  }

  /**
   * Broadcast data to all peers
   */
  public async broadcast(data: any): Promise<number> {
    let successCount = 0;
    
    for (const peer of this.peers.values()) {
      try {
        // Simulate broadcasting to peer
        if (Math.random() < peer.reliability) {
          successCount++;
        }
      } catch (error) {
        console.warn(`[NUTShellNetwork] Failed to broadcast to ${peer.id}`);
      }
    }
    
    console.log(`[NUTShellNetwork] Broadcast successful to ${successCount}/${this.peers.size} peers`);
    return successCount;
  }

  /**
   * Sync energy data across the network
   */
  public async syncEnergyData(energyData: any): Promise<boolean> {
    try {
      const syncResult = await this.broadcast({
        type: 'energy_sync',
        data: energyData,
        timestamp: Date.now()
      });
      
      return syncResult > 0;
    } catch (error) {
      console.error('[NUTShellNetwork] Energy sync failed:', error);
      return false;
    }
  }

  /**
   * Get network statistics
   */
  public getStats() {
    return {
      totalPeers: this.peers.size,
      connectedPeers: Array.from(this.peers.values()).filter(p => 
        Date.now() - p.lastSeen < 60000
      ).length,
      averageReliability: Array.from(this.peers.values())
        .reduce((sum, peer) => sum + peer.reliability, 0) / Math.max(this.peers.size, 1),
      networkHealth: this.networkHealth,
      uptime: Date.now() - this.lastSyncTime,
      isConnected: this.isConnected
    };
  }

  /**
   * Disconnect from the network
   */
  public disconnect() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    
    this.peers.clear();
    this.isConnected = false;
    
    console.log('[NUTShellNetwork] Disconnected from network');
  }

  /**
   * Reconnect to the network
   */
  public reconnect() {
    this.disconnect();
    this.initialize();
    
    console.log('[NUTShellNetwork] Reconnected to network');
  }
}

// Create singleton instance
const networkInstance = new NUTShellNetwork();

export { NUTShellNetwork };
export default networkInstance;

export function getNUTShellNetwork(): NUTShellNetwork {
  // Add to window for debugging/global access
  if (typeof window !== 'undefined' && !(window as any).nutshellNetwork) {
    (window as any).nutshellNetwork = networkInstance;
  }

  return networkInstance;
}
