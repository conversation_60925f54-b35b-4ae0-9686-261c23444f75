
/**
 * NUTShell Network - P2P Network Layer for nU Universe
 * Provides network connectivity for GhostBot and other services
 */

interface NetworkPeer {
  id: string;
  lastSeen: number;
  verified: boolean;
  capabilities: string[];
}

interface NetworkStatus {
  peers: number;
  networkHealth: number;
  lastUpdate: number;
}

class NUTShellNetwork {
  private peers: Map<string, NetworkPeer> = new Map();
  private isActive: boolean = false;
  private networkHealth: number = 0;
  private lastUpdate: number = 0;

  constructor() {
    this.initialize();
  }

  private async initialize() {
    console.log('[NUTShellNetwork] Initializing P2P network layer');
    this.isActive = true;
    this.updateNetworkHealth();
    
    // Start periodic health updates
    setInterval(() => {
      this.updateNetworkHealth();
    }, 30000);
  }

  private updateNetworkHealth() {
    // Calculate network health based on active peers and connectivity
    const activePeers = Array.from(this.peers.values())
      .filter(peer => Date.now() - peer.lastSeen < 60000).length;
    
    this.networkHealth = Math.min(1.0, activePeers / 10); // Optimal at 10+ peers
    this.lastUpdate = Date.now();
    
    console.log(`[NUTShellNetwork] Health: ${(this.networkHealth * 100).toFixed(1)}%, Peers: ${activePeers}`);
  }

  addPeer(peerId: string, capabilities: string[] = []): void {
    this.peers.set(peerId, {
      id: peerId,
      lastSeen: Date.now(),
      verified: true,
      capabilities
    });
    
    this.updateNetworkHealth();
    console.log(`[NUTShellNetwork] Added peer: ${peerId}`);
  }

  removePeer(peerId: string): void {
    this.peers.delete(peerId);
    this.updateNetworkHealth();
    console.log(`[NUTShellNetwork] Removed peer: ${peerId}`);
  }

  getNetworkStatus(): NetworkStatus {
    const activePeers = Array.from(this.peers.values())
      .filter(peer => Date.now() - peer.lastSeen < 60000).length;

    return {
      peers: activePeers,
      networkHealth: this.networkHealth,
      lastUpdate: this.lastUpdate
    };
  }

  getPeers(): NetworkPeer[] {
    return Array.from(this.peers.values())
      .filter(peer => Date.now() - peer.lastSeen < 60000);
  }

  isConnected(): boolean {
    return this.isActive && this.peers.size > 0;
  }

  broadcast(message: any): void {
    if (!this.isActive) return;

    console.log('[NUTShellNetwork] Broadcasting message to', this.peers.size, 'peers');
    // In a real implementation, this would use WebRTC or WebSocket connections
  }

  // Compatibility methods for GhostBot
  on(event: string, callback: (data: any) => void): void {
    // Event listener for network events
    console.log(`[NUTShellNetwork] Registered listener for: ${event}`);
  }

  emit(event: string, data: any): void {
    // Emit network events
    console.log(`[NUTShellNetwork] Emitted event: ${event}`);
  }
}

// Singleton instance
let nutshellNetworkInstance: NUTShellNetwork | null = null;

export function getNUTShellNetwork(): NUTShellNetwork {
  if (!nutshellNetworkInstance) {
    nutshellNetworkInstance = new NUTShellNetwork();
  }
  return nutshellNetworkInstance;
}

export { NUTShellNetwork };
export type { NetworkPeer, NetworkStatus };



