/**
 * Real Network Scanner for Browser Environment
 * Uses WebRTC and modern browser APIs to discover real devices
 */

interface DiscoveredDevice {
  id: string;
  name: string;
  type: 'iPhone' | 'Android' | 'iPad' | 'Laptop';
  signal: string;
  distance: string;
  lastSeen: number;
  capabilities: string[];
}

export class RealNetworkScanner {
  private devices = new Map<string, DiscoveredDevice>();
  private isScanning = false;
  private listeners = new Set<(devices: DiscoveredDevice[]) => void>();

  /**
   * Start real device discovery using browser APIs
   */
  async startDiscovery(): Promise<void> {
    if (this.isScanning) return;
    
    this.isScanning = true;
    console.log('[RealNetworkScanner] Starting real device discovery...');

    // Clear previous devices
    this.devices.clear();

    try {
      // Use multiple discovery methods
      await Promise.all([
        this.discoverWebRTCPeers(),
        this.discoverBluetoothDevices(),
        this.discoverNetworkDevices(),
        this.discoverServiceWorkerClients()
      ]);

      this.notifyListeners();
    } catch (error) {
      console.error('[RealNetworkScanner] Discovery failed:', error);
    }
  }

  /**
   * Discover devices via WebRTC peer connections
   */
  private async discoverWebRTCPeers(): Promise<void> {
    try {
      console.log('[RealNetworkScanner] Scanning for WebRTC peers...');
      
      const pc = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
      });

      // Create data channel for P2P discovery
      const channel = pc.createDataChannel('device-discovery');
      
      channel.onopen = () => {
        console.log('[RealNetworkScanner] WebRTC channel opened for device discovery');
        channel.send(JSON.stringify({
          type: 'nU-discovery',
          message: 'Looking for nU Universe devices',
          timestamp: Date.now()
        }));
      };

      // Get local network info via ICE candidates
      pc.onicecandidate = (event) => {
        if (event.candidate && event.candidate.candidate.includes('192.168.')) {
          const match = event.candidate.candidate.match(/(\d+\.\d+\.\d+\.\d+)/);
          if (match) {
            const localIP = match[1];
            console.log(`[RealNetworkScanner] Local IP detected: ${localIP}`);
            this.scanLocalNetwork(localIP);
          }
        }
      };

      await pc.createOffer().then(offer => pc.setLocalDescription(offer));
      
      // Cleanup after discovery
      setTimeout(() => {
        pc.close();
      }, 5000);

    } catch (error) {
      console.log('[RealNetworkScanner] WebRTC discovery not available:', (error as Error).message);
    }
  }

  /**
   * Scan local network based on detected IP
   */
  private async scanLocalNetwork(localIP: string): Promise<void> {
    const baseNetwork = localIP.substring(0, localIP.lastIndexOf('.'));
    console.log(`[RealNetworkScanner] Scanning network ${baseNetwork}.1-254...`);

    // REAL network scanning implementation
    try {
      for (let i = 1; i <= 254; i++) {
        const targetIP = `${baseNetwork}.${i}`;
        
        try {
          // Attempt real network connection test
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 1000);

          const response = await fetch(`http://${targetIP}:80`, {
            method: 'HEAD',
            mode: 'no-cors',
            signal: controller.signal
          });

          clearTimeout(timeoutId);
          
          // If we get any response, device exists
          const discoveredDevice: DiscoveredDevice = {
            id: `real_${targetIP.replace(/\./g, '_')}`,
            name: `Device at ${targetIP}`,
            type: 'Laptop' as const,
            signal: 'Connected',
            distance: 'Local Network',
            lastSeen: Date.now(),
            capabilities: ['network-reachable']
          };
          
          this.devices.set(discoveredDevice.id, discoveredDevice);
          console.log(`[RealNetworkScanner] ✅ Real device found: ${targetIP}`);
          
        } catch (error) {
          // Device not reachable - continue scanning
          continue;
        }
      }
      
      this.notifyListeners();
      console.log(`[RealNetworkScanner] Network scan complete: ${this.devices.size} real devices found`);
      
    } catch (error) {
      console.error('[RealNetworkScanner] Real network scan failed:', error);
      // Only if real scanning completely fails, log the failure
      console.log('[RealNetworkScanner] No devices discovered - network may be isolated');
    }
  }

  /**
   * Discover Bluetooth devices
   */
  private async discoverBluetoothDevices(): Promise<void> {
    try {
      if ('bluetooth' in navigator) {
        console.log('[RealNetworkScanner] Scanning for Bluetooth devices...');
        
        const device = await (navigator as any).bluetooth.requestDevice({
          acceptAllDevices: true,
          optionalServices: ['battery_service', 'device_information']
        });

        if (device) {
          const bluetoothDevice: DiscoveredDevice = {
            id: `bluetooth_${device.id}`,
            name: device.name || 'Bluetooth Device',
            type: 'Laptop', // Default for Bluetooth
            signal: 'Bluetooth Connected',
            distance: 'Within 10 meters',
            lastSeen: Date.now(),
            capabilities: ['bluetooth-sync', 'energy-sync']
          };

          this.devices.set(bluetoothDevice.id, bluetoothDevice);
          console.log(`[RealNetworkScanner] ✅ Bluetooth device found: ${device.name}`);
          this.notifyListeners();
        }
      }
    } catch (error) {
      console.log('[RealNetworkScanner] Bluetooth discovery requires user permission');
    }
  }

  /**
   * Discover devices via network requests
   */
  private async discoverNetworkDevices(): Promise<void> {
    try {
      console.log('[RealNetworkScanner] Probing for network devices...');
      
      // Try to discover mDNS/Bonjour services (local network discovery)
      const commonDeviceNames = [
        'iPhone.local',
        'iPad.local',
        'android.local',
        'MacBook.local'
      ];

      for (const hostname of commonDeviceNames) {
        try {
          // In a real implementation, this would use mDNS or network scanning
          // For demonstration, we simulate network discovery
          const response = await fetch(`http://${hostname}`, { 
            method: 'HEAD',
            mode: 'no-cors',
            signal: AbortSignal.timeout(1000)
          });
          
          // If we get any response, consider it a discovered device
          const networkDevice: DiscoveredDevice = {
            id: `network_${hostname.replace('.', '_')}`,
            name: `${hostname} (Network)`,
            type: hostname.includes('iPhone') ? 'iPhone' : 
                  hostname.includes('iPad') ? 'iPad' : 
                  hostname.includes('android') ? 'Android' : 'Laptop',
            signal: 'Network Reachable',
            distance: 'Same WiFi Network',
            lastSeen: Date.now(),
            capabilities: ['web-notifications', 'energy-sync']
          };

          this.devices.set(networkDevice.id, networkDevice);
          console.log(`[RealNetworkScanner] ✅ Network device found: ${hostname}`);
        } catch (error) {
          // Device not reachable
        }
      }

      this.notifyListeners();
    } catch (error) {
      console.log('[RealNetworkScanner] Network device discovery limited in browser environment');
    }
  }

  /**
   * Discover service worker clients (same origin)
   */
  private async discoverServiceWorkerClients(): Promise<void> {
    try {
      if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
        console.log('[RealNetworkScanner] Checking for service worker clients...');
        
        // This would discover other browser instances/tabs running nU Universe
        const channel = new BroadcastChannel('nu-universe-discovery');
        
        channel.postMessage({
          type: 'device-discovery',
          timestamp: Date.now(),
          requesterId: this.generateDeviceId()
        });

        channel.onmessage = (event) => {
          if (event.data.type === 'device-response') {
            const clientDevice: DiscoveredDevice = {
              id: `client_${event.data.deviceId}`,
              name: `${event.data.deviceType} (Browser)`,
              type: event.data.deviceType || 'Laptop',
              signal: 'Same Browser',
              distance: 'Local Client',
              lastSeen: Date.now(),
              capabilities: ['web-notifications', 'energy-sync', 'browser-sync']
            };

            this.devices.set(clientDevice.id, clientDevice);
            console.log(`[RealNetworkScanner] ✅ Browser client found: ${event.data.deviceType}`);
            this.notifyListeners();
          }
        };

        // Close channel after discovery
        setTimeout(() => {
          channel.close();
        }, 3000);
      }
    } catch (error) {
      console.log('[RealNetworkScanner] Service worker discovery not available');
    }
  }

  /**
   * Generate unique device ID
   */
  private generateDeviceId(): string {
    return `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Stop device discovery
   */
  stopDiscovery(): void {
    this.isScanning = false;
    console.log('[RealNetworkScanner] Device discovery stopped');
  }

  /**
   * Get discovered devices
   */
  getDevices(): DiscoveredDevice[] {
    return Array.from(this.devices.values());
  }

  /**
   * Subscribe to device updates
   */
  subscribe(callback: (devices: DiscoveredDevice[]) => void): () => void {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  /**
   * Notify all listeners
   */
  private notifyListeners(): void {
    const devices = this.getDevices();
    this.listeners.forEach(callback => callback(devices));
  }
}

export const realNetworkScanner = new RealNetworkScanner();