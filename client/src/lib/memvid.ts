/**
 * Memvid - Video-based Memory Storage Integration
 * Adapted from attached memvid files for browser environment
 */

// Mock implementations for browser environment
class MemvidEncoder {
  private chunks: Map<string, any> = new Map();
  private config: any;

  constructor(config: any) {
    this.config = config;
  }

  addChunk(text: string, metadata: any): string {
    const id = `chunk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.chunks.set(id, { text, metadata, timestamp: Date.now() });
    return id;
  }

  async buildVideo(videoPath: string, indexPath: string): Promise<void> {
    // Store chunks in localStorage for persistence
    if (typeof window !== 'undefined') {
      localStorage.setItem(`memvid_index_${indexPath}`, JSON.stringify(Array.from(this.chunks.entries())));
    }
  }

  clear(): void {
    this.chunks.clear();
  }
}

class MemvidRetriever {
  private videoPath: string;
  private indexPath: string;

  constructor(videoPath: string, indexPath: string) {
    this.videoPath = videoPath;
    this.indexPath = indexPath;
  }

  async search(query: string, options: any = {}): Promise<any[]> {
    // Simple text search implementation
    const chunks = this.loadChunks();
    const results = chunks
      .filter(([id, chunk]) => chunk.text.toLowerCase().includes(query.toLowerCase()))
      .map(([id, chunk]) => ({
        id,
        chunk: chunk.text,
        score: 0.8,
        metadata: chunk.metadata
      }))
      .slice(0, options.topK || 10);
    
    return results;
  }

  async getStats(): Promise<{ totalChunks: number }> {
    const chunks = this.loadChunks();
    return { totalChunks: chunks.length };
  }

  async getAllChunks(): Promise<any[]> {
    const chunks = this.loadChunks();
    return chunks.map(([id, chunk]) => ({
      id,
      chunk: chunk.text,
      metadata: chunk.metadata,
      timestamp: chunk.timestamp
    }));
  }

  exportIndex(): any {
    return this.loadChunks();
  }

  clearCache(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(`memvid_index_${this.indexPath}`);
    }
  }

  private loadChunks(): Array<[string, any]> {
    if (typeof window === 'undefined') return [];
    
    const stored = localStorage.getItem(`memvid_index_${this.indexPath}`);
    if (!stored) return [];
    
    try {
      return JSON.parse(stored);
    } catch {
      return [];
    }
  }
}

const quotaManager = {
  async getQuotaStats(): Promise<{
    used: number;
    available: number;
    total: number;
    percentage: number;
  }> {
    if (typeof navigator !== 'undefined' && 'storage' in navigator && 'estimate' in navigator.storage) {
      const estimate = await navigator.storage.estimate();
      const used = estimate.usage || 0;
      const total = estimate.quota || 0;
      const available = total - used;
      const percentage = total > 0 ? (used / total) * 100 : 0;
      
      return { used, available, total, percentage };
    }
    
    return { used: 0, available: 0, total: 0, percentage: 0 };
  }
};

export interface MemvidInteractionChunk {
  id: string;
  sessionId: string;
  interactionData: any;
  timestamp: number;
  embedding?: number[];
  encrypted: boolean;
}

export interface MemvidSearchResult {
  chunk: string;
  score: number;
  id: string;
  metadata?: any;
  sessionId: string;
}

export class MemvidInteractionStorage {
  private encoder: MemvidEncoder;
  private retriever: MemvidRetriever;
  private readonly VIDEO_PATH = 'nuos_interactions.mp4';
  private readonly INDEX_PATH = 'nuos_interactions_index.json';

  constructor() {
    this.encoder = new MemvidEncoder({
      chunkSize: 1024,
      overlap: 100,
      qrErrorCorrection: 'H',
      fps: 30,
      frameSize: 512
    });

    this.retriever = new MemvidRetriever(this.VIDEO_PATH, this.INDEX_PATH);
  }

  /**
   * Store interaction session as Memvid chunks
   */
  async storeInteractionSession(
    sessionId: string,
    interactions: any[],
    metadata: Record<string, any> = {}
  ): Promise<string> {
    try {
      // Convert interactions to text chunks for Memvid encoding
      const interactionText = this.serializeInteractions(interactions);
      
      // Add chunk to encoder with session metadata
      const chunkId = this.encoder.addChunk(interactionText, {
        sessionId,
        interactionCount: interactions.length,
        timestamp: Date.now(),
        ...metadata
      });

      // Build video with current chunks
      await this.encoder.buildVideo(this.VIDEO_PATH, this.INDEX_PATH);

      // Store in server if possible
      await this.syncToServer(sessionId, interactions);

      console.log(`[Memvid] Stored interaction session ${sessionId} with ${interactions.length} interactions`);
      return chunkId;

    } catch (error) {
      console.error('[Memvid] Failed to store interaction session:', error);
      throw error;
    }
  }

  /**
   * Search for interactions by semantic query
   */
  async searchInteractions(
    query: string,
    options: {
      topK?: number;
      threshold?: number;
      sessionId?: string;
    } = {}
  ): Promise<MemvidSearchResult[]> {
    try {
      const searchResults = await this.retriever.search(query, {
        topK: options.topK || 10,
        threshold: options.threshold || 0.1,
        includeMetadata: true
      });

      // Filter by session if specified
      let filteredResults = searchResults;
      if (options.sessionId) {
        filteredResults = searchResults.filter(result => 
          result.metadata?.sessionId === options.sessionId
        );
      }

      // Convert to our interface
      return filteredResults.map(result => ({
        chunk: result.chunk,
        score: result.score,
        id: result.id,
        metadata: result.metadata,
        sessionId: result.metadata?.sessionId || ''
      }));

    } catch (error) {
      console.error('[Memvid] Failed to search interactions:', error);
      return [];
    }
  }

  /**
   * Get interaction patterns and insights
   */
  async getInteractionPatterns(sessionId?: string): Promise<{
    totalSessions: number;
    commonPatterns: Array<{
      pattern: string;
      frequency: number;
      lastSeen: number;
    }>;
    sessionInsights: Array<{
      sessionId: string;
      interactionCount: number;
      duration: number;
      timestamp: number;
    }>;
  }> {
    try {
      const stats = await this.retriever.getStats();
      const allChunks = await this.retriever.getAllChunks();

      // Extract session insights
      const sessionInsights = allChunks
        .filter(chunk => !sessionId || chunk.metadata?.sessionId === sessionId)
        .map(chunk => ({
          sessionId: chunk.metadata?.sessionId || '',
          interactionCount: chunk.metadata?.interactionCount || 0,
          duration: chunk.metadata?.duration || 0,
          timestamp: chunk.timestamp
        }));

      // Analyze common patterns (simplified)
      const commonPatterns = this.analyzeInteractionPatterns(allChunks);

      return {
        totalSessions: stats.totalChunks,
        commonPatterns,
        sessionInsights
      };

    } catch (error) {
      console.error('[Memvid] Failed to get interaction patterns:', error);
      return {
        totalSessions: 0,
        commonPatterns: [],
        sessionInsights: []
      };
    }
  }

  /**
   * Export interaction history for privacy compliance
   */
  async exportInteractionHistory(): Promise<{
    format: 'memvid';
    data: any;
    exportedAt: number;
    totalChunks: number;
  }> {
    try {
      const index = this.retriever.exportIndex();
      const stats = await this.retriever.getStats();

      return {
        format: 'memvid',
        data: index,
        exportedAt: Date.now(),
        totalChunks: stats.totalChunks
      };

    } catch (error) {
      console.error('[Memvid] Failed to export interaction history:', error);
      throw error;
    }
  }

  /**
   * Clear interaction history for privacy
   */
  async clearInteractionHistory(): Promise<void> {
    try {
      // Clear local storage
      if (typeof window !== 'undefined') {
        localStorage.removeItem(`memvid_video_${this.VIDEO_PATH}`);
        localStorage.removeItem(`memvid_index_${this.INDEX_PATH}`);
      }

      // Clear retriever cache
      this.retriever.clearCache();

      // Reset encoder
      this.encoder.clear();

      console.log('[Memvid] Interaction history cleared');

    } catch (error) {
      console.error('[Memvid] Failed to clear interaction history:', error);
      throw error;
    }
  }

  /**
   * Get storage quota information
   */
  async getStorageInfo(): Promise<{
    used: number;
    available: number;
    total: number;
    percentage: number;
  }> {
    return await quotaManager.getQuotaStats();
  }

  /**
   * Serialize interactions to text for Memvid encoding
   */
  private serializeInteractions(interactions: any[]): string {
    return interactions.map(interaction => {
      return `[${new Date(interaction.timestamp).toISOString()}] ${interaction.type}: ${interaction.element || 'unknown'} - ${JSON.stringify(interaction.metadata || {})}`;
    }).join('\n');
  }

  /**
   * Analyze interaction patterns from chunks
   */
  private analyzeInteractionPatterns(chunks: any[]): Array<{
    pattern: string;
    frequency: number;
    lastSeen: number;
  }> {
    const patterns: Map<string, { frequency: number; lastSeen: number }> = new Map();

    chunks.forEach(chunk => {
      const chunkText = chunk.chunk || '';
      
      // Extract simple patterns (interaction types)
      const interactionTypes = chunkText.match(/\] (\w+):/g) || [];
      
      interactionTypes.forEach((match: string) => {
        const type = match.replace(/\] /, '').replace(':', '');
        const existing = patterns.get(type) || { frequency: 0, lastSeen: 0 };
        patterns.set(type, {
          frequency: existing.frequency + 1,
          lastSeen: Math.max(existing.lastSeen, chunk.timestamp)
        });
      });
    });

    return Array.from(patterns.entries())
      .map(([pattern, data]) => ({
        pattern,
        frequency: data.frequency,
        lastSeen: data.lastSeen
      }))
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, 10); // Top 10 patterns
  }

  /**
   * Sync interaction data to server
   */
  private async syncToServer(sessionId: string, interactions: any[]): Promise<void> {
    try {
      const response = await fetch('/api/memvid/chunks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          chunkId: `session_${sessionId}`,
          chunkData: {
            sessionId,
            interactions,
            timestamp: Date.now()
          },
          encrypted: true
        })
      });

      if (!response.ok) {
        throw new Error(`Server sync failed: ${response.statusText}`);
      }

    } catch (error) {
      console.warn('[Memvid] Failed to sync to server:', error);
      // Continue without server sync - data is stored locally
    }
  }
}

// Global Memvid instance
export const memvidStorage = new MemvidInteractionStorage();
