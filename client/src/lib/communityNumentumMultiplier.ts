/**
 * Community nUmentum Multiplier System
 * Real-time social interaction tracking with authentic joy detection
 */

// Disabled import - using extension-only tracking
// import { realTimeNumentum } from './realTimeNumentum';

interface CommunityInteraction {
  type: 'message' | 'reaction' | 'share' | 'collaboration' | 'help' | 'celebrate';
  recipientId: string;
  intensity: number; // 0.1 to 2.0
  mutuality: number; // How much both parties benefit
  timestamp: number;
  authenticityScore: number; // Based on real interaction patterns
}

interface SocialMetrics {
  activeConnections: number;
  mutualInteractions: number;
  helpGiven: number;
  helpReceived: number;
  celebrationsMade: number;
  authenticConnections: Set<string>;
}

export class CommunityNumentumMultiplier {
  private socialMetrics: SocialMetrics;
  private recentInteractions: CommunityInteraction[] = [];
  private joyMultiplierBase = 1.0;
  private communityBonus = 0;
  private currentMultiplier = 1.0;

  constructor() {
    this.socialMetrics = {
      activeConnections: 0,
      mutualInteractions: 0,
      helpGiven: 0,
      helpReceived: 0,
      celebrationsMade: 0,
      authenticConnections: new Set()
    };

    this.initializeRealCommunityTracking();
  }

  /**
   * Initialize real-time community interaction tracking
   */
  public initializeRealCommunityTracking() {
    // Listen to existing SpUnder interaction events
    window.addEventListener('spunderInteraction', this.handleSpunderInteraction.bind(this) as EventListener);

    // Listen to message events from existing chat system
    window.addEventListener('messageReceived', this.handleMessageReceived.bind(this) as EventListener);
    window.addEventListener('messageSent', this.handleMessageSent.bind(this) as EventListener);

    // Track energy sharing events
    window.addEventListener('energyShared', this.handleEnergyShared.bind(this) as EventListener);
    window.addEventListener('energyReceived', this.handleEnergyReceived.bind(this) as EventListener);
    
    // Calculate community multipliers every 30 seconds
    // Only calculate from real extension community activity
    this.setupRealCommunityTracking();
  }

  /**
   * Setup real community tracking system
   */
  private setupRealCommunityTracking() {
    console.log('[CommunityNumentum] Real community tracking initialized');
    
    // Track community interactions every 30 seconds
    setInterval(() => {
      this.calculateCommunityMultipliers();
    }, 30000);
    
    // Clean up old interactions every 5 minutes
    setInterval(() => {
      this.cleanupOldInteractions();
    }, 300000);
  }



  /**
   * Handle SpUnder encrypted interactions
   */
  private handleSpunderInteraction(event: CustomEvent) {
    const { interactionData, recipientId } = event.detail;
    
    const interaction: CommunityInteraction = {
      type: this.classifyInteractionType(interactionData),
      recipientId,
      intensity: this.calculateInteractionIntensity(interactionData),
      mutuality: this.calculateMutuality(recipientId),
      timestamp: Date.now(),
      authenticityScore: this.calculateAuthenticityScore(interactionData, recipientId)
    };

    this.processInteraction(interaction);
  }

  /**
   * Handle real-time message interactions
   */
  private handleMessageReceived(event: CustomEvent) {
    const { senderId, messageData } = event.detail;
    
    const interaction: CommunityInteraction = {
      type: 'message',
      recipientId: senderId, // They sent to us
      intensity: this.analyzeMessageIntensity(messageData),
      mutuality: this.calculateMutuality(senderId),
      timestamp: Date.now(),
      authenticityScore: this.analyzeMessageAuthenticity(messageData)
    };

    this.processInteraction(interaction);
    this.socialMetrics.helpReceived++;
  }

  /**
   * Handle outgoing message interactions
   */
  private handleMessageSent(event: CustomEvent) {
    const { recipientId, messageData } = event.detail;
    
    const interaction: CommunityInteraction = {
      type: 'message',
      recipientId,
      intensity: this.analyzeMessageIntensity(messageData),
      mutuality: this.calculateMutuality(recipientId),
      timestamp: Date.now(),
      authenticityScore: this.analyzeMessageAuthenticity(messageData)
    };

    this.processInteraction(interaction);
    this.socialMetrics.helpGiven++;
  }

  /**
   * Handle energy sharing (UMatter, trU, nUva transfers)
   */
  private handleEnergyShared(event: CustomEvent) {
    const { recipientId, energyType, amount } = event.detail;
    
    const interaction: CommunityInteraction = {
      type: 'share',
      recipientId,
      intensity: Math.min(2.0, amount * 0.1), // Energy amount affects intensity
      mutuality: 1.5, // Sharing energy is highly mutual
      timestamp: Date.now(),
      authenticityScore: 1.0 // Energy sharing is inherently authentic
    };

    this.processInteraction(interaction);
    this.socialMetrics.helpGiven++;
  }

  /**
   * Handle energy received
   */
  private handleEnergyReceived(event: CustomEvent) {
    const { senderId, energyType, amount } = event.detail;
    
    const interaction: CommunityInteraction = {
      type: 'celebrate',
      recipientId: senderId,
      intensity: Math.min(1.8, amount * 0.08),
      mutuality: 1.4,
      timestamp: Date.now(),
      authenticityScore: 1.0
    };

    this.processInteraction(interaction);
    this.socialMetrics.helpReceived++;
    this.socialMetrics.celebrationsMade++;
  }

  /**
   * Process and analyze community interaction
   */
  private processInteraction(interaction: CommunityInteraction) {
    this.recentInteractions.push(interaction);
    
    // Keep only last 100 interactions for performance
    if (this.recentInteractions.length > 100) {
      this.recentInteractions = this.recentInteractions.slice(-100);
    }

    // Update social metrics
    this.socialMetrics.authenticConnections.add(interaction.recipientId);
    this.socialMetrics.activeConnections = this.socialMetrics.authenticConnections.size;
    
    if (interaction.mutuality > 1.0) {
      this.socialMetrics.mutualInteractions++;
    }

    // Calculate immediate multiplier effect
    const immediateMultiplier = this.calculateImmediateMultiplier(interaction);
    
    // Apply to current nUmentum
    this.applyMultiplierToNumentum(immediateMultiplier, interaction);
  }

  /**
   * Classify interaction type from SpUnder data
   */
  private classifyInteractionType(interactionData: any): CommunityInteraction['type'] {
    if (!interactionData) return 'message';
    
    const data = JSON.stringify(interactionData).toLowerCase();
    
    if (data.includes('help') || data.includes('assist') || data.includes('support')) {
      return 'help';
    } else if (data.includes('share') || data.includes('give') || data.includes('transfer')) {
      return 'share';
    } else if (data.includes('celebrate') || data.includes('congrat') || data.includes('awesome')) {
      return 'celebrate';
    } else if (data.includes('collaborate') || data.includes('together') || data.includes('team')) {
      return 'collaboration';
    } else if (data.includes('react') || data.includes('like') || data.includes('love')) {
      return 'reaction';
    }
    
    return 'message';
  }

  /**
   * Calculate interaction intensity from real patterns
   */
  private calculateInteractionIntensity(data: any): number {
    if (!data) return 0.3;
    
    const content = JSON.stringify(data);
    let intensity = 0.3;
    
    // Length indicates engagement
    intensity += Math.min(0.4, content.length / 1000);
    
    // Exclamation marks indicate excitement
    const exclamations = (content.match(/!/g) || []).length;
    intensity += Math.min(0.3, exclamations * 0.1);
    
    // Questions indicate engagement
    const questions = (content.match(/\?/g) || []).length;
    intensity += Math.min(0.2, questions * 0.05);
    
    // Caps indicate strong emotion
    const capsRatio = (content.match(/[A-Z]/g) || []).length / content.length;
    if (capsRatio > 0.3) intensity += 0.2;
    
    return Math.min(2.0, intensity);
  }

  /**
   * Calculate mutuality score (how much both parties benefit)
   */
  private calculateMutuality(recipientId: string): number {
    const recentWithUser = this.recentInteractions
      .filter(i => i.recipientId === recipientId && Date.now() - i.timestamp < 300000) // 5 minutes
      .length;
    
    // More recent interactions = higher mutuality
    return Math.min(2.0, 1.0 + recentWithUser * 0.2);
  }

  /**
   * Calculate authenticity score based on interaction patterns
   */
  private calculateAuthenticityScore(data: any, recipientId: string): number {
    let authenticity = 0.8; // Base authentic score
    
    // Check for spam patterns (too many identical interactions)
    const identicalRecent = this.recentInteractions
      .filter(i => 
        i.recipientId === recipientId && 
        Date.now() - i.timestamp < 60000 && // 1 minute
        JSON.stringify(data) === JSON.stringify(i)
      ).length;
    
    if (identicalRecent > 3) authenticity -= 0.4; // Spam penalty
    
    // Check interaction timing (too fast = less authentic)
    const lastInteraction = this.recentInteractions
      .filter(i => i.recipientId === recipientId)
      .pop();
    
    if (lastInteraction && Date.now() - lastInteraction.timestamp < 1000) {
      authenticity -= 0.2; // Too fast penalty
    }
    
    // Reward consistent interaction patterns
    const interactionsWithUser = this.recentInteractions
      .filter(i => i.recipientId === recipientId).length;
    
    if (interactionsWithUser > 5) authenticity += 0.1; // Relationship bonus
    
    return Math.max(0.1, Math.min(1.0, authenticity));
  }

  /**
   * Analyze message intensity from content
   */
  private analyzeMessageIntensity(messageData: any): number {
    if (!messageData?.content) return 0.2;
    
    const content = messageData.content.toLowerCase();
    let intensity = 0.2;
    
    // Positive words increase intensity
    const positiveWords = ['amazing', 'awesome', 'great', 'love', 'fantastic', 'wonderful', 'brilliant'];
    positiveWords.forEach(word => {
      if (content.includes(word)) intensity += 0.1;
    });
    
    // Emotional expressions
    const emotions = ['😊', '😂', '❤️', '🎉', '👏', '🔥', '💯'];
    emotions.forEach(emoji => {
      if (content.includes(emoji)) intensity += 0.15;
    });
    
    return Math.min(2.0, intensity);
  }

  /**
   * Analyze message authenticity
   */
  private analyzeMessageAuthenticity(messageData: any): number {
    if (!messageData?.content) return 0.5;
    
    const content = messageData.content;
    let authenticity = 0.7; // Base score
    
    // Length indicates thoughtfulness
    if (content.length > 50) authenticity += 0.1;
    if (content.length > 200) authenticity += 0.1;
    
    // Personal pronouns indicate authentic communication
    const personalWords = ['i', 'me', 'my', 'you', 'your', 'we', 'our'];
    const personalCount = personalWords.filter(word => 
      content.toLowerCase().includes(` ${word} `)
    ).length;
    authenticity += Math.min(0.2, personalCount * 0.05);
    
    return Math.min(1.0, authenticity);
  }

  /**
   * Calculate immediate multiplier for current interaction
   */
  private calculateImmediateMultiplier(interaction: CommunityInteraction): number {
    let multiplier = 1.0;
    
    // Base multiplier from interaction type
    const typeMultipliers = {
      'help': 1.4,
      'share': 1.3,
      'celebrate': 1.25,
      'collaboration': 1.35,
      'reaction': 1.1,
      'message': 1.05
    };
    
    multiplier *= typeMultipliers[interaction.type];
    
    // Intensity bonus
    multiplier += (interaction.intensity - 1.0) * 0.3;
    
    // Mutuality bonus
    multiplier += (interaction.mutuality - 1.0) * 0.2;
    
    // Authenticity bonus
    multiplier *= interaction.authenticityScore;
    
    return Math.max(0.8, Math.min(3.0, multiplier));
  }

  /**
   * Apply multiplier to current nUmentum generation
   */
  private applyMultiplierToNumentum(multiplier: number, interaction: CommunityInteraction) {
    // Create enhanced activity event
    const enhancedActivity = {
      activityType: `community_${interaction.type}`,
      umatterGenerated: 0.2 * multiplier,
      truTraded: interaction.type === 'share' ? 0.1 * multiplier : 0,
      nuvaShared: interaction.type === 'collaboration' ? 0.15 * multiplier : 0,
      joyModifier: 1.0 + (multiplier - 1.0) * 0.5, // Convert multiplier to joy
      deviceEnergyDrain: 0.05, // Community interactions are low energy
      dailyScore: 0.2 * multiplier,
      metadata: {
        communityInteraction: true,
        socialMultiplier: multiplier,
        interactionType: interaction.type,
        authenticityScore: interaction.authenticityScore
      }
    };

    // Track the enhanced activity
    this.trackCommunityActivity(enhancedActivity);
  }

  /**
   * Clean up old interactions
   */
  private cleanupOldInteractions() {
    const now = Date.now();
    const oneHourAgo = now - 3600000; // 1 hour
    
    this.recentInteractions = this.recentInteractions.filter(i => i.timestamp > oneHourAgo);
  }

  /**
   * Calculate ongoing community multipliers
   */
  private calculateCommunityMultipliers() {
    const now = Date.now();
    const recentInteractions = this.recentInteractions
      .filter(i => now - i.timestamp < 1800000); // Last 30 minutes
    
    if (recentInteractions.length === 0) return;
    
    // Base community bonus
    let communityMultiplier = 1.0;
    
    // Active connections bonus
    const connectionBonus = Math.min(2.0, 1.0 + this.socialMetrics.activeConnections * 0.1);
    communityMultiplier *= connectionBonus;
    
    // Mutual interaction bonus
    const mutualityRatio = this.socialMetrics.mutualInteractions / Math.max(1, recentInteractions.length);
    communityMultiplier += mutualityRatio * 0.3;
    
    // Help balance bonus (giving and receiving)
    const helpBalance = Math.min(1.0, this.socialMetrics.helpGiven / Math.max(1, this.socialMetrics.helpReceived));
    const helpBonus = helpBalance > 0.7 ? 0.2 : helpBalance * 0.28;
    communityMultiplier += helpBonus;
    
    // Celebration bonus
    if (this.socialMetrics.celebrationsMade > 0) {
      communityMultiplier += Math.min(0.3, this.socialMetrics.celebrationsMade * 0.05);
    }
    
    // Apply ongoing multiplier to nUmentum system
    this.joyMultiplierBase = Math.min(4.0, communityMultiplier);
    this.communityBonus = (communityMultiplier - 1.0) * 100; // Percentage bonus
    
    // Notify the real-time system of community boost
    window.dispatchEvent(new CustomEvent('communityMultiplier', {
      detail: { 
        multiplier: this.joyMultiplierBase, 
        bonus: this.communityBonus,
        metrics: this.socialMetrics 
      }
    }));
  }

  /**
   * Track community-enhanced activity
   */
  private async trackCommunityActivity(activityData: any) {
    try {
      const response = await fetch('/api/numentum/track', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(activityData)
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`[CommunityNumentum] Enhanced activity tracked:`, result.dailyNumentumScore);
      }
    } catch (error) {
      console.error('[CommunityNumentum] Failed to track activity:', error);
    }
  }

  /**
   * Get current community metrics
   */
  public getCommunityMetrics() {
    return {
      socialMetrics: this.socialMetrics,
      joyMultiplier: this.joyMultiplierBase,
      communityBonus: this.communityBonus,
      recentInteractionCount: this.recentInteractions.length
    };
  }

  /**
   * Trigger community celebration (for testing)
   */
  public triggerCelebration(recipientId: string, intensity: number = 1.0) {
    const celebration: CommunityInteraction = {
      type: 'celebrate',
      recipientId,
      intensity,
      mutuality: 1.3,
      timestamp: Date.now(),
      authenticityScore: 0.9
    };
    
    this.processInteraction(celebration);
  }

  /**
   * Trigger help interaction (for testing)
   */
  public triggerHelp(recipientId: string, isGiving: boolean = true) {
    const help: CommunityInteraction = {
      type: 'help',
      recipientId,
      intensity: 1.2,
      mutuality: 1.5,
      timestamp: Date.now(),
      authenticityScore: 1.0
    };
    
    this.processInteraction(help);
    
    if (isGiving) {
      this.socialMetrics.helpGiven++;
    } else {
      this.socialMetrics.helpReceived++;
    }
  }

  private processRealCommunityActivity(activity: any) {
    if (!activity || !activity.verified) return;
    
    // Real community multiplier from actual extension usage
    const baseMultiplier = 1.0;
    let activityBoost = 0;
    
    switch (activity.type) {
      case 'ad_block': activityBoost = 0.1; break;
      case 'data_intercept': activityBoost = 0.05; break;
      case 'page_visit': activityBoost = 0.02; break;
    }
    
    this.currentMultiplier = Math.min(baseMultiplier + activityBoost, 2.0);
    console.log(`[CommunityNumentum] Real multiplier: ${this.currentMultiplier.toFixed(2)} from ${activity.type}`);
    
    this.broadcastMultiplierUpdate();
  }

  /**
   * Broadcast multiplier update to other components
   */
  private broadcastMultiplierUpdate() {
    const event = new CustomEvent('multiplierUpdate', {
      detail: {
        multiplier: this.currentMultiplier,
        communityBonus: this.communityBonus,
        timestamp: Date.now()
      }
    });
    window.dispatchEvent(event);
  }
}

export const communityMultiplier = new CommunityNumentumMultiplier();