/**
 * Energy Batch Manager - Batches UMatter deposits to prevent system overload
 */

interface EnergyBatch {
  amount: number;
  source: string;
  timestamp: number;
  deviceData?: any;
}

class EnergyBatchManager {
  private batch: EnergyBatch[] = [];
  private lastFlush = 0;
  private readonly BATCH_SIZE = 2;
  private readonly FLUSH_INTERVAL = 30000; // 30 seconds
  private flushTimer: number | null = null;

  constructor() {
    // Auto-flush on page unload
    window.addEventListener('beforeunload', () => {
      this.flushBatch();
    });
  }

  public addToBatch(energy: EnergyBatch) {
    this.batch.push(energy);
    
    // Auto-flush if batch is full or enough time has passed
    if (this.batch.length >= this.BATCH_SIZE || 
        (Date.now() - this.lastFlush) >= this.FLUSH_INTERVAL) {
      this.flushBatch();
    } else {
      this.scheduleFlush();
    }
  }

  private scheduleFlush() {
    if (this.flushTimer) return;
    
    this.flushTimer = window.setTimeout(() => {
      this.flushBatch();
    }, this.FLUSH_INTERVAL);
  }

  private async flushBatch() {
    if (this.batch.length === 0) return;

    if (this.flushTimer) {
      clearTimeout(this.flushTimer);
      this.flushTimer = null;
    }

    const batchToSend = [...this.batch];
    this.batch = [];
    this.lastFlush = Date.now();

    try {
      const totalAmount = batchToSend.reduce((sum, item) => sum + item.amount, 0);
      
      console.log(`[EnergyBatch] Sending batch: ${batchToSend.length} items, ${totalAmount.toFixed(6)} total UMatter`);

      const response = await fetch('/api/banking/deposit-umatter-batch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          batch: batchToSend,
          totalAmount,
          batchSize: batchToSend.length,
          timestamp: Date.now()
        })
      });

      if (!response.ok) {
        throw new Error(`Batch failed: ${response.status}`);
      }

      const result = await response.json();
      console.log(`[EnergyBatch] Batch successful: ${result.newBalance} total balance`);

    } catch (error) {
      console.log('[EnergyBatch] Batch failed, energy lost (authentic system only)');
    }
  }

  public forceFlush() {
    this.flushBatch();
  }

  public getBatchSize() {
    return this.batch.length;
  }
}

export const energyBatchManager = new EnergyBatchManager();