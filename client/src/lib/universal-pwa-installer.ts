/**
 * Universal PWA Installer - Works on ALL devices
 * Supports mobile, tablet, desktop, iOS, Android, all browsers
 */

interface PWAInstallPrompt {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

interface InstallationStatus {
  canInstall: boolean;
  isInstalled: boolean;
  platform: 'ios' | 'android' | 'desktop' | 'mobile' | 'unknown';
  browser: string;
  installMethod: 'native' | 'manual' | 'share';
  instructions: string[];
}

export class UniversalPWAInstaller {
  private deferredPrompt: PWAInstallPrompt | null = null;
  private installListeners: Set<(status: InstallationStatus) => void> = new Set();

  constructor() {
    this.setupEventListeners();
    this.detectInstallCapability();
  }

  /**
   * Get current installation status for any device
   */
  getInstallationStatus(): InstallationStatus {
    const userAgent = navigator.userAgent.toLowerCase();
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
    const isInWebAppiOS = (window.navigator as any).standalone === true;
    const isInstalled = isStandalone || isInWebAppiOS;

    // Detect platform
    let platform: InstallationStatus['platform'] = 'unknown';
    if (/iphone|ipad|ipod/.test(userAgent)) {
      platform = 'ios';
    } else if (/android/.test(userAgent)) {
      platform = 'android';
    } else if (/mobile/.test(userAgent)) {
      platform = 'mobile';
    } else {
      platform = 'desktop';
    }

    // Detect browser
    let browser = 'unknown';
    if (userAgent.includes('chrome')) browser = 'chrome';
    else if (userAgent.includes('firefox')) browser = 'firefox';
    else if (userAgent.includes('safari')) browser = 'safari';
    else if (userAgent.includes('edge')) browser = 'edge';

    // Determine install capability and method
    const canInstall = this.deferredPrompt !== null || platform === 'ios' || platform === 'android';
    const installMethod: InstallationStatus['installMethod'] = 
      this.deferredPrompt ? 'native' : 
      platform === 'ios' ? 'share' : 
      'manual';

    // Generate instructions based on platform
    const instructions = this.generateInstructions(platform, browser, installMethod);

    return {
      canInstall,
      isInstalled,
      platform,
      browser,
      installMethod,
      instructions
    };
  }

  /**
   * Attempt to install PWA using native prompt
   */
  async installNative(): Promise<boolean> {
    if (!this.deferredPrompt) {
      return false;
    }

    try {
      await this.deferredPrompt.prompt();
      const choiceResult = await this.deferredPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        console.log('[PWAInstaller] User accepted native install');
        this.deferredPrompt = null;
        this.notifyListeners();
        return true;
      } else {
        console.log('[PWAInstaller] User dismissed native install');
        return false;
      }
    } catch (error) {
      console.error('[PWAInstaller] Native install failed:', error);
      return false;
    }
  }

  /**
   * Show manual installation instructions
   */
  showManualInstructions(): void {
    const status = this.getInstallationStatus();
    const instructions = status.instructions.join('\n');
    
    // Create modal with instructions
    this.createInstructionModal(status);
  }

  /**
   * Register for installation updates
   */
  onInstallationUpdate(callback: (status: InstallationStatus) => void): () => void {
    this.installListeners.add(callback);
    
    // Call immediately with current status
    callback(this.getInstallationStatus());
    
    return () => {
      this.installListeners.delete(callback);
    };
  }

  /**
   * Check if service worker is registered and working
   */
  async checkServiceWorkerStatus(): Promise<boolean> {
    if (!('serviceWorker' in navigator)) {
      return false;
    }

    try {
      const registration = await navigator.serviceWorker.getRegistration();
      return registration !== undefined;
    } catch (error) {
      console.error('[PWAInstaller] Service worker check failed:', error);
      return false;
    }
  }

  /**
   * Register service worker for PWA functionality
   */
  async registerServiceWorker(): Promise<boolean> {
    if (!('serviceWorker' in navigator)) {
      console.warn('[PWAInstaller] Service workers not supported');
      return false;
    }

    try {
      // Check if service worker is already registered
      const existingRegistration = await navigator.serviceWorker.getRegistration();
      if (existingRegistration && existingRegistration.active) {
        console.log('[PWAInstaller] Service worker already active');
        return true;
      }

      console.log('[PWAInstaller] Registering service worker...');
      const registration = await navigator.serviceWorker.register('/service-worker.js', {
        scope: '/',
        updateViaCache: 'imports'
      });
      
      console.log('[PWAInstaller] Service worker registered successfully:', registration.scope);
      
      // Wait for service worker to be ready or active
      if (registration.installing) {
        await new Promise<void>((resolve) => {
          const worker = registration.installing!;
          worker.addEventListener('statechange', () => {
            if (worker.state === 'activated') {
              resolve();
            }
          });
        });
      } else if (registration.waiting) {
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      } else if (registration.active) {
        console.log('[PWAInstaller] Service worker already active');
      }
      
      // Wait for service worker to be ready
      await navigator.serviceWorker.ready;
      
      // Listen for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              console.log('[PWAInstaller] New service worker available');
            }
          });
        }
      });
      
      return true;
    } catch (error) {
      console.error('[PWAInstaller] Service worker registration failed:', error);
      return false;
    }
  }

  private setupEventListeners(): void {
    // Listen for beforeinstallprompt (Chrome, Edge, etc.)
    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault();
      this.deferredPrompt = e as any;
      console.log('[PWAInstaller] Install prompt available');
      this.notifyListeners();
    });

    // Listen for app installed event
    window.addEventListener('appinstalled', () => {
      console.log('[PWAInstaller] PWA installed successfully');
      this.deferredPrompt = null;
      this.notifyListeners();
    });

    // Listen for display mode changes
    window.matchMedia('(display-mode: standalone)').addEventListener('change', () => {
      console.log('[PWAInstaller] Display mode changed');
      this.notifyListeners();
    });
  }

  private detectInstallCapability(): void {
    // Check for iOS standalone mode
    if ((window.navigator as any).standalone !== undefined) {
      console.log('[PWAInstaller] iOS PWA capability detected');
      this.notifyListeners();
    }

    // Check for Android WebAPK support
    if ('getInstalledRelatedApps' in navigator) {
      (navigator as any).getInstalledRelatedApps().then((apps: any[]) => {
        console.log('[PWAInstaller] Related apps check:', apps.length);
        this.notifyListeners();
      });
    }
  }

  private generateInstructions(platform: string, browser: string, method: string): string[] {
    const instructions: string[] = [];

    if (platform === 'ios') {
      instructions.push('1. Open this page in Safari');
      instructions.push('2. Tap the Share button (square with arrow)');
      instructions.push('3. Scroll down and tap "Add to Home Screen"');
      instructions.push('4. Tap "Add" to confirm');
    } else if (platform === 'android') {
      if (browser === 'chrome') {
        instructions.push('1. Tap the menu (3 dots) in Chrome');
        instructions.push('2. Tap "Add to Home screen"');
        instructions.push('3. Confirm by tapping "Add"');
      } else {
        instructions.push('1. Open in Chrome browser for best experience');
        instructions.push('2. Tap menu and select "Add to Home screen"');
      }
    } else {
      // Desktop instructions
      if (browser === 'chrome' || browser === 'edge') {
        instructions.push('1. Click the install button in the address bar');
        instructions.push('2. Or use browser menu → "Install nU Universe"');
      } else if (browser === 'firefox') {
        instructions.push('1. Bookmark this page');
        instructions.push('2. Or use a Chromium browser for full install support');
      } else {
        instructions.push('1. Use Chrome, Edge, or Safari for installation');
        instructions.push('2. Look for install prompts in the browser');
      }
    }

    instructions.push('');
    instructions.push('After installation:');
    instructions.push('• Access from home screen/app menu');
    instructions.push('• Works offline with cached energy data');
    instructions.push('• Receives push notifications for energy alerts');
    instructions.push('• Syncs across all your devices');

    return instructions;
  }

  private createInstructionModal(status: InstallationStatus): void {
    // Remove existing modal if present
    const existingModal = document.getElementById('pwa-install-modal');
    if (existingModal) {
      existingModal.remove();
    }

    // Create modal HTML
    const modal = document.createElement('div');
    modal.id = 'pwa-install-modal';
    modal.innerHTML = `
      <div style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
      ">
        <div style="
          background: #1a1a1a;
          border: 1px solid #00ff41;
          border-radius: 12px;
          padding: 24px;
          max-width: 400px;
          width: 90%;
          color: white;
          box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
        ">
          <h3 style="
            color: #00ff41;
            margin: 0 0 16px 0;
            font-size: 18px;
            text-align: center;
          ">Install nU Universe</h3>
          
          <p style="
            color: #ccc;
            margin: 0 0 16px 0;
            font-size: 14px;
            text-align: center;
          ">Get the full experience on ${status.platform === 'ios' ? 'iOS' : status.platform === 'android' ? 'Android' : 'your device'}</p>
          
          <div style="
            background: #0a0a0a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            font-size: 13px;
            line-height: 1.5;
          ">
            ${status.instructions.map(instruction => `<div style="margin: 4px 0;">${instruction}</div>`).join('')}
          </div>
          
          <div style="display: flex; gap: 12px; margin-top: 20px;">
            ${status.installMethod === 'native' ? `
              <button id="pwa-install-btn" style="
                flex: 1;
                background: #00ff41;
                color: #000;
                border: none;
                border-radius: 6px;
                padding: 12px;
                font-weight: bold;
                cursor: pointer;
              ">Install Now</button>
            ` : ''}
            <button id="pwa-close-btn" style="
              flex: 1;
              background: transparent;
              color: #999;
              border: 1px solid #333;
              border-radius: 6px;
              padding: 12px;
              cursor: pointer;
            ">Close</button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Add event listeners
    const closeBtn = modal.querySelector('#pwa-close-btn');
    closeBtn?.addEventListener('click', () => modal.remove());

    const installBtn = modal.querySelector('#pwa-install-btn');
    installBtn?.addEventListener('click', async () => {
      const success = await this.installNative();
      if (success) {
        modal.remove();
      }
    });

    // Close on background click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.remove();
      }
    });
  }

  private notifyListeners(): void {
    const status = this.getInstallationStatus();
    this.installListeners.forEach(listener => {
      try {
        listener(status);
      } catch (error) {
        console.error('[PWAInstaller] Listener error:', error);
      }
    });
  }
}

export const universalPWAInstaller = new UniversalPWAInstaller();