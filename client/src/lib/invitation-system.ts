/**
 * Social Invitation System - Invite non-users with 25% NUVA rewards
 */

export interface SocialInvitation {
  id: string;
  inviterName: string;
  inviterDevice: string;
  phoneNumber?: string;
  email?: string;
  message: string;
  nuvaReward: number; // 25% NUVA bonus
  qrCode: string;
  status: 'pending' | 'sent' | 'accepted' | 'expired';
  createdAt: number;
  expiresAt: number;
  acceptedAt?: number;
}

export interface InvitationResponse {
  success: boolean;
  invitation?: SocialInvitation;
  error?: string;
}

class SocialInvitationSystem {
  private invitations: Map<string, SocialInvitation> = new Map();
  private readonly NUVA_REWARD_PERCENTAGE = 0.25; // 25% NUVA bonus
  private readonly INVITATION_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days

  constructor() {
    this.loadInvitations();
  }

  /**
   * Create and send invitation to non-user
   */
  async createInvitation(
    inviterName: string,
    contact: { phone?: string; email?: string },
    customMessage?: string
  ): Promise<InvitationResponse> {
    try {
      const invitationId = this.generateInvitationId();
      const deviceInfo = this.getDeviceInfo();
      
      const invitation: SocialInvitation = {
        id: invitationId,
        inviterName,
        inviterDevice: deviceInfo.name,
        phoneNumber: contact.phone,
        email: contact.email,
        message: customMessage || this.getDefaultMessage(inviterName),
        nuvaReward: this.NUVA_REWARD_PERCENTAGE,
        qrCode: this.generateQRCode(invitationId),
        status: 'pending',
        createdAt: Date.now(),
        expiresAt: Date.now() + this.INVITATION_DURATION
      };

      // Store invitation
      this.invitations.set(invitationId, invitation);
      this.saveInvitations();

      // Send invitation via available methods
      await this.sendInvitation(invitation);

      console.log(`[SocialInvite] Created invitation ${invitationId} with ${this.NUVA_REWARD_PERCENTAGE * 100}% NUVA reward`);
      
      return { success: true, invitation };
    } catch (error) {
      console.error('[SocialInvite] Failed to create invitation:', error);
      return { success: false, error: 'Failed to create invitation' };
    }
  }

  /**
   * Send invitation via multiple channels
   */
  private async sendInvitation(invitation: SocialInvitation): Promise<void> {
    try {
      // Method 1: SMS (if phone number provided)
      if (invitation.phoneNumber) {
        await this.sendSMSInvitation(invitation);
      }

      // Method 2: Email (if email provided)
      if (invitation.email) {
        await this.sendEmailInvitation(invitation);
      }

      // Method 3: Generate shareable link
      const shareableLink = this.generateShareableLink(invitation.id);
      console.log(`[SocialInvite] Shareable link: ${shareableLink}`);

      // Update status
      invitation.status = 'sent';
      this.saveInvitations();
    } catch (error) {
      console.error('[SocialInvite] Failed to send invitation:', error);
    }
  }

  /**
   * Send SMS invitation using Web API
   */
  private async sendSMSInvitation(invitation: SocialInvitation): Promise<void> {
    const message = `${invitation.message}\n\nJoin nU Universe and get ${invitation.nuvaReward * 100}% NUVA bonus!\nClick: ${this.generateShareableLink(invitation.id)}`;
    
    // Use Web Share API if available
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Join nU Universe Energy Network',
          text: message,
          url: this.generateShareableLink(invitation.id)
        });
        console.log('[SocialInvite] SMS invitation shared via Web Share API');
      } catch (error) {
        console.log('[SocialInvite] Web Share cancelled or failed');
      }
    }

    // Fallback: Copy to clipboard
    if (navigator.clipboard) {
      try {
        await navigator.clipboard.writeText(message);
        console.log('[SocialInvite] Invitation message copied to clipboard');
      } catch (error) {
        console.warn('[SocialInvite] Failed to copy to clipboard');
      }
    }
  }

  /**
   * Send email invitation
   */
  private async sendEmailInvitation(invitation: SocialInvitation): Promise<void> {
    const subject = `Join nU Universe - ${invitation.nuvaReward * 100}% NUVA Bonus!`;
    const body = `${invitation.message}\n\nClick here to join: ${this.generateShareableLink(invitation.id)}`;
    
    // Create mailto link
    const mailtoLink = `mailto:${invitation.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    
    // Open default email client
    if (typeof window !== 'undefined') {
      window.open(mailtoLink);
      console.log('[SocialInvite] Email invitation opened in default client');
    }
  }

  /**
   * Accept invitation and process NUVA reward
   */
  async acceptInvitation(invitationId: string, newUserData: { name: string; device: string }): Promise<InvitationResponse> {
    const invitation = this.invitations.get(invitationId);
    
    if (!invitation) {
      return { success: false, error: 'Invitation not found' };
    }

    if (invitation.status !== 'sent') {
      return { success: false, error: 'Invitation already processed or expired' };
    }

    if (Date.now() > invitation.expiresAt) {
      invitation.status = 'expired';
      this.saveInvitations();
      return { success: false, error: 'Invitation has expired' };
    }

    try {
      // Process NUVA reward
      await this.processNUVAReward(invitation, newUserData);

      // Update invitation status
      invitation.status = 'accepted';
      invitation.acceptedAt = Date.now();
      this.saveInvitations();

      console.log(`[SocialInvite] Invitation ${invitationId} accepted, NUVA reward processed`);
      
      return { success: true, invitation };
    } catch (error) {
      console.error('[SocialInvite] Failed to accept invitation:', error);
      return { success: false, error: 'Failed to process invitation' };
    }
  }

  /**
   * Process NUVA reward for both inviter and invitee
   */
  private async processNUVAReward(invitation: SocialInvitation, newUserData: { name: string; device: string }): Promise<void> {
    const baseNuvaAmount = 1.0; // Base NUVA amount
    const rewardAmount = baseNuvaAmount * invitation.nuvaReward;

    // Award NUVA to new user
    await this.awardNUVA(newUserData.name, rewardAmount, 'invitation_bonus');
    
    // Award referral bonus to inviter (10% of reward)
    const referralBonus = rewardAmount * 0.1;
    await this.awardNUVA(invitation.inviterName, referralBonus, 'referral_bonus');

    console.log(`[SocialInvite] NUVA rewards processed: ${rewardAmount} to ${newUserData.name}, ${referralBonus} to ${invitation.inviterName}`);
  }

  /**
   * Award NUVA tokens to user
   */
  private async awardNUVA(userName: string, amount: number, reason: string): Promise<void> {
    try {
      // Send to backend for processing
      const response = await fetch('/api/social-invite/award-nuva', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userName,
          amount,
          reason,
          timestamp: Date.now()
        })
      });

      if (!response.ok) {
        throw new Error('Failed to award NUVA');
      }

      console.log(`[SocialInvite] Awarded ${amount} NUVA to ${userName} for ${reason}`);
    } catch (error) {
      console.error('[SocialInvite] Failed to award NUVA:', error);
    }
  }

  /**
   * Get all invitations for current user
   */
  getInvitations(): SocialInvitation[] {
    return Array.from(this.invitations.values());
  }

  /**
   * Get invitation by ID
   */
  getInvitation(id: string): SocialInvitation | undefined {
    return this.invitations.get(id);
  }

  /**
   * Get invitation statistics
   */
  getInvitationStats() {
    const invitations = Array.from(this.invitations.values());
    
    return {
      total: invitations.length,
      pending: invitations.filter(i => i.status === 'pending').length,
      sent: invitations.filter(i => i.status === 'sent').length,
      accepted: invitations.filter(i => i.status === 'accepted').length,
      expired: invitations.filter(i => i.status === 'expired').length,
      totalNuvaAwarded: invitations
        .filter(i => i.status === 'accepted')
        .reduce((sum, i) => sum + (i.nuvaReward * 1.0), 0)
    };
  }

  // Private helper methods
  private generateInvitationId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `invite_${timestamp}_${random}`;
  }

  private generateQRCode(invitationId: string): string {
    const data = {
      type: 'nu_social_invite',
      invitationId,
      url: this.generateShareableLink(invitationId),
      timestamp: Date.now()
    };
    return JSON.stringify(data);
  }

  private generateShareableLink(invitationId: string): string {
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://nu-universe.replit.app';
    return `${baseUrl}/join?invite=${invitationId}`;
  }

  private getDefaultMessage(inviterName: string): string {
    return `Hi! ${inviterName} invited you to join nU Universe, a revolutionary energy tracking platform. You'll get 25% bonus NUVA tokens just for signing up! It's completely free and helps you track and monetize your device's energy.`;
  }

  private getDeviceInfo(): { name: string; type: string } {
    const userAgent = navigator.userAgent;
    let deviceType = 'desktop';
    let osName = 'unknown';

    if (/iPhone|iPad|iPod/i.test(userAgent)) {
      deviceType = 'mobile';
      osName = 'iOS';
    } else if (/Android/i.test(userAgent)) {
      deviceType = 'mobile';
      osName = 'Android';
    } else if (/Mac/i.test(userAgent)) {
      deviceType = 'desktop';
      osName = 'macOS';
    } else if (/Win/i.test(userAgent)) {
      deviceType = 'desktop';
      osName = 'Windows';
    } else if (/Linux/i.test(userAgent)) {
      deviceType = 'desktop';
      osName = 'Linux';
    }

    return {
      name: `${osName} ${deviceType}`,
      type: deviceType
    };
  }

  private loadInvitations(): void {
    try {
      const saved = localStorage.getItem('social_invitations');
      if (saved) {
        const invitations = JSON.parse(saved);
        this.invitations = new Map(Object.entries(invitations));
      }
    } catch (error) {
      console.warn('[SocialInvite] Failed to load invitations:', error);
    }
  }

  private saveInvitations(): void {
    try {
      const invitations = Object.fromEntries(this.invitations);
      localStorage.setItem('social_invitations', JSON.stringify(invitations));
    } catch (error) {
      console.warn('[SocialInvite] Failed to save invitations:', error);
    }
  }
}

export const socialInvitationSystem = new SocialInvitationSystem();