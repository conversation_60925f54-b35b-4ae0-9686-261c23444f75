
export class DeviceIntegrationManager {
  private batteryAPI: any = null;
  private devices: Map<string, any> = new Map();
  private wakeLock: any = null;

  async initializeBatteryAPI() {
    try {
      // @ts-ignore - Battery API not in TS types
      this.batteryAPI = await navigator.getBattery?.();
      
      if (this.batteryAPI) {
        this.batteryAPI.addEventListener('chargingchange', this.handleBatteryChange.bind(this));
        this.batteryAPI.addEventListener('levelchange', this.handleBatteryChange.bind(this));
        return true;
      }
    } catch (error) {
      console.warn('Battery API not available:', error);
    }
    return false;
  }

  async initializeDeviceAPIs() {
    // Initialize USB devices
    if ('usb' in navigator) {
      try {
        // @ts-ignore - WebUSB API
        const devices = await navigator.usb.getDevices();
        devices.forEach((device: any) => {
          this.devices.set(device.serialNumber || device.productId.toString(), device);
        });
      } catch (error) {
        console.warn('USB API not available:', error);
      }
    }

    // Initialize Bluetooth devices
    if ('bluetooth' in navigator) {
      try {
        // @ts-ignore - Web Bluetooth API
        const device = await navigator.bluetooth.requestDevice({
          acceptAllDevices: true,
          optionalServices: ['battery_service', 'device_information']
        });
        this.devices.set(device.id, device);
      } catch (error) {
        console.warn('Bluetooth API not available:', error);
      }
    }

    return this.devices.size;
  }

  async acquireWakeLock() {
    try {
      // @ts-ignore - Wake Lock API
      this.wakeLock = await navigator.wakeLock?.request('screen');
      return true;
    } catch (error) {
      console.warn('Wake Lock API not available:', error);
      return false;
    }
  }

  getBatteryStatus() {
    if (!this.batteryAPI) return null;
    
    return {
      level: Math.round(this.batteryAPI.level * 100),
      charging: this.batteryAPI.charging,
      chargingTime: this.batteryAPI.chargingTime,
      dischargingTime: this.batteryAPI.dischargingTime
    };
  }

  getConnectedDevices() {
    return Array.from(this.devices.entries()).map(([id, device]) => ({
      id,
      name: device.productName || device.name || 'Unknown Device',
      type: this.detectDeviceType(device),
      connected: device.opened || device.gatt?.connected || true
    }));
  }

  private detectDeviceType(device: any): string {
    if (device.vendorId) return 'usb';
    if (device.gatt) return 'bluetooth';
    return 'unknown';
  }

  private handleBatteryChange() {
    const status = this.getBatteryStatus();
    if (status) {
      // Emit battery change event
      window.dispatchEvent(new CustomEvent('battery-change', { detail: status }));
    }
  }

  async trackInteraction(type: string, data: any) {
    const deviceStatus = this.getBatteryStatus();
    const connectedDevices = this.getConnectedDevices();
    
    return {
      type,
      data,
      batteryLevel: deviceStatus?.level || 0,
      isCharging: deviceStatus?.charging || false,
      deviceCount: connectedDevices.length,
      timestamp: Date.now()
    };
  }
}

export const deviceManager = new DeviceIntegrationManager();
