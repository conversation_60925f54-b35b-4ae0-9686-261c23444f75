
/**
 * AUTHENTIC ENERGY SYNC CONTROLLER - Real Hardware Only
 * Manages authentic energy data synchronization with zero simulation
 */

import { authenticHardwareMonitor } from './authentic-hardware-energy';
import { realBatteryAPI } from './real-battery-api';
import { authenticSpUnderSystem } from './authentic-spunder';

interface AuthenticEnergyBatch {
  energyAmount: number;
  source: string;
  timestamp: number;
  deviceId: string;
  authentic: true;
}

class AuthenticEnergySyncController {
  private energyAccumulator: AuthenticEnergyBatch[] = [];
  private batchProcessingActive = false;
  private syncInterval: number | null = null;
  private readonly BATCH_SIZE = 5;
  private readonly SYNC_INTERVAL = 3000; // 3 seconds

  constructor() {
    this.startAuthenticSync();
    this.subscribeToAuthenticSources();
  }

  /**
   * Add authentic energy to the accumulator
   */
  addEnergy(source: string, amount: number, metadata: any = {}): void {
    const energyBatch: AuthenticEnergyBatch = {
      energyAmount: amount,
      source,
      timestamp: Date.now(),
      deviceId: this.getDeviceId(),
      authentic: true
    };

    this.energyAccumulator.push(energyBatch);
    console.log(`[EnergySyncController] Added ${amount} energy from ${source}`);
  }

  /**
   * Get unique device identifier
   */
  private getDeviceId(): string {
    return `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Start authentic energy synchronization
   */
  private startAuthenticSync(): void {
    console.log('[EnergySyncController] Starting authentic energy sync - real hardware only');
    
    this.syncInterval = setInterval(() => {
      this.processAuthenticEnergyBatch();
    }, this.SYNC_INTERVAL) as any;
  }

  /**
   * Subscribe to authentic energy sources only
   */
  private subscribeToAuthenticSources(): void {
    // Real battery API energy
    realBatteryAPI.subscribe((batteryData) => {
      if (batteryData.isRealDevice) {
        const energyFromBattery = this.calculateBatteryEnergy(batteryData);
        if (energyFromBattery > 0) {
          this.addAuthenticEnergy(energyFromBattery, 'real_battery_api');
        }
      }
    });

    // Real hardware monitor energy
    window.addEventListener('authentic-hardware-energy', ((event: CustomEvent) => {
      const energyData = event.detail;
      if (energyData.authentic) {
        this.addAuthenticEnergy(energyData.energy, 'real_hardware_monitor');
      }
    }) as EventListener);

    // Authentic SpUnder energy
    window.addEventListener('authentic-spunder-energy', ((event: CustomEvent) => {
      const spunderData = event.detail;
      if (spunderData.authentic) {
        this.addAuthenticEnergy(spunderData.energy, 'authentic_spunder');
      }
    }) as EventListener);

    console.log('[EnergySyncController] Subscribed to authentic energy sources only');
  }

  /**
   * Calculate energy from real battery data
   */
  private calculateBatteryEnergy(batteryData: any): number {
    if (!batteryData.isRealDevice || batteryData.charging) return 0;
    
    // Calculate energy based on actual battery drain
    const drainRate = (100 - (batteryData.level * 100)) / 100;
    return drainRate * 0.001; // Convert to UMatter
  }

  /**
   * Add authentic energy to accumulator
   */
  private addAuthenticEnergy(amount: number, source: string): void {
    if (amount <= 0) return;
    
    const authenticBatch: AuthenticEnergyBatch = {
      energyAmount: amount,
      source,
      timestamp: Date.now(),
      deviceId: this.getAuthenticDeviceId(),
      authentic: true
    };
    
    this.energyAccumulator.push(authenticBatch);
    console.log(`[EnergySyncController] Added ${amount.toFixed(6)} UMatter from ${source}, accumulator size: ${this.energyAccumulator.length}`);
    
    // Process batch if it reaches the size limit
    if (this.energyAccumulator.length >= this.BATCH_SIZE) {
      this.processAuthenticEnergyBatch();
    }
  }

  /**
   * Process authentic energy batch
   */
  private async processAuthenticEnergyBatch(): Promise<void> {
    if (this.batchProcessingActive || this.energyAccumulator.length === 0) return;
    
    this.batchProcessingActive = true;
    
    try {
      const batchToProcess = [...this.energyAccumulator];
      this.energyAccumulator = []; // Clear accumulator
      
      const totalEnergy = batchToProcess.reduce((sum, batch) => sum + batch.energyAmount, 0);
      
      console.log(`[EnergySyncController] Processing batch: ${batchToProcess.length} items, ${totalEnergy.toFixed(6)} UMatter total`);
      
      // Send authentic energy to backend
      const response = await fetch('/api/energy/deposit-batch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          energyBatches: batchToProcess,
          totalEnergy,
          authentic: true
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log(`[EnergySyncController] ✅ Batch processed successfully: ${batchToProcess.length} items`);
        
        // Dispatch success event
        window.dispatchEvent(new CustomEvent('authentic-energy-synced', {
          detail: {
            totalEnergy,
            batchSize: batchToProcess.length,
            authentic: true
          }
        }));
      } else {
        console.error('[EnergySyncController] Batch processing failed, re-queuing');
        // Re-add failed batch to accumulator
        this.energyAccumulator.unshift(...batchToProcess);
      }
      
    } catch (error) {
      console.error('[EnergySyncController] Batch processing error:', error);
    } finally {
      this.batchProcessingActive = false;
    }
  }

  /**
   * Get authentic device ID
   */
  private getAuthenticDeviceId(): string {
    return `authentic_${navigator.hardwareConcurrency}_${screen.width}x${screen.height}`;
  }

  /**
   * Get accumulator status
   */
  getAccumulatorStatus(): { size: number; pendingEnergy: number } {
    const pendingEnergy = this.energyAccumulator.reduce((sum, batch) => sum + batch.energyAmount, 0);
    return {
      size: this.energyAccumulator.length,
      pendingEnergy
    };
  }

  /**
   * Force process current batch
   */
  async forceProcessBatch(): Promise<void> {
    await this.processAuthenticEnergyBatch();
  }

  /**
   * Stop authentic sync
   */
  stopAuthenticSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    console.log('[EnergySyncController] Authentic energy sync stopped');
  }
}

export const authenticEnergySyncController = new AuthenticEnergySyncController();
export const energySyncController = authenticEnergySyncController; // Alias for compatibility
export type { AuthenticEnergyBatch };
