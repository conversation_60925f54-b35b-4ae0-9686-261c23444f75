@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&family=Orbitron:wght@400;700;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 10%, 3.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(187, 100%, 50%);
  --primary-foreground: hsl(240, 10%, 3.9%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;

  /* nU Universe Custom Colors */
  --space: hsl(240, 10%, 3.9%);
  --panel: hsl(240, 10%, 11.8%);
  --neon-cyan: hsl(187, 100%, 50%);
  --neon-purple: hsl(266, 85%, 58%);
  --text-primary: hsl(0, 0%, 98%);
  --text-secondary: hsl(240, 5%, 64.9%);
}

.light {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(240, 10%, 3.9%);
  --muted: hsl(240, 4.8%, 95.9%);
  --muted-foreground: hsl(240, 3.8%, 46.1%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(240, 10%, 3.9%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(240, 10%, 3.9%);
  --border: hsl(240, 5.9%, 90%);
  --input: hsl(240, 5.9%, 90%);
  --primary: hsl(187, 100%, 50%);
  --primary-foreground: hsl(0, 0%, 98%);
  --secondary: hsl(240, 4.8%, 95.9%);
  --secondary-foreground: hsl(240, 5.9%, 10%);
  --accent: hsl(240, 4.8%, 95.9%);
  --accent-foreground: hsl(240, 5.9%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 5.9%, 10%);

  /* nU Universe Custom Colors for Light Mode */
  --space: hsl(0, 0%, 100%);
  --panel: hsl(240, 4.8%, 95.9%);
  --neon-cyan: hsl(187, 100%, 50%);
  --neon-purple: hsl(266, 85%, 58%);
  --text-primary: hsl(240, 10%, 3.9%);
  --text-secondary: hsl(240, 3.8%, 46.1%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-inter antialiased text-foreground;
    background: var(--space);
    min-height: 100vh;
  }

  .cosmic-bg {
    background: radial-gradient(circle at 20% 50%, rgba(157, 78, 221, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(157, 78, 221, 0.05) 0%, transparent 50%);
  }

  .glass-panel {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
}

@layer components {
  .text-space { color: var(--space); }
  .text-panel { color: var(--panel); }
  .text-neon-cyan { color: var(--neon-cyan); }
  .text-neon-purple { color: var(--neon-purple); }
  .text-neon-green { color: hsl(140, 100%, 50%); }
  .text-text-primary { color: var(--text-primary); }
  .text-text-secondary { color: var(--text-secondary); }

  .bg-space { background-color: var(--space); }
  .bg-panel { background-color: var(--panel); }
  .bg-neon-cyan { background-color: var(--neon-cyan); }
  .bg-neon-purple { background-color: var(--neon-purple); }
  .bg-neon-green { background-color: hsl(140, 100%, 50%); }

  .border-space { border-color: var(--space); }
  .border-panel { border-color: var(--panel); }
  .border-neon-cyan { border-color: var(--neon-cyan); }
  .border-neon-purple { border-color: var(--neon-purple); }
  .border-neon-green { border-color: hsl(140, 100%, 50%); }

  .font-inter { font-family: 'Inter', sans-serif; }
  .font-mono { font-family: 'JetBrains Mono', monospace; }
  .font-orbitron { font-family: 'Orbitron', sans-serif; }
}

@layer utilities {
  .animate-pulse-neon {
    animation: pulse-neon 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .node-connection {
    stroke-dasharray: 5,5;
    animation: dash 2s linear infinite;
  }
}

@keyframes pulse-neon {
  0%, 100% { 
    box-shadow: 0 0 5px var(--neon-cyan), 0 0 10px var(--neon-cyan), 0 0 15px var(--neon-cyan);
  }
  50% { 
    box-shadow: 0 0 10px var(--neon-cyan), 0 0 20px var(--neon-cyan), 0 0 30px var(--neon-cyan);
  }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@keyframes glow {
  from { text-shadow: 0 0 5px var(--neon-cyan); }
  to { text-shadow: 0 0 20px var(--neon-cyan), 0 0 30px var(--neon-purple); }
}

@keyframes dash {
  to { stroke-dashoffset: -10; }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--panel);
}

::-webkit-scrollbar-thumb {
  background: var(--neon-cyan);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neon-purple);
}

/* Loading animations */
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* Notification styles */
.notification-enter {
  transform: translateX(100%);
  opacity: 0;
}

.notification-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: all 300ms ease-out;
}

.notification-exit {
  transform: translateX(0);
  opacity: 1;
}

.notification-exit-active {
  transform: translateX(100%);
  opacity: 0;
  transition: all 300ms ease-in;
}
