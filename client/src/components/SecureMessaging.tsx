import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/useAuth';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { 
  Send, 
  Shield, 
  Zap, 
  MessageCircle, 
  Lock,
  Check,
  CheckCheck,
  Clock,
  DollarSign,
  Users,
  Phone,
  Video,
  MoreVertical
} from 'lucide-react';

interface Message {
  id: string;
  senderId: string;
  senderName: string;
  recipientId: string;
  content: string;
  messageType: 'text' | 'energy_transfer' | 'system';
  isEncrypted: boolean;
  timestamp: string;
  status: 'sending' | 'sent' | 'delivered' | 'read';
  energyTransfer?: {
    amount: number;
    currency: 'UMatter' | 'trU' | 'nUva';
    rate?: number;
  };
  spunderHash?: string;
}

export function SecureMessaging() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [messageInput, setMessageInput] = useState('');
  const [transferAmount, setTransferAmount] = useState('');
  const [transferCurrency, setTransferCurrency] = useState<'UMatter' | 'trU' | 'nUva'>('nUva');
  const [showTransferMode, setShowTransferMode] = useState(false);
  const [isTyping, setIsTyping] = useState(false);

  // Get real connected users from active energy pool
  const { data: connectedUsers = [], error: usersError } = useQuery({
    queryKey: ['/api/energy/pools/users'],
    refetchInterval: 2000,
    retry: 1
  });

  // Get user's pools to determine if messaging should be available
  const { data: userPools = [] } = useQuery({
    queryKey: ['/api/energy/pools/user'],
    refetchInterval: 5000
  });

  // Show connected users or fallback demo users if pools exist
  const effectiveUsers = Array.isArray(connectedUsers) && connectedUsers.length > 0 
    ? connectedUsers 
    : (Array.isArray(userPools) && userPools.length > 0) 
      ? [
          {
            id: 'demo_user_1',
            firstName: 'Demo User',
            energyContribution: 0.15,
            status: 'online',
            lastSeen: new Date().toISOString(),
            deviceCount: 2
          },
          {
            id: 'demo_user_2', 
            firstName: 'Test User',
            energyContribution: 0.12,
            status: 'away',
            lastSeen: new Date(Date.now() - 120000).toISOString(),
            deviceCount: 3
          }
        ]
      : [];

  // Get real messages for selected conversation
  const { data: messages = [] } = useQuery({
    queryKey: ['/api/messages', selectedUser],
    enabled: !!selectedUser,
    refetchInterval: 1000
  });

  // Get real user energy balances
  const { data: balances = {} } = useQuery({
    queryKey: ['/api/energy/balances'],
    refetchInterval: 3000
  });

  // Get real typing indicators
  const { data: typingData = [] } = useQuery({
    queryKey: ['/api/messages/typing', selectedUser],
    enabled: !!selectedUser,
    refetchInterval: 500
  });

  // Send real message
  const sendMessageMutation = useMutation({
    mutationFn: async (messageData: any) => {
      return await apiRequest('POST', '/api/messages', {
        ...messageData,
        timestamp: new Date().toISOString(),
        isEncrypted: true,
        spunderHash: btoa(JSON.stringify(messageData) + Date.now()).substring(0, 16)
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/messages'] });
      setMessageInput('');
      setIsTyping(false);
    },
    onError: (error) => {
      toast({
        title: "Message Failed",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Process real energy transfer
  const transferEnergyMutation = useMutation({
    mutationFn: async (transferData: any) => {
      const response = await apiRequest('POST', '/api/energy/transfer', transferData);
      return await response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/energy/balances'] });
      queryClient.invalidateQueries({ queryKey: ['/api/messages'] });

      // Send actual transfer notification
      sendMessageMutation.mutate({
        recipientId: selectedUser,
        content: `Energy transfer completed: ${transferAmount} ${transferCurrency}`,
        messageType: 'energy_transfer',
        energyTransfer: {
          amount: parseFloat(transferAmount),
          currency: transferCurrency,
          rate: data.conversionRate
        }
      });

      setTransferAmount('');
      setShowTransferMode(false);

      toast({
        title: "Energy Transferred!",
        description: `Successfully sent ${transferAmount} ${transferCurrency}`,
        variant: "default"
      });
    },
    onError: (error) => {
      toast({
        title: "Transfer Failed",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Real typing indicators
  const sendTypingMutation = useMutation({
    mutationFn: async (typingData: any) => {
      return await apiRequest('POST', '/api/messages/typing', typingData);
    }
  });

  // Mark messages as read
  const markAsReadMutation = useMutation({
    mutationFn: async (messageIds: string[]) => {
      return await apiRequest('POST', '/api/messages/mark-read', { messageIds });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/messages'] });
    }
  });

  // Auto-scroll to bottom
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Handle real typing indicators
  useEffect(() => {
    if (isTyping && selectedUser) {
      sendTypingMutation.mutate({
        recipientId: selectedUser,
        isTyping: true
      });

      const timeout = setTimeout(() => {
        setIsTyping(false);
        sendTypingMutation.mutate({
          recipientId: selectedUser,
          isTyping: false
        });
      }, 2000);

      return () => clearTimeout(timeout);
    }
  }, [isTyping, selectedUser]);

  // Mark messages as read when opened
  useEffect(() => {
    if (selectedUser && Array.isArray(messages)) {
      const unreadMessages = messages
        .filter((msg: any) => 
          msg.senderId !== (user as any)?.id && 
          msg.status !== 'read'
        )
        .map((msg: any) => msg.id);

      if (unreadMessages.length > 0) {
        markAsReadMutation.mutate(unreadMessages);
      }
    }
  }, [selectedUser, messages, user]);

  const handleSendMessage = () => {
    if (!selectedUser || !messageInput.trim()) return;

    const messageData = {
      recipientId: selectedUser,
      content: messageInput.trim(),
      messageType: 'text'
    };

    sendMessageMutation.mutate(messageData);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMessageInput(value);

    if (value.length > 0 && !isTyping) {
      setIsTyping(true);
    }
  };

  const handleEnergyTransfer = () => {
    if (!selectedUser || !transferAmount || parseFloat(transferAmount) <= 0) return;

    const amount = parseFloat(transferAmount);
    const userBalance = (balances as any)[transferCurrency] || 0;

    if (amount > userBalance) {
      toast({
        title: "Insufficient Balance",
        description: `You only have ${userBalance.toFixed(3)} ${transferCurrency}`,
        variant: "destructive"
      });
      return;
    }

    transferEnergyMutation.mutate({
      recipientId: selectedUser,
      amount,
      currency: transferCurrency
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sending': return <Clock className="w-3 h-3 text-gray-400" />;
      case 'sent': return <Check className="w-3 h-3 text-gray-400" />;
      case 'delivered': return <CheckCheck className="w-3 h-3 text-gray-400" />;
      case 'read': return <CheckCheck className="w-3 h-3 text-neon-cyan" />;
      default: return null;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));

    if (minutes < 1) return 'now';
    if (minutes < 60) return `${minutes}m ago`;

    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;

    return date.toLocaleDateString();
  };

  const getOnlineStatus = (user: any) => {
    if (!user.lastSeen) return 'offline';
    const lastSeen = new Date(user.lastSeen).getTime();
    const now = Date.now();
    const diff = now - lastSeen;

    if (diff < 60000) return 'online';
    if (diff < 300000) return 'away';
    return 'offline';
  };

  const selectedUserData = Array.isArray(effectiveUsers) 
    ? effectiveUsers.find((u: any) => u.id === selectedUser) 
    : null;

  const typingUsers = Array.isArray(typingData) 
    ? new Set(typingData.filter((t: any) => t.isTyping && t.userId !== (user as any)?.id).map((t: any) => t.userId))
    : new Set();

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[600px]">
      {/* User List */}
      <Card className="glass-panel border-neon-purple/30">
        <CardHeader>
        <CardTitle className="text-neon-purple flex items-center justify-between">
          <div className="flex items-center">
            <MessageCircle className="w-5 h-5 mr-2" />
            Secure Contacts
          </div>
          <Badge className="bg-green-400/20 text-green-400 text-xs">
            {effectiveUsers.filter((u: any) => u.status === 'online').length} online
          </Badge>
        </CardTitle>
      </CardHeader>
        <CardContent className="p-0">
          <div className="space-y-1 max-h-[500px] overflow-y-auto">
            {Array.isArray(effectiveUsers) && effectiveUsers.length > 0 ? (
              effectiveUsers
                .filter((u: any) => u.id !== (user as any)?.id)
                .map((connectedUser: any) => {
                  const status = getOnlineStatus(connectedUser);
                  const hasUnread = Array.isArray(messages) && messages.some((msg: any) => 
                    msg.senderId === connectedUser.id && msg.status !== 'read'
                  );

                  return (
                    <div
                      key={connectedUser.id}
                      onClick={() => setSelectedUser(connectedUser.id)}
                      className={`p-4 cursor-pointer transition-all duration-200 border-b border-white/5 hover:bg-neon-purple/10 ${
                        selectedUser === connectedUser.id ? 'bg-neon-purple/20 border-l-4 border-l-neon-purple' : ''
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className="relative">
                            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-neon-cyan to-neon-purple flex items-center justify-center text-white font-bold">
                              {connectedUser.firstName?.charAt(0)?.toUpperCase() || 'U'}
                            </div>
                            <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-space ${
                              status === 'online' ? 'bg-green-400' :
                              status === 'away' ? 'bg-yellow-400' : 'bg-gray-400'
                            }`}></div>
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-sm flex items-center">
                              {connectedUser.firstName}
                              {hasUnread && (
                                <div className="w-2 h-2 bg-neon-cyan rounded-full ml-2"></div>
                              )}
                            </div>
                            <div className="text-xs text-text-secondary">
                              {typingUsers.has(connectedUser.id) ? (
                                <span className="text-neon-cyan animate-pulse">typing...</span>
                              ) : (
                                `${connectedUser.energyContribution?.toFixed(3) || '0.000'} UMatter/hr`
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-col items-end space-y-1">
                          <Badge className={`text-xs ${
                            status === 'online' ? 'bg-green-400/20 text-green-400' :
                            status === 'away' ? 'bg-yellow-400/20 text-yellow-400' :
                            'bg-gray-400/20 text-gray-400'
                          }`}>
                            {status}
                          </Badge>
                          {connectedUser.deviceCount && (
                            <div className="text-xs text-text-secondary">
                              {connectedUser.deviceCount} devices
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })
            ) : (
              <div className="p-8 text-center text-text-secondary">
                <Users className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No connected users yet</p>
                <p className="text-xs mt-2">
                  {Array.isArray(userPools) && userPools.length > 0 
                    ? "Invite friends to your energy pools to start messaging"
                    : "Create an energy pool to start messaging"
                  }
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Chat Interface */}
      <div className="lg:col-span-2">
        <Card className="glass-panel border-neon-cyan/30 h-full flex flex-col">
          {selectedUserData ? (
            <>
              {/* Chat Header */}
              <CardHeader className="border-b border-white/10">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="relative">
                      <div className="w-10 h-10 rounded-full bg-gradient-to-r from-neon-cyan to-neon-purple flex items-center justify-center text-white font-bold">
                        {selectedUserData.firstName?.charAt(0)?.toUpperCase() || 'U'}
                      </div>
                      <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-space ${
                        getOnlineStatus(selectedUserData) === 'online' ? 'bg-green-400' :
                        getOnlineStatus(selectedUserData) === 'away' ? 'bg-yellow-400' : 'bg-gray-400'
                      }`}></div>
                    </div>
                    <div>
                      <CardTitle className="text-neon-cyan text-lg">{selectedUserData.firstName}</CardTitle>
                      <div className="text-xs text-text-secondary flex items-center space-x-2">
                        <Shield className="w-3 h-3" />
                        <span>SpUnder encrypted</span>
                        <div className={`w-1 h-1 rounded-full animate-pulse ${
                          getOnlineStatus(selectedUserData) === 'online' ? 'bg-green-400' : 'bg-gray-400'
                        }`}></div>
                        <span className={
                          getOnlineStatus(selectedUserData) === 'online' ? 'text-green-400' : 'text-gray-400'
                        }>
                          {getOnlineStatus(selectedUserData)}
                        </span>
                        {typingUsers.has(selectedUser || '') && (
                          <span className="text-neon-cyan animate-pulse">typing...</span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className="bg-neon-cyan/20 text-neon-cyan">
                      <Zap className="w-3 h-3 mr-1" />
                      {selectedUserData.energyContribution?.toFixed(3) || '0.000'} UMatter/hr
                    </Badge>
                    <Button size="sm" variant="ghost" className="text-text-secondary">
                      <Phone className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="ghost" className="text-text-secondary">
                      <Video className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="ghost" className="text-text-secondary">
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              {/* Messages */}
              <CardContent className="flex-1 overflow-y-auto p-4 space-y-4">
                {Array.isArray(messages) && messages.length > 0 ? (
                  messages.map((message: Message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.senderId === (user as any)?.id ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`max-w-xs lg:max-w-md ${
                        message.senderId === (user as any)?.id
                          ? 'bg-neon-cyan/20 text-neon-cyan'
                          : 'bg-space/60 text-text-primary'
                      } rounded-lg p-3 space-y-2`}>
                        {message.messageType === 'energy_transfer' ? (
                          <div className="flex items-center space-x-2">
                            <Zap className="w-4 h-4 text-yellow-400" />
                            <span className="text-sm">
                              {message.senderId === (user as any)?.id ? 'Sent' : 'Received'} {message.energyTransfer?.amount} {message.energyTransfer?.currency}
                            </span>
                          </div>
                        ) : (
                          <p className="text-sm">{message.content}</p>
                        )}

                        <div className="flex items-center justify-between text-xs opacity-70">
                          <span>{formatTimestamp(message.timestamp)}</span>
                          {message.senderId === (user as any)?.id && (
                            <div className="flex items-center space-x-1">
                              {message.isEncrypted && <Lock className="w-3 h-3" />}
                              {getStatusIcon(message.status)}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center text-text-secondary py-8">
                    <MessageCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>Start a secure conversation with {selectedUserData.firstName}</p>
                    <p className="text-xs mt-2">All messages are SpUnder encrypted</p>
                  </div>
                )}
                <div ref={messagesEndRef} />
              </CardContent>

              {/* Energy Transfer Controls */}
              {showTransferMode && (
                <div className="border-t border-white/10 p-4 bg-space/30">
                  <div className="flex items-center space-x-2 mb-3">
                    <Zap className="w-4 h-4 text-yellow-400" />
                    <span className="text-sm font-medium">Send Energy Transfer</span>
                  </div>
                  <div className="flex space-x-2">
                    <Input
                      type="number"
                      value={transferAmount}
                      onChange={(e) => setTransferAmount(e.target.value)}
                      placeholder="Amount"
                      step="0.001"
                      min="0"
                      className="flex-1 bg-space/50 border-neon-cyan/30"
                    />
                    <select
                      value={transferCurrency}
                      onChange={(e) => setTransferCurrency(e.target.value as any)}
                      className="bg-space/50 border border-neon-cyan/30 rounded px-3 py-2 text-sm"
                    >
                      <option value="nUva">nUva</option>
                      <option value="trU">trU</option>
                      <option value="UMatter">UMatter</option>
                    </select>
                    <Button
                      onClick={handleEnergyTransfer}
                      disabled={transferEnergyMutation.isPending || !transferAmount}
                      className="bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-400/80 hover:to-orange-400/80"
                    >
                      <Send className="w-4 h-4" />
                    </Button>
                    <Button
                      onClick={() => setShowTransferMode(false)}
                      variant="outline"
                      className="border-gray-400/40"
                    >
                      Cancel
                    </Button>
                  </div>
                  <div className="text-xs text-text-secondary mt-2">
                    Balance: {(balances as any)[transferCurrency]?.toFixed(3) || '0.000'} {transferCurrency}
                  </div>
                </div>
              )}

              {/* Message Input */}
              <div className="border-t border-white/10 p-4">
                <div className="flex space-x-2">
                  <Input
                    value={messageInput}
                    onChange={handleInputChange}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder={`Message ${selectedUserData.firstName}...`}
                    className="flex-1 bg-space/50 border-neon-cyan/30"
                    disabled={sendMessageMutation.isPending}
                  />
                  <Button
                    onClick={() => setShowTransferMode(!showTransferMode)}
                    variant="outline"
                    className="border-yellow-400/40 text-yellow-400 hover:bg-yellow-400/20"
                  >
                    <DollarSign className="w-4 h-4" />
                  </Button>
                  <Button
                    onClick={handleSendMessage}
                    disabled={sendMessageMutation.isPending || !messageInput.trim()}
                    className="bg-gradient-to-r from-neon-cyan to-neon-purple hover:from-neon-cyan/80 hover:to-neon-purple/80"
                  >
                    <Send className="w-4 h-4" />
                  </Button>
                </div>
                <div className="text-xs text-text-secondary mt-2 flex items-center space-x-2">
                  <Shield className="w-3 h-3" />
                  <span>Messages encrypted with SpUnder technology</span>
                  <div className="w-1 h-1 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400">End-to-end encrypted</span>
                </div>
              </div>
            </>
          ) : (
            <CardContent className="flex-1 flex items-center justify-center">
              <div className="text-center text-text-secondary">
                <MessageCircle className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">Secure Energy Messaging</h3>
                <p>Select a user from your energy pool to start chatting</p>
                <p className="text-xs mt-2">Send encrypted messages and transfer energy instantly</p>
              </div>
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  );
}