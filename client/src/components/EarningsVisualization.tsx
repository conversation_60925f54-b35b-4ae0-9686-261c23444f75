import { useEffect, useRef, useState } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, DollarSign, Shield, Zap } from "lucide-react";

interface EarningsData {
  date: string;
  amount: number;
  company: string;
  dataType: string;
  privacyLevel: number;
}

interface EarningsVisualizationProps {
  earnings: EarningsData[];
  totalEarnings: number;
}

export function EarningsVisualization({ earnings, totalEarnings }: EarningsVisualizationProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  const [hoveredPoint, setHoveredPoint] = useState<EarningsData | null>(null);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    ctx.scale(dpr, dpr);

    let animationProgress = 0;

    const animate = () => {
      animationProgress += 0.02;
      if (animationProgress > 1) animationProgress = 1;

      ctx.clearRect(0, 0, rect.width, rect.height);
      
      // Create 3D-style earnings flow visualization
      drawEarningsFlow(ctx, rect.width, rect.height, animationProgress);
      
      if (animationProgress < 1) {
        animationRef.current = requestAnimationFrame(animate);
      }
    };

    animate();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [earnings]);

  const drawEarningsFlow = (ctx: CanvasRenderingContext2D, width: number, height: number, progress: number) => {
    const centerX = width / 2;
    const centerY = height / 2;

    // Draw background grid
    ctx.strokeStyle = 'rgba(0, 212, 255, 0.1)';
    ctx.lineWidth = 1;
    for (let i = 0; i < width; i += 20) {
      ctx.beginPath();
      ctx.moveTo(i, 0);
      ctx.lineTo(i, height);
      ctx.stroke();
    }
    for (let i = 0; i < height; i += 20) {
      ctx.beginPath();
      ctx.moveTo(0, i);
      ctx.lineTo(width, i);
      ctx.stroke();
    }

    // Draw central user node
    const userRadius = 30 * progress;
    const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, userRadius);
    gradient.addColorStop(0, 'rgba(0, 212, 255, 0.8)');
    gradient.addColorStop(1, 'rgba(157, 78, 221, 0.4)');
    
    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.arc(centerX, centerY, userRadius, 0, Math.PI * 2);
    ctx.fill();

    // Draw earnings nodes around the center
    earnings.slice(0, Math.floor(earnings.length * progress)).forEach((earning, index) => {
      const angle = (index * (Math.PI * 2)) / earnings.length + Date.now() * 0.001;
      const distance = 80 + Math.sin(Date.now() * 0.002 + index) * 20;
      const x = centerX + Math.cos(angle) * distance;
      const y = centerY + Math.sin(angle) * distance;

      // Draw connection line
      ctx.strokeStyle = `rgba(0, 212, 255, ${0.6 * progress})`;
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.lineTo(x, y);
      ctx.stroke();

      // Draw earning node
      const nodeRadius = 8 + (earning.amount / 100) * 2;
      const nodeGradient = ctx.createRadialGradient(x, y, 0, x, y, nodeRadius);
      nodeGradient.addColorStop(0, 'rgba(0, 255, 127, 0.9)');
      nodeGradient.addColorStop(1, 'rgba(0, 255, 127, 0.3)');
      
      ctx.fillStyle = nodeGradient;
      ctx.beginPath();
      ctx.arc(x, y, nodeRadius, 0, Math.PI * 2);
      ctx.fill();

      // Draw earning amount
      ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
      ctx.font = '10px monospace';
      ctx.textAlign = 'center';
      ctx.fillText(`$${(earning.amount / 100).toFixed(0)}`, x, y - nodeRadius - 5);
    });

    // Draw data flow particles
    for (let i = 0; i < 20; i++) {
      const particleProgress = (Date.now() * 0.001 + i * 0.1) % 1;
      const angle = i * 0.3;
      const startX = centerX + Math.cos(angle) * 30;
      const startY = centerY + Math.sin(angle) * 30;
      const endX = centerX + Math.cos(angle) * 100;
      const endY = centerY + Math.sin(angle) * 100;
      
      const particleX = startX + (endX - startX) * particleProgress;
      const particleY = startY + (endY - startY) * particleProgress;
      
      ctx.fillStyle = `rgba(157, 78, 221, ${(1 - particleProgress) * 0.8})`;
      ctx.beginPath();
      ctx.arc(particleX, particleY, 2, 0, Math.PI * 2);
      ctx.fill();
    }

    // Draw total earnings in center
    ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.font = 'bold 16px monospace';
    ctx.textAlign = 'center';
    ctx.fillText(`$${(totalEarnings / 100).toFixed(2)}`, centerX, centerY + 5);
    
    ctx.font = '10px monospace';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.fillText('Total Earnings', centerX, centerY + 20);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;
    
    setMousePos({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
  };

  const formatCurrency = (cents: number) => `$${(cents / 100).toFixed(2)}`;

  return (
    <Card className="glass-panel border-neon-cyan/30">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-orbitron text-neon-cyan">
            Earnings Flow Visualization
          </CardTitle>
          <Badge variant="outline" className="text-green-400 border-green-400">
            <TrendingUp className="w-3 h-3 mr-1" />
            Live Data
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* 3D Canvas Visualization */}
        <div className="relative bg-space/50 rounded-lg border border-neon-cyan/20 overflow-hidden">
          <canvas
            ref={canvasRef}
            width={400}
            height={300}
            className="w-full h-[300px] cursor-crosshair"
            onMouseMove={handleMouseMove}
          />
          
          {/* Overlay controls */}
          <div className="absolute top-2 right-2 flex space-x-2">
            <div className="bg-panel/80 px-2 py-1 rounded text-xs flex items-center space-x-1">
              <Shield className="w-3 h-3 text-neon-cyan" />
              <span>Encrypted</span>
            </div>
            <div className="bg-panel/80 px-2 py-1 rounded text-xs flex items-center space-x-1">
              <Zap className="w-3 h-3 text-green-400" />
              <span>Real-time</span>
            </div>
          </div>
        </div>

        {/* Recent Earnings Summary */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <div className="bg-panel/50 p-3 rounded border border-neon-cyan/20">
            <div className="text-xs text-text-secondary">Today</div>
            <div className="text-lg font-bold text-neon-cyan">
              {formatCurrency(
                earnings
                  .filter(e => new Date(e.date).toDateString() === new Date().toDateString())
                  .reduce((sum, e) => sum + e.amount, 0)
              )}
            </div>
          </div>
          
          <div className="bg-panel/50 p-3 rounded border border-green-400/20">
            <div className="text-xs text-text-secondary">This Week</div>
            <div className="text-lg font-bold text-green-400">
              {formatCurrency(
                earnings
                  .filter(e => {
                    const week = new Date();
                    week.setDate(week.getDate() - 7);
                    return new Date(e.date) >= week;
                  })
                  .reduce((sum, e) => sum + e.amount, 0)
              )}
            </div>
          </div>
          
          <div className="bg-panel/50 p-3 rounded border border-yellow-400/20">
            <div className="text-xs text-text-secondary">Avg/Deal</div>
            <div className="text-lg font-bold text-yellow-400">
              {earnings.length > 0 ? formatCurrency(Math.floor(totalEarnings / earnings.length)) : '$0.00'}
            </div>
          </div>
          
          <div className="bg-panel/50 p-3 rounded border border-purple-400/20">
            <div className="text-xs text-text-secondary">Total Deals</div>
            <div className="text-lg font-bold text-purple-400">
              {earnings.length}
            </div>
          </div>
        </div>

        {/* Company Breakdown */}
        {earnings.length > 0 && (
          <div className="space-y-2">
            <div className="text-sm font-semibold text-text-secondary">Recent Companies</div>
            <div className="flex flex-wrap gap-2">
              {Array.from(new Set(earnings.map(e => e.company))).slice(0, 6).map(company => (
                <Badge key={company} variant="outline" className="text-xs">
                  {company}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* TRU Transaction Info */}
        <div className="bg-gradient-to-r from-neon-cyan/10 to-neon-purple/10 p-3 rounded border border-neon-cyan/30">
          <div className="flex items-center space-x-2 mb-2">
            <DollarSign className="w-4 h-4 text-neon-cyan" />
            <span className="text-sm font-semibold">TRU Transactions</span>
          </div>
          <div className="text-xs text-text-secondary">
            All earnings are processed instantly via secure TRU blockchain transactions. 
            Your data ownership is verified through SpUnder encryption before each transfer.
          </div>
        </div>
      </CardContent>
    </Card>
  );
}