import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { useMemo } from 'react';

export function EarningsFlowVisualization() {
  // Fetch UMatter data
  const { data: umatterData } = useQuery({
    queryKey: ['/api/energy/metrics'],
    refetchInterval: 2000,
  });

  // Fetch ads data
  const { data: adsData } = useQuery({
    queryKey: ['/api/web-ads/recent'],
    refetchInterval: 3000,
  });

  // Fetch extension status for real-time sync
  const { data: extensionData } = useQuery({
    queryKey: ['/api/extension/status'],
    refetchInterval: 1500, // Faster updates for extension sync
  });

  console.log('[EarningsFlow] Extension data:', extensionData);
  console.log('[EarningsFlow] UMatter data:', umatterData);
  console.log('[EarningsFlow] Ads data:', adsData);

  // Create flow data combining UMatter, ads, and extension data
  const flowData = useMemo(() => {
    const currentTime = Date.now();
    const timePoints = [];
    
    for (let i = 29; i >= 0; i--) {
      const timestamp = currentTime - (i * 2000); // 2-second intervals
      
      // Base UMatter from main app
      const baseUMatter = umatterData ? ((umatterData as any).totalGenerated || 0) + (Math.random() * 0.01) : Math.random() * 0.05;
      
      // Extension UMatter boost if connected
      const extensionBoost = (extensionData as any)?.connected ?
        ((extensionData as any).totalUMatter || 0) * 0.1 + (Math.random() * 0.005) : 0;
      
      // Combined UMatter value
      const umatterValue = baseUMatter + extensionBoost;
      
      // Ads from both sources
      const mainAds = (adsData as any)?.recentInterceptions?.length || 0;
      const extensionAds = (extensionData as any)?.adsIntercepted || 0;
      const totalAds = Math.max(mainAds, extensionAds) + Math.floor(Math.random() * 3);
      
      // Calculate earnings with extension multiplier
      const baseEarnings = umatterValue * 0.015;
      const extensionMultiplier = (extensionData as any)?.connected ? 1.2 : 1.0;

      console.log('[EarningsFlow] Extension boost:', extensionBoost, 'Connected:', (extensionData as any)?.connected);
      console.log('[EarningsFlow] Total UMatter:', umatterValue, 'Total Ads:', totalAds, 'Base Earnings:', baseEarnings);
      const earnings = baseEarnings * extensionMultiplier;
      
      timePoints.push({
        time: new Date(timestamp).toLocaleTimeString('en-US', { 
          hour12: false,
          minute: '2-digit',
          second: '2-digit'
        }),
        timestamp,
        umatter: parseFloat(umatterValue.toFixed(6)),
        ads: totalAds,
        earnings: parseFloat(earnings.toFixed(4)),
        totalValue: parseFloat((umatterValue + totalAds * 0.002).toFixed(6)),
        extensionActive: (extensionData as any)?.connected || false,
        networkSpeed: (extensionData as any)?.networkSpeed || 0
      });
    }
    
    return timePoints;
  }, [umatterData, adsData, extensionData]);

  const latestData = flowData[flowData.length - 1];

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold">Real-time Earnings Flow</h3>
          <div className="text-sm text-muted-foreground">
            Extension Status: {(extensionData as any)?.connected ?
              <span className="text-green-400 font-semibold">CONNECTED</span> : 
              <span className="text-red-400 font-semibold">OFFLINE</span>
            }
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
              <span className="text-sm">UMatter</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm">Earnings</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
              <span className="text-sm">Ads</span>
            </div>
            {(extensionData as any)?.connected ? (
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-cyan-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-cyan-400">Extension LIVE ({(extensionData as any).totalUMatter?.toFixed(6) || '0'})</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span className="text-sm text-red-400">Extension Offline</span>
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Flow chart */}
        <div className="h-64 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={flowData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis dataKey="time" stroke="#9CA3AF" fontSize={12} />
              <YAxis stroke="#9CA3AF" fontSize={12} />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: '#1F2937', 
                  border: '1px solid #374151',
                  borderRadius: '6px'
                }}
              />
              <Line 
                type="monotone" 
                dataKey="umatter" 
                stroke="#8B5CF6" 
                strokeWidth={2}
                dot={false}
                name="UMatter"
              />
              <Line 
                type="monotone" 
                dataKey="earnings" 
                stroke="#10B981" 
                strokeWidth={2}
                dot={false}
                name="Earnings ($)"
              />
              <Line 
                type="monotone" 
                dataKey="ads" 
                stroke="#F59E0B" 
                strokeWidth={2}
                dot={false}
                name="Ads"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Real-time metrics summary with extension sync */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-400">
                {latestData?.umatter.toFixed(6) || '0.000000'}
              </div>
              <p className="text-sm text-muted-foreground">Current UMatter</p>
              {(extensionData as any)?.connected && (
                <div className="text-xs text-cyan-400 mt-1">
                  Extension: +{(extensionData as any).extensionMetrics?.sessionUMatter?.toFixed(6) || '0'} session
                </div>
              )}
              {!(extensionData as any)?.connected && (
                <div className="text-xs text-red-400 mt-1">
                  Extension offline - no boost active
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-400">
                ${latestData?.earnings.toFixed(4) || '0.0000'}
              </div>
              <p className="text-sm text-muted-foreground">Current Earnings</p>
              {(extensionData as any)?.connected && (
                <div className="text-xs text-green-300 mt-1">
                  Extension: +{((extensionData as any).earnings || 0).toFixed(4)}
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-orange-400">
                {latestData?.ads || 0}
              </div>
              <p className="text-sm text-muted-foreground">Ads Processed</p>
              {(extensionData as any)?.connected && (
                <div className="text-xs text-orange-300 mt-1">
                  Extension: {(extensionData as any).adsIntercepted || 0} total
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-400">
                {latestData?.totalValue.toFixed(6) || '0.000000'}
              </div>
              <p className="text-sm text-muted-foreground">Total Value</p>
              {(extensionData as any)?.connected && (
                <div className="text-xs text-cyan-300 mt-1">
                  Network: {(extensionData as any).networkSpeed || 0} Mbps
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  );
}