import { Link, useLocation } from 'wouter';
import {
  Home,
  Store,
  Zap,
  Users,
  RefreshCw,
  TrendingUp,
  Link as LinkIcon,
  DollarSign,
  Search,
  Calculator
} from 'lucide-react';
import { cn } from '@/lib/utils';

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: Home },
  { name: 'Marketplace', href: '/marketplace', icon: Store },
  { name: 'Energy', href: '/energy-marketplace', icon: Zap },
  { name: 'Party Portal', href: '/party-portal', icon: Users },
  { name: 'Social Sync', href: '/sync', icon: RefreshCw },
  { name: 'InUrtia', href: '/inurtia', icon: TrendingUp },
  { name: 'LinkVibe', href: '/linkvibe', icon: LinkIcon },
  { name: 'Data Monetization', href: '/data-monetization', icon: DollarSign },
  { name: 'AI Search', href: '/ai-search', icon: Search },
  { name: 'nUQuantum', href: '/nuquantum', icon: Calculator },
];

export function Navigation() {
  const [location] = useLocation();

  return (
    <nav className="space-y-1">
      {navigation.map((item) => {
        const Icon = item.icon;
        const isActive = location === item.href || (item.href !== '/dashboard' && location.startsWith(item.href));
        
        return (
          <Link key={item.name} href={item.href}>
            <a
              className={cn(
                'group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                isActive
                  ? 'bg-gradient-to-r from-cyan-600/20 to-purple-600/20 text-cyan-400 border-l-2 border-cyan-400'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              )}
            >
              <Icon 
                className={cn(
                  'mr-3 h-5 w-5 flex-shrink-0',
                  isActive ? 'text-cyan-400' : 'text-gray-400 group-hover:text-white'
                )}
              />
              {item.name}
            </a>
          </Link>
        );
      })}
    </nav>
  );
}