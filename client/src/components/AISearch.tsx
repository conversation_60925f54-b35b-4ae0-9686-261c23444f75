import { useState, useEffect, useRef } from 'react';
import { Search, Brain, Zap, Globe, Filter, Sparkles, Clock, TrendingU<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Calculator } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { motion, AnimatePresence } from 'framer-motion';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';

interface AISearchResult {
  id: string;
  title: string;
  content: string;
  url?: string;
  source: 'web' | 'nuos' | 'extension' | 'iot' | 'biometric';
  confidence: number;
  timestamp: number;
  contextual_data?: any;
  energy_boost?: number;
  umatter_generated?: number;
}

interface SearchContext {
  biometric_state: string;
  energy_level: number;
  location?: string;
  iot_devices: string[];
  time_of_day: string;
  recent_interactions: string[];
}

export function AISearch() {
  const [query, setQuery] = useState('');
  const [searchMode, setSearchMode] = useState<'smart' | 'deep' | 'contextual'>('smart');
  const [isSearching, setIsSearching] = useState(false);
  const [results, setResults] = useState<AISearchResult[]>([]);
  const [searchContext, setSearchContext] = useState<SearchContext | null>(null);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [instantResults, setInstantResults] = useState<AISearchResult[]>([]);
  const [searchOperator, setSearchOperator] = useState<string>('');
  const queryClient = useQueryClient();
  const recognitionRef = useRef<any>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  const suggestionTimeoutRef = useRef<NodeJS.Timeout>();

  // Initialize voice recognition
  useEffect(() => {
    if (typeof window !== 'undefined' && 'webkitSpeechRecognition' in window) {
      const SpeechRecognition = (window as any).webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = true;
      recognitionRef.current.lang = 'en-US';

      recognitionRef.current.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript;
        setQuery(transcript);
        setShowSuggestions(false);
      };

      recognitionRef.current.onend = () => {
        setIsListening(false);
      };
    }
  }, []);

  // Get search context from biometric and IoT data
  const { data: contextData } = useQuery({
    queryKey: ['/api/search/context'],
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  useEffect(() => {
    if (contextData) {
      setSearchContext(contextData as SearchContext);
    }
  }, [contextData]);

  // AI-powered search mutation
  const searchMutation = useMutation({
    mutationFn: async (searchData: {
      query: string;
      mode: string;
      context?: SearchContext;
    }) => {
      const response = await fetch('/api/search/ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(searchData)
      });
      
      if (!response.ok) {
        throw new Error('Search failed');
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      setResults(data.results || []);
      setSuggestions(data.suggestions || []);
      setIsSearching(false);
    },
    onError: (error) => {
      console.error('Search failed:', error);
      setIsSearching(false);
    }
  });

  // Generate AI suggestions as user types
  const suggestionsMutation = useMutation({
    mutationFn: async (partialQuery: string) => {
      const response = await fetch('/api/search/suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          query: partialQuery,
          context: searchContext
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to get suggestions');
      }
      
      return response.json();
    },
    onSuccess: (data) => {
      setSuggestions(data.suggestions || []);
    }
  });

  useEffect(() => {
    if (query.length > 2) {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
      
      searchTimeoutRef.current = setTimeout(() => {
        suggestionsMutation.mutate(query);
      }, 300);
    } else {
      setSuggestions([]);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [query]);

  const handleSearch = async () => {
    if (!query.trim()) return;
    
    setIsSearching(true);
    searchMutation.mutate({
      query,
      mode: searchMode,
      context: searchContext || undefined
    });
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const selectSuggestion = (suggestion: string) => {
    setQuery(suggestion);
    setSuggestions([]);
    setTimeout(() => handleSearch(), 100);
  };

  // Voice search functions
  const startVoiceSearch = () => {
    if (recognitionRef.current && !isListening) {
      setIsListening(true);
      recognitionRef.current.start();
    }
  };

  const stopVoiceSearch = () => {
    if (recognitionRef.current && isListening) {
      setIsListening(false);
      recognitionRef.current.stop();
    }
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'web': return <Globe className="w-4 h-4" />;
      case 'nuos': return <Brain className="w-4 h-4" />;
      case 'extension': return <Zap className="w-4 h-4" />;
      case 'iot': return <Filter className="w-4 h-4" />;
      case 'biometric': return <TrendingUp className="w-4 h-4" />;
      default: return <Search className="w-4 h-4" />;
    }
  };

  const getSourceColor = (source: string) => {
    switch (source) {
      case 'web': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'nuos': return 'bg-purple-500/20 text-purple-400 border-purple-500/30';
      case 'extension': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'iot': return 'bg-orange-500/20 text-orange-400 border-orange-500/30';
      case 'biometric': return 'bg-pink-500/20 text-pink-400 border-pink-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-4xl mx-auto p-6 space-y-6"
    >
      {/* Header */}
      <div className="text-center space-y-2">
        <motion.div
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          className="flex items-center justify-center gap-2 text-3xl font-bold bg-gradient-to-r from-purple-400 via-blue-400 to-green-400 bg-clip-text text-transparent"
        >
          <Sparkles className="w-8 h-8 text-purple-400" />
          nU AI Search
        </motion.div>
        <p className="text-slate-400">
          AI-powered search enhanced by your biometric data, IoT devices, and real-time context
        </p>
      </div>

      {/* Search Interface */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Search Mode Selection */}
            <Tabs value={searchMode} onValueChange={(value: any) => setSearchMode(value)}>
              <TabsList className="grid w-full grid-cols-3 bg-slate-700/50">
                <TabsTrigger value="smart" className="text-xs">
                  <Brain className="w-3 h-3 mr-1" />
                  Smart
                </TabsTrigger>
                <TabsTrigger value="deep" className="text-xs">
                  <Zap className="w-3 h-3 mr-1" />
                  Deep
                </TabsTrigger>
                <TabsTrigger value="contextual" className="text-xs">
                  <Filter className="w-3 h-3 mr-1" />
                  Contextual
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Google-like Search Input with Voice */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
              <Input
                value={query}
                onChange={(e) => {
                  setQuery(e.target.value);
                  // Auto-suggest as user types
                  if (e.target.value.length > 2) {
                    suggestionsMutation.mutate(e.target.value);
                    setShowSuggestions(true);
                  } else {
                    setShowSuggestions(false);
                  }
                }}
                onKeyPress={handleKeyPress}
                onFocus={() => {
                  if (suggestions.length > 0) setShowSuggestions(true);
                }}
                onBlur={() => {
                  // Delay hiding suggestions to allow clicks
                  setTimeout(() => setShowSuggestions(false), 200);
                }}
                placeholder="Search anything... Try voice search or advanced operators"
                className="pl-12 pr-24 py-4 bg-slate-700/50 border-slate-600 text-white placeholder:text-slate-400 text-lg rounded-full focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              />
              
              {/* Voice Search Button */}
              <Button
                onClick={isListening ? stopVoiceSearch : startVoiceSearch}
                className={`absolute right-14 top-1/2 transform -translate-y-1/2 p-2 rounded-full ${
                  isListening 
                    ? 'bg-red-600 hover:bg-red-700 animate-pulse' 
                    : 'bg-slate-600 hover:bg-slate-500'
                }`}
                title={isListening ? 'Stop voice search' : 'Start voice search'}
              >
                {isListening ? (
                  <MicOff className="w-4 h-4" />
                ) : (
                  <Mic className="w-4 h-4" />
                )}
              </Button>

              {/* Search Button */}
              <Button
                onClick={handleSearch}
                disabled={isSearching || !query.trim()}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-purple-600 hover:bg-purple-700 p-2 rounded-full"
                title="Search"
              >
                {isSearching ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    <Brain className="w-4 h-4" />
                  </motion.div>
                ) : (
                  <Search className="w-4 h-4" />
                )}
              </Button>

              {/* Google-like Instant Suggestions */}
              <AnimatePresence>
                {showSuggestions && suggestions.length > 0 && (
                  <motion.div
                    initial={{ opacity: 0, y: -10, scaleY: 0.9 }}
                    animate={{ opacity: 1, y: 0, scaleY: 1 }}
                    exit={{ opacity: 0, y: -10, scaleY: 0.9 }}
                    transition={{ duration: 0.2, ease: "easeOut" }}
                    className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-600 rounded-lg shadow-2xl z-50 max-h-80 overflow-y-auto"
                    style={{ transformOrigin: 'top' }}
                  >
                    {suggestions.map((suggestion, index) => (
                      <motion.button
                        key={index}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.03 }}
                        onClick={() => {
                          setQuery(suggestion);
                          setShowSuggestions(false);
                          handleSearch();
                        }}
                        className="w-full text-left px-4 py-3 text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700/50 first:rounded-t-lg last:rounded-b-lg flex items-center gap-3 group transition-colors duration-150"
                      >
                        <Search className="w-4 h-4 text-slate-400 group-hover:text-slate-600 dark:group-hover:text-slate-300" />
                        <span className="group-hover:text-slate-900 dark:group-hover:text-white font-medium">
                          {suggestion}
                        </span>
                        <div className="ml-auto text-xs text-slate-400">
                          Press Enter
                        </div>
                      </motion.button>
                    ))}
                    
                    {/* Search Operators Hint */}
                    {query.length > 0 && (
                      <div className="border-t border-slate-200 dark:border-slate-600 p-3 bg-slate-50 dark:bg-slate-700/30">
                        <div className="text-xs text-slate-500 dark:text-slate-400">
                          <span className="font-medium">Search operators:</span> 
                          <span className="ml-2">"exact phrase"</span>
                          <span className="ml-2">site:domain.com</span>
                          <span className="ml-2">-exclude</span>
                        </div>
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Search Context Display */}
            {searchContext && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                className="flex flex-wrap gap-2 p-3 bg-slate-700/30 rounded-lg border border-slate-600/30"
              >
                <Badge variant="secondary" className="text-xs">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  Energy: {searchContext.energy_level}%
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  <Brain className="w-3 h-3 mr-1" />
                  State: {searchContext.biometric_state}
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  <Clock className="w-3 h-3 mr-1" />
                  {searchContext.time_of_day}
                </Badge>
                {searchContext.iot_devices.length > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    <Filter className="w-3 h-3 mr-1" />
                    {searchContext.iot_devices.length} IoT devices
                  </Badge>
                )}
              </motion.div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Search Results */}
      <AnimatePresence>
        {results.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-4"
          >
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-white">
                Search Results ({results.length})
              </h3>
              <Badge variant="outline" className="text-xs">
                <Sparkles className="w-3 h-3 mr-1" />
                AI Enhanced
              </Badge>
            </div>

            <div className="grid gap-4">
              {results.map((result, index) => (
                <motion.div
                  key={result.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-colors">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-white text-base leading-tight">
                          {result.url ? (
                            <a 
                              href={result.url} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="hover:text-purple-400 transition-colors"
                            >
                              {result.title}
                            </a>
                          ) : (
                            result.title
                          )}
                        </CardTitle>
                        <div className="flex items-center gap-2 ml-4">
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${getSourceColor(result.source)}`}
                          >
                            {getSourceIcon(result.source)}
                            <span className="ml-1 capitalize">{result.source}</span>
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {Math.round(result.confidence * 100)}%
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-slate-300 text-sm mb-3 leading-relaxed">
                        {result.content}
                      </p>
                      
                      <div className="flex items-center justify-between text-xs text-slate-400">
                        <span>
                          {new Date(result.timestamp).toLocaleString()}
                        </span>
                        <div className="flex items-center gap-3">
                          {result.energy_boost && (
                            <span className="text-green-400">
                              +{result.energy_boost} Energy
                            </span>
                          )}
                          {result.umatter_generated && (
                            <span className="text-purple-400">
                              +{result.umatter_generated} UMatter
                            </span>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* No Results State */}
      {!isSearching && results.length === 0 && query && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <Search className="w-12 h-12 text-slate-600 mx-auto mb-4" />
          <p className="text-slate-400">
            No results found. Try adjusting your search or switching modes.
          </p>
        </motion.div>
      )}
    </motion.div>
  );
}