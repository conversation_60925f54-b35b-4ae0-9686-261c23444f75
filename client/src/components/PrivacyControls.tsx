import { useState, useEffect } from 'react';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { 
  Shield, 
  Download, 
  Trash2, 
  UserX, 
  Settings, 
  AlertTriangle,
  CheckCircle 
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useInteractionTracking } from '@/hooks/useInteractionTracking';
import { apiRequest } from '@/lib/queryClient';
import { motion } from 'framer-motion';

interface PrivacySettings {
  spunderEncryption: boolean;
  didAuthentication: boolean;
  anonymousMode: boolean;
  dataRetentionDays: number;
  shareAnalytics: boolean;
}

export function PrivacyControls() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { updatePrivacySettings, exportHistory, clearHistory } = useInteractionTracking();

  const [settings, setSettings] = useState<PrivacySettings>({
    spunderEncryption: true,
    didAuthentication: true,
    anonymousMode: false,
    dataRetentionDays: 30,
    shareAnalytics: false
  });

  // Export data mutation
  const exportMutation = useMutation({
    mutationFn: async () => {
      return await exportHistory();
    },
    onSuccess: (data) => {
      // Create and download file
      const blob = new Blob([JSON.stringify(data, null, 2)], { 
        type: 'application/json' 
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `interaction-history-${new Date().toISOString().split('T')[0]}.json`;
      a.click();
      URL.revokeObjectURL(url);

      toast({
        title: "Export Complete",
        description: "Your interaction history has been downloaded.",
        variant: "default"
      });
    },
    onError: (error) => {
      toast({
        title: "Export Failed", 
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // Clear history mutation
  const clearMutation = useMutation({
    mutationFn: async () => {
      await clearHistory();
      // Also clear server data
      await apiRequest('DELETE', '/api/memvid/chunks');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/memvid/chunks'] });
      toast({
        title: "History Cleared",
        description: "All interaction history has been permanently deleted.",
        variant: "default"
      });
    },
    onError: (error) => {
      toast({
        title: "Clear Failed",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  const handleSettingChange = (key: keyof PrivacySettings, value: boolean) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);

    // Update interaction tracking settings
    updatePrivacySettings({
      privacyMode: newSettings.anonymousMode,
      encryptionEnabled: newSettings.spunderEncryption,
      anonymousMode: newSettings.anonymousMode
    });

    toast({
      title: "Privacy Setting Updated",
      description: `${key.replace(/([A-Z])/g, ' $1').toLowerCase()} has been ${value ? 'enabled' : 'disabled'}.`,
      variant: "default"
    });
  };

  const handleExportData = () => {
    exportMutation.mutate();
  };

  const handleClearHistory = () => {
    if (confirm('Are you sure you want to permanently delete all interaction history? This action cannot be undone.')) {
      clearMutation.mutate();
    }
  };

  const { data: privacyAlerts = [] } = useQuery({
    queryKey: ['/api/privacy/alerts'],
    refetchInterval: 10000 // Check for new alerts every 10 seconds
  });

  const { data: privacyMetrics } = useQuery({
    queryKey: ['/api/privacy/metrics'],
    refetchInterval: 30000
  });

  // Enhanced alert display with real-time updates
  const [newAlerts, setNewAlerts] = useState<any[]>([]);

  useEffect(() => {
    if (privacyAlerts.length > 0) {
      const recentAlerts = privacyAlerts.filter((alert: any) => 
        Date.now() - new Date(alert.timestamp).getTime() < 300000 // 5 minutes
      );
      setNewAlerts(recentAlerts);
    }
  }, [privacyAlerts]);

  const resolveAlert = (alertId: string) => {
    // Placeholder for resolve alert function
    toast({
      title: "Alert Resolved",
      description: `Alert ${alertId} has been marked as resolved.`,
      variant: "default"
    });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
       {/* Privacy Alerts - Real-time */}
        {privacyAlerts && privacyAlerts.length > 0 && (
          <Card className="border-red-500/30 bg-red-500/5">
            <CardHeader>
              <CardTitle className="text-red-400 flex items-center gap-2">
                <AlertTriangle className="w-5 h-5 animate-pulse" />
                Active Privacy Alerts ({privacyAlerts.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {privacyAlerts.slice(0, 5).map((alert: any) => (
                  <motion.div 
                    key={alert.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium text-red-200">{alert.alertType}</h4>
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${
                              alert.severity === 'high' ? 'border-red-500 text-red-400' :
                              alert.severity === 'medium' ? 'border-yellow-500 text-yellow-400' :
                              'border-blue-500 text-blue-400'
                            }`}
                          >
                            {alert.severity}
                          </Badge>
                        </div>
                        <p className="text-sm text-red-300/80 mt-1">{alert.description}</p>
                        {alert.dataType && (
                          <div className="text-xs text-red-400/60 mt-2">
                            Data type: <span className="font-mono">{alert.dataType}</span>
                          </div>
                        )}
                        <p className="text-xs text-red-400/60 mt-1">
                          {Math.floor((Date.now() - new Date(alert.detectedAt).getTime()) / 60000)}m ago
                        </p>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="text-blue-400 hover:bg-blue-500/10 text-xs px-2"
                          onClick={() => {
                            // Show details
                            console.log('Alert details:', alert);
                          }}
                        >
                          Details
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-green-500/30 text-green-400 hover:bg-green-500/10 text-xs px-2"
                          onClick={() => resolveAlert(alert.id)}
                        >
                          Resolve
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                ))}

                {privacyAlerts.length > 5 && (
                  <div className="text-center">
                    <Button variant="ghost" size="sm" className="text-red-400">
                      View all {privacyAlerts.length} alerts
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Privacy Metrics */}
        {privacyMetrics && (
          <Card className="border-blue-500/20 bg-blue-500/5">
            <CardHeader>
              <CardTitle className="text-blue-400 flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Privacy Health Score
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-400">
                    {privacyMetrics.overallScore || 85}%
                  </div>
                  <div className="text-xs text-slate-400">Overall</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-400">
                    {privacyMetrics.encryptionLevel || 'AES-256'}
                  </div>
                  <div className="text-xs text-slate-400">Encryption</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-purple-400">
                    {privacyMetrics.dataShares || 3}
                  </div>
                  <div className="text-xs text-slate-400">Active Shares</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-orange-400">
                    {privacyMetrics.exposureRisk || 'Low'}
                  </div>
                  <div className="text-xs text-slate-400">Risk Level</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      {/* Privacy Shield Settings */}
      <Card className="bg-panel/40 backdrop-blur-md border border-red-400/20">
        <CardHeader className="border-b border-red-400/20">
          <CardTitle className="text-red-400 flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            Privacy Shield
          </CardTitle>
        </CardHeader>

        <CardContent className="p-4 space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-sm font-medium">SpUnder Encryption</Label>
                <div className="text-xs text-text-secondary">
                  Encrypt all interaction data with advanced cryptography
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.spunderEncryption}
                  onCheckedChange={(checked) => handleSettingChange('spunderEncryption', checked)}
                />
                {settings.spunderEncryption && (
                  <Badge variant="default" className="bg-neon-cyan/20 text-neon-cyan">
                    Active
                  </Badge>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-sm font-medium">DID Authentication</Label>
                <div className="text-xs text-text-secondary">
                  Use decentralized identity for secure authentication
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.didAuthentication}
                  onCheckedChange={(checked) => handleSettingChange('didAuthentication', checked)}
                />
                {settings.didAuthentication && (
                  <Badge variant="default" className="bg-neon-purple/20 text-neon-purple">
                    Active
                  </Badge>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-sm font-medium">Anonymous Mode</Label>
                <div className="text-xs text-text-secondary">
                  Hide personal identifiers in interaction tracking
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={settings.anonymousMode}
                  onCheckedChange={(checked) => handleSettingChange('anonymousMode', checked)}
                />
                {settings.anonymousMode && (
                  <Badge variant="default" className="bg-yellow-400/20 text-yellow-400">
                    Active
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Privacy Status */}
          <div className="mt-4 p-3 bg-space/50 rounded border border-green-400/20">
            <div className="flex items-center space-x-2 mb-2">
              <CheckCircle className="w-4 h-4 text-green-400" />
              <span className="text-sm font-medium text-green-400">Privacy Status</span>
            </div>
            <div className="text-xs text-text-secondary">
              All systems secure. Your interaction data is protected with enterprise-grade encryption.
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Export & Controls */}
      <Card className="bg-panel/40 backdrop-blur-md border border-neon-cyan/20">
        <CardHeader className="border-b border-neon-cyan/20">
          <CardTitle className="text-neon-cyan flex items-center">
            <Download className="w-5 h-5 mr-2" />
            Data Export & Backup
          </CardTitle>
        </CardHeader>

        <CardContent className="p-4 space-y-4">
          <Button
            onClick={handleExportData}
            disabled={exportMutation.isPending}
            className="w-full bg-neon-cyan/20 text-neon-cyan border border-neon-cyan/40 hover:bg-neon-cyan/30"
          >
            <Download className="w-4 h-4 mr-2" />
            {exportMutation.isPending ? 'Exporting...' : 'Export Interaction History (Memvid)'}
          </Button>

          <Button
            variant="outline"
            className="w-full bg-neon-purple/20 text-neon-purple border border-neon-purple/40 hover:bg-neon-purple/30"
          >
            <Shield className="w-4 h-4 mr-2" />
            Download Encrypted Backup
          </Button>

          <Button
            variant="outline"
            className="w-full bg-yellow-400/20 text-yellow-400 border border-yellow-400/40 hover:bg-yellow-400/30"
          >
            <Settings className="w-4 h-4 mr-2" />
            Generate Privacy Report
          </Button>

          {/* Danger Zone */}
          <div className="mt-6 p-3 bg-red-900/20 rounded border border-red-400/20">
            <div className="flex items-center space-x-2 mb-3">
              <AlertTriangle className="w-4 h-4 text-red-400" />
              <span className="text-sm font-medium text-red-400">Danger Zone</span>
            </div>

            <Button
              onClick={handleClearHistory}
              disabled={clearMutation.isPending}
              variant="destructive"
              className="w-full mb-2 bg-red-400/20 text-red-400 border border-red-400/40 hover:bg-red-400/30"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              {clearMutation.isPending ? 'Clearing...' : 'Purge Interaction History'}
            </Button>

            <Button
              variant="outline"
              className="w-full mb-2 bg-orange-400/20 text-orange-400 border border-orange-400/40 hover:bg-orange-400/30"
            >
              <UserX className="w-4 h-4 mr-2" />
              Enable Anonymous Mode
            </Button>

            <div className="text-xs text-text-secondary mt-2">
              Warning: These actions are irreversible and will permanently delete your data.
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}