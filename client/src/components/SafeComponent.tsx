import { ReactNode } from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface SafeComponentProps {
  children: ReactNode;
  fallback?: ReactNode;
  className?: string;
}

export function SafeComponent({ children, fallback, className }: SafeComponentProps) {
  try {
    return <div className={className}>{children}</div>;
  } catch (error) {
    console.error('SafeComponent error:', error);
    return fallback || (
      <Card className="border-red-500/30 bg-red-900/20">
        <CardContent className="p-4">
          <div className="text-red-400 text-sm">Component loading...</div>
        </CardContent>
      </Card>
    );
  }
}