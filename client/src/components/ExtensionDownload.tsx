import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  Chrome, 
  Shield, 
  Zap, 
  Eye, 
  CheckCircle,
  ExternalLink,
  Globe,
  Activity
} from 'lucide-react';

export function ExtensionDownload() {
  const [downloadStarted, setDownloadStarted] = useState(false);

  const handleDownload = () => {
    setDownloadStarted(true);

    // Create download link for the working quantum extension
    const link = document.createElement('a');
    link.href = '/api/extension/download';
    link.download = 'nu-universe-quantum-extension.zip';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Reset download state after a delay
    setTimeout(() => setDownloadStarted(false), 3000);
  };

  const openExtensionsPage = () => {
    window.open('chrome://extensions/', '_blank');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <div className="w-12 h-12 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center">
            <Chrome className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-bold">nU Universe Browser Extension</h2>
            <p className="text-gray-400">Real UMatter generation from your web browsing</p>
          </div>
        </div>

        <Badge variant="secondary" className="bg-green-900/30 text-green-400 border-green-400/30">
          <Activity className="w-3 h-3 mr-1" />
          100% Authentic Tracking
        </Badge>
      </div>

      {/* Download Card */}
      <Card className="border-gray-800 bg-black/40 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Download className="w-5 h-5 text-blue-400" />
            <span>Download & Install</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {!downloadStarted ? (
            <Button 
              onClick={handleDownload}
              className="w-full bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600"
              size="lg"
            >
              <Download className="w-4 h-4 mr-2" />
              Download nU Universe Extension
            </Button>
          ) : (
            <div className="space-y-3">
              <div className="flex items-center space-x-2 text-green-400">
                <CheckCircle className="w-5 h-5" />
                <span>Extension downloaded successfully!</span>
              </div>

              <div className="bg-gray-900/50 rounded-lg p-4 space-y-3">
                <h4 className="font-medium">Installation Steps:</h4>
                <ol className="space-y-2 text-sm text-gray-300">
                  <li className="flex items-start space-x-2">
                    <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">1</span>
                    <span>Extract the downloaded ZIP file to a folder</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">2</span>
                    <span>Open Chrome and go to chrome://extensions/</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">3</span>
                    <span>Enable "Developer mode" toggle in the top right</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">4</span>
                    <span>Click "Load unpacked" and select the extracted folder</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <span className="bg-green-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">5</span>
                    <span>Visit any webpage to see the floating UMatter indicator</span>
                  </li>
                </ol>
              </div>

              <Button 
                onClick={openExtensionsPage}
                variant="outline"
                className="w-full"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Open Chrome Extensions Page
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="border-gray-800 bg-black/40 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Zap className="w-5 h-5 text-yellow-400" />
              <h3 className="font-medium">Real UMatter Generation</h3>
            </div>
            <ul className="space-y-1 text-sm text-gray-300">
              <li>• 0.05 UMatter per page visit</li>
              <li>• 0.01 UMatter per click</li>
              <li>• 0.015 UMatter per keystroke</li>
              <li>• Engagement bonuses up to 0.1</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="border-gray-800 bg-black/40 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Eye className="w-5 h-5 text-blue-400" />
              <h3 className="font-medium">Live Tracking</h3>
            </div>
            <ul className="space-y-1 text-sm text-gray-300">
              <li>• Visual UMatter counter on every page</li>
              <li>• Real-time sync every 30 seconds</li>
              <li>• Automatic app integration</li>
              <li>• Persistent activity tracking</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="border-gray-800 bg-black/40 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Shield className="w-5 h-5 text-green-400" />
              <h3 className="font-medium">Privacy & Security</h3>
            </div>
            <ul className="space-y-1 text-sm text-gray-300">
              <li>• No sensitive data collection</li>
              <li>• Local storage only</li>
              <li>• Password fields ignored</li>
              <li>• Open source and transparent</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="border-gray-800 bg-black/40 backdrop-blur-sm">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3 mb-3">
              <Globe className="w-5 h-5 text-purple-400" />
              <h3 className="font-medium">Browser Support</h3>
            </div>
            <ul className="space-y-1 text-sm text-gray-300">
              <li>• Chrome (recommended)</li>
              <li>• Microsoft Edge</li>
              <li>• Chromium-based browsers</li>
              <li>• Manifest V3 compatible</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* How It Works */}
      <Card className="border-gray-800 bg-black/40 backdrop-blur-sm">
        <CardHeader>
          <CardTitle>How the Extension Works</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
              <div>
                <h4 className="font-medium">Background Tracking</h4>
                <p className="text-sm text-gray-400">Service worker monitors tab navigation and manages app synchronization</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
              <div>
                <h4 className="font-medium">Interaction Detection</h4>
                <p className="text-sm text-gray-400">Content script tracks real user clicks, scrolls, and keyboard activity</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">3</div>
              <div>
                <h4 className="font-medium">UMatter Calculation</h4>
                <p className="text-sm text-gray-400">Authentic interactions generate real UMatter based on activity type and engagement</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">4</div>
              <div>
                <h4 className="font-medium">Live Sync</h4>
                <p className="text-sm text-gray-400">Data syncs with nU Universe app in real-time, updating your dashboard</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}