import React, { useState, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Wifi, WifiOff, Activity, RefreshCw, Zap } from "lucide-react";
import { motion } from "framer-motion";
import { webSocketManager } from '@/lib/websocket-manager';
import { useSimpleNuvaStore } from '@/lib/stores/simpleNuvaStore';

export function WebSocketStatus() {
  const { isConnected, umatter, lastConnectionTime, connectWebSocket } = useSimpleNuvaStore();
  const [connectionState, setConnectionState] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const [lastMessage, setLastMessage] = useState<string>('No data received');
  const [connectionHealth, setConnectionHealth] = useState(0);

  useEffect(() => {
    setConnectionState(isConnected ? 'connected' : 'disconnected');
    setConnectionHealth(isConnected ? 100 : 0);
    
    if (isConnected) {
      setLastMessage(`Connected • UMatter: ${umatter.toFixed(3)}`);
    } else {
      setLastMessage('Disconnected');
    }
    
    // Auto-connect on mount
    if (!isConnected) {
      connectWebSocket();
    }
  }, [isConnected, umatter, connectWebSocket]);

  const handleReconnect = () => {
    setConnectionState('connecting');
    connectWebSocket();
  };

  const getStatusIcon = () => {
    switch (connectionState) {
      case 'connected':
        return <Wifi className="w-4 h-4 text-green-400" />;
      case 'connecting':
        return <RefreshCw className="w-4 h-4 text-blue-400 animate-spin" />;
      default:
        return <WifiOff className="w-4 h-4 text-red-400" />;
    }
  };

  const getStatusColor = () => {
    switch (connectionState) {
      case 'connected': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'connecting': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      default: return 'bg-red-500/20 text-red-400 border-red-500/30';
    }
  };

  return (
    <Card className="border-cyan-500/30 bg-gradient-to-r from-cyan-900/20 to-blue-900/20 backdrop-blur-sm">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <motion.div
              animate={{ 
                scale: connectionState === 'connected' ? [1, 1.1, 1] : 1,
                rotate: connectionState === 'connecting' ? 360 : 0
              }}
              transition={{ 
                duration: connectionState === 'connected' ? 2 : 1,
                repeat: connectionState === 'connected' ? Infinity : 0 
              }}
            >
              {getStatusIcon()}
            </motion.div>
            
            <div>
              <div className="text-sm font-semibold text-white">
                nU Universe WebSocket
              </div>
              <div className="text-xs text-gray-400">
                {lastMessage}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-xs text-gray-400">UMatter Sync</div>
              <div className="text-lg font-bold text-cyan-400 font-mono">
                {umatter.toFixed(6)}
              </div>
            </div>
            
            <Badge variant="secondary" className={getStatusColor()}>
              {connectionState.toUpperCase()}
            </Badge>
            
            {connectionState !== 'connected' && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleReconnect}
                disabled={connectionState === 'connecting'}
              >
                {connectionState === 'connecting' ? (
                  <RefreshCw className="w-3 h-3 animate-spin" />
                ) : (
                  <>
                    <Zap className="w-3 h-3 mr-1" />
                    Connect
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
        
        {connectionState === 'connected' && (
          <div className="mt-3 flex items-center space-x-2">
            <div className="flex-1 bg-gray-700 rounded-full h-2">
              <motion.div
                className="bg-gradient-to-r from-green-500 to-cyan-500 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${connectionHealth}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
            <span className="text-xs text-green-400">
              {connectionHealth}% Health
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}