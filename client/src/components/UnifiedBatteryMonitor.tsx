import { useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Battery, BatteryLow, Zap, Activity } from 'lucide-react';
import { useSimpleNuvaStore } from '@/lib/stores/simpleNuvaStore';
import { useRealTimeData } from '@/hooks/useRealTimeData';

export function UnifiedBatteryMonitor() {
  const { batteryLevel, isCharging, isConnected, connectWebSocket } = useSimpleNuvaStore();
  const realTimeData = useRealTimeData();
  
  // Use authenticated battery data with real-time fallback
  const currentBatteryLevel = batteryLevel || realTimeData.batteryLevel || 0;
  const chargingStatus = isCharging !== undefined ? isCharging : realTimeData.isCharging;
  const batteryPercentage = Math.round(currentBatteryLevel * 100);
  
  // Ensure WebSocket connection
  useEffect(() => {
    if (!isConnected) {
      connectWebSocket();
    }
  }, [isConnected, connectWebSocket]);

  const getBatteryIcon = () => {
    if (chargingStatus) return <Zap className="w-6 h-6 text-green-400" />;
    if (batteryPercentage <= 20) return <BatteryLow className="w-6 h-6 text-red-400" />;
    return <Battery className="w-6 h-6 text-blue-400" />;
  };

  const getBatteryColor = () => {
    if (chargingStatus) return 'text-green-400';
    if (batteryPercentage <= 20) return 'text-red-400';
    if (batteryPercentage <= 50) return 'text-yellow-400';
    return 'text-blue-400';
  };

  return (
    <Card className="border-blue-500/30 bg-gradient-to-br from-blue-900/20 to-cyan-900/20 backdrop-blur-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg text-blue-400 flex items-center space-x-2">
          {getBatteryIcon()}
          <span>Battery Monitor</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-3xl font-bold font-mono text-white">
              {batteryPercentage}%
            </div>
            <div className="text-sm text-gray-400">
              {chargingStatus ? 'Charging' : 'Discharging'}
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-xs text-gray-400">Status</div>
            <div className={`text-sm font-medium ${chargingStatus ? 'text-green-400' : 'text-blue-400'}`}>
              {chargingStatus ? 'Charging' : 'On Battery'}
            </div>
          </div>
        </div>

        <Progress 
          value={batteryPercentage} 
          className="h-3"
        />

        <div className="flex items-center justify-between">
          <Badge variant={chargingStatus ? "default" : "secondary"} className={
            chargingStatus ? 'bg-green-500/20 text-green-400 border-green-500/30' : 'bg-blue-500/20 text-blue-400 border-blue-500/30'
          }>
            {chargingStatus ? 'CHARGING' : 'ON BATTERY'}
          </Badge>
          
          <div className="flex items-center space-x-1">
            <Activity className="w-3 h-3 text-green-400" />
            <span className="text-xs text-green-400">
              {isConnected ? 'Live' : 'Offline'}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}