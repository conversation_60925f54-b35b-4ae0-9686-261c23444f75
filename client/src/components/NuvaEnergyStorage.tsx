import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Zap, Battery, Smartphone, Bluetooth, Wifi, Settings } from 'lucide-react';

// Extend Navigator interface for Bluetooth API
declare global {
  interface Navigator {
    bluetooth?: {
      requestDevice(options: {
        acceptAllDevices?: boolean;
        filters?: Array<{ 
          services?: string[]; 
          name?: string; 
          namePrefix?: string;
          manufacturerData?: Array<{
            companyIdentifier: number;
            dataPrefix?: BufferSource;
            mask?: BufferSource;
          }>;
        }>;
        optionalServices?: string[];
      }): Promise<{
        id: string;
        name: string | null;
        gatt?: {
          connected: boolean;
          connect(): Promise<any>;
        };
        watchAdvertisements(): Promise<void>;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject): void;
      }>;
      getAvailability(): Promise<boolean>;
    };
  }
}

interface NuvaEnergyStorageProps {
  isOpen: boolean;
  onClose: () => void;
}

export function NuvaEnergyStorage({ isOpen, onClose }: NuvaEnergyStorageProps) {
  const [energyLevel, setEnergyLevel] = useState(74);
  const [isCharging, setIsCharging] = useState(false);
  const [connectedDevices, setConnectedDevices] = useState<string[]>([]);
  const [bluetoothAvailable, setBluetoothAvailable] = useState(false);
  const [neuralActivity, setNeuralActivity] = useState(0.65);
  const [umatterGenerated, setUmatterGenerated] = useState(12.5);
  const [truBalance, setTruBalance] = useState(1.25);
  const [nuvaBalance, setNuvaBalance] = useState(12.5);

  useEffect(() => {
    // Check Bluetooth availability
    if (navigator.bluetooth) {
      navigator.bluetooth.getAvailability().then(setBluetoothAvailable);
    }

    // Simulate real-time energy updates
    const interval = setInterval(() => {
      // Simulate battery level changes
      setEnergyLevel(prev => {
        const change = isCharging ? Math.random() * 2 : -Math.random() * 0.5;
        return Math.max(0, Math.min(100, prev + change));
      });

      // Simulate neural activity fluctuations
      setNeuralActivity(prev => {
        const change = (Math.random() - 0.5) * 0.1;
        return Math.max(0.2, Math.min(1.0, prev + change));
      });

      // Generate UMatter based on neural activity
      setUmatterGenerated(prev => prev + neuralActivity * 0.01);
      setTruBalance(prev => prev + neuralActivity * 0.001);
      setNuvaBalance(prev => prev + neuralActivity * 0.01);
    }, 2000);

    return () => clearInterval(interval);
  }, [isCharging, neuralActivity]);

  const connectBluetoothDevice = async () => {
    if (!navigator.bluetooth) {
      alert('Bluetooth is not supported in this browser');
      return;
    }

    try {
      const device = await navigator.bluetooth.requestDevice({
        acceptAllDevices: true,
        optionalServices: ['battery_service', 'device_information']
      });

      if (device.name) {
        setConnectedDevices(prev => [...prev, device.name!]);
      }

      // Listen for device events
      device.addEventListener('gattserverdisconnected', () => {
        setConnectedDevices(prev => prev.filter(d => d !== device.name));
      });

    } catch (error) {
      console.error('Bluetooth connection failed:', error);
    }
  };

  const getNeuralEfficiency = () => {
    return ((neuralActivity * energyLevel) / 100 * 100).toFixed(1);
  };

  const getEnergyStatusColor = () => {
    if (energyLevel > 70) return 'text-green-400';
    if (energyLevel > 30) return 'text-yellow-400';
    return 'text-red-400';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl bg-panel/90 border-neon-cyan/30 text-text-primary">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2 text-neon-cyan">
              <Zap className="w-6 h-6" />
              <span>nUva Energy Storage</span>
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-text-secondary hover:text-text-primary"
            >
              ✕
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Energy Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="bg-space/50 border-neon-cyan/20">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-text-secondary">Energy Level</span>
                  <Battery className={`w-4 h-4 ${getEnergyStatusColor()}`} />
                </div>
                <div className="text-2xl font-bold mb-2">{energyLevel.toFixed(1)}%</div>
                <Progress value={energyLevel} className="h-2" />
                {isCharging && (
                  <Badge variant="outline" className="mt-2 text-green-400 border-green-400">
                    Charging
                  </Badge>
                )}
              </CardContent>
            </Card>

            <Card className="bg-space/50 border-neon-purple/20">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-text-secondary">Neural Activity</span>
                  <div className="w-4 h-4 bg-neon-purple rounded-full animate-pulse"></div>
                </div>
                <div className="text-2xl font-bold mb-2">{(neuralActivity * 100).toFixed(1)}%</div>
                <Progress value={neuralActivity * 100} className="h-2" />
                <div className="text-xs text-text-secondary mt-2">
                  Efficiency: {getNeuralEfficiency()}%
                </div>
              </CardContent>
            </Card>

            <Card className="bg-space/50 border-neon-green/20">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-text-secondary">Connected Devices</span>
                  <Smartphone className="w-4 h-4 text-neon-green" />
                </div>
                <div className="text-2xl font-bold mb-2">{connectedDevices.length}</div>
                <div className="text-xs text-text-secondary">
                  {connectedDevices.length > 0 ? 'Syncing' : 'Ready to connect'}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Token Balances */}
          <div className="grid grid-cols-3 gap-4">
            <Card className="bg-gradient-to-br from-orange-500/10 to-red-500/10 border-orange-500/30">
              <CardContent className="p-4 text-center">
                <div className="text-sm text-orange-400 mb-1">UMatter</div>
                <div className="text-xl font-bold text-orange-300">{umatterGenerated.toFixed(2)}</div>
                <div className="text-xs text-orange-400/60">Generated</div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 border-blue-500/30">
              <CardContent className="p-4 text-center">
                <div className="text-sm text-blue-400 mb-1">trU</div>
                <div className="text-xl font-bold text-blue-300">{truBalance.toFixed(3)}</div>
                <div className="text-xs text-blue-400/60">Tradeable</div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border-green-500/30">
              <CardContent className="p-4 text-center">
                <div className="text-sm text-green-400 mb-1">nUva</div>
                <div className="text-xl font-bold text-green-300">{nuvaBalance.toFixed(2)}</div>
                <div className="text-xs text-green-400/60">Rechargeable</div>
              </CardContent>
            </Card>
          </div>

          {/* Device Management */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-neon-cyan">Device Management</h3>
            
            <div className="flex flex-wrap gap-2">
              <Button
                onClick={connectBluetoothDevice}
                disabled={!bluetoothAvailable}
                className="flex items-center space-x-2 bg-blue-600/20 hover:bg-blue-600/30 border border-blue-500/50"
              >
                <Bluetooth className="w-4 h-4" />
                <span>Connect Bluetooth</span>
              </Button>

              <Button
                onClick={() => setIsCharging(!isCharging)}
                className="flex items-center space-x-2 bg-green-600/20 hover:bg-green-600/30 border border-green-500/50"
              >
                <Battery className="w-4 h-4" />
                <span>{isCharging ? 'Stop Charging' : 'Start Charging'}</span>
              </Button>

              <Button
                className="flex items-center space-x-2 bg-purple-600/20 hover:bg-purple-600/30 border border-purple-500/50"
              >
                <Settings className="w-4 h-4" />
                <span>Energy Settings</span>
              </Button>
            </div>

            {/* Connected Devices List */}
            {connectedDevices.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-text-secondary">Connected Devices:</h4>
                {connectedDevices.map((device, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-space/30 rounded border border-neon-cyan/20"
                  >
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-sm">{device}</span>
                    </div>
                    <Badge variant="outline" className="text-green-400 border-green-400">
                      Active
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Energy Conversion Science */}
          <Card className="bg-gradient-to-r from-neon-cyan/10 to-neon-purple/10 border-neon-cyan/30">
            <CardContent className="p-4">
              <h4 className="text-sm font-semibold text-neon-cyan mb-2">Energy Conversion Science</h4>
              <div className="text-xs text-text-secondary space-y-1">
                <div>• 20W neural power → 0.28 kWh/day brain energy</div>
                <div>• Sleep: 0.74Wh battery drain → 8MB data → 0.5 UMatter</div>
                <div>• Active: 0.296Wh drain → 20MB data → 0.2 UMatter</div>
                <div>• Conversion: 1 UMatter = 0.1 trU = 1 nUva = 0.74Wh rechargeable</div>
                <div>• Data value: $0.0007/MB (energy-based pricing)</div>
              </div>
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  );
}