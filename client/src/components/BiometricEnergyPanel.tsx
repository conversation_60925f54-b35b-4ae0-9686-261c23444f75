import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Heart, 
  Brain, 
  Activity, 
  Zap, 
  Eye, 
  Thermometer,
  Sun,
  TrendingUp,
  Focus
} from 'lucide-react';
// import { biometricEnergyTracker } from '@/lib/biometricEnergyTracking';
import '@/lib/killAllSimulations';

interface BiometricEnergyPanelProps {
  className?: string;
}

export function BiometricEnergyPanel({ className }: BiometricEnergyPanelProps) {
  const [biometricState, setBiometricState] = useState<any>(null);
  const [activePatterns, setActivePatterns] = useState<any[]>([]);
  const [energyHistory, setEnergyHistory] = useState<number[]>([]);

  useEffect(() => {
    // Initialize authentic biometric sensors only
    // TODO: Implement authentic biometric authentication
    // authenticBiometrics.startAuthentication();

    // Update biometric data every 2 seconds
    const updateBiometrics = async () => {
      try {
        // Get data from real biometric tracker
        const { realBiometricTracker } = await import('@/lib/realBiometricTracking');
        const summary = realBiometricTracker.getCurrentSummary();
        
        // Get real battery data from the device
        if ('getBattery' in navigator) {
          try {
            const battery = await (navigator as any).getBattery();
            console.log('[BiometricPanel] Battery API connected, level:', battery.level);
            setBiometricState({
              current: {
                energyLevel: battery.level,
                focusScore: 0,
                stressLevel: 0,
                heartRate: 0,
                state: battery.level > 0.7 ? 'high' : battery.level > 0.5 ? 'elevated' : battery.level > 0.3 ? 'balanced' : 'low'
              },
              numentumMultiplier: 1.0,
              isMonitoring: true,
              deviceCapabilities: ['battery']
            });
            
            setEnergyHistory(prev => {
              const newHistory = [...prev, battery.level * 100];
              return newHistory.slice(-20);
            });
            
            // Listen for battery changes
            battery.addEventListener('levelchange', () => {
              setBiometricState((prev: any) => ({
                ...prev,
                current: {
                  ...prev.current,
                  energyLevel: battery.level,
                  state: battery.level > 0.7 ? 'high' : battery.level > 0.5 ? 'elevated' : battery.level > 0.3 ? 'balanced' : 'low'
                }
              }));
            });
            
            return;
          } catch (error) {
            console.log('[BiometricPanel] Battery API failed:', error);
          }
        }
        
        // No real data available
        setBiometricState({
          current: {
            energyLevel: 0,
            focusScore: 0,
            stressLevel: 0,
            heartRate: 0,
            state: 'offline'
          },
          numentumMultiplier: 1.0,
          isMonitoring: false,
          deviceCapabilities: []
        });

        // Generate active patterns based on current state
        const patterns = [];
        if (summary.energyLevel > 0.8 && summary.focusScore > 0.7) {
          patterns.push({ type: 'flow', intensity: Math.min(summary.energyLevel, summary.focusScore), numentumMultiplier: 1.5 });
        }
        if (summary.focusScore > 0.85) {
          patterns.push({ type: 'focus', intensity: summary.focusScore, numentumMultiplier: 1.25 });
        }
        if (summary.energyLevel > 0.9) {
          patterns.push({ type: 'peak', intensity: summary.energyLevel, numentumMultiplier: 1.4 });
        }
        if (summary.isCharging) {
          patterns.push({ type: 'recovery', intensity: 0.8, numentumMultiplier: 1.15 });
        }
        
        setActivePatterns(patterns);
        
      } catch (error) {
        console.log('[BiometricPanel] No battery API or sensors available');
        setBiometricState({
          current: {
            energyLevel: 0,
            focusScore: 0,
            stressLevel: 0,
            heartRate: 0,
            state: 'offline'
          },
          numentumMultiplier: 1.0,
          isMonitoring: false,
          patterns: []
        });
        setActivePatterns([]);
      }
    };

    updateBiometrics();
    const interval = setInterval(updateBiometrics, 1000);

    // Listen for real biometric update events
    const handleBiometricUpdate = (event: any) => {
      const { summary } = event.detail;
      console.log(`[BiometricPanel] Real biometric update:`, summary);
    };

    // Listen for energy pattern events
    const handleEnergyPattern = (event: any) => {
      const { pattern } = event.detail;
      console.log(`[BiometricPanel] New energy pattern: ${pattern.type}`);
    };

    window.addEventListener('biometricUpdate', handleBiometricUpdate);
    window.addEventListener('biometricEnergyPattern', handleEnergyPattern);

    return () => {
      clearInterval(interval);
      window.removeEventListener('biometricUpdate', handleBiometricUpdate);
      window.removeEventListener('biometricEnergyPattern', handleEnergyPattern);
    };
  }, []);

  if (!biometricState) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Biometric Energy Tracking
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground">
            Initializing biometric sensors...
          </div>
        </CardContent>
      </Card>
    );
  }

  const { current, numentumMultiplier, isMonitoring } = biometricState;
  const energyLevel = (current?.energyLevel || 0) * 100;
  const focusScore = (current?.focusScore || 0) * 100;
  const stressLevel = (current?.stressLevel || 0) * 100;
  const heartRate = current?.heartRate || 0;
  
  // Show loading state while initializing
  if (!biometricState) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Neural Energy Monitor
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-muted-foreground">
            Connecting to device sensors...
          </div>
        </CardContent>
      </Card>
    );
  }

  const getEnergyStateColor = (state: string) => {
    switch (state) {
      case 'high': return 'text-red-500 bg-red-500/10';
      case 'elevated': return 'text-orange-500 bg-orange-500/10';
      case 'balanced': return 'text-green-500 bg-green-500/10';
      case 'low': return 'text-blue-500 bg-blue-500/10';
      case 'offline': return 'text-gray-500 bg-gray-500/10';
      default: return 'text-gray-500 bg-gray-500/10';
    }
  };

  const getPatternIcon = (type: string) => {
    switch (type) {
      case 'peak': return <TrendingUp className="w-4 h-4" />;
      case 'flow': return <Zap className="w-4 h-4" />;
      case 'focus': return <Focus className="w-4 h-4" />;
      case 'recovery': return <Heart className="w-4 h-4" />;
      case 'creative': return <Brain className="w-4 h-4" />;
      default: return <Activity className="w-4 h-4" />;
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Biometric Energy
          </div>
          <div className="flex items-center gap-2">
            {isMonitoring && (
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            )}
            <Badge variant="outline" className="text-xs">
              {numentumMultiplier.toFixed(2)}x boost
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Current State Overview */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="flex items-center gap-1">
                <Zap className="w-4 h-4" />
                Energy Level
              </span>
              <span className="font-medium">{energyLevel.toFixed(0)}%</span>
            </div>
            <Progress value={isNaN(energyLevel) ? 0 : Math.max(0, Math.min(100, energyLevel))} className="h-2" />
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="flex items-center gap-1">
                <Focus className="w-4 h-4" />
                Focus Score
              </span>
              <span className="font-medium">{focusScore.toFixed(0)}%</span>
            </div>
            <Progress value={isNaN(focusScore) ? 0 : Math.max(0, Math.min(100, focusScore))} className="h-2" />
          </div>
        </div>

        {/* Energy State Badge */}
        <div className="flex items-center justify-between">
          <Badge className={`${getEnergyStateColor(current?.state)} border-0`}>
            <Activity className="w-3 h-3 mr-1" />
            {current?.state || 'balanced'} energy
          </Badge>
          
          {current?.heartRate && (
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Heart className="w-4 h-4 text-red-500" />
              {Math.round(current.heartRate)} bpm
            </div>
          )}
        </div>

        {/* Stress Level */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="flex items-center gap-1">
              <Thermometer className="w-4 h-4" />
              Stress Level
            </span>
            <span className="font-medium">{stressLevel.toFixed(0)}%</span>
          </div>
          <Progress 
            value={stressLevel} 
            className="h-2" 
            // Red for high stress, green for low stress
            style={{
              background: stressLevel > 70 ? 'rgba(239, 68, 68, 0.2)' : 
                         stressLevel > 40 ? 'rgba(245, 158, 11, 0.2)' : 
                         'rgba(34, 197, 94, 0.2)'
            }}
          />
        </div>

        {/* Active Energy Patterns */}
        {activePatterns.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <Sun className="w-4 h-4" />
              Active Energy Patterns
            </h4>
            <div className="grid gap-2">
              {activePatterns.map((pattern, index) => (
                <div key={index} className="flex items-center justify-between p-2 rounded-lg bg-muted/50">
                  <div className="flex items-center gap-2">
                    {getPatternIcon(pattern.type)}
                    <span className="text-sm font-medium capitalize">{pattern.type}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="text-xs text-muted-foreground">
                      {(pattern.intensity * 100).toFixed(0)}%
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {pattern.numentumMultiplier.toFixed(2)}x
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Energy History Mini-Chart */}
        {energyHistory.length > 5 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              Energy Trend (Last 40s)
            </h4>
            <div className="h-16 flex items-end gap-1">
              {energyHistory.map((level, index) => (
                <div
                  key={index}
                  className="flex-1 bg-gradient-to-t from-blue-500/60 to-blue-500/20 rounded-sm min-h-[2px]"
                  style={{ 
                    height: `${Math.max(2, (level / 100) * 64)}px`,
                    opacity: 0.3 + (index / energyHistory.length) * 0.7
                  }}
                />
              ))}
            </div>
          </div>
        )}

        {/* Biometric Insights */}
        <div className="text-xs text-muted-foreground space-y-1">
          <div className="flex items-center gap-1">
            <Eye className="w-3 h-3" />
            Monitoring: Heart rate, movement, focus, light exposure
          </div>
          <div className="flex items-center gap-1 text-blue-600">
            <Activity className="w-3 h-3" />
            Real-time data: {biometricState.deviceCapabilities ? 
              Object.values(biometricState.deviceCapabilities).filter(Boolean).length : 0}/6 sensors active
          </div>
          {numentumMultiplier > 1.0 && (
            <div className="flex items-center gap-1 text-green-600">
              <Zap className="w-3 h-3" />
              Biometric boost active: +{((numentumMultiplier - 1) * 100).toFixed(0)}% nUmentum
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}