/**
 * Live Activity Feed - Real-time visual feed of SpUnder Butler actions
 * Shows live functions, edits, and system actions like Cursor/VS Code
 */

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { activityDispatcher, ActivityEvent } from "@/lib/activity-dispatcher";
import { 
  Bot, 
  Code, 
  FileEdit, 
  Zap, 
  Brain, 
  Activity, 
  Eye, 
  Settings,
  Play,
  Pause,
  Trash2,
  Download,
  Clock,
  CheckCircle,
  AlertTriangle,
  Info,
  Wrench,
  Database,
  Network,
  Shield,
  TrendingUp
} from "lucide-react";

interface LiveAction extends ActivityEvent {
  id: string;
  timestamp: number;
}

export function LiveActivityFeed() {
  const [actions, setActions] = useState<LiveAction[]>([]);
  const [isRecording, setIsRecording] = useState(true);
  const [filter, setFilter] = useState<string>('all');
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isRecording) return;

    // Subscribe to activity dispatcher
    const unsubscribe = activityDispatcher.subscribe((event: ActivityEvent) => {
      const action: LiveAction = {
        ...event,
        id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now()
      };

      setActions(prev => [action, ...prev.slice(0, 199)]); // Keep only last 200
    });

    // Also listen for window events for backwards compatibility
    const handleSpUnderActivity = (event: any) => {
      const action: LiveAction = {
        id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
        type: event.detail.type || 'system_analysis',
        title: event.detail.title || 'System Activity',
        description: event.detail.description || 'Processing...',
        details: event.detail.details,
        status: event.detail.status || 'starting',
        metadata: event.detail.metadata
      };

      setActions(prev => [action, ...prev.slice(0, 199)]);
    };

    window.addEventListener('spunder-activity', handleSpUnderActivity);

    // Initial welcome message
    setTimeout(() => {
      activityDispatcher.dispatch({
        type: 'system_analysis',
        title: 'Live Activity Feed Initialized',
        description: 'Real-time monitoring of SpUnder Butler functions started',
        status: 'completed',
        details: 'All system activities, function calls, and AI operations will appear here in real-time'
      });
    }, 1000);

    return () => {
      unsubscribe();
      window.removeEventListener('spunder-activity', handleSpUnderActivity);
    };
  }, [isRecording]);

  useEffect(() => {
    // Auto-scroll to top when new actions arrive
    if (scrollRef.current) {
      scrollRef.current.scrollTop = 0;
    }
  }, [actions]);

  const startActivityMonitoring = () => {
    // Monitor console logs for SpUnder activities
    const originalConsoleLog = console.log;
    console.log = (...args) => {
      originalConsoleLog.apply(console, args);
      
      if (args[0] && typeof args[0] === 'string') {
        const message = args[0];
        
        // Parse SpUnder Bot activities
        if (message.includes('[SpUnderBot]')) {
          parseSpUnderActivity(message, args);
        } else if (message.includes('[AdvancedSpUnderBot]')) {
          parseAdvancedActivity(message, args);
        } else if (message.includes('[SystemInitializer]')) {
          parseSystemActivity(message, args);
        } else if (message.includes('[WalletFix]')) {
          parseWalletActivity(message, args);
        } else if (message.includes('[EnergySyncController]')) {
          parseEnergyActivity(message, args);
        }
      }
    };

    // Simulate some initial activities
    setTimeout(() => {
      dispatchActivity({
        type: 'system_analysis',
        title: 'System Butler Initialized',
        description: 'SpUnder Butler has started autonomous monitoring',
        status: 'completed',
        details: 'All monitoring systems are now active and tracking real-time activities'
      });
    }, 1000);
  };

  const parseSpUnderActivity = (message: string, args: any[]) => {
    if (message.includes('Running comprehensive system diagnostics')) {
      dispatchActivity({
        type: 'diagnosis',
        title: 'System Diagnostics',
        description: 'Scanning system health and performance',
        status: 'in_progress',
        details: 'Checking wallet, extension, buttons, energy generation, and API endpoints'
      });
    } else if (message.includes('Repair task completed')) {
      const taskType = message.match(/completed: (\w+)/)?.[1];
      dispatchActivity({
        type: 'repair',
        title: 'Repair Completed',
        description: `Successfully repaired ${taskType?.replace('_', ' ')} system`,
        status: 'completed',
        details: `Automated repair process has restored functionality`
      });
    } else if (message.includes('Issue reported')) {
      const issueType = message.match(/: (\w+) -/)?.[1];
      dispatchActivity({
        type: 'diagnosis',
        title: 'Issue Detected',
        description: `Found ${issueType?.replace('_', ' ')} issue`,
        status: 'warning',
        details: message.split(' - ')[1]
      });
    }
  };

  const parseAdvancedActivity = (message: string, args: any[]) => {
    if (message.includes('Autonomous system butler initialized')) {
      dispatchActivity({
        type: 'system_analysis',
        title: 'Advanced AI Butler Online',
        description: 'Autonomous development capabilities activated',
        status: 'completed',
        details: 'Code generation, architecture analysis, and learning engine are now active'
      });
    } else if (message.includes('system analysis')) {
      dispatchActivity({
        type: 'system_analysis',
        title: 'System Analysis',
        description: 'Analyzing performance, security, and code quality',
        status: 'in_progress',
        details: 'Deep scanning architecture, performance metrics, and identifying optimization opportunities'
      });
    } else if (message.includes('task submitted')) {
      dispatchActivity({
        type: 'function_call',
        title: 'AI Task Submitted',
        description: 'New autonomous task queued for execution',
        status: 'starting',
        details: 'Task will be processed by the AI development engine'
      });
    }
  };

  const parseSystemActivity = (message: string, args: any[]) => {
    if (message.includes('system initialization')) {
      dispatchActivity({
        type: 'system_analysis',
        title: 'System Integration',
        description: 'Connecting all nU Universe components',
        status: 'in_progress',
        details: 'Initializing nUCore, Ghost Bots, IoT Manager, AI Orchestrator, and energy systems'
      });
    } else if (message.includes('Fixing wallet updates')) {
      dispatchActivity({
        type: 'repair',
        title: 'Wallet System Repair',
        description: 'Restoring real-time wallet functionality',
        status: 'in_progress',
        details: 'Re-establishing API connections and update mechanisms'
      });
    }
  };

  const parseWalletActivity = (message: string, args: any[]) => {
    if (message.includes('Wallet updated')) {
      const balance = args[1];
      dispatchActivity({
        type: 'optimization',
        title: 'Wallet Balance Updated',
        description: `Balance: ${balance?.toFixed?.(2) || balance} UMatter`,
        status: 'completed',
        details: 'Real-time balance synchronization successful'
      });
    }
  };

  const parseEnergyActivity = (message: string, args: any[]) => {
    if (message.includes('Batch processed successfully')) {
      const data = args[1];
      dispatchActivity({
        type: 'optimization',
        title: 'Energy Batch Processed',
        description: `Processed ${data?.batchSize || 'multiple'} energy transactions`,
        status: 'completed',
        details: `Generated ${data?.totalAmount?.toFixed?.(6) || 'N/A'} UMatter from authentic device data`,
        metadata: data
      });
    }
  };

  const dispatchActivity = (activityData: Omit<LiveAction, 'id' | 'timestamp'>) => {
    const event = new CustomEvent('spunder-activity', {
      detail: activityData
    });
    window.dispatchEvent(event);
  };

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'code_edit': 
      case 'file_edit': return <Code className="h-4 w-4" />;
      case 'file_creation': return <FileEdit className="h-4 w-4" />;
      case 'system_repair': 
      case 'repair': return <Wrench className="h-4 w-4" />;
      case 'analysis':
      case 'system_analysis': return <Brain className="h-4 w-4" />;
      case 'optimization': return <TrendingUp className="h-4 w-4" />;
      case 'diagnosis': return <Activity className="h-4 w-4" />;
      case 'learning': return <Bot className="h-4 w-4" />;
      case 'task_execution': return <Play className="h-4 w-4" />;
      case 'function_call':
      case 'api_call': return <Network className="h-4 w-4" />;
      case 'code_generation': return <Zap className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400 bg-green-900/20';
      case 'in_progress': return 'text-blue-400 bg-blue-900/20';
      case 'starting': return 'text-yellow-400 bg-yellow-900/20';
      case 'failed': return 'text-red-400 bg-red-900/20';
      case 'warning': return 'text-orange-400 bg-orange-900/20';
      default: return 'text-gray-400 bg-gray-900/20';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-3 w-3" />;
      case 'in_progress': return <Activity className="h-3 w-3 animate-spin" />;
      case 'starting': return <Clock className="h-3 w-3" />;
      case 'failed': return <AlertTriangle className="h-3 w-3" />;
      case 'warning': return <AlertTriangle className="h-3 w-3" />;
      default: return <Info className="h-3 w-3" />;
    }
  };

  const filteredActions = actions.filter(action => 
    filter === 'all' || action.type === filter
  );

  const clearFeed = () => {
    setActions([]);
  };

  const exportFeed = () => {
    const data = JSON.stringify(actions, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `spunder-activity-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <Card className="bg-gradient-to-br from-gray-900/20 to-slate-900/20 border-gray-500/30 h-[600px] flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-gray-300">
            <Eye className="h-5 w-5" />
            Live Activity Feed
            <Badge variant="secondary" className="bg-green-600 text-white">
              <Activity className="h-3 w-3 mr-1" />
              {isRecording ? 'LIVE' : 'PAUSED'}
            </Badge>
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsRecording(!isRecording)}
              className="text-gray-400 hover:text-white"
            >
              {isRecording ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={exportFeed}
              className="text-gray-400 hover:text-white"
            >
              <Download className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFeed}
              className="text-gray-400 hover:text-white"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="flex gap-2 flex-wrap">
          {['all', 'system_analysis', 'function_call', 'code_generation', 'repair', 'optimization', 'diagnosis'].map(filterType => (
            <Button
              key={filterType}
              variant="ghost"
              size="sm"
              onClick={() => setFilter(filterType)}
              className={`text-xs ${filter === filterType ? 'bg-blue-600 text-white' : 'text-gray-400'}`}
            >
              {filterType.replace('_', ' ').toUpperCase()}
            </Button>
          ))}
        </div>
        
        <div className="text-sm text-gray-400">
          {actions.length} total activities • {filteredActions.length} filtered
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 p-0">
        <ScrollArea className="h-full px-4" ref={scrollRef}>
          <div className="space-y-2 pb-4">
            {filteredActions.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Bot className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No activities yet</p>
                <p className="text-xs">SpUnder Butler actions will appear here</p>
              </div>
            ) : (
              filteredActions.map(action => (
                <div
                  key={action.id}
                  className="bg-gray-800/30 rounded-lg p-3 border border-gray-700/30 hover:border-gray-600/50 transition-colors"
                >
                  <div className="flex items-start gap-3">
                    <div className={`p-1.5 rounded ${getStatusColor(action.status)}`}>
                      {getActionIcon(action.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-gray-200 text-sm">
                          {action.title}
                        </span>
                        <Badge variant="secondary" className={`text-xs ${getStatusColor(action.status)}`}>
                          {getStatusIcon(action.status)}
                          <span className="ml-1">{action.status.replace('_', ' ')}</span>
                        </Badge>
                      </div>
                      
                      <p className="text-gray-400 text-sm mb-1">
                        {action.description}
                      </p>
                      
                      {action.details && (
                        <p className="text-gray-500 text-xs">
                          {action.details}
                        </p>
                      )}

                      {action.functionName && (
                        <div className="flex items-center gap-1 mt-1">
                          <Badge variant="outline" className="text-xs bg-blue-900/20 text-blue-400 border-blue-500/30">
                            <Code className="h-2 w-2 mr-1" />
                            {action.functionName}()
                          </Badge>
                        </div>
                      )}

                      {action.file && (
                        <div className="flex items-center gap-1 mt-1">
                          <Badge variant="outline" className="text-xs bg-purple-900/20 text-purple-400 border-purple-500/30">
                            <FileEdit className="h-2 w-2 mr-1" />
                            {action.file}
                            {action.lines && `:${action.lines[0]}-${action.lines[1]}`}
                          </Badge>
                        </div>
                      )}
                      
                      <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                        <Clock className="h-3 w-3" />
                        <span>{new Date(action.timestamp).toLocaleTimeString()}</span>
                        {action.duration && (
                          <span>• {action.duration.toFixed(1)}ms</span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

export default LiveActivityFeed;