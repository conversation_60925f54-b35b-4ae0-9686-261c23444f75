import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Card, CardHeader, CardTitle, CardContent } from './ui/card';
import { Button } from './ui/button';
import { Brain, Zap, AlertTriangle } from 'lucide-react';

interface AnalysisResult {
  energyEfficiency: number;
  anomalies: string[];
  recommendations: string[];
  realTimeScore: number;
}

export const AIAnalysisPanel: React.FC = () => {
  // Real-time analysis from backend
  const { data: analysis, isLoading: isAnalyzing, refetch } = useQuery({
    queryKey: ['/api/ai/analyze-energy'],
    refetchInterval: 30000,
    enabled: false // Manual trigger only
  });

  // Manual analysis trigger
  const analysisMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch('/api/ai/analyze-energy', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          includeDeviceMetrics: true,
          includeEnergyHistory: true,
          includeMarketTrends: true
        })
      });
      return response.json();
    }
  });

  const performRealAnalysis = () => {
    analysisMutation.mutate();
  };

  useEffect(() => {
    // Initial analysis
    performRealAnalysis();
  }, []);

  return (
    <Card className="glass-panel border-purple-400/30">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-purple-400 flex items-center">
            <Brain className="w-5 h-5 mr-2" />
            AI Energy Analysis
          </CardTitle>
          <Button
            onClick={performRealAnalysis}
            disabled={isAnalyzing}
            className="bg-gradient-to-r from-purple-400 to-blue-400"
          >
            {isAnalyzing ? 'Analyzing...' : 'Refresh Analysis'}
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {(analysis || analysisMutation.data) ? (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-text-secondary">Energy Efficiency:</span>
              <div className="flex items-center">
                <Zap className="w-4 h-4 mr-1 text-yellow-400" />
                <span className="text-neon-cyan font-bold">
                  {(analysis || analysisMutation.data)?.energyEfficiency || 0}%
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-text-secondary">Real-time Score:</span>
              <span className="text-green-400 font-bold">
                {(analysis || analysisMutation.data)?.realTimeScore || 0}/100
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-text-secondary">Data Points:</span>
              <span className="text-blue-400 font-bold">
                {(analysis || analysisMutation.data)?.dataPoints || 0}
              </span>
            </div>

            {((analysis || analysisMutation.data)?.anomalies?.length > 0) && (
              <div className="space-y-2">
                <h4 className="text-red-400 flex items-center">
                  <AlertTriangle className="w-4 h-4 mr-1" />
                  Detected Issues:
                </h4>
                {(analysis || analysisMutation.data).anomalies.map((anomaly: string, index: number) => (
                  <p key={index} className="text-xs text-red-300 ml-5">• {anomaly}</p>
                ))}
              </div>
            )}

            {((analysis || analysisMutation.data)?.recommendations?.length > 0) && (
              <div className="space-y-2">
                <h4 className="text-green-400">Optimization Recommendations:</h4>
                {(analysis || analysisMutation.data).recommendations.map((rec: string, index: number) => (
                  <p key={index} className="text-xs text-green-300 ml-4">• {rec}</p>
                ))}
              </div>
            )}

            {(analysis || analysisMutation.data)?.lastAnalysis && (
              <div className="text-xs text-gray-400 mt-2">
                Last analysis: {new Date((analysis || analysisMutation.data).lastAnalysis).toLocaleTimeString()}
              </div>
            )}
          </div>
        ) : (
          <div className="text-center text-text-secondary">
            <Brain className="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>Click to analyze your real energy data</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};