
import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Zap } from 'lucide-react';

export function SimpleUMatterDisplay() {
  const [umatter, setUMatter] = useState(0);
  const [updateCount, setUpdateCount] = useState(0);
  const [lastUpdate, setLastUpdate] = useState(0);
  const [componentKey, setComponentKey] = useState(0);

  useEffect(() => {
    let mounted = true;
    let fetchInterval: NodeJS.Timeout;

    const fetchBalance = async () => {
      if (!mounted) return;

      try {
        const timestamp = Date.now();
        const response = await fetch(`/api/banking/balance?t=${timestamp}&r=${Math.random()}`, {
          method: 'GET',
          cache: 'no-store',
          headers: { 
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          }
        });

        if (response.ok) {
          const data = await response.json();
          const newBalance = Number(data.umatter) || 0;
          
          console.log(`[SimpleUMatterDisplay] LIVE Backend Data: ${newBalance} UMatter`);
          
          // Force updates
          setUMatter(newBalance);
          setUpdateCount(prev => prev + 1);
          setLastUpdate(timestamp);
          setComponentKey(prev => prev + 1); // Force re-render
        } else {
          console.error(`[SimpleUMatterDisplay] API Error: ${response.status}`);
        }
      } catch (error) {
        console.error('[SimpleUMatterDisplay] Error:', error);
      }
    };

    fetchBalance();
    fetchInterval = setInterval(fetchBalance, 2000);
    
    return () => {
      mounted = false;
      if (fetchInterval) clearInterval(fetchInterval);
    };
  }, []);

  const secondsSinceUpdate = lastUpdate > 0 ? Math.floor((Date.now() - lastUpdate) / 1000) : 0;

  return (
    <Card 
      className="bg-gradient-to-br from-yellow-900/20 to-orange-900/20 border-yellow-500/20" 
      key={componentKey}
    >
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="w-5 h-5 text-yellow-400" />
          UMatter Balance (Update #{updateCount})
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-4xl font-bold text-yellow-400" key={`balance-${umatter}-${updateCount}`}>
          {umatter.toFixed(8)} UMatter
        </div>
        <div className="text-sm text-gray-400 mt-2">
          Direct from Backend API - {secondsSinceUpdate < 5 ? 'LIVE' : `${secondsSinceUpdate}s ago`}
        </div>
        <div className="text-xs text-green-400 mt-1">
          Updates Received: {updateCount}
        </div>
      </CardContent>
    </Card>
  );
}
