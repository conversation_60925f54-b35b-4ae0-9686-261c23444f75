import React, { useState } from 'react';
import { Link, useLocation } from 'wouter';
import { But<PERSON> } from "@/components/ui/button";
import { She<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, SheetTitle } from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { 
  Menu, 
  X, 
  Home, 
  Zap, 
  Wallet, 
  ShoppingCart, 
  Settings, 
  Users,
  BarChart3,
  Smartphone,
  Globe,
  Database,
  Bot,
  Search
} from "lucide-react";

const navigationSections = {
  core: [
    { path: '/', label: 'Dashboard', icon: Home },
    { path: '/energy-hub', label: 'Energy Hub', icon: Zap },
    { path: '/wallet', label: 'Wallet', icon: Wallet },
    { path: '/trading', label: 'Trading', icon: BarChart3 }
  ],
  mobile: [
    { path: '/mobile-sync', label: 'Mobile Sync', icon: Smartphone },
    { path: '/extension', label: 'Extension', icon: Globe },
    { path: '/devices', label: 'Devices', icon: Database }
  ],
  marketplace: [
    { path: '/marketplace', label: 'Marketplace', icon: ShoppingCart },
    { path: '/social-sync', label: 'Social', icon: Users },
    { path: '/ai-search', label: 'AI Search', icon: Search }
  ],
  tools: [
    { path: '/quantum', label: 'Quantum', icon: Bot },
    { path: '/settings', label: 'Settings', icon: Settings }
  ]
};

export function MobileResponsiveNavigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [location] = useLocation();

  const isActive = (path: string) => {
    if (path === '/') return location === '/';
    return location.startsWith(path);
  };

  const handleNavigation = () => {
    setIsOpen(false);
  };

  return (
    <>
      {/* Mobile Header Bar */}
      <div className="lg:hidden fixed top-0 left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md border-b border-gray-800">
        <div className="flex items-center justify-between px-4 py-3">
          {/* Logo */}
          <Link href="/" onClick={handleNavigation}>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-cyan-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Zap className="w-4 h-4 text-white" />
              </div>
              <span className="text-lg font-bold bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
                nU
              </span>
            </div>
          </Link>

          {/* Menu Button */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm" className="text-white">
                <Menu className="w-5 h-5" />
              </Button>
            </SheetTrigger>
            <SheetContent 
              side="right" 
              className="w-full sm:w-80 p-0 bg-gray-900 border-gray-800"
            >
              <SheetHeader className="p-4 border-b border-gray-800">
                <div className="flex items-center justify-between">
                  <SheetTitle className="text-white">Navigation</SheetTitle>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => setIsOpen(false)}
                    className="text-white"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </SheetHeader>

              <div className="overflow-y-auto h-full p-4 space-y-6">
                {/* Core Features */}
                <div>
                  <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wide mb-3">
                    Core Platform
                  </h3>
                  <div className="space-y-1">
                    {navigationSections.core.map((item) => {
                      const Icon = item.icon;
                      return (
                        <Link
                          key={item.path}
                          href={item.path}
                          onClick={handleNavigation}
                        >
                          <div className={`
                            flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors
                            ${isActive(item.path) 
                              ? 'bg-blue-600/20 text-blue-400 border border-blue-600/30' 
                              : 'text-gray-300 hover:bg-gray-800/50 hover:text-white'
                            }
                          `}>
                            <Icon className="w-5 h-5 flex-shrink-0" />
                            <span className="font-medium">{item.label}</span>
                            {isActive(item.path) && (
                              <Badge variant="secondary" className="ml-auto bg-blue-900/30 text-blue-400">
                                Active
                              </Badge>
                            )}
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                </div>

                {/* Mobile Features */}
                <div>
                  <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wide mb-3">
                    Mobile & Devices
                  </h3>
                  <div className="space-y-1">
                    {navigationSections.mobile.map((item) => {
                      const Icon = item.icon;
                      return (
                        <Link
                          key={item.path}
                          href={item.path}
                          onClick={handleNavigation}
                        >
                          <div className={`
                            flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors
                            ${isActive(item.path) 
                              ? 'bg-green-600/20 text-green-400 border border-green-600/30' 
                              : 'text-gray-300 hover:bg-gray-800/50 hover:text-white'
                            }
                          `}>
                            <Icon className="w-5 h-5 flex-shrink-0" />
                            <span className="font-medium">{item.label}</span>
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                </div>

                {/* Marketplace */}
                <div>
                  <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wide mb-3">
                    Marketplace & Social
                  </h3>
                  <div className="space-y-1">
                    {navigationSections.marketplace.map((item) => {
                      const Icon = item.icon;
                      return (
                        <Link
                          key={item.path}
                          href={item.path}
                          onClick={handleNavigation}
                        >
                          <div className={`
                            flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors
                            ${isActive(item.path) 
                              ? 'bg-purple-600/20 text-purple-400 border border-purple-600/30' 
                              : 'text-gray-300 hover:bg-gray-800/50 hover:text-white'
                            }
                          `}>
                            <Icon className="w-5 h-5 flex-shrink-0" />
                            <span className="font-medium">{item.label}</span>
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                </div>

                {/* Tools */}
                <div>
                  <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wide mb-3">
                    Tools & Settings
                  </h3>
                  <div className="space-y-1">
                    {navigationSections.tools.map((item) => {
                      const Icon = item.icon;
                      return (
                        <Link
                          key={item.path}
                          href={item.path}
                          onClick={handleNavigation}
                        >
                          <div className={`
                            flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors
                            ${isActive(item.path) 
                              ? 'bg-orange-600/20 text-orange-400 border border-orange-600/30' 
                              : 'text-gray-300 hover:bg-gray-800/50 hover:text-white'
                            }
                          `}>
                            <Icon className="w-5 h-5 flex-shrink-0" />
                            <span className="font-medium">{item.label}</span>
                          </div>
                        </Link>
                      );
                    })}
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* Mobile Bottom Navigation */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 z-40 bg-gray-900/95 backdrop-blur-md border-t border-gray-800">
        <div className="grid grid-cols-4 gap-1 p-2">
          {navigationSections.core.map((item) => {
            const Icon = item.icon;
            return (
              <Link
                key={item.path}
                href={item.path}
                onClick={handleNavigation}
              >
                <div className={`
                  flex flex-col items-center space-y-1 p-2 rounded-lg transition-colors
                  ${isActive(item.path) 
                    ? 'bg-blue-600/20 text-blue-400' 
                    : 'text-gray-400 hover:text-white'
                  }
                `}>
                  <Icon className="w-5 h-5" />
                  <span className="text-xs font-medium">{item.label}</span>
                </div>
              </Link>
            );
          })}
        </div>
      </div>

      {/* Desktop padding for content */}
      <div className="lg:hidden h-16"></div>
    </>
  );
}