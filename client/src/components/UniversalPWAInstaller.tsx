import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { 
  Download, 
  Smartphone, 
  Monitor, 
  Share, 
  Plus,
  CheckCircle,
  ExternalLink,
  Wifi,
  Bell,
  Zap
} from 'lucide-react';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

interface InstallationStatus {
  canInstall: boolean;
  isInstalled: boolean;
  platform: 'ios' | 'android' | 'desktop' | 'mobile' | 'unknown';
  browser: string;
  installMethod: 'native' | 'manual' | 'share';
  supportsInstall: boolean;
}

export function UniversalPWAInstaller() {
  const [installPrompt, setInstallPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [installStatus, setInstallStatus] = useState<InstallationStatus>({
    canInstall: false,
    isInstalled: false,
    platform: 'unknown',
    browser: 'unknown',
    installMethod: 'manual',
    supportsInstall: false
  });
  const [showInstructions, setShowInstructions] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [installSuccess, setInstallSuccess] = useState(false);

  // Detect platform and browser capabilities
  useEffect(() => {
    const detectPlatform = (): InstallationStatus => {
      const userAgent = navigator.userAgent;
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isInWebAppiOS = (window.navigator as any).standalone === true;
      
      let platform: InstallationStatus['platform'] = 'unknown';
      let browser = 'unknown';
      let installMethod: InstallationStatus['installMethod'] = 'manual';
      let supportsInstall = false;

      // Detect platform
      if (/iPhone|iPad|iPod/i.test(userAgent)) {
        platform = 'ios';
        browser = /CriOS/i.test(userAgent) ? 'Chrome' : 'Safari';
        installMethod = 'manual';
        supportsInstall = true;
      } else if (/Android/i.test(userAgent)) {
        platform = 'android';
        browser = /Chrome/i.test(userAgent) ? 'Chrome' : 'Browser';
        installMethod = 'native';
        supportsInstall = true;
      } else if (/Windows|Mac|Linux/i.test(userAgent)) {
        platform = 'desktop';
        if (/Chrome/i.test(userAgent)) {
          browser = 'Chrome';
          installMethod = 'native';
          supportsInstall = true;
        } else if (/Firefox/i.test(userAgent)) {
          browser = 'Firefox';
          installMethod = 'manual';
          supportsInstall = true;
        } else if (/Edge/i.test(userAgent)) {
          browser = 'Edge';
          installMethod = 'native';
          supportsInstall = true;
        }
      } else {
        platform = 'mobile';
        supportsInstall = true;
      }

      return {
        canInstall: !!installPrompt || supportsInstall,
        isInstalled: isStandalone || isInWebAppiOS,
        platform,
        browser,
        installMethod: installPrompt ? 'native' : installMethod,
        supportsInstall
      };
    };

    setInstallStatus(detectPlatform());
  }, [installPrompt]);

  // Listen for beforeinstallprompt event
  useEffect(() => {
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setInstallPrompt(e as BeforeInstallPromptEvent);
      console.log('[PWA] Install prompt available');
    };

    const handleAppInstalled = () => {
      setInstallPrompt(null);
      setInstallSuccess(true);
      setInstallStatus(prev => ({ ...prev, isInstalled: true, canInstall: false }));
      console.log('[PWA] App installed successfully');
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  // Register service worker
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/service-worker.js')
        .then(registration => {
          console.log('[PWA] Service Worker registered:', registration);
        })
        .catch(error => {
          console.error('[PWA] Service Worker registration failed:', error);
        });
    }
  }, []);

  const handleInstall = async () => {
    if (!installPrompt) {
      setShowInstructions(true);
      return;
    }

    setIsInstalling(true);
    try {
      await installPrompt.prompt();
      const { outcome } = await installPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('[PWA] User accepted install prompt');
        setInstallSuccess(true);
      } else {
        console.log('[PWA] User dismissed install prompt');
      }
    } catch (error) {
      console.error('[PWA] Install failed:', error);
    } finally {
      setIsInstalling(false);
      setInstallPrompt(null);
    }
  };

  const getInstallInstructions = () => {
    const { platform, browser } = installStatus;
    
    if (platform === 'ios') {
      return [
        'Tap the Share button (square with arrow up) in Safari',
        'Scroll down and tap "Add to Home Screen"',
        'Customize the name if desired',
        'Tap "Add" in the top right corner'
      ];
    } else if (platform === 'android') {
      return [
        'Tap the menu button (three dots) in your browser',
        'Select "Add to Home screen" or "Install app"',
        'Confirm the installation',
        'The app will be added to your home screen'
      ];
    } else if (platform === 'desktop') {
      if (browser === 'Chrome' || browser === 'Edge') {
        return [
          'Look for the install icon (⊕) in your address bar',
          'Click the install icon or go to Settings > Install nU Universe',
          'Click "Install" in the dialog that appears',
          'The app will be added to your applications'
        ];
      } else if (browser === 'Firefox') {
        return [
          'Firefox supports PWAs through add-ons',
          'Install the "PWA for Firefox" extension',
          'Use the extension to install nU Universe',
          'Access the app from your applications menu'
        ];
      }
    }
    
    return [
      'Look for browser-specific install options',
      'Check your browser menu for "Add to Home Screen"',
      'Visit this site regularly for bookmark-like access',
      'Enable notifications for the best experience'
    ];
  };

  if (installStatus.isInstalled) {
    return (
      <Card className="bg-gradient-to-r from-green-900/20 to-emerald-900/20 border-green-500/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-400">
            <CheckCircle className="h-6 w-6" />
            nU Universe Installed Successfully
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-green-300">
              <Zap className="h-4 w-4" />
              <span>Running as installed PWA</span>
            </div>
            <div className="space-y-2 text-sm text-gray-300">
              <div className="flex items-center gap-2">
                <Wifi className="h-4 w-4 text-blue-400" />
                <span>Offline access enabled</span>
              </div>
              <div className="flex items-center gap-2">
                <Bell className="h-4 w-4 text-purple-400" />
                <span>Push notifications available</span>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-yellow-400" />
                <span>Optimized performance</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="bg-gradient-to-r from-blue-900/20 to-cyan-900/20 border-blue-500/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-400">
            {installStatus.platform === 'ios' || installStatus.platform === 'android' ? (
              <Smartphone className="h-6 w-6" />
            ) : (
              <Monitor className="h-6 w-6" />
            )}
            Universal PWA Installation
          </CardTitle>
          <p className="text-gray-300">Install nU Universe as a native app on ANY device</p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Platform Detection */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Platform</span>
              <Badge className="bg-blue-500/20 text-blue-300 capitalize">
                {installStatus.platform} {installStatus.browser}
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Install Method</span>
              <Badge className={installStatus.installMethod === 'native' ? "bg-green-500/20 text-green-300" : "bg-yellow-500/20 text-yellow-300"}>
                {installStatus.installMethod === 'native' ? 'One-Click' : 'Manual'}
              </Badge>
            </div>
          </div>

          {/* Supported Platforms */}
          <div className="bg-blue-500/10 p-4 rounded border border-blue-500/20">
            <h4 className="font-semibold text-blue-300 mb-3">✓ Supported Platforms</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm text-gray-300">
              <span>• iOS Safari</span>
              <span>• Android Chrome</span>
              <span>• Desktop Chrome</span>
              <span>• Firefox</span>
              <span>• Edge</span>
              <span>• Samsung Internet</span>
              <span>• Opera</span>
              <span>• All PWA browsers</span>
            </div>
          </div>

          {/* Install Button */}
          <div className="space-y-4">
            <Button 
              size="lg" 
              className="w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 disabled:opacity-50"
              onClick={handleInstall}
              disabled={isInstalling || !installStatus.supportsInstall}
            >
              {isInstalling ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Installing...
                </>
              ) : installPrompt ? (
                <>
                  <Download className="mr-2 h-5 w-5" />
                  Install nU Universe App
                </>
              ) : (
                <>
                  <Share className="mr-2 h-5 w-5" />
                  Show Install Instructions
                </>
              )}
            </Button>

            {installSuccess && (
              <div className="text-center p-3 bg-green-500/10 border border-green-500/20 rounded text-green-300">
                <CheckCircle className="inline mr-2 h-4 w-4" />
                Installation successful! Check your home screen or applications.
              </div>
            )}

            <div className="text-sm text-gray-400 space-y-1">
              <p><strong>Benefits:</strong> Offline access • Push notifications • Faster loading • Native feel</p>
              <p><strong>Storage:</strong> Works without internet • Automatic updates • Secure local data</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Manual Installation Instructions Dialog */}
      <Dialog open={showInstructions} onOpenChange={setShowInstructions}>
        <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-blue-400">
              {installStatus.platform === 'ios' ? <Smartphone className="h-5 w-5" /> : <Monitor className="h-5 w-5" />}
              Install Instructions
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-gray-300">
              For {installStatus.platform} {installStatus.browser}:
            </p>
            <ol className="space-y-2 text-sm text-gray-300">
              {getInstallInstructions().map((step, index) => (
                <li key={index} className="flex gap-3">
                  <span className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs">
                    {index + 1}
                  </span>
                  <span>{step}</span>
                </li>
              ))}
            </ol>
            <div className="pt-4 border-t border-gray-700">
              <Button 
                onClick={() => setShowInstructions(false)}
                className="w-full bg-blue-500 hover:bg-blue-600"
              >
                Got it!
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}