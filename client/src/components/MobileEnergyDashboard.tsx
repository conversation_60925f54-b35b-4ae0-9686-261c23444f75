/**
 * Mobile Energy Dashboard - Real mobile device energy generation
 * Uses authentic mobile browser APIs, no simulations
 */
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Battery, 
  Smartphone, 
  Zap, 
  TrendingUp, 
  Wifi, 
  Navigation,
  TouchPadIcon,
  Activity,
  MapPin
} from 'lucide-react';
import { realMobileAPIs } from '@/lib/realMobileAPIs';

export function MobileEnergyDashboard() {
  const [capabilities, setCapabilities] = useState<any>(null);
  const [energyGenerated, setEnergyGenerated] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);
  const [totalUMatter, setTotalUMatter] = useState(0);
  const [touchCount, setTouchCount] = useState(0);
  const [motionEnergy, setMotionEnergy] = useState(0);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initializeMobileAPIs = async () => {
      console.log('[MobileEnergyDashboard] Initializing real mobile APIs...');
      
      const success = await realMobileAPIs.initialize();
      setIsInitialized(success);
      
      if (success) {
        const unsubscribe = realMobileAPIs.subscribe((caps) => {
          setCapabilities(caps);
          
          // Calculate real energy from device capabilities
          const motionEnergy = realMobileAPIs.calculateEnergyFromMotion();
          const batteryEnergy = realMobileAPIs.calculateEnergyFromBattery();
          
          setMotionEnergy(motionEnergy);
          setEnergyGenerated(prev => prev + motionEnergy + batteryEnergy);
        });
        
        return unsubscribe;
      }
    };

    initializeMobileAPIs();
  }, []);

  // Real touch energy generation
  useEffect(() => {
    const handleTouch = (event: TouchEvent) => {
      const touchEnergy = realMobileAPIs.calculateEnergyFromTouch(event.touches.length);
      setTouchCount(prev => prev + event.touches.length);
      setEnergyGenerated(prev => prev + touchEnergy);
      setTotalUMatter(prev => prev + touchEnergy);
      
      console.log('[MobileEnergyDashboard] Touch energy generated:', touchEnergy);
    };

    document.addEventListener('touchstart', handleTouch);
    document.addEventListener('touchmove', handleTouch);
    
    return () => {
      document.removeEventListener('touchstart', handleTouch);
      document.removeEventListener('touchmove', handleTouch);
    };
  }, []);

  const startEnergyGeneration = async () => {
    setIsGenerating(true);
    
    // Send real energy data to backend
    try {
      const response = await fetch('/api/banking/deposit-umatter-batch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          batch: [{
            amount: energyGenerated,
            source: 'mobile_device_authentic',
            timestamp: Date.now(),
            deviceData: capabilities
          }],
          totalAmount: energyGenerated,
          batchSize: 1
        })
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log('[MobileEnergyDashboard] ✅ Energy synced to backend:', result);
        setTotalUMatter(result.newBalance || totalUMatter);
      }
    } catch (error) {
      console.error('[MobileEnergyDashboard] Failed to sync energy:', error);
    }
    
    setIsGenerating(false);
  };

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900 text-white">
        <div className="text-center">
          <Activity className="h-12 w-12 animate-spin mx-auto mb-4 text-blue-400" />
          <h2 className="text-xl font-semibold mb-2">Initializing Mobile Device APIs</h2>
          <p className="text-gray-400">Connecting to authentic device capabilities...</p>
        </div>
      </div>
    );
  }

  if (!capabilities) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900 text-white">
        <div className="text-center">
          <Smartphone className="h-12 w-12 mx-auto mb-4 text-red-400" />
          <h2 className="text-xl font-semibold mb-2">Mobile APIs Not Available</h2>
          <p className="text-gray-400">This device doesn't support the required mobile APIs</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-4 space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Mobile Energy Generator</h1>
        <p className="text-gray-400">Real device energy from authentic mobile APIs</p>
      </div>

      {/* Energy Overview */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-400">
            <Zap className="h-5 w-5" />
            Live Energy Generation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-400">{energyGenerated.toFixed(6)}</p>
              <p className="text-sm text-gray-400">Current Session</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-400">{totalUMatter.toFixed(3)}</p>
              <p className="text-sm text-gray-400">Total UMatter</p>
            </div>
          </div>
          
          <Button 
            onClick={startEnergyGeneration}
            disabled={isGenerating}
            className="w-full mt-4 bg-green-600 hover:bg-green-700"
          >
            {isGenerating ? 'Syncing Energy...' : 'Sync Energy to Backend'}
          </Button>
        </CardContent>
      </Card>

      {/* Battery Status */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Battery className="h-5 w-5" />
            Real Battery Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span>Battery Level</span>
              <Badge className={capabilities.battery.level > 0.2 ? 'bg-green-600' : 'bg-red-600'}>
                {(capabilities.battery.level * 100).toFixed(1)}%
              </Badge>
            </div>
            <Progress value={capabilities.battery.level * 100} className="h-2" />
            <div className="flex justify-between items-center">
              <span>Charging Status</span>
              <Badge className={capabilities.battery.charging ? 'bg-green-600' : 'bg-gray-600'}>
                {capabilities.battery.charging ? 'Charging' : 'Not Charging'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Device Motion */}
      {capabilities.motion.acceleration && (
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Device Motion
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-2 text-sm">
              <div className="text-center">
                <p className="text-xs text-gray-400">X-Axis</p>
                <p className="font-mono">{capabilities.motion.acceleration.x.toFixed(2)}</p>
              </div>
              <div className="text-center">
                <p className="text-xs text-gray-400">Y-Axis</p>
                <p className="font-mono">{capabilities.motion.acceleration.y.toFixed(2)}</p>
              </div>
              <div className="text-center">
                <p className="text-xs text-gray-400">Z-Axis</p>
                <p className="font-mono">{capabilities.motion.acceleration.z.toFixed(2)}</p>
              </div>
            </div>
            <div className="mt-3 text-center">
              <p className="text-sm text-green-400">Motion Energy: {motionEnergy.toFixed(6)} UMatter</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Network Status */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wifi className="h-5 w-5" />
            Network Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Connection Type</span>
              <Badge>{capabilities.network.effectiveType}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Downlink Speed</span>
              <span>{capabilities.network.downlink} Mbps</span>
            </div>
            <div className="flex justify-between">
              <span>RTT</span>
              <span>{capabilities.network.rtt} ms</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Touch Interactions */}
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TouchPadIcon className="h-5 w-5" />
            Touch Energy
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Touch Points</span>
              <Badge>{capabilities.touch.maxTouchPoints}</Badge>
            </div>
            <div className="flex justify-between">
              <span>Touch Count</span>
              <span className="text-green-400">{touchCount}</span>
            </div>
            <p className="text-xs text-gray-400 mt-2">
              Touch the screen to generate energy from real touch interactions
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Location (if available) */}
      {capabilities.location && (
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Location
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Latitude</span>
                <span className="font-mono">{capabilities.location.latitude.toFixed(6)}</span>
              </div>
              <div className="flex justify-between">
                <span>Longitude</span>
                <span className="font-mono">{capabilities.location.longitude.toFixed(6)}</span>
              </div>
              <div className="flex justify-between">
                <span>Accuracy</span>
                <span>{capabilities.location.accuracy.toFixed(0)}m</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}