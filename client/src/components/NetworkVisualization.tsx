import { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { useQuery } from '@tanstack/react-query';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Play, Pause, Maximize2, Zap, Shield, Database, Brain, Users, Eye } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface NetworkNode {
  id: string;
  group: number;
  strength: number;
  type: 'user' | 'ai' | 'storage' | 'encryption' | 'cluster' | 'spunder' | 'memvid';
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
  activity?: number;
  timestamp?: number;
  label?: string;
  status?: 'active' | 'idle' | 'processing';
}

interface NetworkLink {
  source: string | NetworkNode;
  target: string | NetworkNode;
  value: number;
}

interface NetworkData {
  nodes: NetworkNode[];
  links: NetworkLink[];
}

export function NetworkVisualization() {
  const svgRef = useRef<SVGSVGElement>(null);
  const [isLive, setIsLive] = useState(true);
  const [selectedNode, setSelectedNode] = useState<NetworkNode | null>(null);
  const [animationFrame, setAnimationFrame] = useState(0);

  // Fetch real interaction nodes and connections
  const { data: nodes = [], isLoading: nodesLoading } = useQuery({
    queryKey: ['/api/interactions/nodes'],
    refetchInterval: isLive ? 5000 : false,
    enabled: true
  });

  const { data: connections = [], isLoading: connectionsLoading } = useQuery({
    queryKey: ['/api/interactions/connections'],
    refetchInterval: isLive ? 5000 : false,
    enabled: true
  });

  // Use real data directly from backend
  const dynamicNodes = nodes.length > 0 ? nodes : [];
  const dynamicConnections = connections.length > 0 ? connections : [];

  const networkData: NetworkData = {
    nodes: dynamicNodes.map((node: any) => ({
      id: node.id,
      group: node.group || 1,
      strength: node.strength || 10,
      type: node.type || 'user',
      activity: node.activity || 0,
      timestamp: node.timestamp || Date.now(),
      label: node.label || node.id,
      status: node.status || 'idle'
    })),
    links: dynamicConnections.map((link: any) => ({
      source: link.source,
      target: link.target,
      value: link.value || 1
    }))
  };

  useEffect(() => {
    if (!svgRef.current || networkData.nodes.length === 0) return;

    const svg = d3.select(svgRef.current);
    const width = 800;
    const height = 400;

    // Clear previous content
    svg.selectAll("*").remove();

    // Create simulation
    const simulation = d3.forceSimulation(networkData.nodes as any)
      .force("link", d3.forceLink(networkData.links).id((d: any) => d.id).distance(80))
      .force("charge", d3.forceManyBody().strength(-200))
      .force("center", d3.forceCenter(width / 2, height / 2));

    // Create gradient definitions
    const defs = svg.append("defs");

    const gradientCyan = defs.append("radialGradient")
      .attr("id", "gradientCyan")
      .attr("cx", "50%")
      .attr("cy", "50%")
      .attr("r", "50%");
    gradientCyan.append("stop").attr("offset", "0%").attr("stop-color", "hsl(187, 100%, 50%)").attr("stop-opacity", 0.8);
    gradientCyan.append("stop").attr("offset", "100%").attr("stop-color", "hsl(187, 100%, 50%)").attr("stop-opacity", 0.2);

    const gradientPurple = defs.append("radialGradient")
      .attr("id", "gradientPurple")
      .attr("cx", "50%")
      .attr("cy", "50%")
      .attr("r", "50%");
    gradientPurple.append("stop").attr("offset", "0%").attr("stop-color", "hsl(266, 85%, 58%)").attr("stop-opacity", 0.8);
    gradientPurple.append("stop").attr("offset", "100%").attr("stop-color", "hsl(266, 85%, 58%)").attr("stop-opacity", 0.2);

    // Draw links
    const link = svg.append("g")
      .attr("stroke", "hsl(187, 100%, 50%)")
      .attr("stroke-opacity", 0.6)
      .attr("stroke-width", 2)
      .selectAll("line")
      .data(networkData.links)
      .join("line")
      .attr("class", "node-connection")
      .style("stroke-dasharray", "5,5");

    // Animate connections
    link.style("animation", "dash 2s linear infinite");

    // Draw nodes
    const node = svg.append("g")
      .selectAll("circle")
      .data(networkData.nodes)
      .join("circle")
      .attr("r", (d: any) => {
        const baseSize = Math.sqrt(d.strength) * 2;
        return d.status === 'active' ? baseSize * 1.2 : baseSize;
      })
      .attr("fill", (d: any) => {
        switch(d.type) {
          case "user": return "url(#gradientCyan)";
          case "ai": return "url(#gradientPurple)";
          case "spunder": return "hsl(266, 85%, 58%)";
          case "memvid": return "hsl(187, 100%, 50%)";
          case "storage": return "hsl(160, 84%, 39%)";
          case "encryption": return "hsl(43, 96%, 56%)";
          case "cluster": return "hsl(0, 84%, 60%)";
          default: return "url(#gradientCyan)";
        }
      })
      .attr("stroke", (d: any) => {
        return d.status === 'active' ? "hsl(43, 96%, 56%)" : "hsl(0, 0%, 100%)";
      })
      .attr("stroke-width", (d: any) => d.status === 'active' ? 2 : 1)
      .style("cursor", "pointer")
      .style("filter", (d: any) => d.status === 'processing' ? "drop-shadow(0 0 8px hsl(187, 100%, 50%))" : "none")
      .on("click", (event, d) => {
        setSelectedNode(d);
        // Add pulse animation on click
        d3.select(event.target)
          .transition()
          .duration(200)
          .attr("r", (original: any) => Math.sqrt(original.strength) * 3)
          .transition()
          .duration(200)
          .attr("r", (original: any) => Math.sqrt(original.strength) * 2);
      })
      .call(d3.drag<SVGCircleElement, NetworkNode>()
        .on("start", (event, d) => {
          if (!event.active) simulation.alphaTarget(0.3).restart();
          d.fx = d.x;
          d.fy = d.y;
        })
        .on("drag", (event, d) => {
          d.fx = event.x;
          d.fy = event.y;
        })
        .on("end", (event, d) => {
          if (!event.active) simulation.alphaTarget(0);
          d.fx = null;
          d.fy = null;
        }));

    // Add particle effects
    const particles = svg.append("g").attr("class", "particles");
    const numParticles = 20;
    
    for (let i = 0; i < numParticles; i++) {
      particles.append("circle")
        .attr("r", Math.random() * 2 + 1)
        .attr("cx", Math.random() * width)
        .attr("cy", Math.random() * height)
        .attr("fill", "hsl(187, 100%, 50%)")
        .attr("opacity", Math.random() * 0.5 + 0.1)
        .style("animation", `float ${Math.random() * 10 + 5}s linear infinite`);
    }

    // Add labels
    const labels = svg.append("g")
      .selectAll("text")
      .data(networkData.nodes)
      .join("text")
      .text((d: any) => d.label || d.id.split("-")[0])
      .attr("font-size", "10px")
      .attr("fill", "hsl(0, 0%, 100%)")
      .attr("text-anchor", "middle")
      .attr("dy", "0.3em")
      .style("pointer-events", "none");

    // Node interactions
    node
      .on("mouseover", (event, d) => {
        d3.select(event.currentTarget).attr("stroke-width", 3);
        setSelectedNode(d);
      })
      .on("mouseout", (event) => {
        d3.select(event.currentTarget).attr("stroke-width", 1);
        setSelectedNode(null);
      });

    // Simulation tick
    simulation.on("tick", () => {
      link
        .attr("x1", (d: any) => d.source.x)
        .attr("y1", (d: any) => d.source.y)
        .attr("x2", (d: any) => d.target.x)
        .attr("y2", (d: any) => d.target.y);

      node
        .attr("cx", (d: any) => d.x)
        .attr("cy", (d: any) => d.y);

      labels
        .attr("x", (d: any) => d.x)
        .attr("y", (d: any) => d.y);
    });

    // Add CSS for animations
    const style = document.createElement('style');
    style.textContent = `
      @keyframes dash {
        to { stroke-dashoffset: -10; }
      }
      .node-connection {
        animation: dash 2s linear infinite;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
      simulation.stop();
    };
  }, [networkData]);

  // Node type icons
  const getNodeIcon = (type: string) => {
    switch (type) {
      case 'user': return Users;
      case 'ai': return Brain;
      case 'spunder': return Eye;
      case 'memvid': return Database;
      case 'storage': return Database;
      case 'encryption': return Shield;
      case 'cluster': return Zap;
      default: return Users;
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div className="lg:col-span-2">
        <Card className="bg-panel/40 backdrop-blur-md border border-neon-cyan/20 overflow-hidden">
          <CardHeader className="border-b border-neon-cyan/20">
            <div className="flex items-center justify-between">
              <CardTitle className="text-neon-cyan flex items-center gap-2">
                <Eye className="w-5 h-5" />
                Interaction Network
              </CardTitle>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary" className="bg-neon-purple/20 text-neon-purple">
                  {networkData.nodes.length} Nodes
                </Badge>
                <Badge variant="secondary" className="bg-neon-cyan/20 text-neon-cyan">
                  {networkData.links.length} Links
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsLive(!isLive)}
                  className={`${
                    isLive 
                      ? 'bg-neon-cyan/20 text-neon-cyan hover:bg-neon-cyan/30' 
                      : 'bg-panel/60 text-text-secondary hover:bg-panel/80'
                  }`}
                >
                  {isLive ? <Pause className="w-4 h-4 mr-1" /> : <Play className="w-4 h-4 mr-1" />}
                  {isLive ? 'Live' : 'Paused'}
                </Button>
            <Button
              variant="ghost"
              size="sm"
              className="bg-panel/60 text-text-secondary hover:bg-panel/80"
            >
              <Maximize2 className="w-4 h-4 mr-1" />
              Fullscreen
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <div className="relative h-96 bg-gradient-to-br from-space via-panel/20 to-space">
          <svg
            ref={svgRef}
            width="100%"
            height="100%"
            viewBox="0 0 800 400"
            className="absolute inset-0"
          />
          
          {/* Node tooltip */}
          {selectedNode && (
            <div className="absolute top-4 left-4 bg-panel/90 backdrop-blur-md border border-neon-cyan/40 rounded p-3 text-sm font-mono">
              <div className="text-neon-cyan font-semibold">{selectedNode.id}</div>
              <div className="text-text-secondary">Type: {selectedNode.type}</div>
              <div className="text-text-secondary">Strength: {selectedNode.strength}</div>
              <div className="text-text-secondary">Group: {selectedNode.group}</div>
            </div>
          )}

          {/* Loading state */}
          {(nodesLoading || connectionsLoading) && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin w-8 h-8 border-2 border-neon-cyan border-t-transparent rounded-full mx-auto mb-4"></div>
                <p className="text-text-secondary">Loading real interaction network...</p>
              </div>
            </div>
          )}
          
          {/* No data state */}
          {!nodesLoading && !connectionsLoading && networkData.nodes.length === 0 && (
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <p className="text-text-secondary mb-2">No network interactions yet</p>
                <p className="text-xs text-text-secondary">Start using the app to build your network</p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
        </Card>
      </div>

      {/* Node Inspection Panel */}
      <div className="space-y-6">
        <Card className="bg-panel/40 backdrop-blur-md border border-neon-cyan/20">
          <CardHeader className="border-b border-neon-cyan/20">
            <CardTitle className="text-neon-cyan flex items-center gap-2">
              <Brain className="w-5 h-5" />
              Network Analytics
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-neon-cyan/10 rounded border border-neon-cyan/20">
                <div className="text-xl font-bold text-neon-cyan">{networkData.nodes.length}</div>
                <div className="text-sm text-text-secondary">Active Nodes</div>
              </div>
              <div className="text-center p-3 bg-neon-purple/10 rounded border border-neon-purple/20">
                <div className="text-xl font-bold text-neon-purple">{networkData.links.length}</div>
                <div className="text-sm text-text-secondary">Connections</div>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="text-sm font-semibold text-neon-cyan">Node Types</h4>
              {['user', 'spunder', 'memvid', 'ai', 'storage', 'encryption'].map(type => {
                const count = networkData.nodes.filter(n => n.type === type).length;
                const Icon = getNodeIcon(type);
                return count > 0 ? (
                  <div key={type} className="flex items-center justify-between p-2 bg-panel/30 rounded">
                    <div className="flex items-center gap-2">
                      <Icon className="w-4 h-4 text-neon-cyan" />
                      <span className="text-sm capitalize">{type}</span>
                    </div>
                    <Badge variant="secondary" className="bg-neon-cyan/20 text-neon-cyan">
                      {count}
                    </Badge>
                  </div>
                ) : null;
              })}
            </div>
          </CardContent>
        </Card>

        {selectedNode && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <Card className="bg-panel/40 backdrop-blur-md border border-neon-purple/20">
              <CardHeader className="border-b border-neon-purple/20">
                <CardTitle className="text-neon-purple flex items-center gap-2">
                  {(() => {
                    const Icon = getNodeIcon(selectedNode.type);
                    return <Icon className="w-5 h-5" />;
                  })()}
                  Node Details
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 space-y-3">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-text-secondary">ID:</span>
                    <span className="font-mono text-neon-cyan">{selectedNode.label}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Type:</span>
                    <Badge variant="secondary" className="capitalize">
                      {selectedNode.type}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Status:</span>
                    <Badge 
                      variant="secondary" 
                      className={`${
                        selectedNode.status === 'active' ? 'bg-green-500/20 text-green-400' :
                        selectedNode.status === 'processing' ? 'bg-yellow-500/20 text-yellow-400' :
                        'bg-gray-500/20 text-gray-400'
                      }`}
                    >
                      {selectedNode.status}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Strength:</span>
                    <span className="text-neon-cyan">{selectedNode.strength?.toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-text-secondary">Activity:</span>
                    <span className="text-neon-purple">{selectedNode.activity?.toFixed(1)}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
}
