import { ReactNode } from "react";
import { UnifiedNavigation, usePageInfo } from "@/components/UnifiedNavigation";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";

interface PageLayoutProps {
  children: ReactNode;
  showBreadcrumbs?: boolean;
  className?: string;
}

export function PageLayout({ children, showBreadcrumbs = true, className = "" }: PageLayoutProps) {
  const { title, description, breadcrumbs } = usePageInfo();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black">
      <UnifiedNavigation />
      {/* Main Content */}
      <div className="lg:pl-72">
        <div className="flex flex-col min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black">
          {/* Page Header */}
          {showBreadcrumbs && breadcrumbs.length > 0 && (
            <div className="bg-gradient-to-r from-gray-900 via-slate-900 to-gray-800 border-b border-blue-500/30 px-4 py-3">
              <Breadcrumb>
                <BreadcrumbList>
                  {breadcrumbs.map((crumb, index) => (
                    <div key={crumb.path} className="flex items-center">
                      {index > 0 && <BreadcrumbSeparator className="text-gray-400" />}
                      <BreadcrumbItem>
                        {index === breadcrumbs.length - 1 ? (
                          <BreadcrumbPage className="text-cyan-300">{crumb.label}</BreadcrumbPage>
                        ) : (
                          <BreadcrumbLink href={crumb.path} className="text-gray-400 hover:text-cyan-400">{crumb.label}</BreadcrumbLink>
                        )}
                      </BreadcrumbItem>
                    </div>
                  ))}
                </BreadcrumbList>
              </Breadcrumb>
              
              {description && (
                <p className="text-sm text-gray-400 mt-1">
                  {description}
                </p>
              )}
            </div>
          )}
          
          {/* Page Content */}
          <main className={`flex-1 ${className}`}>
            {children}
          </main>
          
          {/* Mobile Bottom Padding (for bottom nav) */}
          <div className="lg:hidden h-20"></div>
        </div>
      </div>
    </div>
  );
}