import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { 
  Database, 
  DollarSign, 
  TrendingUp, 
  Package, 
  Zap,
  CheckCircle,
  Clock,
  ArrowRight
} from 'lucide-react';

interface DataPackage {
  id: string;
  categories: string[];
  dataSize: number;
  estimatedValue: number;
  qualityScore: number;
  status: string;
  timestamp: number;
}

interface StreamMetrics {
  totalUMatter: number;
  packagesQueued: number;
  packagesListed: number;
  totalEarnings: number;
  streamRate: number;
}

export function MarketplaceDataStream() {
  const [packages, setPackages] = useState<DataPackage[]>([]);
  const [metrics, setMetrics] = useState<StreamMetrics>({
    totalUMatter: 0,
    packagesQueued: 0,
    packagesListed: 0,
    totalEarnings: 0,
    streamRate: 0
  });
  const [isStreaming, setIsStreaming] = useState(false);

  useEffect(() => {
    // Listen for real-time extension updates
    const handleExtensionUpdate = (event: MessageEvent) => {
      if (event.data.type === 'extension_update') {
        const { umatter, dataSize, marketplaceReady } = event.data;
        
        setIsStreaming(true);
        
        // Update metrics
        setMetrics(prev => ({
          ...prev,
          totalUMatter: prev.totalUMatter + (umatter || 0),
          streamRate: umatter || 0,
          packagesQueued: marketplaceReady ? prev.packagesQueued + 1 : prev.packagesQueued
        }));

        // Add new package if data is ready for marketplace
        if (marketplaceReady && dataSize > 0) {
          const newPackage: DataPackage = {
            id: `pkg-${Date.now()}`,
            categories: ['browsing', 'general'],
            dataSize,
            estimatedValue: Math.round((dataSize / 1000) * 0.05 * 100) / 100,
            qualityScore: 0.8 + Math.random() * 0.2,
            status: 'processing',
            timestamp: Date.now()
          };
          
          setPackages(prev => [newPackage, ...prev.slice(0, 9)]);
          
          // Simulate processing to listing
          setTimeout(() => {
            setPackages(prev => 
              prev.map(pkg => 
                pkg.id === newPackage.id 
                  ? { ...pkg, status: 'listed' }
                  : pkg
              )
            );
            
            setMetrics(prev => ({
              ...prev,
              packagesListed: prev.packagesListed + 1,
              totalEarnings: prev.totalEarnings + newPackage.estimatedValue
            }));
          }, 2000);
        }
        
        // Reset streaming indicator
        setTimeout(() => setIsStreaming(false), 1000);
      }
    };

    window.addEventListener('message', handleExtensionUpdate);
    
    // Fetch existing marketplace data
    fetchMarketplaceData();
    
    return () => window.removeEventListener('message', handleExtensionUpdate);
  }, []);

  const fetchMarketplaceData = async () => {
    try {
      const response = await fetch('/api/marketplace/queue');
      if (response.ok) {
        const data = await response.json();
        setPackages(data.packages || []);
        setMetrics(prev => ({
          ...prev,
          packagesQueued: data.queued || 0,
          packagesListed: data.listed || 0,
          totalEarnings: data.earnings || 0
        }));
      }
    } catch (error) {
      console.error('Failed to fetch marketplace data:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processing': return 'bg-yellow-500';
      case 'listed': return 'bg-green-500';
      case 'sold': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'processing': return <Clock className="w-3 h-3" />;
      case 'listed': return <Package className="w-3 h-3" />;
      case 'sold': return <CheckCircle className="w-3 h-3" />;
      default: return <Database className="w-3 h-3" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Stream Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-cyan-500/30 bg-gradient-to-br from-cyan-900/20 to-blue-900/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-cyan-400">UMatter Streamed</p>
                <p className="text-2xl font-bold text-white">{metrics.totalUMatter.toFixed(3)}</p>
                {isStreaming && (
                  <div className="flex items-center gap-1 mt-1">
                    <Zap className="w-3 h-3 text-yellow-400" />
                    <span className="text-xs text-yellow-400">+{metrics.streamRate.toFixed(3)}</span>
                  </div>
                )}
              </div>
              <Zap className={`w-8 h-8 ${isStreaming ? 'text-yellow-400' : 'text-cyan-400'}`} />
            </div>
          </CardContent>
        </Card>

        <Card className="border-green-500/30 bg-gradient-to-br from-green-900/20 to-emerald-900/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-400">Packages Queued</p>
                <p className="text-2xl font-bold text-white">{metrics.packagesQueued}</p>
              </div>
              <Database className="w-8 h-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-purple-500/30 bg-gradient-to-br from-purple-900/20 to-pink-900/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-400">Listed for Sale</p>
                <p className="text-2xl font-bold text-white">{metrics.packagesListed}</p>
              </div>
              <Package className="w-8 h-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-yellow-500/30 bg-gradient-to-br from-yellow-900/20 to-orange-900/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-yellow-400">Total Earnings</p>
                <p className="text-2xl font-bold text-white">${metrics.totalEarnings.toFixed(2)}</p>
              </div>
              <DollarSign className="w-8 h-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Data Stream Status */}
      <Card className="border-cyan-500/30 bg-gradient-to-br from-gray-900/50 to-gray-800/50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <TrendingUp className="w-5 h-5 text-cyan-400" />
            Real-Time Data Stream
            {isStreaming && (
              <Badge className="bg-green-500 text-white animate-pulse">
                LIVE
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-gray-300">
            Extension data automatically streams to marketplace queue. High-quality packages are auto-listed for sale.
          </div>
          
          {packages.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <Database className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>Install and use the nU Universe extension to start streaming data</p>
              <Button variant="outline" className="mt-4">
                <ArrowRight className="w-4 h-4 mr-2" />
                Download Extension
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              {packages.map((pkg) => (
                <div
                  key={pkg.id}
                  className="flex items-center justify-between p-3 rounded-lg border border-gray-700 bg-gray-800/50"
                >
                  <div className="flex items-center gap-3">
                    <Badge 
                      className={`${getStatusColor(pkg.status)} text-white flex items-center gap-1`}
                    >
                      {getStatusIcon(pkg.status)}
                      {pkg.status}
                    </Badge>
                    <div>
                      <p className="text-white font-medium">
                        {pkg.categories.join(', ')} Data
                      </p>
                      <p className="text-sm text-gray-400">
                        {pkg.dataSize} data points • Quality: {(pkg.qualityScore * 100).toFixed(0)}%
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-white font-medium">${pkg.estimatedValue}</p>
                    <p className="text-xs text-gray-400">
                      {new Date(pkg.timestamp).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}