import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Pause, 
  MessageCircle, 
  Zap, 
  Shield, 
  Users,
  Check,
  CheckCheck,
  Clock,
  DollarSign
} from 'lucide-react';

interface DemoMessage {
  id: string;
  sender: string;
  content: string;
  type: 'text' | 'energy_transfer';
  timestamp: number;
  status: 'sending' | 'sent' | 'delivered' | 'read';
  energyTransfer?: {
    amount: number;
    currency: string;
  };
}

export function MessagingDemo() {
  const [isRunning, setIsRunning] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [messages, setMessages] = useState<DemoMessage[]>([]);
  const [typingUser, setTypingUser] = useState<string | null>(null);

  const demoSteps = [
    {
      action: 'initial',
      description: 'Sarah joins your energy pool',
      delay: 1000
    },
    {
      action: 'message',
      sender: '<PERSON>',
      content: 'Hey! Just joined your energy pool 😊',
      delay: 2000
    },
    {
      action: 'typing',
      sender: 'You',
      delay: 1500
    },
    {
      action: 'message',
      sender: 'You',
      content: 'Welcome! Ready to share some energy?',
      delay: 2000
    },
    {
      action: 'typing',
      sender: 'Sarah',
      delay: 1000
    },
    {
      action: 'message',
      sender: 'Sarah',
      content: 'Absolutely! Let me send you some nUva tokens',
      delay: 2500
    },
    {
      action: 'transfer',
      sender: 'Sarah',
      content: 'Energy transfer: 5.0 nUva',
      amount: 5.0,
      currency: 'nUva',
      delay: 3000
    },
    {
      action: 'typing',
      sender: 'You',
      delay: 1500
    },
    {
      action: 'message',
      sender: 'You',
      content: 'Amazing! The energy transfer worked perfectly',
      delay: 2000
    },
    {
      action: 'message',
      sender: 'Sarah',
      content: 'This SpUnder encryption feels so secure!',
      delay: 2500
    },
    {
      action: 'transfer',
      sender: 'You',
      content: 'Energy transfer: 2.5 trU',
      amount: 2.5,
      currency: 'trU',
      delay: 3000
    }
  ];

  useEffect(() => {
    if (!isRunning) return;

    const timer = setTimeout(() => {
      if (currentStep >= demoSteps.length) {
        setIsRunning(false);
        return;
      }

      const step = demoSteps[currentStep];
      
      if (step.action === 'typing') {
        setTypingUser(step.sender);
        setTimeout(() => setTypingUser(null), step.delay);
      } else if (step.action === 'message') {
        const newMessage: DemoMessage = {
          id: `demo_${Date.now()}_${Math.random()}`,
          sender: step.sender!,
          content: step.content!,
          type: 'text',
          timestamp: Date.now(),
          status: 'sent'
        };
        
        setMessages(prev => [...prev, newMessage]);
        
        // Simulate message status updates
        setTimeout(() => {
          setMessages(prev => prev.map(msg => 
            msg.id === newMessage.id ? { ...msg, status: 'delivered' } : msg
          ));
        }, 500);
        
        setTimeout(() => {
          setMessages(prev => prev.map(msg => 
            msg.id === newMessage.id ? { ...msg, status: 'read' } : msg
          ));
        }, 1000);
        
      } else if (step.action === 'transfer') {
        const newMessage: DemoMessage = {
          id: `demo_${Date.now()}_${Math.random()}`,
          sender: step.sender!,
          content: step.content!,
          type: 'energy_transfer',
          timestamp: Date.now(),
          status: 'sent',
          energyTransfer: {
            amount: step.amount!,
            currency: step.currency!
          }
        };
        
        setMessages(prev => [...prev, newMessage]);
        
        setTimeout(() => {
          setMessages(prev => prev.map(msg => 
            msg.id === newMessage.id ? { ...msg, status: 'delivered' } : msg
          ));
        }, 500);
        
        setTimeout(() => {
          setMessages(prev => prev.map(msg => 
            msg.id === newMessage.id ? { ...msg, status: 'read' } : msg
          ));
        }, 1000);
      }

      setCurrentStep(prev => prev + 1);
    }, demoSteps[currentStep]?.delay || 1000);

    return () => clearTimeout(timer);
  }, [isRunning, currentStep]);

  const startDemo = () => {
    setMessages([]);
    setCurrentStep(0);
    setTypingUser(null);
    setIsRunning(true);
  };

  const stopDemo = () => {
    setIsRunning(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sending': return <Clock className="w-3 h-3 text-gray-400" />;
      case 'sent': return <Check className="w-3 h-3 text-gray-400" />;
      case 'delivered': return <CheckCheck className="w-3 h-3 text-gray-400" />;
      case 'read': return <CheckCheck className="w-3 h-3 text-neon-cyan" />;
      default: return null;
    }
  };

  const formatTimestamp = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const seconds = Math.floor(diff / 1000);
    
    if (seconds < 60) return `${seconds}s ago`;
    return `${Math.floor(seconds / 60)}m ago`;
  };

  return (
    <Card className="glass-panel border-pink-400/30">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-pink-400 flex items-center">
            <MessageCircle className="w-5 h-5 mr-2" />
            Live Messaging Demo
          </CardTitle>
          <div className="flex space-x-2">
            <Button
              onClick={startDemo}
              disabled={isRunning}
              className="bg-gradient-to-r from-pink-400 to-purple-400 hover:from-pink-400/80 hover:to-purple-400/80"
            >
              <Play className="w-4 h-4 mr-2" />
              Start Demo
            </Button>
            <Button
              onClick={stopDemo}
              disabled={!isRunning}
              variant="outline"
              className="border-gray-400/40"
            >
              <Pause className="w-4 h-4 mr-2" />
              Stop
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[400px]">
          {/* User List */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-text-secondary">Energy Network Users</h4>
            
            <div className="space-y-2">
              <div className="p-3 rounded-lg bg-space/30 border border-neon-purple/20">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-neon-cyan to-neon-purple flex items-center justify-center text-white text-sm font-bold">
                    Y
                  </div>
                  <div>
                    <div className="font-medium text-sm">You</div>
                    <div className="text-xs text-text-secondary">Host</div>
                  </div>
                  <Badge className="bg-green-400/20 text-green-400 text-xs ml-auto">
                    Online
                  </Badge>
                </div>
              </div>
              
              <div className="p-3 rounded-lg bg-space/30 border border-neon-purple/20">
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-r from-pink-400 to-purple-400 flex items-center justify-center text-white text-sm font-bold">
                      S
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-space bg-green-400"></div>
                  </div>
                  <div>
                    <div className="font-medium text-sm">Sarah</div>
                    <div className="text-xs text-text-secondary">
                      {typingUser === 'Sarah' ? (
                        <span className="text-neon-cyan animate-pulse">typing...</span>
                      ) : (
                        '0.125 UMatter/hr'
                      )}
                    </div>
                  </div>
                  <Badge className="bg-green-400/20 text-green-400 text-xs ml-auto">
                    Online
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* Chat Interface */}
          <div className="lg:col-span-2">
            <div className="h-full flex flex-col">
              {/* Chat Header */}
              <div className="border-b border-white/10 p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-pink-400 to-purple-400 flex items-center justify-center text-white text-sm font-bold">
                    S
                  </div>
                  <div>
                    <div className="font-medium text-sm">Sarah</div>
                    <div className="text-xs text-text-secondary flex items-center space-x-2">
                      <Shield className="w-3 h-3" />
                      <span>End-to-end encrypted</span>
                      <div className="w-1 h-1 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-green-400">online</span>
                      {typingUser === 'Sarah' && (
                        <span className="text-neon-cyan animate-pulse">typing...</span>
                      )}
                    </div>
                  </div>
                  <Badge className="bg-neon-cyan/20 text-neon-cyan ml-auto">
                    <Zap className="w-3 h-3 mr-1" />
                    0.125 UMatter/hr
                  </Badge>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.length > 0 ? (
                  messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.sender === 'You' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`max-w-xs lg:max-w-md ${
                        message.sender === 'You'
                          ? 'bg-neon-cyan/20 text-neon-cyan'
                          : 'bg-space/60 text-text-primary'
                      } rounded-lg p-3 space-y-2`}>
                        {message.type === 'energy_transfer' ? (
                          <div className="flex items-center space-x-2">
                            <DollarSign className="w-4 h-4 text-yellow-400" />
                            <span className="text-sm">
                              {message.sender === 'You' ? 'Sent' : 'Received'} {message.energyTransfer?.amount} {message.energyTransfer?.currency}
                            </span>
                          </div>
                        ) : (
                          <p className="text-sm">{message.content}</p>
                        )}
                        
                        <div className="flex items-center justify-between text-xs opacity-70">
                          <span>{formatTimestamp(message.timestamp)}</span>
                          {message.sender === 'You' && (
                            <div className="flex items-center space-x-1">
                              <Shield className="w-3 h-3" />
                              {getStatusIcon(message.status)}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center text-text-secondary py-8">
                    <MessageCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>Click "Start Demo" to see secure messaging in action</p>
                    <p className="text-xs mt-2">Watch real-time energy transfers and encrypted messages</p>
                  </div>
                )}
                
                {typingUser === 'You' && (
                  <div className="flex justify-end">
                    <div className="bg-neon-cyan/20 text-neon-cyan rounded-lg p-3">
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-neon-cyan rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-neon-cyan rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-neon-cyan rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                        <span className="text-xs">typing...</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Status Bar */}
              <div className="border-t border-white/10 p-4">
                <div className="text-xs text-text-secondary flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Shield className="w-3 h-3" />
                    <span>Messages encrypted with SpUnder technology</span>
                  </div>
                  {isRunning && (
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-green-400">Demo running...</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}