
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useRealTimeDeviceManager } from "@/lib/hooks/use-real-time-device-manager";
import { useBattery } from "@/lib/hooks/useBattery";
import { 
  Battery, 
  Smartphone, 
  Laptop, 
  Bluetooth, 
  Usb, 
  Wifi,
  Zap,
  Activity,
  TrendingUp,
  RefreshCw
} from "lucide-react";
import { motion } from "framer-motion";

interface RealDeviceSyncProps {
  onUMatterGenerated?: (amount: number, source: string) => void;
}

export function RealDeviceSync({ onUMatterGenerated }: RealDeviceSyncProps) {
  const { devices, energyMetrics, discoverDevices, scanBluetoothDevices } = useRealTimeDeviceManager();
  const batteryInfo = useBattery();
  const [isScanning, setIsScanning] = useState(false);
  const [totalUMatter, setTotalUMatter] = useState(0);

  // Listen for UMatter generation events
  useEffect(() => {
    const handleUMatterGenerated = (event: CustomEvent) => {
      const { amount, source } = event.detail;
      setTotalUMatter(prev => prev + amount);
      onUMatterGenerated?.(amount, source);
    };

    window.addEventListener('umatter-generated', handleUMatterGenerated as EventListener);
    
    return () => {
      window.removeEventListener('umatter-generated', handleUMatterGenerated as EventListener);
    };
  }, [onUMatterGenerated]);

  const handleDiscoverDevices = async () => {
    setIsScanning(true);
    try {
      await discoverDevices();
    } catch (error) {
      console.error('Device discovery failed:', error);
    } finally {
      setIsScanning(false);
    }
  };

  const handleBluetoothScan = async () => {
    setIsScanning(true);
    try {
      await scanBluetoothDevices();
    } catch (error) {
      console.error('Bluetooth scan failed:', error);
    } finally {
      setIsScanning(false);
    }
  };

  const getDeviceIcon = (type: string) => {
    switch (type) {
      case 'phone': return <Smartphone className="h-4 w-4" />;
      case 'laptop': return <Laptop className="h-4 w-4" />;
      case 'bluetooth': return <Bluetooth className="h-4 w-4" />;
      case 'usb': return <Usb className="h-4 w-4" />;
      default: return <Wifi className="h-4 w-4" />;
    }
  };

  const getStatusColor = (isConnected: boolean, realHardware: boolean) => {
    if (!isConnected) return 'bg-red-500/20 text-red-400 border-red-500/30';
    if (realHardware) return 'bg-green-500/20 text-green-400 border-green-500/30';
    return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
  };

  return (
    <div className="space-y-6">
      {/* Real Device Status */}
      <Card className="bg-black/40 border-blue-500/30">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Battery className="h-5 w-5 text-blue-400" />
              Real Device Status
            </div>
            <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
              {batteryInfo.supported ? 'Hardware API' : 'Estimated'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Device */}
          <div className="p-3 rounded-lg bg-blue-900/20 border border-blue-500/20">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <Battery className={`h-4 w-4 ${batteryInfo.charging ? 'text-green-400' : 'text-blue-400'}`} />
                <span className="font-medium">This Device</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm">{batteryInfo.level}%</span>
                {batteryInfo.charging && <Zap className="h-3 w-3 text-green-400" />}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-white/60">CPU: </span>
                <span className="text-blue-300">{energyMetrics.cpuUsage.toFixed(1)}%</span>
              </div>
              <div>
                <span className="text-white/60">Memory: </span>
                <span className="text-blue-300">{energyMetrics.memoryUsage.toFixed(0)}MB</span>
              </div>
              <div>
                <span className="text-white/60">Network: </span>
                <span className="text-blue-300">{energyMetrics.networkActivity.toFixed(1)}KB/s</span>
              </div>
              <div>
                <span className="text-white/60">Power: </span>
                <span className="text-blue-300">{energyMetrics.batteryDrain.toFixed(2)}Wh</span>
              </div>
            </div>
          </div>

          {/* UMatter Generation */}
          <div className="p-3 rounded-lg bg-gradient-to-r from-purple-900/20 to-blue-900/20 border border-purple-500/20">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-purple-400" />
                <span className="font-medium text-purple-300">Real UMatter Generation</span>
              </div>
              <Badge className="bg-purple-500/20 text-purple-300 border-purple-500/30">
                Live
              </Badge>
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-white/60">Generated: </span>
                <span className="text-purple-300 font-medium">{energyMetrics.umatterGenerated.toFixed(6)} UM</span>
              </div>
              <div>
                <span className="text-white/60">Rate: </span>
                <span className="text-purple-300">{(energyMetrics.umatterGenerated / (Date.now() / 1000 / 60) || 0).toFixed(6)} UM/min</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Connected Devices */}
      <Card className="bg-black/40 border-blue-500/30">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Wifi className="h-5 w-5 text-blue-400" />
              Connected Devices ({devices.length})
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleBluetoothScan}
                disabled={isScanning}
                className="border-blue-500/30 bg-blue-900/20 text-blue-300"
              >
                {isScanning ? (
                  <RefreshCw className="h-3 w-3 animate-spin mr-1" />
                ) : (
                  <Bluetooth className="h-3 w-3 mr-1" />
                )}
                Bluetooth
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDiscoverDevices}
                disabled={isScanning}
                className="border-blue-500/30 bg-blue-900/20 text-blue-300"
              >
                {isScanning ? (
                  <RefreshCw className="h-3 w-3 animate-spin mr-1" />
                ) : (
                  <RefreshCw className="h-3 w-3 mr-1" />
                )}
                Discover
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {devices.length === 0 ? (
            <div className="text-center py-4 text-white/60">
              <p>No devices connected</p>
              <p className="text-xs mt-1">Click "Discover" or "Bluetooth" to find devices</p>
            </div>
          ) : (
            devices.map((device) => (
              <motion.div
                key={device.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-3 rounded-lg bg-gradient-to-r from-gray-900/40 to-blue-900/20 border border-gray-500/20"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getDeviceIcon(device.type)}
                    <span className="font-medium">{device.name}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(device.isConnected, device.realHardware)}>
                      {device.realHardware ? 'Real Hardware' : 'Simulated'}
                    </Badge>
                    {device.battery > 0 && (
                      <span className="text-xs text-white/60">{device.battery}%</span>
                    )}
                  </div>
                </div>
                <div className="flex flex-wrap gap-1">
                  {device.capabilities.map((capability) => (
                    <Badge
                      key={capability}
                      variant="outline"
                      className="text-xs bg-blue-900/20 text-blue-300 border-blue-500/30"
                    >
                      {capability.replace('_', ' ')}
                    </Badge>
                  ))}
                </div>
                <div className="text-xs text-white/50 mt-1">
                  Last sync: {new Date(device.lastSync).toLocaleTimeString()}
                </div>
              </motion.div>
            ))
          )}
        </CardContent>
      </Card>

      {/* Real-time Energy Metrics */}
      <Card className="bg-black/40 border-green-500/30">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-green-400" />
            Real-time Energy Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-white/70">Battery Drain</span>
                <span className="text-sm font-medium text-green-300">{energyMetrics.batteryDrain.toFixed(3)} Wh</span>
              </div>
              <div className="h-2 bg-black/40 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-green-500 to-blue-500"
                  initial={{ width: 0 }}
                  animate={{ width: `${Math.min(100, (energyMetrics.batteryDrain / 0.5) * 100)}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-white/70">CPU Usage</span>
                <span className="text-sm font-medium text-blue-300">{energyMetrics.cpuUsage.toFixed(1)}%</span>
              </div>
              <div className="h-2 bg-black/40 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-blue-500 to-purple-500"
                  initial={{ width: 0 }}
                  animate={{ width: `${energyMetrics.cpuUsage}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-white/70">Memory Usage</span>
                <span className="text-sm font-medium text-purple-300">{energyMetrics.memoryUsage.toFixed(0)} MB</span>
              </div>
              <div className="h-2 bg-black/40 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-purple-500 to-pink-500"
                  initial={{ width: 0 }}
                  animate={{ width: `${Math.min(100, (energyMetrics.memoryUsage / 500) * 100)}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-white/70">Network Activity</span>
                <span className="text-sm font-medium text-cyan-300">{energyMetrics.networkActivity.toFixed(1)} KB/s</span>
              </div>
              <div className="h-2 bg-black/40 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-cyan-500 to-teal-500"
                  initial={{ width: 0 }}
                  animate={{ width: `${Math.min(100, (energyMetrics.networkActivity / 100) * 100)}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
