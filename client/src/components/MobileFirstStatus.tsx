import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Smartphone, Battery, Wifi, Zap } from "lucide-react";

export function MobileFirstStatus() {
  return (
    <Card className="bg-gradient-to-br from-emerald-900/20 to-green-900/20 border-emerald-700/30">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-emerald-400">
          <CheckCircle className="h-5 w-5" />
          Mobile-First System Status
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center gap-2">
            <Smartphone className="h-4 w-4 text-emerald-400" />
            <span className="text-sm">Mobile APIs</span>
            <Badge variant="default" className="bg-emerald-600 text-white">Active</Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <Battery className="h-4 w-4 text-emerald-400" />
            <span className="text-sm">Battery Sync</span>
            <Badge variant="default" className="bg-emerald-600 text-white">Real</Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <Wifi className="h-4 w-4 text-emerald-400" />
            <span className="text-sm">Network</span>
            <Badge variant="default" className="bg-emerald-600 text-white">Connected</Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4 text-emerald-400" />
            <span className="text-sm">Energy Batching</span>
            <Badge variant="default" className="bg-emerald-600 text-white">Optimized</Badge>
          </div>
        </div>
        
        <div className="mt-4 p-3 bg-emerald-900/30 rounded-lg">
          <p className="text-sm text-emerald-300">
            System running smoothly with performance optimizations active. 
            Individual API calls blocked, energy batching enabled.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}