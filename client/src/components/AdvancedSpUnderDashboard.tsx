import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Bot, Activity, Shield, Zap, Database, Monitor, Cpu, MemoryStick } from 'lucide-react';

interface RealSpUnderData {
  systemHealth: string;
  performance: string;
  security: string;
  database: string;
  services: string;
  uptime: number;
  memory: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
  };
  cpu: {
    user: number;
    system: number;
  };
  timestamp: number;
}

interface AdvancedSpUnderDashboardProps {
  className?: string;
}

export function AdvancedSpUnderDashboard({ className = '' }: AdvancedSpUnderDashboardProps) {
  const [spunderData, setSpunderData] = useState<RealSpUnderData | null>(null);
  const [systemStats, setSystemStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [authenticData, setAuthenticData] = useState<any>({
    systemActive: false,
    realAdInterceptions: 0,
    realEnergyHarvested: 0,
    realBrowserActions: 0,
    realNetworkRequests: 0
  });
  const [hardwareMetrics, setHardwareMetrics] = useState<any>({
    isRealDevice: false,
    batteryLevel: null,
    memoryUsed: null,
    networkSpeed: null
  });
  const [recentOperations, setRecentOperations] = useState<any[]>([]);
  const [authenticSpUnderSystem, setAuthenticSpUnderSystem] = useState<any>({
    startAuthenticOperations: () => {},
    stopAuthenticOperations: () => {}
  });

  // Fetch real SpUnder Butler status from server
  const fetchSpUnderStatus = async () => {
    try {
      const response = await fetch('/api/spunder/status');
      if (response.ok) {
        const data = await response.json();
        setSpunderData(data);
        setLastUpdate(new Date());
      }
    } catch (error) {
      console.error('Failed to fetch SpUnder status:', error);
    }
  };

  // Fetch real system statistics
  const fetchSystemStats = async () => {
    try {
      const response = await fetch('/api/stats/system');
      if (response.ok) {
        const data = await response.json();
        setSystemStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch system stats:', error);
    }
  };

  // Initialize real data fetching
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      await Promise.all([fetchSpUnderStatus(), fetchSystemStats()]);
      setIsLoading(false);
    };

    fetchData();
    
    // Update every 5 seconds with real server data
    const interval = setInterval(fetchData, 5000);

    return () => clearInterval(interval);
  }, []);

  const formatMemory = (bytes: number) => {
    return Math.round(bytes / 1024 / 1024);
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <Bot className="w-16 h-16 mx-auto text-neon-cyan animate-pulse" />
          <h2 className="text-2xl font-bold text-neon-cyan">Loading SpUnder Butler...</h2>
          <p className="text-text-secondary">Connecting to real system data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Butler Status Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="w-6 h-6 text-neon-cyan" />
            SpUnder Butler Status
            <Badge variant="outline" className="bg-green-500/20 text-green-400 border-green-500">
              ACTIVE
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4 text-green-400" />
                <span className="text-sm">System Health</span>
              </div>
              <p className="text-lg font-semibold text-green-400">
                {spunderData?.systemHealth || 'Loading...'}
              </p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Zap className="w-4 h-4 text-blue-400" />
                <span className="text-sm">Performance</span>
              </div>
              <p className="text-lg font-semibold text-blue-400">
                {spunderData?.performance || 'Loading...'}
              </p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Database className="w-4 h-4 text-purple-400" />
                <span className="text-sm">Database</span>
              </div>
              <p className="text-lg font-semibold text-purple-400">
                {spunderData?.database || 'Loading...'}
              </p>
            </div>
          </div>
          <div className="mt-4 text-xs text-text-secondary">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </div>
        </CardContent>
      </Card>

      {/* System Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MemoryStick className="w-5 h-5 text-orange-400" />
              Memory Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Heap Used</span>
                  <span>{formatMemory(spunderData?.memory.heapUsed || 0)} MB</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className="bg-orange-400 h-2 rounded-full" 
                    style={{ 
                      width: `${((spunderData?.memory.heapUsed || 0) / (spunderData?.memory.heapTotal || 1)) * 100}%` 
                    }}
                  ></div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-text-secondary">RSS:</span>
                  <span className="ml-2">{formatMemory(spunderData?.memory.rss || 0)} MB</span>
                </div>
                <div>
                  <span className="text-text-secondary">External:</span>
                  <span className="ml-2">{formatMemory(spunderData?.memory.external || 0)} MB</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Cpu className="w-5 h-5 text-yellow-400" />
              CPU & Uptime
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>User CPU Time</span>
                  <span>{Math.round((spunderData?.cpu.user || 0) / 1000000)} ms</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>System CPU Time</span>
                  <span>{Math.round((spunderData?.cpu.system || 0) / 1000000)} ms</span>
                </div>
              </div>
              <div className="pt-2 border-t border-gray-700">
                <div className="flex justify-between text-sm">
                  <span>Uptime</span>
                  <span className="text-green-400 font-semibold">
                    {formatUptime(spunderData?.uptime || 0)}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Butler Activities */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="w-5 h-5 text-neon-cyan" />
            Active Monitoring
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span>System Health Monitoring</span>
              </div>
              <Badge variant="outline" className="text-green-400 border-green-400">Running</Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                <span>Database Connection Monitor</span>
              </div>
              <Badge variant="outline" className="text-blue-400 border-blue-400">Active</Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                <span>Performance Analytics</span>
              </div>
              <Badge variant="outline" className="text-purple-400 border-purple-400">Analyzing</Badge>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-800/50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-orange-400 rounded-full animate-pulse"></div>
                <span>Security Monitoring</span>
              </div>
              <Badge variant="outline" className="text-orange-400 border-orange-400">Scanning</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const formatEnergy = (energy: number) => {
    if (energy >= 1) return energy.toFixed(3);
    if (energy >= 0.001) return energy.toFixed(6);
    return energy.toExponential(3);
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🕷️ Authentic SpUnder System
            <Badge variant={authenticData.systemActive ? "default" : "secondary"}>
              {authenticData.systemActive ? "ACTIVE" : "INACTIVE"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">
                {authenticData.realAdInterceptions}
              </div>
              <div className="text-sm text-gray-400">Real Ads Intercepted</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">
                {formatEnergy(authenticData.realEnergyHarvested)}
              </div>
              <div className="text-sm text-gray-400">UMatter Harvested</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">
                {authenticData.realBrowserActions}
              </div>
              <div className="text-sm text-gray-400">Browser Actions</div>
            </div>

            <div className="text-center">
              <div className="text-2xl font-bold text-orange-400">
                {authenticData.realNetworkRequests}
              </div>
              <div className="text-sm text-gray-400">Network Requests</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Hardware Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔋 Authentic Hardware Metrics
            <Badge variant={hardwareMetrics.isRealDevice ? "default" : "destructive"}>
              {hardwareMetrics.isRealDevice ? "REAL DEVICE" : "NO HARDWARE APIs"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-xl font-bold">
                {hardwareMetrics.batteryLevel ? `${(hardwareMetrics.batteryLevel * 100).toFixed(1)}%` : 'N/A'}
              </div>
              <div className="text-sm text-gray-400">Battery Level</div>
            </div>

            <div className="text-center">
              <div className="text-xl font-bold">
                {hardwareMetrics.memoryUsed ? `${(hardwareMetrics.memoryUsed / 1024 / 1024).toFixed(1)}MB` : 'N/A'}
              </div>
              <div className="text-sm text-gray-400">Memory Used</div>
            </div>

            <div className="text-center">
              <div className="text-xl font-bold">
                {hardwareMetrics.networkSpeed ? `${hardwareMetrics.networkSpeed}Mbps` : 'N/A'}
              </div>
              <div className="text-sm text-gray-400">Network Speed</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Operations */}
      <Card>
        <CardHeader>
          <CardTitle>🔍 Recent Authentic Operations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {recentOperations.length === 0 ? (
              <div className="text-center text-gray-400 py-4">
                No authentic operations yet. Browse websites with ads to start harvesting energy.
              </div>
            ) : (
              recentOperations.map((operation, index) => (
                <div key={index} className="flex items-center justify-between p-2 bg-gray-800 rounded">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{operation.type}</Badge>
                    <span className="text-sm text-gray-300 truncate max-w-64">
                      {operation.url}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-mono text-green-400">
                      +{formatEnergy(operation.energy)} UMatter
                    </span>
                    <span className="text-xs text-gray-500">
                      {formatTime(operation.timestamp)}
                    </span>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* System Controls */}
      <Card>
        <CardHeader>
          <CardTitle>⚙️ System Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Button 
              onClick={() => {
                if (authenticData.systemActive) {
                  authenticSpUnderSystem.stopAuthenticOperations();
                } else {
                  authenticSpUnderSystem.startAuthenticOperations();
                }
              }}
              variant={authenticData.systemActive ? "destructive" : "default"}
            >
              {authenticData.systemActive ? "Stop" : "Start"} Authentic SpUnder
            </Button>

            <Button 
              onClick={() => window.location.reload()}
              variant="outline"
            >
              Refresh Dashboard
            </Button>
          </div>

          <div className="mt-4 p-4 bg-gray-800 rounded">
            <h4 className="font-semibold mb-2">🔒 100% Authentic Operation</h4>
            <p className="text-sm text-gray-300">
              This SpUnder system operates with real browser APIs only. It monitors actual DOM changes, 
              network requests, and user interactions to harvest genuine energy from your browsing activity.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}