import { useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Zap, Brain, TrendingUp, Activity } from 'lucide-react';
import { useSimpleNuvaStore } from '@/lib/stores/simpleNuvaStore';
import { useRealTimeData } from '@/hooks/useRealTimeData';

export function UnifiedEnergyDisplay() {
  const { umatter, batteryLevel, isCharging, isConnected, connectWebSocket } = useSimpleNuvaStore();
  const realTimeData = useRealTimeData();
  
  // Use authenticated data with real-time fallback
  const currentBatteryLevel = batteryLevel || realTimeData.batteryLevel || 0;
  const totalEnergyGenerated = (umatter || 0) + (realTimeData.umatterTotal || 0);
  const safeEnergyTotal = isNaN(totalEnergyGenerated) ? 0 : totalEnergyGenerated;
  const chargingStatus = isCharging !== undefined ? isCharging : realTimeData.isCharging;
  
  // Energy calculations based on authenticated real-time data
  const energyEfficiency = Math.round(currentBatteryLevel * 100);
  const neuralPower = currentBatteryLevel > 0.5 ? 22 : 18;
  
  // Ensure WebSocket connection
  useEffect(() => {
    if (!isConnected) {
      connectWebSocket();
    }
  }, [isConnected, connectWebSocket]);

  return (
    <Card className="border-green-500/30 bg-gradient-to-br from-green-900/20 to-emerald-900/20 backdrop-blur-sm">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg text-green-400 flex items-center space-x-2">
          <Zap className="w-5 h-5" />
          <span>Energy Generation</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Primary Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-xs text-gray-400 uppercase">Total Energy</div>
            <div className="text-lg font-bold text-green-400 font-mono">
              {safeEnergyTotal.toFixed(6)}
            </div>
          </div>
          
          <div>
            <div className="text-xs text-gray-400 uppercase">Neural Power</div>
            <div className="text-lg font-bold text-blue-400">
              {neuralPower}W
            </div>
          </div>
        </div>

        {/* Efficiency Progress */}
        <div>
          <div className="flex justify-between text-xs text-gray-400 mb-1">
            <span>Efficiency</span>
            <span>{energyEfficiency}%</span>
          </div>
          <Progress value={energyEfficiency} className="h-2" />
        </div>

        {/* Status Indicators */}
        <div className="flex items-center justify-between">
          <Badge variant={chargingStatus ? "default" : "secondary"} 
                 className={chargingStatus ? 'bg-green-500/20 text-green-400' : 'bg-orange-500/20 text-orange-400'}>
            {chargingStatus ? 'CHARGING' : 'DISCHARGING'}
          </Badge>
          
          <div className="flex items-center space-x-4 text-xs">
            <div className="flex items-center space-x-1">
              <Brain className="w-3 h-3 text-blue-400" />
              <span className="text-blue-400">Neural</span>
            </div>
            <div className="flex items-center space-x-1">
              <Activity className="w-3 h-3 text-green-400" />
              <span className={`${isConnected ? 'text-green-400' : 'text-gray-400'}`}>
                {isConnected ? 'Live' : 'Offline'}
              </span>
            </div>
          </div>
        </div>

        {/* Real-time Generation Rate */}
        <div className="pt-2 border-t border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-xs text-gray-400">Generation Rate</div>
              <div className="text-sm font-mono text-cyan-400">
                {(realTimeData.umatterRate || 0).toFixed(6)}/sec
              </div>
            </div>
            <TrendingUp className="w-4 h-4 text-cyan-400" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}