import { useQuery } from '@tanstack/react-query';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Network, 
  Lock, 
  Brain, 
  AlertTriangle,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

interface SystemStats {
  totalInteractions: number;
  encryptedSessions: number;
  aiInsights: number;
  privacyAlerts: number;
}

interface StatsCardsProps {
  extensionMetrics?: {
    totalUMatter: number;
    totalAdsBlocked: number;
    isConnected: boolean;
    umatterRate: number;
  };
}

export function StatsCards({ extensionMetrics }: StatsCardsProps = {}) {
  // Fetch system statistics
  const { data: stats, isLoading } = useQuery<SystemStats>({
    queryKey: ['/api/stats/system'],
    refetchInterval: 30000, // Refresh every 30 seconds
    initialData: {
      totalInteractions: 0,
      encryptedSessions: 0,
      aiInsights: 0,
      privacyAlerts: 0
    }
  });

  // Mock percentage changes for demonstration
  const getPercentageChange = (type: string) => {
    const changes: Record<string, number> = {
      interactions: 12.3,
      encrypted: 2.1,
      insights: 23.4,
      alerts: -15.7
    };
    return changes[type] || 0;
  };

  const formatNumber = (num: number | undefined) => {
    if (num === undefined || num === null || isNaN(Number(num))) {
      return '0';
    }
    const value = Number(num);
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    }
    if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toLocaleString();
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-400';
    if (change < 0) return 'text-red-400';
    return 'text-text-secondary';
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="w-3 h-3" />;
    if (change < 0) return <TrendingDown className="w-3 h-3" />;
    return null;
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="bg-panel/60 backdrop-blur-md border border-neon-cyan/20 animate-pulse">
            <CardContent className="p-4">
              <div className="h-16 bg-panel/40 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      {/* Extension UMatter */}
      <Card className="bg-panel/60 backdrop-blur-md border border-green-500/20 hover:border-green-500/40 transition-colors">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-green-400">Extension UMatter</h3>
            <Network className="w-5 h-5 text-green-400" />
          </div>
          <div className="text-2xl font-mono font-bold text-green-400">
            {(extensionMetrics?.totalUMatter || 0).toFixed(6)}
          </div>
          <div className="flex items-center space-x-2">
            <Badge 
              variant="outline" 
              className={`text-xs ${extensionMetrics?.isConnected ? 'text-green-400 border-green-400/30' : 'text-gray-400 border-gray-400/30'}`}
            >
              {extensionMetrics?.isConnected ? 'ACTIVE' : 'OFFLINE'}
            </Badge>
            <span className="text-xs text-gray-400">
              {extensionMetrics?.totalAdsBlocked || 0} ads blocked
            </span>
          </div>
        </CardContent>
      </Card>

      {/* UMatter Generation Rate */}
      <Card className="bg-panel/60 backdrop-blur-md border border-neon-purple/20 hover:border-neon-purple/40 transition-colors">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-neon-purple">UMatter Rate</h3>
            <TrendingUp className="w-5 h-5 text-neon-purple" />
          </div>
          <div className="text-2xl font-mono font-bold text-neon-purple">
            {(extensionMetrics?.umatterRate || 0).toFixed(4)}
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="border-purple-400/30 text-purple-400 text-xs">
              /hour
            </Badge>
            <span className="text-xs text-gray-400">
              From ad blocking
            </span>
          </div>
        </CardContent>
      </Card>

      {/* AI Insights */}
      <Card className="bg-panel/60 backdrop-blur-md border border-yellow-400/20 hover:border-yellow-400/40 transition-colors">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-yellow-400">AI Insights</h3>
            <Brain className="w-5 h-5 text-yellow-400" />
          </div>
          <div className="text-2xl font-mono font-bold text-text-primary">
            {formatNumber(stats.aiInsights)}
          </div>
          <div className={`text-sm flex items-center space-x-1 ${getChangeColor(getPercentageChange('insights'))}`}>
            {getChangeIcon(getPercentageChange('insights'))}
            <span>+{Math.abs(getPercentageChange('insights'))} new patterns</span>
          </div>
        </CardContent>
      </Card>

      {/* Privacy Alerts */}
      <Card className="bg-panel/60 backdrop-blur-md border border-red-400/20 hover:border-red-400/40 transition-colors">
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="font-semibold text-red-400">Privacy Alerts</h3>
            <AlertTriangle className="w-5 h-5 text-red-400" />
          </div>
          <div className="text-2xl font-mono font-bold text-text-primary">
            {stats.privacyAlerts}
          </div>
          <div className="flex items-center space-x-2">
            {stats.privacyAlerts === 0 ? (
              <Badge variant="default" className="bg-green-400/20 text-green-400 text-xs">
                All systems secure
              </Badge>
            ) : (
              <Badge variant="destructive" className="text-xs">
                Requires attention
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
