import { Button } from '@/components/ui/button';
import { Menu, X } from 'lucide-react';
import { Link } from 'wouter';
import { useState } from 'react';

export function PageNavigation() {
  const [showMobileNav, setShowMobileNav] = useState(false);

  return (
    <>
      {/* Mobile Navigation Toggle */}
      <div className="md:hidden">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowMobileNav(!showMobileNav)}
          className="text-gray-300 hover:text-white"
        >
          {showMobileNav ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
        </Button>
      </div>

      {/* Desktop Navigation Dropdown */}
      <div className="hidden md:block relative group">
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-300 hover:text-white"
        >
          <Menu className="h-4 w-4" />
          <span className="ml-2">All Pages</span>
        </Button>
        
        <div className="absolute right-0 top-full mt-2 w-56 bg-gray-800 border border-gray-700 rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
          <div className="p-2 space-y-1">
            <div className="px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wide">
              Core
            </div>
            <Link href="/dashboard" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              Dashboard
            </Link>
            
            <div className="border-t border-gray-600 my-2"></div>
            <div className="px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wide">
              Energy & Trading
            </div>
            <Link href="/wallet" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              <span className="flex items-center justify-between">
                Energy Wallet
                <span className="text-xs text-green-400">LIVE</span>
              </span>
            </Link>
            <Link href="/trading" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              <span className="flex items-center justify-between">
                Token Trading
                <span className="text-xs text-blue-400">ACTIVE</span>
              </span>
            </Link>
            <Link href="/energy-banking" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              Energy Banking
            </Link>
            
            <div className="border-t border-gray-600 my-2"></div>
            <div className="px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wide">
              Marketplaces
            </div>
            <Link href="/marketplace" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              <span className="flex items-center justify-between">
                Energy Marketplace
                <span className="text-xs text-purple-400">8 ITEMS</span>
              </span>
            </Link>
            <Link href="/energy-storage" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              Energy Storage
            </Link>
            <Link href="/quantum-marketplace" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              Quantum Market
            </Link>
            
            <div className="border-t border-gray-600 my-2"></div>
            <div className="px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wide">
              Social & AI
            </div>
            <Link href="/social-sync" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              Social Sync
            </Link>
            <Link href="/party-portal" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              Party Portal
            </Link>
            <Link href="/ai-search" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              AI Search
            </Link>
            
            <div className="border-t border-gray-600 my-2"></div>
            <div className="px-3 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wide">
              Tools
            </div>
            <Link href="/inurtia" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              InUrtia
            </Link>
            <Link href="/link-vibe" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              LinkVibe
            </Link>
            <Link href="/energy-storage" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              Energy Storage
            </Link>
            <Link href="/nu-quantum" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              nU Quantum
            </Link>
            <Link href="/data-monetization" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              Data Monetization
            </Link>
            <Link href="/extension-install" className="block px-3 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700 rounded">
              Extensions
            </Link>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Overlay */}
      {showMobileNav && (
        <div className="md:hidden fixed inset-0 z-50 bg-gray-900/95 backdrop-blur-sm">
          <div className="flex flex-col h-full">
            <div className="flex items-center justify-between p-4 border-b border-gray-700">
              <h2 className="text-lg font-semibold text-white">Navigation</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowMobileNav(false)}
                className="text-gray-300 hover:text-white"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
            
            <div className="flex-1 overflow-y-auto p-4 space-y-6">
              <div>
                <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wide mb-3">
                  Core
                </h3>
                <div className="space-y-2">
                  <Link 
                    href="/dashboard" 
                    className="block px-4 py-3 text-white bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                    onClick={() => setShowMobileNav(false)}
                  >
                    Dashboard
                  </Link>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wide mb-3">
                  Marketplaces
                </h3>
                <div className="space-y-2">
                  <Link 
                    href="/energy-marketplace" 
                    className="block px-4 py-3 text-white bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                    onClick={() => setShowMobileNav(false)}
                  >
                    Energy Market
                  </Link>
                  <Link 
                    href="/marketplace" 
                    className="block px-4 py-3 text-white bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                    onClick={() => setShowMobileNav(false)}
                  >
                    Data Market
                  </Link>
                  <Link 
                    href="/quantum-marketplace" 
                    className="block px-4 py-3 text-white bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                    onClick={() => setShowMobileNav(false)}
                  >
                    Quantum Market
                  </Link>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wide mb-3">
                  Social & AI
                </h3>
                <div className="space-y-2">
                  <Link 
                    href="/social-sync" 
                    className="block px-4 py-3 text-white bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                    onClick={() => setShowMobileNav(false)}
                  >
                    Social Sync
                  </Link>
                  <Link 
                    href="/party-portal" 
                    className="block px-4 py-3 text-white bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                    onClick={() => setShowMobileNav(false)}
                  >
                    Party Portal
                  </Link>
                  <Link 
                    href="/ai-search" 
                    className="block px-4 py-3 text-white bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                    onClick={() => setShowMobileNav(false)}
                  >
                    AI Search
                  </Link>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-wide mb-3">
                  Tools
                </h3>
                <div className="space-y-2">
                  <Link 
                    href="/inurtia" 
                    className="block px-4 py-3 text-white bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                    onClick={() => setShowMobileNav(false)}
                  >
                    InUrtia
                  </Link>
                  <Link 
                    href="/link-vibe" 
                    className="block px-4 py-3 text-white bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                    onClick={() => setShowMobileNav(false)}
                  >
                    LinkVibe
                  </Link>
                  <Link 
                    href="/nu-quantum" 
                    className="block px-4 py-3 text-white bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                    onClick={() => setShowMobileNav(false)}
                  >
                    nU Quantum
                  </Link>
                  <Link 
                    href="/data-monetization" 
                    className="block px-4 py-3 text-white bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                    onClick={() => setShowMobileNav(false)}
                  >
                    Data Monetization
                  </Link>
                  <Link 
                    href="/extension-install" 
                    className="block px-4 py-3 text-white bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
                    onClick={() => setShowMobileNav(false)}
                  >
                    Extensions
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}