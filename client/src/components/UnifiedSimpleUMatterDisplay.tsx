import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Zap, Activity, Globe } from 'lucide-react';
import { useAuthenticEnergyData } from '@/hooks/useAuthenticEnergyData';
import { useRealTimeData } from '@/hooks/useRealTimeData';
import { useRealBalance } from '@/hooks/useRealBalance';

export function UnifiedSimpleUMatterDisplay() {
  const realTimeData = useRealTimeData();
  const { balance } = useRealBalance();

  const [updateCounter, setUpdateCounter] = useState(0);

  // Force re-render whenever balance changes
  useEffect(() => {
    setUpdateCounter(prev => prev + 1);
    console.log(`[UnifiedSimpleUMatterDisplay] FORCE UPDATE - Balance: ${balance} UMatter`);
  }, [balance, realTimeData]);

  // Use LIVE balance directly - no caching, force refresh
  const currentBalance = Number(balance) || 0;
  const isRealDevice = realTimeData?.isRealHardware || false;
  const generationRate = realTimeData?.umatterTotal || 0;

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Live Update Status */}
      <div className="col-span-full space-y-2 mb-4">
        <div className="flex items-center justify-center gap-2">
          <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
          <span className="text-sm text-green-400">LIVE UPDATE #{updateCounter}</span>
          <span className="text-xs text-gray-400">
            Balance: {currentBalance.toFixed(8)} UMatter
          </span>
        </div>

        {/* Debug Info */}
        <div className="text-center text-xs text-yellow-300 bg-gray-800/50 p-2 rounded">
          🔍 DEBUG: Raw Balance: {balance} | Current: {currentBalance} | Updates: {updateCounter}
          <br />
          isRealDevice: {isRealDevice ? 'YES' : 'NO'} | Generation: {generationRate}
          <br />
          Last Update: {new Date().toLocaleTimeString()}
        </div>
      </div>

      {/* Total UMatter - LIVE */}
      <Card className="border-purple-500/30 bg-gradient-to-br from-purple-900/20 to-blue-900/20 backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm font-medium text-purple-400 mb-2">
                Total UMatter (LIVE #{updateCounter})
              </div>
              <div className="text-2xl font-bold text-white font-mono" key={`live-${currentBalance}-${updateCounter}`}>
                {currentBalance.toFixed(8)}
              </div>
              <div className="text-xs text-purple-300 mt-1">
                {isRealDevice ? 'Authentic Hardware Connected' : 'Backend Only'}
              </div>
            </div>
            <div className="relative">
              <Zap className={`w-8 h-8 ${isRealDevice ? 'text-purple-400' : 'text-gray-600'}`} />
              {isRealDevice && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
              )}
            </div>
          </div>
          <Progress value={Math.min(100, (currentBalance % 10) * 10)} className="h-2 mt-4" />
        </CardContent>
      </Card>

      {/* Real-Time Generation */}
      <Card className="border-cyan-500/30 bg-gradient-to-br from-cyan-900/20 to-blue-900/20 backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm font-medium text-cyan-400 mb-2">Generation Rate</div>
              <div className="text-xl font-bold text-white">
                {generationRate.toFixed(6)}/cycle
              </div>
              <div className="text-xs text-cyan-300 mt-1">
                {isRealDevice ? 'Hardware + Backend' : 'Backend APIs Only'}
              </div>
            </div>
            <Activity className="w-8 h-8 text-cyan-400" />
          </div>
        </CardContent>
      </Card>

      {/* Network Status */}
      <Card className="border-green-500/30 bg-gradient-to-br from-green-900/20 to-blue-900/20 backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm font-medium text-green-400 mb-2">Network Speed</div>
              <div className="text-lg font-bold text-white">
                {realTimeData?.networkSpeed || 10} Mbps
              </div>
              <div className="text-xs text-green-300 mt-1">
                Authentic Connection
              </div>
            </div>
            <Globe className="w-8 h-8 text-green-400" />
          </div>
          <Progress value={Math.min(100, (parseFloat(realTimeData?.networkSpeed) || 10) * 5)} className="h-2 mt-3" />
        </CardContent>
      </Card>
    </div>
  );
}