import { Link, useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Store, 
  Cpu, 
  TrendingUp, 
  Activity,
  Zap,
  Network
} from 'lucide-react';

export default function MarketplaceNavigation() {
  const [location] = useLocation();

  const navItems = [
    {
      path: '/nuquantum',
      label: 'nUQuantum Lab',
      icon: Cpu,
      description: 'Direct quantum computation',
      badge: 'Lab'
    },
    {
      path: '/quantum-marketplace',
      label: 'Quantum Market',
      icon: Store,
      description: 'Trade quantum tasks & results',
      badge: 'New'
    },
    {
      path: '/energy-marketplace',
      label: 'Energy Market',
      icon: Zap,
      description: 'UMatter & Ubit trading',
      badge: null
    }
  ];

  return (
    <div className="bg-gray-800 border-b border-gray-700 p-4">
      <div className="container mx-auto">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-bold text-white mb-1">nU Web Quantum Ecosystem</h2>
            <p className="text-gray-400 text-sm">Battery-powered distributed quantum computing across 5B devices</p>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <Network className="w-4 h-4" />
            <span>4.5T Ubits/s capacity</span>
          </div>
        </div>
        
        <div className="flex flex-wrap gap-3">
          {navItems.map((item) => {
            const IconComponent = item.icon;
            const isActive = location === item.path;
            
            return (
              <Link key={item.path} href={item.path}>
                <Button
                  variant={isActive ? "default" : "outline"}
                  className={`h-auto p-4 flex-col items-start text-left min-w-[200px] ${
                    isActive 
                      ? 'bg-gradient-to-r from-cyan-600 to-purple-600 border-cyan-500' 
                      : 'border-gray-600 hover:border-gray-500'
                  }`}
                >
                  <div className="flex items-center gap-2 mb-1 w-full">
                    <IconComponent className="w-5 h-5" />
                    <span className="font-medium">{item.label}</span>
                    {item.badge && (
                      <Badge 
                        variant="secondary" 
                        className="text-xs px-1.5 py-0.5 bg-purple-500/20 text-purple-400 border-purple-400"
                      >
                        {item.badge}
                      </Badge>
                    )}
                  </div>
                  <p className="text-xs text-gray-400 font-normal">{item.description}</p>
                </Button>
              </Link>
            );
          })}
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-4 mt-4 pt-4 border-t border-gray-700">
          <div className="text-center">
            <div className="text-lg font-bold text-cyan-400">1M+</div>
            <div className="text-xs text-gray-400">Active Devices</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-green-400">$16M+</div>
            <div className="text-xs text-gray-400">Annual Revenue</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-purple-400">50B+</div>
            <div className="text-xs text-gray-400">Ubits Available</div>
          </div>
        </div>
      </div>
    </div>
  );
}