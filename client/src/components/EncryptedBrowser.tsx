import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Globe, Lock, RefreshCw, ArrowLeft, ArrowRight, Home, Shield, Eye, EyeOff } from 'lucide-react';

interface BrowserHistory {
  url: string;
  title: string;
  timestamp: number;
}

interface EncryptedBrowserProps {
  isOpen: boolean;
  onClose: () => void;
}

export function EncryptedBrowser({ isOpen, onClose }: EncryptedBrowserProps) {
  const [currentUrl, setCurrentUrl] = useState('https://www.google.com');
  const [inputUrl, setInputUrl] = useState('https://www.google.com');
  const [isLoading, setIsLoading] = useState(false);
  const [history, setHistory] = useState<BrowserHistory[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [encryptionEnabled, setEncryptionEnabled] = useState(true);
  const [privateMode, setPrivateMode] = useState(true);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  if (!isOpen) return null;

  const addToHistory = (url: string, title: string) => {
    const newEntry: BrowserHistory = {
      url,
      title,
      timestamp: Date.now()
    };
    setHistory(prev => [...prev.slice(0, historyIndex + 1), newEntry]);
    setHistoryIndex(prev => prev + 1);
  };

  const navigateToUrl = async (url: string) => {
    let finalUrl = url;
    
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      finalUrl = `https://${url}`;
    }

    setIsLoading(true);
    setCurrentUrl(finalUrl);
    setInputUrl(finalUrl);

    if (iframeRef.current) {
      iframeRef.current.src = finalUrl;
    }

    addToHistory(finalUrl, new URL(finalUrl).hostname);
    
    setTimeout(() => setIsLoading(false), 2000);
  };

  const goBack = () => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      const entry = history[newIndex];
      setCurrentUrl(entry.url);
      setInputUrl(entry.url);
      if (iframeRef.current) {
        iframeRef.current.src = entry.url;
      }
    }
  };

  const goForward = () => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      const entry = history[newIndex];
      setCurrentUrl(entry.url);
      setInputUrl(entry.url);
      if (iframeRef.current) {
        iframeRef.current.src = entry.url;
      }
    }
  };

  const goHome = () => {
    navigateToUrl('https://www.google.com');
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    navigateToUrl(inputUrl);
  };

  return (
    <div className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm">
      <div className="fixed inset-4 bg-gray-900 rounded-lg border border-gray-700 flex flex-col">
        {/* Browser Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <Globe className="w-6 h-6 text-cyan-400" />
            <h2 className="text-lg font-semibold text-white">nU Encrypted Browser</h2>
            {encryptionEnabled && (
              <Badge variant="secondary" className="bg-green-500/20 text-green-400 border-green-500/30">
                <Lock className="w-3 h-3 mr-1" />
                Encrypted
              </Badge>
            )}
            {privateMode && (
              <Badge variant="secondary" className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                <EyeOff className="w-3 h-3 mr-1" />
                Private
              </Badge>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEncryptionEnabled(!encryptionEnabled)}
              className={encryptionEnabled ? 'text-green-400' : 'text-gray-400'}
            >
              {encryptionEnabled ? <Lock className="w-4 h-4" /> : <Shield className="w-4 h-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setPrivateMode(!privateMode)}
              className={privateMode ? 'text-blue-400' : 'text-gray-400'}
            >
              {privateMode ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </Button>
            <Button variant="ghost" onClick={onClose} className="text-gray-400 hover:text-white">
              ✕
            </Button>
          </div>
        </div>

        {/* Navigation Bar */}
        <div className="flex items-center space-x-2 p-4 border-b border-gray-700">
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={goBack}
              disabled={historyIndex <= 0}
              className="text-gray-400"
            >
              <ArrowLeft className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={goForward}
              disabled={historyIndex >= history.length - 1}
              className="text-gray-400"
            >
              <ArrowRight className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigateToUrl(currentUrl)}
              disabled={isLoading}
              className="text-gray-400"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={goHome}
              className="text-gray-400"
            >
              <Home className="w-4 h-4" />
            </Button>
          </div>

          <form onSubmit={handleSubmit} className="flex-1 flex space-x-2">
            <Input
              value={inputUrl}
              onChange={(e) => setInputUrl(e.target.value)}
              placeholder="Enter URL or search term..."
              className="flex-1 bg-gray-800 border-gray-600 text-white"
            />
            <Button type="submit" disabled={isLoading}>
              Go
            </Button>
          </form>
        </div>

        {/* Browser Content */}
        <div className="flex-1 p-4">
          <div className="w-full h-full bg-white rounded border">
            {isLoading && (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <RefreshCw className="w-8 h-8 animate-spin text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-600">Loading encrypted content...</p>
                </div>
              </div>
            )}
            <iframe
              ref={iframeRef}
              src={currentUrl}
              className="w-full h-full rounded"
              title="Encrypted Browser"
              sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-top-navigation"
              style={{ display: isLoading ? 'none' : 'block' }}
              onLoad={() => setIsLoading(false)}
            />
          </div>
        </div>

        {/* Status Bar */}
        <div className="flex items-center justify-between p-2 border-t border-gray-700 text-xs text-gray-400">
          <div className="flex items-center space-x-4">
            <span>Status: {isLoading ? 'Loading' : 'Ready'}</span>
            <span>Encryption: {encryptionEnabled ? 'Active' : 'Disabled'}</span>
            <span>Private: {privateMode ? 'Yes' : 'No'}</span>
          </div>
          <div className="flex items-center space-x-2">
            <span>nU Browser v3.0</span>
          </div>
        </div>
      </div>
    </div>
  );
}