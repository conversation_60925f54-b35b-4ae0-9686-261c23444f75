import React, { ReactNode } from 'react';
import { MobileResponsiveNavigation } from '@/components/MobileResponsiveNavigation';

interface MobileFirstLayoutProps {
  children: ReactNode;
  title?: string;
  showBottomNav?: boolean;
}

export function MobileFirstLayout({ children, title, showBottomNav = true }: MobileFirstLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-slate-900 to-black text-white">
      {/* Mobile Navigation */}
      <MobileResponsiveNavigation />
      
      {/* Main Content Area */}
      <main className="pt-16 pb-20 lg:pt-0 lg:pb-0">
        {title && (
          <div className="lg:hidden px-4 py-4 border-b border-gray-800 bg-gray-900/50 backdrop-blur-sm">
            <h1 className="text-xl font-bold text-white">{title}</h1>
          </div>
        )}
        
        {/* Content with mobile-optimized padding */}
        <div className="px-4 py-6 lg:px-8 lg:py-8">
          {children}
        </div>
      </main>
      
      {/* Desktop Sidebar Spacer */}
      <div className="hidden lg:block lg:w-64 lg:fixed lg:left-0 lg:top-0 lg:h-full">
        {/* Desktop navigation would go here */}
      </div>
    </div>
  );
}