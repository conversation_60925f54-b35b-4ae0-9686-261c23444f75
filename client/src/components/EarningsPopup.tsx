import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { DollarSign, Shield, Eye, CheckCircle, X, Sparkles } from "lucide-react";

interface EarningsOpportunity {
  id: string;
  buyerCompany: string;
  dataType: string;
  offeredAmount: number;
  description: string;
  privacyLevel: number;
  estimatedValue: string;
  category: string;
  icon: string;
}

interface EarningsPopupProps {
  isOpen: boolean;
  onClose: () => void;
  opportunity: EarningsOpportunity | null;
}

export function EarningsPopup({ isOpen, onClose, opportunity }: EarningsPopupProps) {
  const { toast } = useToast();
  const [isAccepting, setIsAccepting] = useState(false);

  // Accept earnings opportunity mutation
  const acceptOpportunityMutation = useMutation({
    mutationFn: async () => {
      if (!opportunity) return;
      
      // Create a purchase request that's pre-approved
      return await apiRequest("POST", "/api/marketplace/purchase-requests", {
        buyerEmail: `partnerships@${opportunity.buyerCompany.toLowerCase()}.com`,
        buyerCompany: opportunity.buyerCompany,
        dataPackageId: `auto_${opportunity.id}`,
        requestType: "one_time",
        offeredPrice: opportunity.offeredAmount,
        message: `Instant earnings opportunity: ${opportunity.description}`,
        status: "approved"
      });
    },
    onSuccess: () => {
      setIsAccepting(true);
      
      // Simulate TRU transaction processing
      setTimeout(() => {
        toast({
          title: "Earnings Secured!",
          description: `You've earned $${(opportunity!.offeredAmount / 100).toFixed(2)} from ${opportunity!.buyerCompany}`,
        });
        
        // Update queries to reflect new earnings
        queryClient.invalidateQueries({ queryKey: ["/api/marketplace/earnings"] });
        queryClient.invalidateQueries({ queryKey: ["/api/marketplace/purchase-requests"] });
        
        setIsAccepting(false);
        onClose();
      }, 2000);
    },
    onError: (error: any) => {
      toast({
        title: "Transaction Failed",
        description: error.message,
        variant: "destructive",
      });
      setIsAccepting(false);
    },
  });

  if (!opportunity) return null;

  const formatCurrency = (cents: number) => `$${(cents / 100).toFixed(2)}`;

  const getPrivacyLevelColor = (level: number) => {
    if (level <= 2) return "text-green-400";
    if (level <= 3) return "text-yellow-400";
    return "text-orange-400";
  };

  const getPrivacyLevelText = (level: number) => {
    const levels = {
      1: "Minimal data shared",
      2: "Basic patterns only", 
      3: "Standard insights",
      4: "Detailed analytics",
      5: "Comprehensive data"
    };
    return levels[level as keyof typeof levels] || "Unknown";
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md bg-panel glass-panel border-neon-cyan/30">
        <DialogHeader className="text-center">
          <div className="flex items-center justify-center mb-2">
            <div className="w-12 h-12 bg-gradient-to-r from-neon-cyan to-neon-purple rounded-full flex items-center justify-center animate-pulse-neon">
              <DollarSign className="w-6 h-6 text-space" />
            </div>
          </div>
          <DialogTitle className="text-xl font-orbitron text-neon-cyan">
            Instant Earnings Opportunity!
          </DialogTitle>
          <DialogDescription className="text-text-secondary">
            {opportunity.buyerCompany} wants to purchase your {opportunity.dataType} insights
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Earnings Amount */}
          <Card className="bg-gradient-to-r from-neon-cyan/10 to-neon-purple/10 border-neon-cyan/30">
            <CardContent className="p-4 text-center">
              <div className="text-3xl font-bold text-neon-cyan mb-1">
                {formatCurrency(opportunity.offeredAmount)}
              </div>
              <div className="text-sm text-text-secondary">
                Instant payment via TRU transaction
              </div>
            </CardContent>
          </Card>

          {/* Opportunity Details */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-text-secondary">Company:</span>
              <Badge variant="outline" className="text-neon-cyan border-neon-cyan">
                {opportunity.buyerCompany}
              </Badge>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-text-secondary">Data Type:</span>
              <span className="text-sm font-medium">{opportunity.category}</span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-text-secondary">Privacy Level:</span>
              <div className="flex items-center space-x-2">
                <Shield className={`w-4 h-4 ${getPrivacyLevelColor(opportunity.privacyLevel)}`} />
                <span className={`text-sm ${getPrivacyLevelColor(opportunity.privacyLevel)}`}>
                  {getPrivacyLevelText(opportunity.privacyLevel)}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-text-secondary">Estimated Value:</span>
              <span className="text-sm font-medium text-green-400">{opportunity.estimatedValue}</span>
            </div>
          </div>

          {/* Description */}
          <div className="bg-space/50 p-3 rounded border border-neon-cyan/20">
            <p className="text-sm text-text-secondary">{opportunity.description}</p>
          </div>

          {/* Privacy Protection Notice */}
          <div className="flex items-start space-x-2 p-3 bg-green-500/10 border border-green-500/30 rounded">
            <Shield className="w-4 h-4 text-green-400 mt-0.5" />
            <div className="text-xs text-green-400">
              <strong>Protected by SpUnder:</strong> Your identity remains encrypted. Only anonymized insights are shared via DID authentication.
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-2">
            <Button
              onClick={() => acceptOpportunityMutation.mutate()}
              disabled={isAccepting}
              className="flex-1 bg-gradient-to-r from-neon-cyan to-neon-purple hover:from-neon-cyan/80 hover:to-neon-purple/80"
            >
              {isAccepting ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Processing TRU...</span>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4" />
                  <span>Accept & Earn</span>
                </div>
              )}
            </Button>
            
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isAccepting}
              className="flex-1 border-red-500/50 text-red-400 hover:bg-red-500/10"
            >
              <X className="w-4 h-4 mr-2" />
              Decline
            </Button>
          </div>

          {/* Processing Animation */}
          {isAccepting && (
            <div className="text-center py-2">
              <div className="flex items-center justify-center space-x-2 text-neon-cyan">
                <Sparkles className="w-4 h-4 animate-spin" />
                <span className="text-sm font-mono">Executing secure TRU transaction...</span>
                <Sparkles className="w-4 h-4 animate-spin" />
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}