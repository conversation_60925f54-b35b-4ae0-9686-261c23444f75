import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Battery, HardDrive, Wifi, Cpu, Zap, CheckCircle } from 'lucide-react';
import { useAuthenticEnergyData } from '@/hooks/useAuthenticEnergyData';

export function AuthenticEnergyDisplay() {
  const data = useAuthenticEnergyData();

  // Convert to display units
  const batteryPercent = (data.batteryLevel * 100).toFixed(1);
  const memoryMB = (data.memoryUsed / 1024 / 1024).toFixed(1);
  const memoryTotalMB = (data.memoryTotal / 1024 / 1024).toFixed(1);
  const memoryPercent = data.memoryTotal > 0 ? (data.memoryUsed / data.memoryTotal * 100).toFixed(1) : '0';

  return (
    <div className="space-y-6">
      {/* Authenticity Badge */}
      <div className="flex items-center justify-center gap-2">
        <CheckCircle className="w-5 h-5 text-green-400" />
        <Badge className="bg-green-500 text-white">
          {data.isRealDevice ? 'AUTHENTIC HARDWARE DATA' : 'NO REAL DEVICE DETECTED'}
        </Badge>
        <span className="text-xs text-gray-400">
          Last Update: {new Date(data.lastUpdate).toLocaleTimeString()}
        </span>
      </div>

      {/* Real Hardware Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="border-blue-500/30 bg-gradient-to-br from-blue-900/20 to-cyan-900/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-400">Battery</p>
                <p className="text-xl font-bold text-white">{batteryPercent}%</p>
                <div className="flex items-center gap-1 mt-1">
                  {data.isCharging && <Zap className="w-3 h-3 text-yellow-400" />}
                  <span className="text-xs text-gray-400">
                    {data.isCharging ? 'Charging' : 'Draining'}
                  </span>
                </div>
              </div>
              <Battery className={`w-8 h-8 ${data.isCharging ? 'text-green-400' : 'text-blue-400'}`} />
            </div>
            <Progress value={parseFloat(batteryPercent)} className="mt-2 h-1" />
          </CardContent>
        </Card>

        <Card className="border-purple-500/30 bg-gradient-to-br from-purple-900/20 to-pink-900/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-400">Memory</p>
                <p className="text-xl font-bold text-white">{memoryMB} MB</p>
                <div className="text-xs text-gray-400 mt-1">
                  {memoryPercent}% of {memoryTotalMB}MB
                </div>
              </div>
              <HardDrive className="w-8 h-8 text-purple-400" />
            </div>
            <Progress value={parseFloat(memoryPercent)} className="mt-2 h-1" />
          </CardContent>
        </Card>

        <Card className="border-green-500/30 bg-gradient-to-br from-green-900/20 to-emerald-900/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-400">Network</p>
                <p className="text-xl font-bold text-white">{data.networkSpeed} Mbps</p>
                <div className="text-xs text-gray-400 mt-1">
                  Connection speed
                </div>
              </div>
              <Wifi className="w-8 h-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-orange-500/30 bg-gradient-to-br from-orange-900/20 to-red-900/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-orange-400">CPU Cores</p>
                <p className="text-xl font-bold text-white">{data.cpuCores}</p>
                <div className="text-xs text-gray-400 mt-1">
                  Hardware threads
                </div>
              </div>
              <Cpu className="w-8 h-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Authentic UMatter Generation */}
      <Card className="border-cyan-500/30 bg-gradient-to-br from-cyan-900/20 to-blue-900/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Zap className="w-5 h-5 text-cyan-400" />
            Authentic UMatter Generation
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-cyan-400 font-mono">
                {data.umatterGenerated.toFixed(6)}
              </div>
              <div className="text-sm text-gray-400">Current Rate</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-white font-mono">
                {data.umatterTotal.toFixed(6)}
              </div>
              <div className="text-sm text-gray-400">Total Accumulated</div>
            </div>
          </div>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-400">From Battery ({batteryPercent}%):</span>
              <span className="text-blue-400">{(data.umatterGenerated * 0.4).toFixed(6)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">From Memory ({memoryMB}MB):</span>
              <span className="text-purple-400">{(data.umatterGenerated * 0.3).toFixed(6)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">From Network ({data.networkSpeed}Mbps):</span>
              <span className="text-green-400">{(data.umatterGenerated * 0.3).toFixed(6)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}