import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Activity, 
  TrendingUp, 
  Database, 
  Eye, 
  Network, 
  Zap,
  DollarSign,
  Clock,
  Globe,
  Monitor
} from 'lucide-react';

interface LiveDataPoint {
  timestamp: number;
  dataType: string;
  value: any;
  marketValue: number;
  category: string;
}

interface DataStream {
  browsing: LiveDataPoint[];
  biometric: LiveDataPoint[];
  interaction: LiveDataPoint[];
  network: LiveDataPoint[];
  total_points: number;
  estimated_value: number;
}

export function LiveDataStream() {
  const [dataStream, setDataStream] = useState<DataStream>({
    browsing: [],
    biometric: [],
    interaction: [],
    network: [],
    total_points: 0,
    estimated_value: 0
  });

  // Fetch extension data for real-time metrics
  const { data: extensionData } = useQuery({
    queryKey: ['/api/extension/status'],
    refetchInterval: 2000,
    queryFn: async () => {
      const response = await fetch('/api/extension/status');
      if (!response.ok) throw new Error('Extension API failed');
      return response.json();
    }
  });

  // Fetch energy metrics for biometric data
  const { data: energyData } = useQuery({
    queryKey: ['/api/energy/metrics'],
    refetchInterval: 3000,
    queryFn: async () => {
      const response = await fetch('/api/energy/metrics');
      if (!response.ok) throw new Error('Energy API failed');
      return response.json();
    }
  });

  // Fetch recent web ads for browsing data
  const { data: adsData } = useQuery({
    queryKey: ['/api/web-ads/recent'],
    refetchInterval: 3000,
    queryFn: async () => {
      const response = await fetch('/api/web-ads/recent');
      if (!response.ok) throw new Error('Ads API failed');
      return response.json();
    }
  });

  // Connect to real backend data streams - NO SIMULATIONS
  useEffect(() => {
    const updateRealDataStream = () => {
      console.log('[LiveDataStream] Updating from real backend APIs:', {
        extensionData,
        energyData,
        adsData
      });

      setDataStream(prev => {
        const timestamp = Date.now();
        const updated = { ...prev };
        
        // Only update if we have REAL API data
        if (extensionData && Object.keys(extensionData).length > 0) {
          const browsingPoint: LiveDataPoint = {
            timestamp,
            dataType: 'real_extension_data',
            value: extensionData,
            marketValue: extensionData.adsIntercepted * 0.05 || 0,
            category: 'browsing'
          };
          updated.browsing = [browsingPoint];
        }

        if (energyData && Object.keys(energyData).length > 0) {
          const biometricPoint: LiveDataPoint = {
            timestamp,
            dataType: 'real_energy_metrics',
            value: energyData,
            marketValue: energyData.neuralPowerWatts * 0.02 || 0,
            category: 'biometric'
          };
          updated.biometric = [biometricPoint];
        }

        if (adsData && Object.keys(adsData).length > 0) {
          const interactionPoint: LiveDataPoint = {
            timestamp,
            dataType: 'real_ad_data',
            value: adsData,
            marketValue: adsData.totalCount * 0.03 || 0,
            category: 'interaction'
          };
          updated.interaction = [interactionPoint];
        }

        // Calculate totals from REAL data only
        const allPoints = [
          ...updated.browsing,
          ...updated.biometric,
          ...updated.interaction,
          ...updated.network
        ];
        
        updated.total_points = allPoints.length;
        updated.estimated_value = allPoints.reduce((sum, point) => sum + point.marketValue, 0);

        return updated;
      });
    };

    // Update immediately and then every 3 seconds with real data
    updateRealDataStream();
    const interval = setInterval(updateRealDataStream, 3000);
    return () => clearInterval(interval);
  }, [extensionData, energyData, adsData]);

  const getLatestValue = (category: keyof DataStream) => {
    const categoryData = dataStream[category] as LiveDataPoint[];
    return Array.isArray(categoryData) && categoryData.length > 0 
      ? categoryData[categoryData.length - 1] 
      : null;
  };

  const getCategoryValue = (category: keyof DataStream) => {
    const categoryData = dataStream[category] as LiveDataPoint[];
    return Array.isArray(categoryData) 
      ? categoryData.reduce((sum, point) => sum + point.marketValue, 0)
      : 0;
  };

  return (
    <div className="space-y-6">
      {/* Live Data Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Live Data Points</p>
                <p className="text-2xl font-bold text-green-400">{dataStream.total_points}</p>
              </div>
              <Activity className="w-8 h-8 text-green-400" />
            </div>
            <div className="mt-2 flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-green-400">Collecting</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Estimated Value</p>
                <p className="text-2xl font-bold text-blue-400">${dataStream.estimated_value.toFixed(2)}</p>
              </div>
              <DollarSign className="w-8 h-8 text-blue-400" />
            </div>
            <div className="mt-2 flex items-center gap-2">
              <TrendingUp className="w-3 h-3 text-blue-400" />
              <span className="text-xs text-blue-400">Growing</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Data Categories</p>
                <p className="text-2xl font-bold text-purple-400">4</p>
              </div>
              <Database className="w-8 h-8 text-purple-400" />
            </div>
            <div className="mt-2 flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
              <span className="text-xs text-purple-400">Active</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Market Readiness</p>
                <p className="text-2xl font-bold text-orange-400">
                  {dataStream.total_points > 20 ? '85%' : Math.floor((dataStream.total_points / 20) * 85)}%
                </p>
              </div>
              <Network className="w-8 h-8 text-orange-400" />
            </div>
            <Progress 
              value={dataStream.total_points > 20 ? 85 : (dataStream.total_points / 20) * 85} 
              className="mt-2 h-2" 
            />
          </CardContent>
        </Card>
      </div>

      {/* Live Data Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Browsing Data */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="w-5 h-5 text-cyan-400" />
              Browsing Behavior
              <Badge variant="outline" className="ml-auto">
                ${getCategoryValue('browsing').toFixed(2)}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {getLatestValue('browsing') && (
                <div className="p-3 bg-gray-800/50 rounded">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-cyan-400">Ads Intercepted</span>
                    <span>{getLatestValue('browsing')?.value.ads_intercepted || 0}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-cyan-400">UMatter Generated</span>
                    <span>{getLatestValue('browsing')?.value.umatter_generated?.toFixed(6) || '0.000000'}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-cyan-400">Network Speed</span>
                    <span>{getLatestValue('browsing')?.value.network_speed || 0} Mbps</span>
                  </div>
                </div>
              )}
              <p className="text-xs text-muted-foreground">
                Real-time browser extension data. Value: $0.05 per ad interaction.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Biometric Data */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="w-5 h-5 text-green-400" />
              Device Biometrics
              <Badge variant="outline" className="ml-auto">
                ${getCategoryValue('biometric').toFixed(2)}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {getLatestValue('biometric') && (
                <div className="p-3 bg-gray-800/50 rounded">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-green-400">Neural Power</span>
                    <span>{getLatestValue('biometric')?.value.neural_power || 0}W</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-green-400">Fabric Nodes</span>
                    <span>{getLatestValue('biometric')?.value.fabric_nodes || 0}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-green-400">Consciousness Level</span>
                    <span>{getLatestValue('biometric')?.value.global_consciousness?.toFixed(3) || '0.000'}</span>
                  </div>
                </div>
              )}
              <p className="text-xs text-muted-foreground">
                Authentic device metrics. Value: $0.02 per watt generated.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Network Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Network className="w-5 h-5 text-purple-400" />
              Network Activity
              <Badge variant="outline" className="ml-auto">
                ${getCategoryValue('network').toFixed(2)}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {getLatestValue('network') && (
                <div className="p-3 bg-gray-800/50 rounded">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-purple-400">Active Connections</span>
                    <span>{getLatestValue('network')?.value.connections || 0}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-purple-400">Bandwidth Usage</span>
                    <span>{getLatestValue('network')?.value.bandwidth_usage || '0 Mbps'}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-purple-400">Device Type</span>
                    <span>{getLatestValue('network')?.value.device_type || 'Unknown'}</span>
                  </div>
                </div>
              )}
              <p className="text-xs text-muted-foreground">
                Real network patterns. Value: $0.05-$0.15 per data point.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Package Recommendations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="w-5 h-5 text-yellow-400" />
              Package Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {dataStream.total_points > 10 && (
                <div className="p-3 bg-yellow-500/10 border border-yellow-500/20 rounded">
                  <h4 className="font-medium text-yellow-400">Ready to Create:</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    "Browsing Behavior Package" - ${getCategoryValue('browsing').toFixed(2)} estimated value
                  </p>
                  <Button size="sm" className="mt-2 bg-yellow-600 hover:bg-yellow-700">
                    Create Package
                  </Button>
                </div>
              )}
              
              {dataStream.total_points > 20 && (
                <div className="p-3 bg-green-500/10 border border-green-500/20 rounded">
                  <h4 className="font-medium text-green-400">Premium Package Available:</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    "Complete Digital Profile" - ${dataStream.estimated_value.toFixed(2)} estimated value
                  </p>
                  <Button size="sm" className="mt-2 bg-green-600 hover:bg-green-700">
                    Create Premium Package
                  </Button>
                </div>
              )}

              {dataStream.total_points <= 10 && (
                <div className="p-3 bg-gray-500/10 border border-gray-500/20 rounded">
                  <h4 className="font-medium text-gray-400">Collecting Data...</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    Need {20 - dataStream.total_points} more data points for premium packages
                  </p>
                  <Progress value={(dataStream.total_points / 20) * 100} className="mt-2 h-2" />
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}