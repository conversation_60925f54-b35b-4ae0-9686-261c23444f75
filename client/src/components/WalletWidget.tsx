import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Wallet, TrendingUp, Eye, EyeOff, ExternalLink, DollarSign } from "lucide-react";
import { Link } from "wouter";

interface WalletData {
  totalUsdValue: number;
  umatterBalance: number;
  truBalance: number;
  nuvaBalance: number;
  inurtiaBalance: number;
  ubitsBalance: number;
  dailyEarnings: number;
  totalEnergyGenerated: number;
}

export function WalletWidget() {
  const [balanceVisible, setBalanceVisible] = useState(true);

  const { data: walletData } = useQuery<WalletData>({
    queryKey: ['/api/wallet/comprehensive'],
    refetchInterval: 5000,
  });

  const { data: extensionData = {} } = useQuery<any>({
    queryKey: ['/api/extension/status'],
    refetchInterval: 3000,
  });

  if (!walletData) {
    return (
      <Card className="bg-gradient-to-br from-green-50 to-emerald-100 border-green-200">
        <CardContent className="p-4">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
            <div className="h-6 bg-gray-200 rounded w-32"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gradient-to-br from-green-50 to-emerald-100 border-green-200 hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Wallet className="h-4 w-4 text-green-600" />
            <span className="text-green-700">nU Wallet</span>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setBalanceVisible(!balanceVisible)}
              className="h-6 w-6 p-0"
            >
              {balanceVisible ? 
                <EyeOff className="h-3 w-3" /> : 
                <Eye className="h-3 w-3" />
              }
            </Button>
            <div className={`w-2 h-2 rounded-full ${
              extensionData?.connected ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
            }`} />
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Total Value */}
        <div>
          <div className="text-2xl font-bold text-green-700">
            {balanceVisible ? 
              `$${walletData.totalUsdValue.toLocaleString(undefined, { 
                minimumFractionDigits: 2, 
                maximumFractionDigits: 2 
              })}` : 
              '••••••'
            }
          </div>
          <div className="text-xs text-green-600 flex items-center gap-1">
            <TrendingUp className="h-3 w-3" />
            +${walletData.dailyEarnings.toFixed(2)} today
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="bg-white/60 rounded-lg p-2">
            <div className="text-gray-600">UMatter</div>
            <div className="font-semibold text-green-700">
              {balanceVisible ? walletData.umatterBalance.toFixed(4) : '••••'}
            </div>
          </div>
          <div className="bg-white/60 rounded-lg p-2">
            <div className="text-gray-600">Total Energy</div>
            <div className="font-semibold text-green-700">
              {balanceVisible ? walletData.totalEnergyGenerated.toFixed(2) : '••••'}
            </div>
          </div>
        </div>

        {/* Token Summary */}
        <div className="space-y-1">
          {[
            { name: 'TRU', balance: walletData.truBalance, rate: 52362.77, icon: '💎' },
            { name: 'NUVA', balance: walletData.nuvaBalance, rate: 100.0, icon: '🌟' },
            { name: 'Ubits', balance: walletData.ubitsBalance, rate: 1.0, icon: '🎯' }
          ].filter(token => token.balance > 0).map((token) => (
            <div key={token.name} className="flex justify-between items-center text-xs">
              <div className="flex items-center gap-1">
                <span>{token.icon}</span>
                <span className="text-gray-600">{token.name}</span>
              </div>
              <div className="text-right">
                <div className="font-medium">
                  {balanceVisible ? token.balance.toFixed(6) : '••••••'}
                </div>
                <div className="text-gray-500">
                  {balanceVisible ? 
                    `$${(token.balance * token.rate).toFixed(2)}` : 
                    '••••'
                  }
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Link href="/unified-wallet?tab=offramp" className="flex-1">
            <Button size="sm" className="w-full bg-green-600 hover:bg-green-700">
              <DollarSign className="h-3 w-3 mr-1" />
              Cash Out
            </Button>
          </Link>
          <Link href="/unified-wallet" className="flex-1">
            <Button variant="outline" size="sm" className="w-full">
              <ExternalLink className="h-3 w-3 mr-1" />
              Full Wallet
            </Button>
          </Link>
        </div>

        {/* Extension Status */}
        <div className={`text-xs p-2 rounded-lg ${
          extensionData?.connected ? 
            'bg-green-100 text-green-700 border border-green-200' : 
            'bg-gray-100 text-gray-600 border border-gray-200'
        }`}>
          <div className="flex items-center gap-1">
            <div className={`w-1.5 h-1.5 rounded-full ${
              extensionData?.connected ? 'bg-green-500' : 'bg-gray-400'
            }`} />
            <span className="font-medium">
              {extensionData?.connected ? 'Extension Active' : 'Extension Offline'}
            </span>
          </div>
          <div className="mt-1">
            {extensionData?.connected ? 
              'Generating energy from browsing' : 
              'Install extension for energy capture'
            }
          </div>
        </div>
      </CardContent>
    </Card>
  );
}