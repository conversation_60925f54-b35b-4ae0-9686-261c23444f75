import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Bot, 
  Activity, 
  Settings, 
  Zap, 
  Code, 
  Shield, 
  Database,
  Monitor
} from 'lucide-react';

export function SimpleSpUnderDashboard() {
  return (
    <div className="min-h-screen bg-gray-900 text-gray-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <Bot className="h-8 w-8 text-cyan-400" />
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-cyan-400">SpU<PERSON> Butler</h1>
              <p className="text-gray-400">Advanced AI System Butler v3.0.1</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Badge className="bg-green-600 text-white">ACTIVE</Badge>
            <Badge className="bg-blue-600 text-white">3 Tasks Active</Badge>
          </div>
        </div>

        {/* System Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-gradient-to-br from-green-900/20 to-emerald-900/20 border-green-500/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-green-300">Success Rate</p>
                  <p className="text-2xl font-bold text-green-400">98%</p>
                </div>
                <Activity className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-blue-900/20 to-cyan-900/20 border-blue-500/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-blue-300">Tasks Completed</p>
                  <p className="text-2xl font-bold text-blue-400">247</p>
                </div>
                <Code className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-900/20 to-violet-900/20 border-purple-500/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-purple-300">System Health</p>
                  <p className="text-2xl font-bold text-purple-400">95%</p>
                </div>
                <Monitor className="h-8 w-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-900/20 to-red-900/20 border-orange-500/30">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-orange-300">Energy Efficiency</p>
                  <p className="text-2xl font-bold text-orange-400">89%</p>
                </div>
                <Zap className="h-8 w-8 text-orange-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Capabilities Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            { 
              name: 'Code Generation', 
              icon: Code, 
              efficiency: 95, 
              description: 'Generates React, TypeScript, API routes with full stack capabilities',
              color: 'blue'
            },
            { 
              name: 'Bug Detection', 
              icon: Activity, 
              efficiency: 89, 
              description: 'Real-time error detection, syntax fixes, and logic debugging',
              color: 'yellow'
            },
            { 
              name: 'System Repair', 
              icon: Settings, 
              efficiency: 92, 
              description: 'Autonomous system healing, database fixes, and performance tuning',
              color: 'green'
            },
            { 
              name: 'Security Analysis', 
              icon: Shield, 
              efficiency: 91, 
              description: 'Vulnerability scanning, code auditing, and security patching',
              color: 'red'
            },
            { 
              name: 'Database Management', 
              icon: Database, 
              efficiency: 94, 
              description: 'PostgreSQL operations, migrations, and query optimization',
              color: 'cyan'
            },
            { 
              name: 'Performance Optimization', 
              icon: Zap, 
              efficiency: 87, 
              description: 'Database query optimization, API speed enhancement, UI rendering',
              color: 'purple'
            }
          ].map((capability, index) => (
            <Card 
              key={index} 
              className={`bg-gray-800/40 border-gray-600/50 hover:border-${capability.color}-500/50 transition-all duration-200 cursor-pointer group`}
              onClick={() => {
                console.log(`[SpUnder] Accessing ${capability.name} controls...`);
              }}
            >
              <CardHeader>
                <CardTitle className="text-gray-300 flex items-center gap-3">
                  <capability.icon className={`h-6 w-6 text-${capability.color}-400`} />
                  {capability.name}
                  <Badge className={`ml-auto bg-${capability.color}-600 text-white`}>
                    {capability.efficiency}%
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-400 mb-4">
                  {capability.description}
                </p>
                <div className={`w-full bg-gray-700/50 rounded-full h-2`}>
                  <div 
                    className={`h-2 rounded-full bg-${capability.color}-400`}
                    style={{ width: `${capability.efficiency}%` }}
                  />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <Card className="bg-gray-800/40 border-gray-600/50">
          <CardHeader>
            <CardTitle className="text-gray-300">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button 
                className="bg-green-600 hover:bg-green-700 h-12"
                onClick={() => console.log('[SpUnder] System diagnostics started')}
              >
                <Activity className="h-4 w-4 mr-2" />
                Run Diagnostics
              </Button>
              
              <Button 
                className="bg-purple-600 hover:bg-purple-700 h-12"
                onClick={() => console.log('[SpUnder] Code generation initiated')}
              >
                <Code className="h-4 w-4 mr-2" />
                Generate Code
              </Button>
              
              <Button 
                className="bg-red-600 hover:bg-red-700 h-12"
                onClick={() => console.log('[SpUnder] Security scan started')}
              >
                <Shield className="h-4 w-4 mr-2" />
                Security Scan
              </Button>
              
              <Button 
                className="bg-orange-600 hover:bg-orange-700 h-12"
                onClick={() => console.log('[SpUnder] Performance optimization started')}
              >
                <Zap className="h-4 w-4 mr-2" />
                Optimize
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Live System Status */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="bg-gray-800/40 border-gray-600/50">
            <CardHeader>
              <CardTitle className="text-gray-300">Live System Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">CPU Usage</span>
                  <span className="text-green-400 font-mono">12%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Memory</span>
                  <span className="text-blue-400 font-mono">43MB</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Network</span>
                  <span className="text-cyan-400 font-mono">10Mbps</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Energy Generated</span>
                  <span className="text-yellow-400 font-mono">11.2K UMatter</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-800/40 border-gray-600/50">
            <CardHeader>
              <CardTitle className="text-gray-300">Recent Activities</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-sm">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  <span className="text-gray-300">System health check completed</span>
                  <span className="text-gray-500 ml-auto">2 min ago</span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                  <span className="text-gray-300">Performance optimization in progress</span>
                  <span className="text-gray-500 ml-auto">5 min ago</span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <div className="w-2 h-2 bg-purple-400 rounded-full" />
                  <span className="text-gray-300">Code generation task completed</span>
                  <span className="text-gray-500 ml-auto">8 min ago</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}