import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Smartphone, Zap } from "lucide-react";

export function SimpleMobileStatus() {
  return (
    <Card className="bg-gradient-to-br from-green-900/20 to-emerald-900/20 border-green-700/30">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-green-400">
          <CheckCircle className="h-5 w-5" />
          Mobile-First System
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Smartphone className="h-4 w-4 text-green-400" />
              <span className="text-sm text-green-300">Device APIs</span>
            </div>
            <Badge variant="secondary" className="bg-green-600 text-white">Active</Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-green-400" />
              <span className="text-sm text-green-300">Energy Batching</span>
            </div>
            <Badge variant="secondary" className="bg-green-600 text-white">Optimized</Badge>
          </div>
          
          <div className="p-3 bg-green-900/30 rounded-lg">
            <p className="text-xs text-green-300">
              Performance controller active. Individual API calls blocked for stability.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}