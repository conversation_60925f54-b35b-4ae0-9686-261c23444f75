import { useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Circle, Shield, Brain, CheckCircle } from 'lucide-react';
import { useExtensionData } from '@/hooks/useExtensionData';
interface ActivityItem {
  id: string;
  type: 'connection' | 'encryption' | 'ai_analysis' | 'memvid_index' | 'privacy_alert';
  message: string;
  timestamp: number;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  metadata?: Record<string, any>;
}
export function ActivityFeed() {
  const { extensionMetrics = {} } = useExtensionData();
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  // Fetch recent activities from various endpoints
  const { data: privacyAlerts = [] } = useQuery({
    queryKey: ['/api/privacy/alerts'],
    refetchInterval: 5000
  });
  const { data: activeSession } = useQuery({
    queryKey: ['/api/sessions/active'],
    refetchInterval: 10000
  });
  const { data: memvidChunks = [] } = useQuery({
    queryKey: ['/api/memvid/chunks'],
    refetchInterval: 15000
  });
  // Transform data into activity items
  useEffect(() => {
    const newActivities: ActivityItem[] = [];
    // Add privacy alerts
    privacyAlerts.forEach((alert: any) => {
      newActivities.push({
        id: `alert-${alert.id}`,
        type: 'privacy_alert',
        message: alert.message,
        timestamp: new Date(alert.createdAt).getTime(),
        severity: alert.severity,
        metadata: alert.metadata
      });
    });
    // Add session activity
    if (activeSession) {
      newActivities.push({
        id: `session-${activeSession.id}`,
        type: 'connection',
        message: 'Active interaction session established',
        timestamp: new Date(activeSession.lastActivityAt).getTime(),
        metadata: { sessionId: activeSession.id }
      });
    }
    // Add memvid indexing activity
    memvidChunks.slice(0, 3).forEach((chunk: any) => {
      newActivities.push({
        id: `memvid-${chunk.id}`,
        type: 'memvid_index',
        message: 'Interaction chunk indexed successfully',
        timestamp: new Date(chunk.createdAt).getTime(),
        metadata: { chunkId: chunk.chunkId }
      });
    });
    // Add extension-based activities
    const extensionActivities: ActivityItem[] = [];
    if (extensionMetrics.recentInterceptions && Array.isArray(extensionMetrics.recentInterceptions)) {
      extensionMetrics.recentInterceptions.slice(0, 5).forEach((ad: any) => {
        extensionActivities.push({
          id: `ext-${ad.id || Math.random()}`,
          type: 'connection',
          message: `Extension blocked ${ad.domain || 'unknown'} (+${(ad.umatterGenerated || 0).toFixed(6)} UMatter)`,
          timestamp: ad.timestamp || Date.now(),
          severity: 'low'
        });
      });
    }

    // Add simulated real-time activities for demo
    const simulatedActivities: ActivityItem[] = [
      {
        id: 'sim-1',
        type: 'encryption',
        message: 'SpUnder encryption activated',
        timestamp: Date.now() - 5000,
      },
      {
        id: 'sim-2',
        type: 'ai_analysis',
        message: 'AI pattern recognition: anomaly detected',
        timestamp: Date.now() - 12000,
        severity: 'medium'
      },
      {
        id: 'sim-3',
        type: 'connection',
        message: 'Node connection established',
        timestamp: Date.now() - 18000,
      }
    ];
    // Combine and sort activities (prioritize extension data)
    const allActivities = [...extensionActivities, ...newActivities, ...simulatedActivities]
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 12); // Keep only recent 12 activities
    setActivities(allActivities);
  }, [privacyAlerts, activeSession, memvidChunks]);
  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'connection':
        return <Circle className="text-neon-cyan" size={12} />;
      case 'encryption':
        return <Shield className="text-neon-purple" size={12} />;
      case 'ai_analysis':
        return <Brain className="text-yellow-400" size={12} />;
      case 'memvid_index':
        return <CheckCircle className="text-green-400" size={12} />;
      case 'privacy_alert':
        return <Circle className="text-red-400" size={12} />;
      default:
        return <Circle className="text-text-secondary" size={12} />;
    }
  };
  const getActivityColor = (type: ActivityItem['type'], severity?: string) => {
    if (severity === 'critical') return 'border-red-400';
    if (severity === 'high') return 'border-orange-400';
    if (severity === 'medium') return 'border-yellow-400';
    switch (type) {
      case 'connection':
        return 'border-neon-cyan';
      case 'encryption':
        return 'border-neon-purple';
      case 'ai_analysis':
        return 'border-yellow-400';
      case 'memvid_index':
        return 'border-green-400';
      case 'privacy_alert':
        return 'border-red-400';
      default:
        return 'border-text-secondary';
    }
  };
  const formatTimeAgo = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    if (seconds < 60) return `${seconds}s`;
    if (minutes < 60) return `${minutes}m`;
    if (hours < 24) return `${hours}h`;
    return `${Math.floor(hours / 24)}d`;
  };
  return (
    <Card className="bg-panel/40 backdrop-blur-md border border-neon-purple/20">
      <CardHeader className="border-b border-neon-purple/20">
        <CardTitle className="text-neon-purple">Live Activity Feed</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-64">
          <div className="p-4 space-y-3">
            {activities.map((activity) => (
              <div
                key={activity.id}
                className={`flex items-start space-x-3 p-2 bg-panel/40 rounded border-l-2 ${getActivityColor(activity.type, activity.severity)}`}
              >
                <div className="mt-2 animate-pulse">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-mono text-text-primary">
                    {activity.message}
                  </div>
                  {activity.metadata && Object.keys(activity.metadata).length > 0 && (
                    <div className="text-xs text-text-secondary font-mono mt-1">
                      {Object.entries(activity.metadata).slice(0, 2).map(([key, value]) => (
                        <span key={key} className="mr-2">
                          {key}: {String(value).substring(0, 20)}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
                <div className="text-xs text-text-secondary font-mono">
                  {formatTimeAgo(activity.timestamp)}
                </div>
              </div>
            ))}
            {activities.length === 0 && (
              <div className="text-center py-8">
                <div className="text-text-secondary">No recent activity</div>
                <div className="text-sm text-text-secondary mt-1">
                  Activity will appear here as interactions occur
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}