import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Cpu, HardDrive, Zap, Activity, CheckCircle } from 'lucide-react';
import { useRealTimeData } from '@/hooks/useRealTimeData';

interface RealHardwareMetrics {
  cpuUsage: number;
  memoryUsage: number;
  powerConsumption: number;
  authenticEnergy: number;
  timestamp: number;
  isReal: boolean;
}

export function RealTimeEnergyDisplay() {
  const [metrics, setMetrics] = useState<RealHardwareMetrics>({
    cpuUsage: 0,
    memoryUsage: 0,
    powerConsumption: 0,
    authenticEnergy: 0,
    timestamp: 0,
    isReal: false
  });

  const [updateCount, setUpdateCount] = useState(0);
  const [lastUpdateTime, setLastUpdateTime] = useState(0);
  const [componentKey, setComponentKey] = useState(0);

  useEffect(() => {
    let mounted = true;
    let fetchInterval: NodeJS.Timeout;

    const fetchRealMetrics = async () => {
      if (!mounted) return;

      try {
        const timestamp = Date.now();

        const response = await fetch(`/api/energy/real-hardware-metrics?t=${timestamp}&r=${Math.random()}`, {
          method: 'GET',
          cache: 'no-store',
          headers: { 
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });

        if (response.ok) {
          const newMetrics = await response.json();

          // Create fresh object
          const freshMetrics = {
            cpuUsage: Number(newMetrics.cpuUsage) || 0,
            memoryUsage: Number(newMetrics.memoryUsage) || 0,
            powerConsumption: Number(newMetrics.powerConsumption) || 0,
            authenticEnergy: Number(newMetrics.authenticEnergy) || 0,
            timestamp: timestamp,
            isReal: true
          };

          setMetrics(freshMetrics);
          setUpdateCount(prev => prev + 1);
          setLastUpdateTime(timestamp);
          setComponentKey(prev => prev + 1); // Force re-render

          console.log(`[RealTimeEnergyDisplay] LIVE HARDWARE UPDATE #${updateCount + 1}:`, {
            cpu: `${newMetrics.cpuUsage.toFixed(2)}%`,
            memory: `${newMetrics.memoryUsage.toFixed(1)}MB`,
            power: `${newMetrics.powerConsumption.toFixed(2)}W`,
            energy: newMetrics.authenticEnergy.toFixed(6)
          });
        } else {
          console.error(`[RealTimeEnergyDisplay] API Error: ${response.status}`);
        }
      } catch (error) {
        console.error('[RealTimeEnergyDisplay] Failed to fetch:', error);
      }
    };

    // Initial fetch
    fetchRealMetrics();

    // Update every 1 second for real-time feel
    fetchInterval = setInterval(fetchRealMetrics, 1000);

    return () => {
      mounted = false;
      if (fetchInterval) clearInterval(fetchInterval);
    };
  }, []);

  const secondsSinceUpdate = lastUpdateTime > 0 ? Math.floor((Date.now() - lastUpdateTime) / 1000) : 0;

  return (
    <div className="space-y-4" key={componentKey}>
      {/* Live Connection Status */}
      <div className="flex items-center justify-center gap-2">
        <CheckCircle className={`w-4 h-4 ${secondsSinceUpdate < 3 ? 'text-green-400 animate-pulse' : 'text-red-400'}`} />
        <Badge variant="default" className="text-xs">
          HARDWARE UPDATE #{updateCount} - {metrics.authenticEnergy.toFixed(6)}/sec
        </Badge>
        <span className="text-xs text-gray-400">
          {secondsSinceUpdate < 3 ? 'LIVE' : `${secondsSinceUpdate}s ago`}
        </span>
      </div>

      {/* Real Metrics Card */}
      <Card className="bg-gradient-to-br from-green-900/20 to-blue-900/20 border-green-500/20">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Activity className="w-5 h-5 text-green-400" />
            Live Node.js Hardware (Update #{updateCount})
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">

          {/* Current Generation */}
          <div className="text-center space-y-2" key={`energy-${metrics.authenticEnergy}-${updateCount}`}>
            <div className="text-2xl font-bold text-yellow-400">
              +{metrics.authenticEnergy.toFixed(6)} UMatter/sec
            </div>
            <div className="text-sm text-gray-400">
              From Real Node.js Process APIs
            </div>
          </div>

          {/* Live Hardware Metrics */}
          <div className="space-y-3">
            <div className="space-y-2" key={`cpu-${metrics.cpuUsage}-${updateCount}`}>
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <Cpu className="w-4 h-4 text-blue-400" />
                  <span>CPU Usage</span>
                </div>
                <span className="text-blue-400 font-mono">
                  {metrics.cpuUsage.toFixed(2)}%
                </span>
              </div>
              <Progress value={Math.min(metrics.cpuUsage, 100)} className="h-2" />
            </div>

            <div className="space-y-2" key={`memory-${metrics.memoryUsage}-${updateCount}`}>
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <HardDrive className="w-4 h-4 text-purple-400" />
                  <span>Memory Usage</span>
                </div>
                <span className="text-purple-400 font-mono">
                  {metrics.memoryUsage.toFixed(1)}MB
                </span>
              </div>
              <Progress value={Math.min((metrics.memoryUsage / 500) * 100, 100)} className="h-2" />
            </div>

            <div className="space-y-2" key={`power-${metrics.powerConsumption}-${updateCount}`}>
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <Zap className="w-4 h-4 text-yellow-400" />
                  <span>Power Draw</span>
                </div>
                <span className="text-yellow-400 font-mono">
                  {metrics.powerConsumption.toFixed(2)}W
                </span>
              </div>
              <Progress value={Math.min((metrics.powerConsumption / 20) * 100, 100)} className="h-2" />
            </div>
          </div>

          {/* Live Status */}
          <div className="pt-3 border-t border-gray-700">
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-400">Updates Received:</span>
              <span className="text-green-400 font-mono">{updateCount}</span>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-gray-400">Connection Status:</span>
              <span className={secondsSinceUpdate < 5 ? 'text-green-400' : 'text-red-400'}>
                {secondsSinceUpdate < 3 ? 'LIVE' : 'DELAYED'}
              </span>
            </div>
          </div>

        </CardContent>
      </Card>
    </div>
  );
}