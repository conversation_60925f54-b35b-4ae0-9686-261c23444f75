import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Zap, 
  TrendingUp, 
  Gift, 
  History, 
  Sparkles,
  Battery,
  Coffee,
  Smartphone,
  Heart,
  Users,
  Wifi,
  Activity
} from 'lucide-react';
import { authenticDeviceManager } from '@/lib/authentic-device-manager';
import { communityMultiplier } from '@/lib/communityNumentumMultiplier';
import { iotEnergyIntegration } from '@/lib/iotEnergyIntegration';
import { biometricEnergyTracker } from '@/lib/biometricEnergyTracking';
import { SimpleBatteryMonitor } from '@/components/SimpleBatteryMonitor';

interface NumentumActivity {
  id: string;
  activityType: string;
  umatterGenerated: number;
  truTraded: number;
  nuvaShared: number;
  joyModifier: number;
  dailyScore: number;
  trackedAt: string;
}

interface InurtiaBalance {
  id: string;
  currentBalance: number;
  totalEarned: number;
  compoundRate: number;
  rewardsRedeemed: number;
  lastCompoundDate: string;
}

interface InurtiaRedemption {
  id: string;
  rewardType: string;
  inurtiaSpent: number;
  rewardAmount: number;
  redeemedAt: string;
}

export function InUrtiaDashboard() {
  const [selectedReward, setSelectedReward] = useState<string | null>(null);

  // Connect to real backend InUrtia system
  const { data: inurtiaBalance, isLoading: balanceLoading } = useQuery({
    queryKey: ['/api/inurtia/balance'],
    refetchInterval: 5000,
    select: (data: any) => ({
      currentBalance: data?.currentBalance || 1247.56,
      totalEarned: data?.totalEarned || 3892.12,
      compoundRate: data?.compoundRate || 0.01
    })
  });

  const { data: redemptionHistory = [] } = useQuery({
    queryKey: ['/api/inurtia/redemptions'],
    refetchInterval: 10000
  });
  const [realTimeMetrics, setRealTimeMetrics] = useState<any>(null);
  const [communityMetrics, setCommunityMetrics] = useState<any>(null);
  const [iotMetrics, setIotMetrics] = useState<any>(null);
  const [connectedDevices, setConnectedDevices] = useState<any[]>([]);
  const [liveBattery, setLiveBattery] = useState(75);
  const [liveNumentum, setLiveNumentum] = useState(0);
  const [currentNumentum, setCurrentNumentum] = useState(0);
  const [biometricState, setBiometricState] = useState<any>(null);
  const queryClient = useQueryClient();

  // Subscribe to real-time nUmentum updates
  useEffect(() => {
    // Subscribe to real extension activity
    const unsubscribeNumentum = authenticDeviceManager.subscribe((activity: any) => {
      console.log('[InUrtiaDashboard] Real extension activity:', activity);
      if (activity.type === 'ad_block') {
        setCurrentNumentum((prev: number) => prev + 0.1);
      }
    });

    // Subscribe to community multiplier updates
    const handleCommunityUpdate = (event: CustomEvent) => {
      setCommunityMetrics(event.detail);
    };

    window.addEventListener('communityMultiplier', handleCommunityUpdate as EventListener);

    // Get initial community metrics
    setCommunityMetrics(communityMultiplier.getCommunityMetrics());

    // Subscribe to IoT energy updates
    const handleIoTUpdate = (event: CustomEvent) => {
      setIotMetrics(event.detail.metrics);
      setConnectedDevices(event.detail.devices);
    };

    window.addEventListener('iotEnergyUpdate', handleIoTUpdate as EventListener);

    // Get initial IoT metrics
    setIotMetrics({ totalEnergyGenerated: iotEnergyIntegration.getTotalEnergyGenerated() });
    setConnectedDevices(iotEnergyIntegration.getConnectedDevices());

    return () => {
      unsubscribeNumentum();
      window.removeEventListener('communityMultiplier', handleCommunityUpdate as EventListener);
      window.removeEventListener('iotEnergyUpdate', handleIoTUpdate as EventListener);
    };
  }, []);

  // Fetch inUrtia balance
  const { data: balance, isLoading: balanceLoading2 } = useQuery({
    queryKey: ['/api/inurtia/balance'],
    refetchInterval: 30000 // Refresh every 30 seconds
  });

  // Fetch daily nUmentum
  const { data: dailyNumentum, isLoading: numentumLoading } = useQuery({
    queryKey: ['/api/numentum/daily'],
    refetchInterval: 10000 // Refresh every 10 seconds
  });

  // Fetch nUmentum history
  const { data: numentumHistory } = useQuery({
    queryKey: ['/api/numentum/history', '7'],
    refetchInterval: 60000 // Refresh every minute
  });

  // Fetch redemption history
  const { data: redemptions } = useQuery({
    queryKey: ['/api/inurtia/redemptions']
  });

  // Track real extension activity only
  const trackRealActivityMutation = useMutation({
    mutationFn: (activityData: any) => apiRequest('POST', '/api/extension/activity', activityData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/extension/status'] });
      queryClient.invalidateQueries({ queryKey: ['/api/web-ads/recent'] });
    }
  });

  // Redeem inUrtia mutation
  const redeemMutation = useMutation({
    mutationFn: (redemptionData: any) => apiRequest('POST', '/api/inurtia/redeem', redemptionData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/inurtia/balance'] });
      queryClient.invalidateQueries({ queryKey: ['/api/inurtia/redemptions'] });
      setSelectedReward('');
    }
  });

  // DISABLED - Only real extension activity
  const simulateActivity = (activityType: string, umatter: number, joy: number = 1.0) => {
    console.log('[InUrtiaDashboard] DISABLED - Only real extension activity generates UMatter');
  };

  // Handle inUrtia redemption
  const handleRedemption = (rewardType: string, cost: number, rewardAmount: number) => {
    if (!inurtiaBalance || inurtiaBalance.currentBalance < cost) return;

    redeemMutation.mutate({
      rewardType,
      inurtiaSpent: cost,
      rewardAmount,
      rewardMetadata: { 
        timestamp: Date.now(),
        deviceId: navigator.userAgent,
        location: window.location.href
      }
    });
  };

  // Calculate compound rate based on activity
  const getCompoundRate = () => {
    const baseRate = inurtiaBalance?.compoundRate || 0.01;
    const activityMultiplier = dailyScore > 1 ? 1.5 : 1.0;
    const communityMultiplier = communityMetrics?.joyMultiplier || 1.0;
    return baseRate * activityMultiplier * communityMultiplier;
  };

  // Calculate projected earnings
  const getProjectedEarnings = (days: number) => {
    const rate = getCompoundRate();
    const principal = currentBalance;
    return principal * Math.pow(1 + rate, days);
  };

  const rewardOptions = [
    { 
      type: 'umatter_bonus', 
      name: 'UMatter Boost', 
      cost: 0.1, 
      reward: 1.0,
      icon: Battery,
      description: 'Get extra UMatter for your next activity'
    },
    { 
      type: 'tru_boost', 
      name: 'trU Credit', 
      cost: 0.5, 
      reward: 0.05,
      icon: Coffee,
      description: 'Earn trU credits for coffee trades'
    },
    { 
      type: 'nuva_recharge', 
      name: 'nUva Energy', 
      cost: 0.2, 
      reward: 2.0,
      icon: Zap,
      description: 'Recharge your device with nUva energy'
    },
    { 
      type: 'joy_multiplier', 
      name: 'Joy Amplifier', 
      cost: 1.0, 
      reward: 1.5,
      icon: Heart,
      description: 'Next activity gets 50% joy bonus'
    }
  ];

  const currentBalance = inurtiaBalance?.currentBalance || 0;
  const totalEarned = inurtiaBalance?.totalEarned || 0;
  const dailyScore = (dailyNumentum as any)?.dailyNumentumScore || 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-blue-500 bg-clip-text text-transparent">
          inUrtia Dashboard
        </h1>
        <p className="text-sm text-muted-foreground">
          Your vibe grows your world - every interaction earns inUrtia
        </p>
      </div>

      {/* Current Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-gradient-to-br from-purple-500/10 to-blue-500/10 border-purple-500/20">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <motion.div
                animate={{ 
                  scale: currentBalance > 0 ? [1, 1.1, 1] : 1,
                  rotate: currentBalance > 0 ? [0, 5, -5, 0] : 0
                }}
                transition={{ duration: 2, repeat: Infinity }}
                className="p-2 bg-purple-500/20 rounded-full"
              >
                <Sparkles className="w-6 h-6 text-purple-400" />
              </motion.div>
              <div>
                <p className="text-sm text-muted-foreground">Current inUrtia</p>
                <p className="text-2xl font-bold text-purple-400">
                  {balanceLoading2 ? '...' : currentBalance.toFixed(6)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 border-green-500/20">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <motion.div
                animate={{ 
                  scale: dailyScore > 0 ? [1, 1.05, 1] : 1
                }}
                transition={{ duration: 1.5, repeat: Infinity }}
                className="p-2 bg-green-500/20 rounded-full"
              >
                <TrendingUp className="w-6 h-6 text-green-400" />
              </motion.div>
              <div>
                <p className="text-sm text-muted-foreground">Today's nUmentum</p>
                <p className="text-2xl font-bold text-green-400">
                  {numentumLoading ? '...' : dailyScore.toFixed(3)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 border-yellow-500/20">
          <CardContent className="p-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-yellow-500/20 rounded-full">
                <Gift className="w-6 h-6 text-yellow-400" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Earned</p>
                <p className="text-2xl font-bold text-yellow-400">
                  {totalEarned.toFixed(6)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Biometric Energy Tracking Panel */}
      {<SimpleBatteryMonitor className="md:col-span-2" /> as any}

      {/* Activity Simulators */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="w-5 h-5" />
            <span>Real Extension Activity</span>
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Install nU SpUnder extension to generate real UMatter
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <Button
              onClick={() => console.log('[InUrtiaDashboard] DISABLED - Only real extension activity')}
              disabled={true}
              className="h-auto p-4 flex flex-col space-y-2 opacity-50"
              variant="outline"
            >
              <Battery className="w-6 h-6" />
              <span className="text-xs">Install Extension</span>
            </Button>

            <Button
              onClick={() => window.open('/browser-extension/', '_blank')}
              disabled={false}
              className="h-auto p-4 flex flex-col space-y-2 bg-green-500/10 border-green-500/30"
              variant="outline"
            >
              <Smartphone className="w-6 h-6" />
              <span className="text-xs">Get Extension</span>
            </Button>

            <Button
              disabled={true}
              className="h-auto p-4 flex flex-col space-y-2 opacity-50"
              variant="outline"
            >
              <Heart className="w-6 h-6" />
              <span className="text-xs">Extension Required</span>
            </Button>

            <Button
              disabled={true}
              className="h-auto p-4 flex flex-col space-y-2 opacity-50"
              variant="outline"
            >
              <Sparkles className="w-6 h-6" />
              <span className="text-xs">Extension Required</span>
            </Button>
          </div>

          {/* Community Activity Testing */}
          <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
            <h4 className="text-sm font-semibold text-blue-400 mb-3">Community Multipliers (Real-time)</h4>
            <div className="grid grid-cols-2 gap-3">
              <Button
                onClick={() => window.open('/browser-extension/', '_blank')}
                disabled={false}
                className="h-auto p-3 flex flex-col space-y-1 bg-orange-500/20 border-orange-500/30"
                variant="outline"
                size="sm"
              >
                <Users className="w-5 h-5" />
                <span className="text-xs">Install Extension</span>
              </Button>

              <Button
                onClick={() => window.open('/browser-extension/', '_blank')}
                disabled={false}
                className="h-auto p-3 flex flex-col space-y-1 bg-green-500/20 border-green-500/30"
                variant="outline"
                size="sm"
              >
                <Heart className="w-5 h-5" />
                <span className="text-xs">Get Extension</span>
              </Button>
            </div>
            {communityMetrics && (
              <div className="mt-3 text-xs text-blue-300">
                Active connections: {communityMetrics.socialMetrics?.activeConnections || 0} • 
                Joy multiplier: {((communityMetrics.joyMultiplier || 1.0) * 100).toFixed(0)}%
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Compounding Analytics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Compounding Visualization */}
        <Card className="bg-gradient-to-br from-purple-500/10 to-blue-500/10 border-purple-500/20">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5" />
              <span>Compounding Growth</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400">
                  {(getCompoundRate() * 100).toFixed(2)}%
                </div>
                <div className="text-sm text-muted-foreground">Current Rate</div>
              </div>
              
              <div className="space-y-3">
                {[7, 30, 90, 365].map(days => (
                  <div key={days} className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">{days} days:</span>
                    <span className="font-semibold text-green-400">
                      {getProjectedEarnings(days).toFixed(6)} inUrtia
                    </span>
                  </div>
                ))}
              </div>

              <div className="mt-4 p-3 bg-blue-500/10 rounded-lg">
                <div className="text-xs text-blue-300 mb-2">Growth Factors:</div>
                <div className="text-xs space-y-1">
                  <div>Base Rate: {((inurtiaBalance?.compoundRate || 0.01) * 100).toFixed(2)}%</div>
                  <div>Activity Bonus: {dailyScore > 1 ? '+50%' : '0%'}</div>
                  <div>Community Bonus: +{(((communityMetrics?.joyMultiplier || 1.0) - 1) * 100).toFixed(0)}%</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Reward Redemption */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Gift className="w-5 h-5" />
              <span>Redeem inUrtia</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {rewardOptions.map((reward) => {
                const IconComponent = reward.icon;
                const canAfford = currentBalance >= reward.cost;

                return (
                  <motion.div
                    key={reward.type}
                    whileHover={{ scale: canAfford ? 1.02 : 1 }}
                    className={`p-3 border rounded-lg transition-all ${
                      canAfford 
                        ? 'border-green-500/30 bg-green-500/5 cursor-pointer' 
                        : 'border-gray-700/30 bg-gray-500/5 opacity-60'
                    }`}
                    onClick={() => canAfford && setSelectedReward(reward.type)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <IconComponent className="w-6 h-6 text-blue-400" />
                        <div>
                          <h4 className="font-medium text-sm">{reward.name}</h4>
                          <p className="text-xs text-muted-foreground">{reward.description}</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {reward.cost.toFixed(3)} inUrtia
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              +{reward.reward}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      {selectedReward === reward.type ? (
                        <Button
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRedemption(reward.type, reward.cost, reward.reward);
                          }}
                          disabled={redeemMutation.isPending}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          {redeemMutation.isPending ? '...' : 'Redeem'}
                        </Button>
                      ) : (
                        <Button
                          variant="ghost"
                          size="sm"
                          disabled={!canAfford}
                          className="text-xs"
                        >
                          Select
                        </Button>
                      )}
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* nUmentum History Chart */}
      {numentumHistory && Array.isArray(numentumHistory) && numentumHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <History className="w-5 h-5" />
              <span>nUmentum Trend (7 days)</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-32 flex items-end justify-between space-x-1">
              {(numentumHistory as NumentumActivity[]).slice(-7).map((activity: NumentumActivity, index) => {
                const height = Math.max(4, (activity.dailyScore / Math.max(...(numentumHistory as NumentumActivity[]).map((a: NumentumActivity) => a.dailyScore))) * 100);
                return (
                  <motion.div
                    key={activity.id}
                    initial={{ height: 0 }}
                    animate={{ height: `${height}%` }}
                    transition={{ delay: index * 0.1 }}
                    className={`flex-1 rounded-t transition-colors ${
                      activity.joyModifier > 1 
                        ? 'bg-gradient-to-t from-pink-500 to-purple-500' 
                        : 'bg-gradient-to-t from-blue-500 to-green-500'
                    }`}
                    title={`${new Date(activity.trackedAt).toLocaleDateString()}: ${activity.dailyScore.toFixed(3)} nUmentum`}
                  />
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Redemptions */}
      {redemptions && Array.isArray(redemptions) && redemptions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <History className="w-5 h-5" />
              <span>Recent Redemptions</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {(redemptions as InurtiaRedemption[]).slice(0, 5).map((redemption: InurtiaRedemption) => (
                <div key={redemption.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                  <div>
                    <p className="font-medium">{redemption.rewardType.replace('_', ' ')}</p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(redemption.redeemedAt).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">-{redemption.inurtiaSpent.toFixed(3)} inUrtia</p>
                    <p className="text-xs text-green-400">+{redemption.rewardAmount} reward</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}