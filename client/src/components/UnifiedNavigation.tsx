import { useState } from "react";
import { useLocation, Link } from "wouter";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { 
  Home, 
  Wallet, 
  Building2, 
  Zap, 
  Database, 
  Activity, 
  Store, 
  Cpu, 
  Atom, 
  Puzzle, 
  Search, 
  DollarSign, 
  Users, 
  PartyPopper, 
  TrendingUp, 
  Link as LinkIcon,
  Menu,
  X,
  Battery,
  Bot
} from "lucide-react";

interface NavItem {
  path: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  description?: string;
  category: 'core' | 'energy' | 'marketplace' | 'tools' | 'social';
}

const navigationItems: NavItem[] = [
  // Core Platform
  { path: "/", label: "Dashboard", icon: Home, category: "core", description: "Quantum command center" },
  { path: "/energy-hub", label: "Energy Hub", icon: Zap, category: "core", description: "All energy management unified", badge: "ALL-IN-ONE" },
  
  // Energy System - Individual Access Points
  { path: "/wallet", label: "Energy Wallet", icon: Wallet, category: "energy", description: "Send, convert & stake tokens", badge: "LIVE" },
  { path: "/trading", label: "Energy Trading", icon: TrendingUp, category: "energy", description: "Trade energy tokens", badge: "ACTIVE" },
  { path: "/energy-banking", label: "Energy Banking", icon: Building2, category: "energy", description: "Persistent energy accounts" },
  { path: "/energy-storage", label: "Energy Storage", icon: Battery, category: "energy", description: "Energy pools & storage" },
  { path: "/energy-metrics", label: "Energy Metrics", icon: Activity, category: "energy", description: "Real-time tracking" },
  
  // Marketplace
  { path: "/marketplace", label: "Energy Marketplace", icon: Store, category: "marketplace", description: "AI models, algorithms & tools", badge: "8 ITEMS" },
  { path: "/data-marketplace", label: "Data Market", icon: Database, category: "marketplace", description: "Monetize your data" },
  { path: "/quantum-marketplace", label: "Quantum Market", icon: Cpu, category: "marketplace", description: "Quantum computing power" },
  { path: "/nu-quantum", label: "nU Quantum", icon: Atom, category: "marketplace", description: "Quantum algorithms" },
  
  // Tools & Extensions
  { path: "/extensions", label: "Extensions", icon: Puzzle, category: "tools", description: "Browser extension hub" },
  { path: "/ai-search", label: "AI Search", icon: Search, category: "tools", description: "Intelligent discovery" },
  { path: "/data-monetization", label: "Data Monetization", icon: DollarSign, category: "tools", description: "Earn from your data" },
  { path: "/spunder-butler", label: "SpUnder Butler", icon: Bot, category: "tools", description: "Autonomous AI system butler", badge: "AI AGENT" },
  
  // Social & Collaboration
  { path: "/social-sync", label: "Social Sync", icon: Users, category: "social", description: "Connect and collaborate" },
  { path: "/party-portal", label: "Party Portal", icon: PartyPopper, category: "social", description: "Group experiences" },
  { path: "/inurtia", label: "InUrtia", icon: TrendingUp, category: "social", description: "Momentum tracking" },
  { path: "/link-vibe", label: "Link Vibe", icon: LinkIcon, category: "social", description: "Connection insights" },
];

const categoryLabels = {
  core: "Core Platform",
  energy: "Energy System", 
  marketplace: "Marketplace",
  tools: "Tools & Extensions",
  social: "Social & Collaboration"
};

export function UnifiedNavigation() {
  const [location] = useLocation();
  const [mobileOpen, setMobileOpen] = useState(false);

  // Group navigation items by category
  const groupedItems = navigationItems.reduce((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {} as Record<string, NavItem[]>);

  const NavContent = ({ mobile = false }) => {
    return (
      <div className={`${mobile ? 'p-4' : 'p-6'} space-y-6 bg-gray-900 min-h-full`}>
        {/* Header */}
        <div className="flex items-center space-x-3 mb-8">
          <div className="w-8 h-8 bg-gradient-to-br from-cyan-500 via-blue-600 to-purple-700 rounded-lg flex items-center justify-center shadow-lg">
            <Zap className="h-4 w-4 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
              nU Universe
            </h1>
            <p className="text-xs text-gray-400">
              Quantum Energy Platform
            </p>
          </div>
        </div>

        {/* Navigation Categories */}
        {Object.entries(groupedItems).map(([category, items]) => (
          <div key={category} className="space-y-2">
            <h3 className="text-xs font-semibold uppercase tracking-wider text-gray-500 px-2">
              {categoryLabels[category as keyof typeof categoryLabels]}
            </h3>
            <div className="space-y-1">
              {items.map((item) => {
                const isActive = location === item.path;
                const Icon = item.icon;
                
                return (
                  <Link
                    key={item.path}
                    href={item.path}
                  >
                    <div
                      className={`
                        flex items-center justify-between px-3 py-2.5 rounded-lg transition-all duration-200 group cursor-pointer
                        ${isActive 
                          ? 'bg-gradient-to-r from-cyan-500/20 via-blue-600/20 to-purple-700/20 text-cyan-400 shadow-md border border-cyan-500/30' 
                          : 'text-gray-300 hover:bg-gray-800 hover:text-white'
                        }
                      `}
                      onClick={() => mobile && setMobileOpen(false)}
                    >
                      <div className="flex items-center space-x-3">
                        <Icon className={`h-5 w-5 transition-colors ${isActive ? 'text-cyan-400' : 'text-gray-400 group-hover:text-gray-300'}`} />
                        <div className="flex-1">
                          <div className={`font-medium text-sm ${isActive ? 'text-cyan-400' : 'text-white'}`}>
                            {item.label}
                          </div>
                          <div className="text-xs text-gray-400 truncate">
                            {item.description}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {item.badge && (
                          <Badge 
                            variant="secondary" 
                            className={`text-xs px-2 py-0.5 ${
                              isActive 
                                ? 'bg-cyan-500/20 text-cyan-300 border-cyan-500/30' 
                                : 'bg-gray-800 text-gray-300'
                            }`}
                          >
                            {item.badge}
                          </Badge>
                        )}
                        {isActive && (
                          <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse" />
                        )}
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          </div>
        ))}

        {/* Extension Status */}
        <div className="pt-4 border-t border-gray-700">
          <div className="bg-gradient-to-r from-green-900/20 to-emerald-900/20 border border-green-700 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-green-300">Extension Active</span>
            </div>
            <p className="text-xs text-green-400 mt-1">
              Generating energy from browsing
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:flex lg:w-72 lg:flex-col lg:fixed lg:inset-y-0 lg:border-r lg:border-gray-800 lg:bg-gray-900">
        <div className="flex-1 flex flex-col min-h-0 overflow-y-auto">
          <NavContent />
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="lg:hidden">
        {/* Mobile Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-800 bg-gray-900">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-gradient-to-br from-blue-600 to-purple-600 rounded-md flex items-center justify-center">
              <Zap className="h-3 w-3 text-white" />
            </div>
            <h1 className="text-lg font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              nU Universe
            </h1>
          </div>
          
          <Sheet open={mobileOpen} onOpenChange={setMobileOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm">
                <Menu className="h-5 w-5 text-white" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80 p-0 bg-gray-900 border-gray-800">
              <div className="flex items-center justify-between p-4 border-b border-gray-800">
                <h2 className="text-lg font-semibold text-white">Navigation</h2>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setMobileOpen(false)}
                  className="text-white"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <div className="overflow-y-auto h-full">
                <NavContent mobile />
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>

      {/* Mobile Bottom Navigation (Quick Access) */}
      <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-800 px-4 py-2 z-50">
        <div className="flex items-center justify-around">
          {[
            { path: "/", icon: Home, label: "Home" },
            { path: "/energy-hub", icon: Zap, label: "Energy" },
            { path: "/wallet", icon: Wallet, label: "Wallet" },
            { path: "/data-marketplace", icon: Store, label: "Market" },
          ].map((item) => {
            const isActive = location === item.path;
            const Icon = item.icon;
            
            return (
              <Link key={item.path} href={item.path}>
                <div className={`flex flex-col items-center space-y-1 p-2 rounded-lg ${
                  isActive ? 'text-cyan-400' : 'text-gray-400'
                }`}>
                  <Icon className="h-5 w-5" />
                  <span className="text-xs font-medium">{item.label}</span>
                </div>
              </Link>
            );
          })}
        </div>
      </div>
    </>
  );
}

// Hook for page information based on current location
export function usePageInfo() {
  const [location] = useLocation();
  
  const currentPage = navigationItems.find(item => item.path === location);
  
  const breadcrumbs = [];
  if (currentPage) {
    breadcrumbs.push({ path: "/", label: "Home" });
    if (currentPage.path !== "/") {
      breadcrumbs.push({ path: currentPage.path, label: currentPage.label });
    }
  }
  
  return {
    title: currentPage?.label || "Dashboard",
    description: currentPage?.description || "Quantum command center",
    breadcrumbs
  };
}