
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function ConsoleLogger() {
  const [logs, setLogs] = useState<string[]>([]);

  useEffect(() => {
    // Intercept console.log and console.warn
    const originalLog = console.log;
    const originalWarn = console.warn;

    console.log = (...args) => {
      originalLog(...args);
      const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' ');
      setLogs(prev => [...prev.slice(-9), `${new Date().toLocaleTimeString()} LOG: ${message}`]);
    };

    console.warn = (...args) => {
      originalWarn(...args);
      const message = args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)).join(' ');
      setLogs(prev => [...prev.slice(-9), `${new Date().toLocaleTimeString()} WARN: ${message}`]);
    };

    return () => {
      console.log = originalLog;
      console.warn = originalWarn;
    };
  }, []);

  return (
    <Card className="border-blue-500/30 bg-gray-900/50">
      <CardHeader>
        <CardTitle className="text-blue-400 text-sm">🔍 Live Console</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-1 max-h-40 overflow-y-auto">
          {logs.length === 0 && (
            <div className="text-gray-500 text-xs">Waiting for logs...</div>
          )}
          {logs.map((log, index) => (
            <div key={index} className="text-xs text-green-400 font-mono bg-black/30 p-1 rounded">
              {log}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
