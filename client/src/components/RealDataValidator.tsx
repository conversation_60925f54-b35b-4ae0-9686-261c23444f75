
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertTriangle, Activity } from 'lucide-react';

interface APIStatus {
  endpoint: string;
  status: 'connected' | 'error' | 'checking';
  lastResponse: any;
  responseTime: number;
  hasRealData: boolean;
}

export function RealDataValidator() {
  const [apiStatuses, setApiStatuses] = useState<APIStatus[]>([]);

  useEffect(() => {
    const checkAPIs = async () => {
      const endpoints = [
        '/api/extension/status',
        '/api/energy/metrics', 
        '/api/web-ads/recent',
        '/api/wallet/balance',
        '/api/energy/real-hardware-metrics'
      ];

      const results: APIStatus[] = [];

      for (const endpoint of endpoints) {
        const startTime = performance.now();
        try {
          const response = await fetch(endpoint);
          const endTime = performance.now();
          const data = await response.json();
          
          const hasRealData = data && Object.keys(data).length > 0 && 
            !Object.values(data).every(v => v === 0 || v === false || v === '' || v === null);

          results.push({
            endpoint,
            status: response.ok ? 'connected' : 'error',
            lastResponse: data,
            responseTime: Math.round(endTime - startTime),
            hasRealData
          });
        } catch (error) {
          const endTime = performance.now();
          results.push({
            endpoint,
            status: 'error',
            lastResponse: { error: error.message },
            responseTime: Math.round(endTime - startTime),
            hasRealData: false
          });
        }
      }

      setApiStatuses(results);
    };

    checkAPIs();
    const interval = setInterval(checkAPIs, 5000);
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: APIStatus) => {
    if (status.status === 'error') return <XCircle className="w-4 h-4 text-red-500" />;
    if (!status.hasRealData) return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    return <CheckCircle className="w-4 h-4 text-green-500" />;
  };

  const getStatusColor = (status: APIStatus) => {
    if (status.status === 'error') return 'destructive';
    if (!status.hasRealData) return 'secondary';
    return 'default';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="w-5 h-5" />
          Real Data Validation
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {apiStatuses.map((status) => (
            <div key={status.endpoint} className="flex items-center justify-between p-2 border rounded">
              <div className="flex items-center gap-2">
                {getStatusIcon(status)}
                <span className="text-sm font-mono">{status.endpoint}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs text-muted-foreground">{status.responseTime}ms</span>
                <Badge variant={getStatusColor(status)}>
                  {status.hasRealData ? 'Real Data' : 'No Data'}
                </Badge>
              </div>
            </div>
          ))}
        </div>
        <div className="mt-4 p-2 bg-muted rounded text-xs">
          <strong>Debug:</strong> Check that APIs return actual data, not zeros/nulls
        </div>
      </CardContent>
    </Card>
  );
}
