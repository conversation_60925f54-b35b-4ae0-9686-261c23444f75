import { useEffect, useState } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Wifi, WifiOff, Zap, Database, RefreshCw } from 'lucide-react';

export function RealTimeSync() {
  const [syncStatus, setSyncStatus] = useState('connecting');
  const [lastSync, setLastSync] = useState(Date.now());
  const queryClient = useQueryClient();

  // Force real-time extension status updates
  const { data: extensionStatus } = useQuery({
    queryKey: ['/api/extension/status'],
    refetchInterval: 2000,
    retry: false,
    refetchOnWindowFocus: false
  });

  const { data: webAds } = useQuery({
    queryKey: ['/api/web-ads/recent'],
    refetchInterval: 2000,
    retry: false,
    refetchOnWindowFocus: false
  });

  const { data: energyMetrics } = useQuery({
    queryKey: ['/api/energy/metrics'],
    refetchInterval: 3000,
    retry: false,
    refetchOnWindowFocus: false
  });

  useEffect(() => {
    const hasData = extensionStatus?.connected || webAds?.totalCount > 0 || energyMetrics?.totalEnergyGenerated > 0;
    setSyncStatus(hasData ? 'connected' : 'searching');
    setLastSync(Date.now());
  }, [extensionStatus, webAds, energyMetrics]);

  const forceSync = async () => {
    setSyncStatus('syncing');
    await queryClient.invalidateQueries();
    setTimeout(() => setSyncStatus('connected'), 1000);
  };

  const getSyncIcon = () => {
    switch (syncStatus) {
      case 'connected': return <Wifi className="w-4 h-4 text-green-400" />;
      case 'syncing': return <RefreshCw className="w-4 h-4 text-blue-400 animate-spin" />;
      default: return <WifiOff className="w-4 h-4 text-red-400" />;
    }
  };

  const getTotalUMatter = () => {
    const extensionUMatter = extensionStatus?.totalUMatter || webAds?.totalUMatter || 0;
    const energyUMatter = energyMetrics?.totalEnergyGenerated || 0;
    return extensionUMatter + energyUMatter;
  };

  return (
    <Card className="border-cyan-500/30 bg-gradient-to-r from-cyan-900/20 to-blue-900/20 backdrop-blur-sm">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getSyncIcon()}
            <div>
              <div className="text-sm font-semibold text-white">
                nU Universe Sync
              </div>
              <div className="text-xs text-gray-400">
                {syncStatus === 'connected' ? 'Live' : syncStatus === 'syncing' ? 'Syncing...' : 'Searching...'}
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="text-right">
              <div className="text-xs text-gray-400">Total UMatter</div>
              <div className="text-lg font-bold text-cyan-400 font-mono">
                {getTotalUMatter().toFixed(8)}
              </div>
            </div>
            
            <Badge 
              variant={syncStatus === 'connected' ? 'default' : 'secondary'}
              className={syncStatus === 'connected' ? 'bg-green-500/20 text-green-400 border-green-500/30' : ''}
            >
              {syncStatus === 'connected' ? 'LIVE' : 'OFFLINE'}
            </Badge>
            
            <Button variant="outline" size="sm" onClick={forceSync} disabled={syncStatus === 'syncing'}>
              <RefreshCw className={`w-3 h-3 ${syncStatus === 'syncing' ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
        
        {syncStatus === 'connected' && (
          <div className="mt-3 pt-3 border-t border-gray-700">
            <div className="grid grid-cols-3 gap-4 text-xs">
              <div className="text-center">
                <div className="text-green-400 font-bold">
                  {extensionStatus?.totalAdsBlocked || webAds?.totalCount || 0}
                </div>
                <div className="text-gray-400">Ads Blocked</div>
              </div>
              <div className="text-center">
                <div className="text-blue-400 font-bold">
                  {energyMetrics?.fabricNodes || 1}
                </div>
                <div className="text-gray-400">Fabric Nodes</div>
              </div>
              <div className="text-center">
                <div className="text-purple-400 font-bold">
                  {((Date.now() - lastSync) / 1000).toFixed(0)}s
                </div>
                <div className="text-gray-400">Last Sync</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}