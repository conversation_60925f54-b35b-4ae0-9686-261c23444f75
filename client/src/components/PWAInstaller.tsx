import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Download, 
  Smartphone, 
  Monitor, 
  CheckCircle,
  QrCode,
  Share,
  X
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

export function PWAInstaller() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [showInstructions, setShowInstructions] = useState(false);
  const [deviceType, setDeviceType] = useState<'desktop' | 'mobile' | 'tablet'>('desktop');

  useEffect(() => {
    // Detect device type
    const userAgent = navigator.userAgent;
    if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
      if (/iPad|Android(?=.*Tablet)/i.test(userAgent)) {
        setDeviceType('tablet');
      } else {
        setDeviceType('mobile');
      }
    }

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      setIsInstallable(true);
    };

    // Check if app is already installed
    const checkIfInstalled = () => {
      if (window.matchMedia('(display-mode: standalone)').matches || 
          (window.navigator as any).standalone === true) {
        setIsInstalled(true);
      }
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', () => {
      setIsInstalled(true);
      setIsInstallable(false);
      setDeferredPrompt(null);
    });

    checkIfInstalled();

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) {
      setShowInstructions(true);
      return;
    }

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        setIsInstalled(true);
      }
      
      setDeferredPrompt(null);
      setIsInstallable(false);
    } catch (error) {
      console.error('Installation failed:', error);
      setShowInstructions(true);
    }
  };

  const shareApp = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'nU Universe - Energy Tracking Platform',
          text: 'Track and monetize your digital energy with nU Universe',
          url: window.location.href
        });
      } catch (error) {
        console.log('Share failed, showing manual options');
        setShowInstructions(true);
      }
    } else {
      setShowInstructions(true);
    }
  };

  const generateQRCode = () => {
    const url = window.location.href;
    const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(url)}`;
    window.open(qrUrl, '_blank');
  };

  if (isInstalled) {
    return (
      <Card className="bg-green-900/20 border-green-600/30">
        <CardContent className="p-4">
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-5 h-5 text-green-400" />
            <div>
              <p className="font-medium text-green-400">App Installed</p>
              <p className="text-sm text-gray-300">nU Universe is ready for offline use</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="bg-gray-800/50 border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-blue-400">
            <Smartphone className="w-5 h-5" />
            <span>Install nU Universe</span>
            <Badge variant="outline" className="bg-blue-900/30 text-blue-400">
              PWA Available
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-300 text-sm">
            Install nU Universe as a Progressive Web App for faster access, offline functionality, and native-like experience.
          </p>

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            {isInstallable && (
              <Button 
                onClick={handleInstallClick}
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Download className="w-4 h-4 mr-2" />
                Install Now
              </Button>
            )}
            
            <Button 
              onClick={shareApp}
              variant="outline"
              className="border-gray-600 text-gray-300 hover:bg-gray-800"
            >
              <Share className="w-4 h-4 mr-2" />
              Share
            </Button>
            
            <Button 
              onClick={generateQRCode}
              variant="outline"
              className="border-gray-600 text-gray-300 hover:bg-gray-800"
            >
              <QrCode className="w-4 h-4 mr-2" />
              QR Code
            </Button>
          </div>

          {!isInstallable && (
            <Button 
              onClick={() => setShowInstructions(true)}
              variant="outline"
              className="w-full border-gray-600 text-gray-300 hover:bg-gray-800"
            >
              <Download className="w-4 h-4 mr-2" />
              Manual Installation
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Installation Instructions Modal */}
      <AnimatePresence>
        {showInstructions && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowInstructions(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 border border-gray-700 rounded-lg p-6 max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">Install Instructions</h3>
                <Button 
                  variant="ghost" 
                  size="sm"
                  onClick={() => setShowInstructions(false)}
                  className="text-gray-400 hover:text-white"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              {deviceType === 'mobile' && (
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-green-400 mb-2 flex items-center">
                      <Smartphone className="w-4 h-4 mr-2" />
                      iOS (Safari)
                    </h4>
                    <ol className="text-sm text-gray-300 space-y-1 list-decimal list-inside">
                      <li>Tap the Share button at the bottom</li>
                      <li>Scroll down and tap "Add to Home Screen"</li>
                      <li>Tap "Add" to confirm installation</li>
                    </ol>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-blue-400 mb-2 flex items-center">
                      <Smartphone className="w-4 h-4 mr-2" />
                      Android (Chrome)
                    </h4>
                    <ol className="text-sm text-gray-300 space-y-1 list-decimal list-inside">
                      <li>Tap the menu (⋮) at the top right</li>
                      <li>Select "Add to Home screen"</li>
                      <li>Tap "Add" to confirm installation</li>
                    </ol>
                  </div>
                </div>
              )}

              {deviceType === 'desktop' && (
                <div>
                  <h4 className="font-medium text-blue-400 mb-2 flex items-center">
                    <Monitor className="w-4 h-4 mr-2" />
                    Desktop Browser
                  </h4>
                  <ol className="text-sm text-gray-300 space-y-1 list-decimal list-inside">
                    <li>Look for the install icon in the address bar</li>
                    <li>Click the install button when it appears</li>
                    <li>Or use Chrome menu → "Install nU Universe..."</li>
                  </ol>
                </div>
              )}

              <div className="mt-6 pt-4 border-t border-gray-700">
                <p className="text-xs text-gray-400 text-center">
                  After installation, you can access nU Universe directly from your home screen or desktop
                </p>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}