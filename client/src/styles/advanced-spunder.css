/* Advanced SpUnder Butler CSS with Cutting-Edge Effects */

/* Glass Morphism Cards */
.glass-morphism {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Quantum Pulse Animation */
@keyframes quantum-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

.quantum-pulse {
  animation: quantum-pulse 2s infinite;
}

/* Holographic Button Effect */
.holographic-btn {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.holographic-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.holographic-btn:hover::before {
  left: 100%;
}

.holographic-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

/* Neon Glow Effects */
.neon-blue {
  text-shadow: 0 0 5px #00bcd4, 0 0 10px #00bcd4, 0 0 15px #00bcd4;
  color: #00bcd4;
}

.neon-green {
  text-shadow: 0 0 5px #4ade80, 0 0 10px #4ade80, 0 0 15px #4ade80;
  color: #4ade80;
}

.neon-purple {
  text-shadow: 0 0 5px #a855f7, 0 0 10px #a855f7, 0 0 15px #a855f7;
  color: #a855f7;
}

/* Matrix Rain Effect */
@keyframes matrix-rain {
  0% {
    transform: translateY(-100vh);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

.matrix-bg {
  position: relative;
  overflow: hidden;
}

.matrix-bg::before {
  content: '01010101010101010101010101010101010101010101';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  color: rgba(34, 197, 94, 0.1);
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.2;
  animation: matrix-rain 10s linear infinite;
  pointer-events: none;
}

/* Quantum Energy Bars */
.energy-bar {
  background: linear-gradient(90deg, #1e40af, #3b82f6, #60a5fa);
  position: relative;
  overflow: hidden;
}

.energy-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: energy-flow 2s linear infinite;
}

@keyframes energy-flow {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Floating Particles */
@keyframes particle-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.5;
  }
}

.floating-particle {
  animation: particle-float 3s ease-in-out infinite;
}

/* Capability Card Hover Effects */
.capability-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

.capability-card:hover {
  transform: translateY(-5px) rotateX(5deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Control Panel Switches */
.quantum-switch {
  position: relative;
  background: linear-gradient(45deg, #1e293b, #334155);
  border: 1px solid #475569;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.quantum-switch[data-state="checked"] {
  background: linear-gradient(45deg, #059669, #10b981);
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.5);
}

/* System Status Indicators */
.status-indicator {
  position: relative;
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.active::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  border: 2px solid currentColor;
  animation: status-pulse 1.5s ease-in-out infinite;
}

@keyframes status-pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* Advanced Progress Bars */
.advanced-progress {
  background: linear-gradient(90deg, #1e293b, #334155);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.advanced-progress-fill {
  background: linear-gradient(90deg, #3b82f6, #60a5fa, #93c5fd);
  height: 100%;
  border-radius: 10px;
  position: relative;
  overflow: hidden;
}

.advanced-progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  animation: progress-shine 2s ease-in-out infinite;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Interactive Button Enhancements */
.interactive-btn {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.interactive-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.interactive-btn:hover::before {
  width: 300px;
  height: 300px;
}

.interactive-btn:active {
  transform: scale(0.95);
}

/* Task Priority Indicators */
.priority-high {
  border-left: 4px solid #ef4444;
  background: linear-gradient(90deg, rgba(239, 68, 68, 0.1), transparent);
}

.priority-medium {
  border-left: 4px solid #f59e0b;
  background: linear-gradient(90deg, rgba(245, 158, 11, 0.1), transparent);
}

.priority-low {
  border-left: 4px solid #10b981;
  background: linear-gradient(90deg, rgba(16, 185, 129, 0.1), transparent);
}

/* Live Data Streams */
.data-stream {
  font-family: 'Courier New', monospace;
  background: #0f172a;
  border: 1px solid #1e293b;
  border-radius: 4px;
  padding: 8px;
  font-size: 12px;
  line-height: 1.4;
  max-height: 200px;
  overflow-y: auto;
}

.data-stream::-webkit-scrollbar {
  width: 4px;
}

.data-stream::-webkit-scrollbar-track {
  background: #1e293b;
}

.data-stream::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 2px;
}

/* Quantum Field Background */
.quantum-field {
  background: 
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(168, 85, 247, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(34, 197, 94, 0.1) 0%, transparent 50%);
  position: relative;
}

.quantum-field::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23475569' fill-opacity='0.05'%3E%3Cpath d='m36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  opacity: 0.1;
  pointer-events: none;
}