import { useState, useEffect } from 'react';
import { realBatteryAPI } from '@/lib/real-battery-api';
import { authenticDeviceManager } from '@/lib/authentic-device-manager';

interface AuthenticEnergyData {
  // AUTHENTIC DATA ONLY - No simulated values
  batteryLevel: number;      // 0-1 from navigator.getBattery()
  isCharging: boolean;       // true/false from navigator.getBattery()
  memoryUsed: number;        // bytes from performance.memory.usedJSHeapSize
  memoryTotal: number;       // bytes from performance.memory.totalJSHeapSize
  networkSpeed: number;      // Mbps from navigator.connection.downlink
  cpuCores: number;          // count from navigator.hardwareConcurrency
  umatterGenerated: number;  // calculated from authentic sources only
  umatterTotal: number;      // accumulated from authentic generation
  lastUpdate: number;        // timestamp of last authentic update
  isRealDevice: boolean;     // true only if all APIs available
}

function generateAuthenticUMatter(batteryLevel: number, memoryUsed: number, networkSpeed: number): number {
  // Only use authentic hardware data for UMatter calculation
  if (!batteryLevel || !memoryUsed || !networkSpeed) return 0;
  
  // Authentic calculation based on real hardware metrics
  const batteryFactor = batteryLevel * 0.4;
  const memoryFactor = (memoryUsed / (1024 * 1024 * 1024)) * 0.3; // GB
  const networkFactor = (networkSpeed / 100) * 0.3; // normalized
  
  return batteryFactor + memoryFactor + networkFactor;
}

export function useAuthenticEnergyData(): AuthenticEnergyData {
  const [data, setData] = useState<AuthenticEnergyData>({
    batteryLevel: 0,
    isCharging: false,
    memoryUsed: 0,
    memoryTotal: 0,
    networkSpeed: 0,
    cpuCores: 0,
    umatterGenerated: 0,
    umatterTotal: 0,
    lastUpdate: 0,
    isRealDevice: false
  });

  useEffect(() => {
    console.log('[AuthenticEnergyData] Starting authentic data collection...');
    
    let cumulativeUMatter = 0;

    // Subscribe to REAL Battery API (primary source)
    const batteryUnsubscribe = realBatteryAPI.subscribe((batteryData) => {
      if (!batteryData.isRealDevice) return; // Only authentic data
      
      setData(prev => ({
        ...prev,
        batteryLevel: batteryData.level,
        isCharging: batteryData.charging,
        lastUpdate: Date.now(),
        isRealDevice: batteryData.isRealDevice
      }));
    });

    // Subscribe to Authentic Device Manager for memory/network/CPU
    const deviceUnsubscribe = authenticDeviceManager.subscribe((metrics) => {
      const memoryUsed = metrics.memory.usedJSHeapSize;
      const memoryTotal = metrics.memory.totalJSHeapSize;
      const networkSpeed = metrics.network.downlink;
      const cpuCores = metrics.hardware.cores;

      // Generate UMatter from authentic sources only
      const currentBattery = data.batteryLevel || 0;
      const umatterGenerated = generateAuthenticUMatter(currentBattery, memoryUsed, networkSpeed);
      cumulativeUMatter += umatterGenerated;

      setData(prev => ({
        ...prev,
        memoryUsed,
        memoryTotal,
        networkSpeed,
        cpuCores,
        umatterGenerated,
        umatterTotal: cumulativeUMatter,
        lastUpdate: Date.now(),
        isRealDevice: true
      }));

      // Dispatch event for other components
      window.dispatchEvent(new CustomEvent('authentic-energy-update', {
        detail: {
          batteryLevel: currentBattery,
          isCharging: data.isCharging,
          memoryUsed,
          networkSpeed,
          cpuCores,
          umatterGenerated,
          umatterTotal: cumulativeUMatter
        }
      }));

      console.log('[AuthenticEnergyData] Update:', {
        battery: `${(currentBattery * 100).toFixed(1)}%`,
        charging: data.isCharging ? 'YES' : 'NO',
        memory: `${(memoryUsed / 1024 / 1024).toFixed(1)}MB`,
        network: `${networkSpeed}Mbps`,
        cores: cpuCores,
        umatter: umatterGenerated.toFixed(6),
        total: cumulativeUMatter.toFixed(6)
      });
    });

    return () => {
      batteryUnsubscribe();
      deviceUnsubscribe();
    };
  }, [data.batteryLevel, data.isCharging]);

  return data;
}