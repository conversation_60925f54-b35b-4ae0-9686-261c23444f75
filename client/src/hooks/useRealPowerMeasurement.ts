
import { useState, useEffect } from 'react';
import { realHardwareEnergyMeasurement, RealPowerMeasurement } from '../lib/real-hardware-energy-measurement';

interface RealPowerData {
  clientPower: RealPowerMeasurement | null;
  serverPower: any;
  totalUMatter: number;
  isRealMeasurement: boolean;
  lastUpdate: number;
}

export function useRealPowerMeasurement(): RealPowerData {
  const [data, setData] = useState<RealPowerData>({
    clientPower: null,
    serverPower: null,
    totalUMatter: 0,
    isRealMeasurement: false,
    lastUpdate: 0
  });

  useEffect(() => {
    let measurementInterval: NodeJS.Timeout;

    const startRealMeasurement = async () => {
      console.log('[RealPowerMeasurement] Starting ACTUAL hardware power measurement...');
      
      // Initialize real hardware measurement
      const initialized = await realHardwareEnergyMeasurement.initialize();
      
      if (initialized) {
        measurementInterval = setInterval(async () => {
          try {
            // Measure actual client-side power consumption
            const clientPower = await realHardwareEnergyMeasurement.measureRealPowerConsumption();
            
            // Fetch actual server-side power consumption
            const serverResponse = await fetch('/api/energy/real-server-power');
            const serverPower = serverResponse.ok ? await serverResponse.json() : null;
            
            if (clientPower.isRealMeasurement || (serverPower && serverPower.authentic)) {
              const clientUMatter = realHardwareEnergyMeasurement.convertWattsToUMatter(clientPower.totalWatts);
              const serverUMatter = serverPower ? serverPower.umatterGenerated : 0;
              
              setData({
                clientPower,
                serverPower: serverPower?.powerMeasurement || null,
                totalUMatter: clientUMatter + serverUMatter,
                isRealMeasurement: true,
                lastUpdate: Date.now()
              });

              console.log(`[RealPowerMeasurement] Client: ${clientPower.totalWatts.toFixed(3)}W, Server: ${serverPower?.powerMeasurement?.totalPowerWatts?.toFixed(3) || 0}W`);
              console.log(`[RealPowerMeasurement] Generated: ${(clientUMatter + serverUMatter).toFixed(6)} UMatter from REAL power consumption`);
            }
          } catch (error) {
            console.error('[RealPowerMeasurement] Measurement failed:', error);
          }
        }, 5000); // Measure every 5 seconds
      } else {
        console.warn('[RealPowerMeasurement] Real hardware APIs not available - no simulated data will be provided');
        setData(prev => ({
          ...prev,
          isRealMeasurement: false,
          lastUpdate: Date.now()
        }));
      }
    };

    startRealMeasurement();

    return () => {
      if (measurementInterval) {
        clearInterval(measurementInterval);
      }
    };
  }, []);

  return data;
}
