import { useEffect, useState } from 'react';
import { useRealTimeData } from './useRealTimeData';
import { useSimpleNuvaStore } from '@/lib/stores/simpleNuvaStore';

interface UnifiedMetrics {
  batteryLevel: number;
  isCharging: boolean;
  umatter: number;
  networkSpeed: string;
  memoryUsage: string;
  cpuCores: number;
  isConnected: boolean;
  energyEfficiency: number;
  neuralPower: number;
  lastUpdate: number;
}

export function useUnifiedRealTimeData(): UnifiedMetrics {
  const realTimeData = useRealTimeData();
  const { 
    batteryLevel: wsBatteryLevel, 
    isCharging: wsCharging, 
    umatter: wsUmatter, 
    isConnected,
    connectWebSocket 
  } = useSimpleNuvaStore();

  // Ensure WebSocket connection
  useEffect(() => {
    if (!isConnected) {
      connectWebSocket();
    }
  }, [isConnected, connectWebSocket]);

  // Combine authentic data from multiple sources
  const batteryLevel = wsBatteryLevel || realTimeData.batteryLevel || 0;
  const isCharging = wsCharging !== undefined ? wsCharging : realTimeData.isCharging;
  const totalUmatter = (wsUmatter || 0) + (realTimeData.umatterTotal || 0);

  return {
    batteryLevel,
    isCharging,
    umatter: isNaN(totalUmatter) ? 0 : totalUmatter,
    networkSpeed: realTimeData.networkSpeed || '0Mbps',
    memoryUsage: realTimeData.memoryUsage || '0MB',
    cpuCores: realTimeData.cpuCores || 4,
    isConnected,
    energyEfficiency: Math.round(batteryLevel * 100),
    neuralPower: batteryLevel > 0.5 ? 22 : 18,
    lastUpdate: Date.now()
  };
}