import { useState, useEffect, useCallback } from 'react';
import { spunder, SpUnderConfig, SpUnderInteraction } from '@/lib/spunder';
import { memvidStorage } from '@/lib/memvid';
import { useAuth } from './useAuth';

export interface InteractionTrackingState {
  isTracking: boolean;
  sessionId: string | null;
  interactionCount: number;
  lastInteraction: SpUnderInteraction | null;
  privacyMode: boolean;
  encryptionEnabled: boolean;
}

export function useInteractionTracking() {
  const { user, isAuthenticated } = useAuth();
  const [state, setState] = useState<InteractionTrackingState>({
    isTracking: false,
    sessionId: null,
    interactionCount: 0,
    lastInteraction: null,
    privacyMode: false,
    encryptionEnabled: true
  });

  /**
   * Start interaction tracking session
   */
  const startTracking = useCallback(async (config?: Partial<SpUnderConfig>) => {
    try {
      const sessionId = await spunder.startSession(isAuthenticated ? user?.id : undefined);

      if (config) {
        spunder.updateConfig(config);
      }

      setState(prev => ({
        ...prev,
        isTracking: true,
        sessionId,
        interactionCount: 0
      }));

      console.log('[InteractionTracking] Started session:', sessionId);
    } catch (error) {
      console.error('[InteractionTracking] Failed to start tracking:', error);
    }
  }, [isAuthenticated, user?.id]);

  /**
   * Stop interaction tracking session
   */
  const stopTracking = useCallback(async () => {
    try {
      const session = spunder.getCurrentSession();

      if (session) {
        // Store session in Memvid before ending
        await memvidStorage.storeInteractionSession(
          session.sessionId,
          session.interactions,
          {
            userId: session.userId,
            duration: Date.now() - session.startTime,
            interactionCount: session.interactions.length
          }
        );
      }

      await spunder.endSession();

      setState(prev => ({
        ...prev,
        isTracking: false,
        sessionId: null,
        interactionCount: 0,
        lastInteraction: null
      }));

      console.log('[InteractionTracking] Stopped session');
    } catch (error) {
      console.error('[InteractionTracking] Failed to stop tracking:', error);
    }
  }, []);

  /**
   * Manually track an interaction
   */
  const trackInteraction = useCallback((
    type: SpUnderInteraction['type'],
    element?: string,
    metadata?: Record<string, any>
  ) => {
    if (!state.isTracking) return;

    try {
      const interaction = spunder.trackInteraction(type, element, metadata);

      setState(prev => ({
        ...prev,
        interactionCount: prev.interactionCount + 1,
        lastInteraction: interaction
      }));

      return interaction;
    } catch (error) {
      console.error('[InteractionTracking] Failed to track interaction:', error);
    }
  }, [state.isTracking]);

  /**
   * Update privacy settings
   */
  const updatePrivacySettings = useCallback((settings: {
    privacyMode?: boolean;
    encryptionEnabled?: boolean;
    anonymousMode?: boolean;
  }) => {
    const config: Partial<SpUnderConfig> = {};

    if (settings.anonymousMode !== undefined) {
      config.anonymousMode = settings.anonymousMode;
    }

    if (settings.encryptionEnabled !== undefined) {
      config.encryptionLevel = settings.encryptionEnabled ? 3 : 1;
    }

    spunder.updateConfig(config);

    setState(prev => ({
      ...prev,
      privacyMode: settings.privacyMode ?? prev.privacyMode,
      encryptionEnabled: settings.encryptionEnabled ?? prev.encryptionEnabled
    }));

    console.log('[InteractionTracking] Updated privacy settings:', settings);
  }, []);

  /**
   * Get session statistics
   */
  const getSessionStats = useCallback(() => {
    return spunder.getSessionStats();
  }, []);

  /**
   * Search interaction history
   */
  const searchInteractions = useCallback(async (query: string, options?: {
    topK?: number;
    threshold?: number;
    sessionId?: string;
  }) => {
    try {
      return await memvidStorage.searchInteractions(query, options);
    } catch (error) {
      console.error('[InteractionTracking] Failed to search interactions:', error);
      return [];
    }
  }, []);

  /**
   * Get interaction patterns
   */
  const getInteractionPatterns = useCallback(async (sessionId?: string) => {
    try {
      return await memvidStorage.getInteractionPatterns(sessionId);
    } catch (error) {
      console.error('[InteractionTracking] Failed to get patterns:', error);
      return {
        totalSessions: 0,
        commonPatterns: [],
        sessionInsights: []
      };
    }
  }, []);

  /**
   * Export interaction history
   */
  const exportHistory = useCallback(async () => {
    try {
      return await memvidStorage.exportInteractionHistory();
    } catch (error) {
      console.error('[InteractionTracking] Failed to export history:', error);
      throw error;
    }
  }, []);

  /**
   * Clear interaction history
   */
  const clearHistory = useCallback(async () => {
    try {
      await memvidStorage.clearInteractionHistory();
      console.log('[InteractionTracking] History cleared');
    } catch (error) {
      console.error('[InteractionTracking] Failed to clear history:', error);
      throw error;
    }
  }, []);

  // Auto-start tracking when user is authenticated
  useEffect(() => {
    if (isAuthenticated && !state.isTracking) {
      startTracking({
        didAuthentication: true,
        encryptionLevel: 3,
        anonymousMode: false,
        silentTracking: true
      });
    }
  }, [isAuthenticated, state.isTracking, startTracking]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (state.isTracking) {
        stopTracking();
      }
    };
  }, [state.isTracking, stopTracking]);

  return {
    ...state,
    startTracking,
    stopTracking,
    trackInteraction,
    updatePrivacySettings,
    getSessionStats,
    searchInteractions,
    getInteractionPatterns,
    exportHistory,
    clearHistory
  };
}