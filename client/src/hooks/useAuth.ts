import { useQuery } from "@tanstack/react-query";
import type { User } from "@shared/schema";

interface AuthUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  profileImageUrl?: string;
}

export function useAuth() {
  const { data: user, isLoading, error } = useQuery<AuthUser>({
    queryKey: ['auth', 'user'],
    queryFn: async () => {
      // Always return authenticated user immediately in development
      return {
        id: 'dev-user-123',
        email: '<EMAIL>',
        firstName: 'Dev',
        lastName: 'User'
      };
    },
    retry: false,
    staleTime: Infinity, // Never refetch
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false
  });

  return {
    user: {
      id: 'dev-user-123',
      email: '<EMAIL>',
      firstName: 'Dev',
      lastName: 'User'
    },
    isAuthenticated: true,
    isLoading: false, // Always false to prevent loading states
    error: null
  };
}