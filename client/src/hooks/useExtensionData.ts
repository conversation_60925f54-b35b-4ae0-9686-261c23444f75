import { useState, useEffect } from 'react';
export function useExtensionData() {
  const [extensionMetrics, setExtensionMetrics] = useState({
    isConnected: false,
    totalUMatter: 0,
    totalAdsBlocked: 0,
    activityCount: 0,
    lastSync: null as Date | null
  });

  useEffect(() => {
    // Check for real browser extension connection
    const checkExtensionConnection = () => {
      if (window.chrome && window.chrome.runtime) {
        try {
          // Try to communicate with the nU Universe extension
          window.chrome.runtime.sendMessage('nu-universe-extension', {
            action: 'getStatus'
          }, (response) => {
            if (response && !window.chrome.runtime.lastError) {
              setExtensionMetrics({
                isConnected: true,
                totalUMatter: response.umatterBalance || 0,
                totalAdsBlocked: response.adsBlocked || 0,
                activityCount: response.activityCount || 0,
                lastSync: new Date()
              });
            }
          });
        } catch (error) {
          console.log('[Extension] No extension detected');
        }
      }
    };

    // Listen for extension messages
    const messageListener = (event: MessageEvent) => {
      if (event.data.source === 'nu-universe-extension') {
        if (event.data.type === 'status_update') {
          setExtensionMetrics(prev => ({
            ...prev,
            isConnected: true,
            totalUMatter: event.data.umatterBalance || prev.totalUMatter,
            totalAdsBlocked: event.data.adsBlocked || prev.totalAdsBlocked,
            activityCount: event.data.activityCount || prev.activityCount,
            lastSync: new Date()
          }));
        }
      }
    };

    window.addEventListener('message', messageListener);

    // Check connection initially and periodically
    checkExtensionConnection();
    const interval = setInterval(checkExtensionConnection, 10000);

    return () => {
      window.removeEventListener('message', messageListener);
      clearInterval(interval);
    };
  }, []);

  return { extensionMetrics };
}