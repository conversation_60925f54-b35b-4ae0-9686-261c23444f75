import { useState, useEffect } from 'react';
import { mobileDeviceManager } from '@/lib/mobile-device-manager';
import { energySyncController } from '@/lib/energy-sync-controller';

interface MobileEnergyData {
  umatterGenerated: number;
  touchInteractions: number;
  motionEnergy: number;
  batteryLevel: number;
  isCharging: boolean;
  networkSpeed: number;
  lastSync: number;
}

export function useMobileSync() {
  const [energyData, setEnergyData] = useState<MobileEnergyData>({
    umatterGenerated: 0,
    touchInteractions: 0,
    motionEnergy: 0,
    batteryLevel: 0,
    isCharging: false,
    networkSpeed: 0,
    lastSync: Date.now()
  });

  const [syncStatus, setSyncStatus] = useState<'connecting' | 'synced' | 'error'>('connecting');

  useEffect(() => {
    // Subscribe to mobile device metrics with batching
    const unsubscribe = mobileDeviceManager.subscribe((metrics) => {
      const now = Date.now();
      
      // Calculate UMatter from mobile interactions
      const umatter = mobileDeviceManager.calculateUMatterFromMobile();
      
      setEnergyData(prev => ({
        ...prev,
        umatterGenerated: prev.umatterGenerated + umatter,
        touchInteractions: metrics.touchInteractions,
        motionEnergy: Math.sqrt(
          metrics.deviceMotion.acceleration.x ** 2 +
          metrics.deviceMotion.acceleration.y ** 2 +
          metrics.deviceMotion.acceleration.z ** 2
        ),
        batteryLevel: metrics.batteryLevel,
        isCharging: metrics.isCharging,
        networkSpeed: metrics.networkConnection.downlink,
        lastSync: now
      }));

      setSyncStatus('synced');

      // Add energy to sync controller for batching
      if (umatter > 0.001) {
        energySyncController.addEnergy('mobile_device', umatter, {
          touchInteractions: metrics.touchInteractions,
          deviceMotion: metrics.deviceMotion,
          batteryLevel: metrics.batteryLevel,
          isCharging: metrics.isCharging,
          networkConnection: metrics.networkConnection
        });
      }
    });

    // Request mobile permissions
    mobileDeviceManager.requestPermissions().catch(console.error);

    return unsubscribe;
  }, []);

  const syncToBackend = async (umatter: number, metrics: any) => {
    try {
      await fetch('/api/mobile-sync/deposit-mobile-umatter', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount: umatter,
          source: 'mobile_device',
          deviceMetrics: {
            touchInteractions: metrics.touchInteractions,
            deviceMotion: metrics.deviceMotion,
            batteryLevel: metrics.batteryLevel,
            isCharging: metrics.isCharging,
            networkConnection: metrics.networkConnection
          },
          timestamp: Date.now()
        })
      });
    } catch (error) {
      console.log('[useMobileSync] Backend sync failed, will retry');
      setSyncStatus('error');
    }
  };

  const triggerManualSync = async () => {
    setSyncStatus('connecting');
    try {
      const currentMetrics = mobileDeviceManager.getCurrentMetrics();
      const umatter = mobileDeviceManager.calculateUMatterFromMobile();
      
      if (umatter > 0) {
        await syncToBackend(umatter, currentMetrics);
        setSyncStatus('synced');
      } else {
        // Force sync even with minimal energy
        await syncToBackend(0.001, currentMetrics);
        setSyncStatus('synced');
      }
    } catch (error) {
      setSyncStatus('error');
    }
  };

  const requestPermissions = async () => {
    await mobileDeviceManager.requestPermissions();
  };

  return {
    energyData,
    syncStatus,
    isReady: mobileDeviceManager.isReady(),
    capabilities: mobileDeviceManager.getCapabilities(),
    triggerManualSync,
    requestPermissions
  };
}