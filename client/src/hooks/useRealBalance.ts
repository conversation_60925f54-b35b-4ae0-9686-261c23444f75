/**
 * Real Balance Hook - Connect to authentic database balance data
 * Eliminates all fake/simulated values, uses only real transaction data
 */
import { useState, useEffect } from 'react';

interface RealBalanceData {
  umatter: number;
  totalGenerated: number;
  lastUpdated: string;
  authentic: boolean;
}

export function useRealBalance() {
  const [balance, setBalance] = useState<RealBalanceData>({
    umatter: 0,
    totalGenerated: 0,
    lastUpdated: '',
    authentic: false
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRealBalance = async () => {
    try {
      // Get real balance from wallet API - connects to actual transaction data
      const response = await fetch('/api/wallet/balance');
      if (!response.ok) {
        throw new Error(`Failed to fetch real balance: ${response.status}`);
      }

      const data = await response.json();

      // Only accept authentic transaction-based balances
      if (data.authentic && data.transactionCount > 0) {
        // Force new object reference every time
        const freshBalance = {
          umatter: Number(data.umatter || 0),
          totalGenerated: Number(data.umatter || 0),
          lastUpdated: new Date().toISOString(),
          authentic: true,
          _timestamp: Date.now(),
          _forceKey: Math.random().toString(36)
        };
        setBalance(freshBalance);
      }
      console.log(`[useRealBalance] Updated balance: ${data.umatter} UMatter from backend`);
      console.warn(`💰 WALLET UPDATE: ${data.umatter} UMatter from ${data.transactionCount} transactions`);

      // Force browser console
      if (typeof window !== 'undefined') {
        window.console.log(`🏦 Balance: ${data.umatter} UMatter`);
        window.console.table({ Balance: data.umatter, Transactions: data.transactionCount });
      }

      // Trigger custom event for other components
      window.dispatchEvent(new CustomEvent('balance-updated', { 
        detail: { umatter: data.umatter } 
      }));

      setError(null);
    } catch (err) {
      console.error('[useRealBalance] Error fetching real balance:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch balance');

      // Do not use fallback data - maintain integrity
      setBalance({
        umatter: 0,
        totalGenerated: 0,
        lastUpdated: '',
        authentic: false
      });
    } finally {
      setIsLoading(false);
    }
  };

  const syncBalance = async () => {
    try {
      console.log('[useRealBalance] Syncing balance with real transaction data...');
      const response = await fetch('/api/balance-fix/sync-real-balance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const result = await response.json();
        console.log('[useRealBalance] Balance synced:', result);

        // Refresh balance after sync
        await fetchRealBalance();
      }
    } catch (err) {
      console.error('[useRealBalance] Error syncing balance:', err);
    }
  };

  useEffect(() => {
    fetchRealBalance();

    // Auto-refresh every 10 seconds to stay current with real data
    const interval = setInterval(fetchRealBalance, 1000);

    // Listen for energy generation events
    const handleEnergyGenerated = () => {
      setTimeout(fetchRealBalance, 1000); // Slight delay for backend processing
    };

    window.addEventListener('umatter-generated', handleEnergyGenerated);

    return () => {
      clearInterval(interval);
      window.removeEventListener('umatter-generated', handleEnergyGenerated);
    };
  }, []);

  return {
    balance: balance.umatter,
    isLoading,
    error,
    refresh: fetchRealBalance,
    syncBalance,
    isAuthentic: balance.authentic
  };
}