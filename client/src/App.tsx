import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { useAuth } from "@/hooks/useAuth";
import './App.css';
import './styles/advanced-spunder.css';

// Import server log receiver for development debugging
if (process.env.NODE_ENV === 'development') {
  import('./lib/server-log-receiver');
}

// Page imports with consistent navigation
import Landing from "@/pages/Landing";
import Dashboard from "@/pages/Dashboard";
import EnergyHub from "@/pages/EnergyHub";
import DataMarketplace from "@/pages/DataMarketplace";
import PartyPortal from "@/pages/PartyPortal";
import EnergyMarketplace from "@/pages/EnergyMarketplace";
import EnergyStorage from "@/pages/EnergyStorage";
import EnergyBanking from "@/pages/EnergyBanking";
import UnifiedWallet from "@/pages/UnifiedWallet";
import Wallet from "@/pages/Wallet";
import Trading from "@/pages/Trading";
import Marketplace from "@/pages/Marketplace";
import SocialSync from "@/pages/SocialSync";
import InvitationAcceptance from "@/pages/InvitationAcceptance";
import InUrtia from "@/pages/InUrtia";
import LinkVibe from "@/pages/LinkVibe";
import DataMonetization from "@/pages/DataMonetization";
import { AISearchPage } from "@/pages/AISearchPage";
import Extensions from "@/pages/Extensions";
import { MobileSyncPage } from "@/pages/MobileSyncPage";
import NuQuantum from "@/pages/NuQuantum";
import QuantumMarketplace from "@/pages/QuantumMarketplace";
import { AdvancedSpUnderDashboard } from "@/components/AdvancedSpUnderDashboard";
import { RealDataIntegrityMonitor } from "@/components/RealDataIntegrityMonitor";

import NotFound from "@/pages/not-found";

function Router() {
  const { isAuthenticated } = useAuth();

  return (
    <ErrorBoundary fallback={
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <h2 className="text-2xl font-bold text-neon-cyan">Loading nU Universe...</h2>
          <p className="text-text-secondary">Initializing quantum systems...</p>
        </div>
      </div>
    }>
      <Switch>
        {/* Core Platform Routes */}
        <Route path="/" component={Dashboard} />
        <Route path="/dashboard" component={Dashboard} />
        
        {/* Energy System Routes */}
        <Route path="/energy-hub" component={EnergyHub} />
        <Route path="/wallet" component={Wallet} />
        <Route path="/unified-wallet" component={UnifiedWallet} />
        <Route path="/trading" component={Trading} />
        <Route path="/marketplace" component={Marketplace} />
        <Route path="/energy-banking" component={EnergyBanking} />
        <Route path="/energy-marketplace" component={EnergyMarketplace} />
        <Route path="/energy-storage" component={EnergyStorage} />
        <Route path="/energy-metrics" component={Dashboard} />
        
        {/* Marketplace Routes */}
        <Route path="/data-marketplace" component={DataMarketplace} />
        <Route path="/marketplace" component={DataMarketplace} />
        <Route path="/quantum-marketplace" component={QuantumMarketplace} />
        <Route path="/nu-quantum" component={NuQuantum} />
        
        {/* Tools & Extensions Routes */}
        <Route path="/extensions" component={Extensions} />
        <Route path="/extension" component={Extensions} />
        <Route path="/mobile-sync" component={MobileSyncPage} />
        <Route path="/ai-search" component={AISearchPage} />
        <Route path="/data-monetization" component={DataMonetization} />
        <Route path="/spunder-butler" component={AdvancedSpUnderDashboard} />
        <Route path="/data-integrity" component={RealDataIntegrityMonitor} />
        
        {/* Social & Collaboration Routes */}
        <Route path="/social-sync" component={SocialSync} />
        <Route path="/sync" component={SocialSync} />
        <Route path="/invite/:invitationId" component={InvitationAcceptance} />
        <Route path="/party-portal" component={PartyPortal} />
        <Route path="/inurtia" component={InUrtia} />
        <Route path="/link-vibe" component={LinkVibe} />
        
        {/* Legacy/Alternative Routes */}
        <Route path="/landing" component={Landing} />
        
        {/* 404 Handler */}
        <Route component={NotFound} />
      </Switch>
    </ErrorBoundary>
  );
}

function App() {
  return (
    <div className="dark">
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Router />
          <Toaster />
        </TooltipProvider>
      </QueryClientProvider>
    </div>
  );
}

export default App;
