<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />
    
    <!-- PWA Configuration -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#06b6d4" />
    <meta name="background-color" content="#0f172a" />
    
    <!-- Mobile-First Meta Tags -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="nU Universe" />
    
    <title>nU Universe - Energy Tracking Platform</title>
    <meta name="description" content="Track and monetize your digital energy with real-time biometric data conversion to UMatter tokens" />
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Service Worker Registration for PWA -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/service-worker.js')
            .then((registration) => {
              console.log('[PWA] Service Worker registered successfully');
            })
            .catch((error) => {
              console.log('[PWA] Service Worker registration failed');
            });
        });
      }
    </script>
  </body>
</html>