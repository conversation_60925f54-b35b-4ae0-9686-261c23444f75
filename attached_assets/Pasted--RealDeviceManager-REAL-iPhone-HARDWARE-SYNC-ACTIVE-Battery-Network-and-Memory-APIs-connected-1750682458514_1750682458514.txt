[RealDeviceManager] ✅ REAL iPhone HARDWARE SYNC ACTIVE - Battery, Network, and Memory APIs connected with live data streaming
[RealTimeDeviceManager] Current device added: 
Object {id: "device-MTQ0MHg5MDBB", name: "Mac<PERSON><PERSON>", type: "laptop", battery: 65, isCharging: false, …}
[RealTimeDeviceManager] USB API initialized with 0 devices
[RealTimeDeviceManager] Bluetooth API available
[RealTimeDeviceManager] WebRTC P2P initialized
[RealTimeDeviceManager] Starting real-time monitoring...
RealTimeEnergyDisplay is not defined
RealTimeEnergyDisplay is not defined
The above error occurred in the <Dashboard> component:

    at Dashboard (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/src/pages/Dashboard.tsx:58:20)
    at Route (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=4c7c94e8:323:16)
    at Switch (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=4c7c94e8:379:17)
    at ErrorBoundary (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/src/components/ErrorBoundary.tsx:10:5)
    at Router (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/src/App.tsx?v=3ohg4Ymu47LYg2gEmrOg1:42:42)
    at Provider (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-PLT6GTVM.js?v=4c7c94e8:38:15)
    at TooltipProvider (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=4c7c94e8:63:5)
    at QueryClientProvider (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@tanstack_react-query.js?v=4c7c94e8:2805:3)
    at App

React will try to recreate this component tree from scratch using the error boundary you provided, ErrorBoundary.
[ErrorBoundary] Component error: 
ReferenceError {}
 
Object {componentStack: "↵    at Dashboard…"}
