[IoTManager] Added device: Living Room Apple TV (speaker)
[IoTManager] Added device: Kitchen HomePod (speaker)
[IoTManager] Added device: Printer (hub)
[IoTManager] UPnP discovery initialized
[IoTManager] Network scanning initialized
[RealTimeDeviceManager] Starting real-time monitoring...
[IoTManager] Zigbee discovery initialized
[IoTManager] Z-Wave discovery initialized
[IoTManager] Added device: Smart Thermostat (thermostat)
[IoTManager] Added device: Smart Light Bulbs (dimmer)
[IoTManager] Added device: Google Nest Hub (hub)
[IoTManager] Added device: Amazon Echo (speaker)
[IoTManager] Added device: Motion Sensor (sensor)
[IoTManager] Added device: Energy Monitor (sensor)
[NuCore] IoT Manager initialized successfully
[PWA] Service Worker registration failed
[AuthenticEnergyData] Starting authentic data collection...
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[MetaAIOrchestrator] Processing task ai_task_1750851544533_z1f64m856 with nU Quantum AI
[MetaAIOrchestrator] Processing task ai_task_1750851544537_0to8bks8c with nU Quantum AI
[SpUnderBot] Issue reported: wallet_not_updating - Wallet balance data not updating properly
[WalletFix] Wallet updated: 11496.566 UMatter
[SpUnderBot] Issue reported: extension_download_failed - Browser extension download endpoint not working
[SpUnderBot] Repair task created: extension_fix - Fix browser extension download and file serving
[SpUnderBot] Issue reported: buttons_not_working - Energy generation buttons not found in DOM