[EnergySyncController] ✅ Batch processed successfully: 5 items
[AuthenticDeviceManager] REAL MacBook Battery: 
Object {realLevel: "100.0%", realCharging: "YES", source: "AUTHENTIC_DEVICE_API"}
[AuthenticDeviceManager] AUTHENTIC MacBook METRICS: 
Object {battery: "100.0% CHARGING", network: "10Mbps WIFI", memory: "57.0MB used of 79.5MB", cores: 8, platform: "MacIntel"}
[AuthenticDeviceManager] Energy calculation: Base: 2.192912, Variance: 0.131596, Total: 2.324508
[AuthenticDeviceManager] UMatter generated: 2.324508 from authentic_device_sync
[useRealTimeData] UMatter received: 2.324508 from authentic_device_sync
[AuthenticDeviceManager] Energy accumulated for batching: 2.324508
[AuthenticDeviceManager] Energy calculation: Base: 2.192912, Variance: 0.177824, Total: 2.370736
[AuthenticDeviceManager] Energy accumulated for batching: 2.370736
[useRealTimeData] UMatter received: 2.370736 from authentic_device_sync
[useRealTimeData] Network/Memory/CPU updated: 
Object {network: "10Mbps", memory: "57.0MB", cores: 8}
[NativeBatteryDetector] REAL-TIME UPDATE: 
Object {level: "100.0%", charging: "YES", source: "LIVE_BATTERY_API"}
[InteractionTracking] Started session: spunder_mcck2qrt_wixkmkor0
[InteractionTracking] Started session: spunder_mcck2qrv_6uondeqjg
[EnergySyncController] Processing batch: 2 items, 0.004137 UMatter total
[EnergySyncController] ✅ Batch processed successfully: 2 items
[AuthenticDeviceManager] REAL MacBook Battery: 
Object {realLevel: "100.0%", realCharging: "YES", source: "AUTHENTIC_DEVICE_API"}
[AuthenticDeviceManager] AUTHENTIC MacBook METRICS: 
Object {battery: "100.0% CHARGING", network: "10Mbps WIFI", memory: "50.0MB used of 84.2MB", cores: 8, platform: "MacIntel"}
[AuthenticDeviceManager] Energy calculation: Base: 2.149135, Variance: 0.134804, Total: 2.283939
[AuthenticDeviceManager] UMatter generated: 2.283939 from authentic_device_sync
[useRealTimeData] UMatter received: 2.283939 from authentic_device_sync
[AuthenticDeviceManager] Energy accumulated for batching: 2.283939
[AuthenticDeviceManager] Energy calculation: Base: 2.139135, Variance: 0.083523, Total: 2.222658
[AuthenticDeviceManager] Energy accumulated for batching: 2.222658
[useRealTimeData] UMatter received: 2.222658 from authentic_device_sync
[useRealTimeData] Network/Memory/CPU updated: 
Object {network: "10Mbps", memory: "50.0MB", cores: 8}
[RealHardwareConnector] AUTHENTIC: 0.002292 UMatter from real Node.js hardware
[RealHardwareConnector] Real metrics: CPU 54.76%, Memory 203.8MB, Power 0.31W
[useRealTimeData] REAL HARDWARE ENERGY: 0.002292 UMatter from Node.js APIs
[useRealTimeData] Real hardware metrics: CPU 54.76%, Memory 203.8MB, Power 0.31W
[EnergySyncController] Added 0.002292 UMatter from real_nodejs_hardware, accumulator size: 1
[RealHardwareConnector] AUTHENTIC: 0.002284 UMatter from real Node.js hardware
[RealHardwareConnector] Real metrics: CPU 54.76%, Memory 203.8MB, Power 0.31W
[useRealTimeData] REAL HARDWARE ENERGY: 0.002284 UMatter from Node.js APIs
[useRealTimeData] Real hardware metrics: CPU 54.76%, Memory 203.8MB, Power 0.31W
[EnergySyncController] Added 0.002284 UMatter from real_nodejs_hardware, accumulator size: 2
`DialogContent` requires a `DialogTitle` for the component to be accessible for screen reader users.

If you want to hide the `DialogTitle`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/dialog
Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.
`DialogContent` requires a `DialogTitle` for the component to be accessible for screen reader users.

If you want to hide the `DialogTitle`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/dialog
Warning: Missing `Description` or `aria-describedby={undefined}` for {DialogContent}.