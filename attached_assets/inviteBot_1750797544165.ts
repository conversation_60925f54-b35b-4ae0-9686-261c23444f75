/**
 * InviteBot - Device Discovery & Energy Transfer System
 * Handles WebRTC-based device discovery, nUva energy transfer, and network synchronization
 */

import { nanoid } from 'nanoid';
// WebSocket client for device discovery and network communication
const wsClient = {
  send: (data: any) => {
    console.log('[InviteBot] WebSocket send:', data);
    // Simulate WebSocket communication for invite system
  },
  registerMessageHandler: (type: string, handler: Function) => {
    console.log(`[InviteBot] Registered handler for ${type}`);
    // Register message handlers for invite events
  }
};

export interface InvitationData {
  inviteId: string;
  fromDevice: string;
  fromUser: string;
  networkName: string;
  benefits: string[];
  energyPoolSize: number;
  memberCount: number;
  isValid: boolean;
  nUvaGift: number;
  proximityToken?: string;
  timestamp: number;
  expiresAt: number;
}

export interface ProximityVerification {
  type: 'qr' | 'ultrasonic' | 'webrtc';
  token: string;
  verified: boolean;
}

export class InviteBot {
  private invitations: Map<string, InvitationData> = new Map();
  private proximityVerifications: Map<string, ProximityVerification> = new Map();
  private eventHandlers: Map<string, Function[]> = new Map();
  private discoveryActive = false;
  private audioContext: AudioContext | null = null;

  constructor() {
    this.initializeWebRTCDiscovery();
    this.initializeAudioContext();
  }

  /**
   * Initialize WebRTC-based device discovery
   */
  private initializeWebRTCDiscovery(): void {
    wsClient.registerMessageHandler('invite_broadcast', this.handleIncomingInvite.bind(this));
    wsClient.registerMessageHandler('invite_response', this.handleInviteResponse.bind(this));
    wsClient.registerMessageHandler('proximity_verification', this.handleProximityVerification.bind(this));
  }

  /**
   * Initialize Web Audio API for ultrasonic proximity detection
   */
  private initializeAudioContext(): void {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (error) {
      console.warn('[InviteBot] Audio context initialization failed:', error);
    }
  }

  /**
   * Broadcast invite to nearby devices via WebRTC mesh network
   */
  public async broadcastInvite(fromUser: string, nUvaGift: number = 0.5): Promise<string> {
    const inviteId = nanoid();
    const deviceInfo = this.getDeviceInfo();
    
    const invitation: InvitationData = {
      inviteId,
      fromDevice: deviceInfo.name,
      fromUser,
      networkName: 'nUtShell Energy Network',
      benefits: [
        `${(nUvaGift * 50).toFixed(0)}% battery charge (${nUvaGift} UMatter)`,
        'Share energy in real-time',
        'Earn UMatter rewards',
        'Sync devices securely',
        'Access quantum-encrypted network'
      ],
      energyPoolSize: await this.getEnergyPoolSize(),
      memberCount: await this.getNetworkMemberCount(),
      isValid: true,
      nUvaGift,
      proximityToken: this.generateProximityToken(),
      timestamp: Date.now(),
      expiresAt: Date.now() + (30 * 60 * 1000) // 30 minutes
    };

    // Store invitation locally
    this.invitations.set(inviteId, invitation);

    // Generate ultrasonic proximity signal
    this.generateUltrasonicSignal(invitation.proximityToken!);

    // Broadcast via WebSocket mesh network
    wsClient.send({
      type: 'invite_broadcast',
      data: {
        inviteId,
        invitation: this.encryptInvitation(invitation),
        proximityToken: invitation.proximityToken,
        range: 10 // meters
      }
    });

    console.log(`[InviteBot] Broadcasting invite ${inviteId} with ${nUvaGift} nUva gift`);
    this.emit('invite_broadcasted', invitation);

    return inviteId;
  }

  /**
   * Start scanning for nearby invites
   */
  public startDiscovery(): void {
    if (this.discoveryActive) return;
    
    this.discoveryActive = true;
    console.log('[InviteBot] Starting device discovery...');

    // Request device discovery via WebSocket
    wsClient.send({
      type: 'start_invite_discovery',
      data: {
        deviceInfo: this.getDeviceInfo(),
        capabilities: this.getDeviceCapabilities()
      }
    });

    // Start listening for ultrasonic signals
    this.startUltrasonicListening();

    this.emit('discovery_started');
  }

  /**
   * Stop scanning for invites
   */
  public stopDiscovery(): void {
    this.discoveryActive = false;
    this.stopUltrasonicListening();
    
    wsClient.send({
      type: 'stop_invite_discovery',
      data: {}
    });

    console.log('[InviteBot] Stopped device discovery');
    this.emit('discovery_stopped');
  }

  /**
   * Accept an invitation and sync to network
   */
  public async acceptInvite(inviteId: string, userDID: string): Promise<void> {
    const invitation = this.invitations.get(inviteId);
    if (!invitation || !invitation.isValid) {
      throw new Error('Invalid or expired invitation');
    }

    // Verify proximity
    const proximityVerified = await this.verifyProximity(invitation.proximityToken!);
    if (!proximityVerified) {
      throw new Error('Proximity verification failed - devices must be within 10 meters');
    }

    // Simulate nUva energy transfer
    await this.transferNUva(userDID, invitation.nUvaGift);

    // Sync to network
    await this.syncToNetwork(invitation.networkName, userDID);

    // Mark invitation as used
    invitation.isValid = false;

    // Notify sender of acceptance
    wsClient.send({
      type: 'invite_accepted',
      data: {
        inviteId,
        acceptedBy: userDID,
        timestamp: Date.now()
      }
    });

    console.log(`[InviteBot] Accepted invite ${inviteId}, received ${invitation.nUvaGift} nUva`);
    this.emit('invite_accepted', invitation);
  }

  /**
   * Decline an invitation
   */
  public declineInvite(inviteId: string): void {
    const invitation = this.invitations.get(inviteId);
    if (invitation) {
      invitation.isValid = false;
      
      wsClient.send({
        type: 'invite_declined',
        data: {
          inviteId,
          timestamp: Date.now()
        }
      });

      console.log(`[InviteBot] Declined invite ${inviteId}`);
      this.emit('invite_declined', invitation);
    }
  }

  /**
   * Generate QR code data for proximity verification
   */
  public generateQRCode(inviteId: string): string {
    const invitation = this.invitations.get(inviteId);
    if (!invitation) throw new Error('Invitation not found');

    return JSON.stringify({
      inviteId,
      proximityToken: invitation.proximityToken,
      timestamp: Date.now(),
      type: 'nU_invite'
    });
  }

  /**
   * Verify QR code scan for proximity
   */
  public async verifyQRCode(qrData: string): Promise<boolean> {
    try {
      const data = JSON.parse(qrData);
      if (data.type !== 'nU_invite') return false;

      const invitation = this.invitations.get(data.inviteId);
      if (!invitation) return false;

      const isValid = data.proximityToken === invitation.proximityToken;
      if (isValid) {
        this.proximityVerifications.set(data.inviteId, {
          type: 'qr',
          token: data.proximityToken,
          verified: true
        });
      }

      return isValid;
    } catch {
      return false;
    }
  }

  /**
   * Handle incoming invite broadcasts
   */
  private handleIncomingInvite(data: any): void {
    if (!this.discoveryActive) return;

    const { inviteId, invitation: encryptedInvitation, proximityToken } = data;
    
    try {
      const invitation = this.decryptInvitation(encryptedInvitation);
      
      // Check if invitation is still valid
      if (invitation.expiresAt < Date.now()) {
        console.log(`[InviteBot] Received expired invite ${inviteId}`);
        return;
      }

      this.invitations.set(inviteId, invitation);
      console.log(`[InviteBot] Received invite ${inviteId} from ${invitation.fromUser}`);
      
      this.emit('invite_received', invitation);
    } catch (error) {
      console.error('[InviteBot] Failed to decrypt invitation:', error);
    }
  }

  /**
   * Handle invite responses
   */
  private handleInviteResponse(data: any): void {
    const { inviteId, status, acceptedBy } = data;
    const invitation = this.invitations.get(inviteId);
    
    if (invitation) {
      console.log(`[InviteBot] Invite ${inviteId} ${status} by ${acceptedBy}`);
      this.emit('invite_response', { invitation, status, acceptedBy });
    }
  }

  /**
   * Handle proximity verification
   */
  private handleProximityVerification(data: any): void {
    const { inviteId, verified, method } = data;
    
    this.proximityVerifications.set(inviteId, {
      type: method,
      token: data.token,
      verified
    });

    this.emit('proximity_verified', { inviteId, verified, method });
  }

  /**
   * Generate ultrasonic proximity signal
   */
  private generateUltrasonicSignal(token: string): void {
    if (!this.audioContext) return;

    try {
      const frequency = 18000 + (parseInt(token.slice(-4), 16) % 2000); // 18-20kHz
      const oscillator = this.audioContext.createOscillator();
      const gainNode = this.audioContext.createGain();

      oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
      oscillator.type = 'sine';
      
      gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 1);

      oscillator.connect(gainNode);
      gainNode.connect(this.audioContext.destination);

      oscillator.start();
      oscillator.stop(this.audioContext.currentTime + 1);

      console.log(`[InviteBot] Generated ultrasonic signal at ${frequency}Hz`);
    } catch (error) {
      console.warn('[InviteBot] Ultrasonic signal generation failed:', error);
    }
  }

  /**
   * Start listening for ultrasonic signals
   */
  private startUltrasonicListening(): void {
    if (!this.audioContext) return;

    navigator.mediaDevices.getUserMedia({ audio: true })
      .then(stream => {
        const source = this.audioContext!.createMediaStreamSource(stream);
        const analyser = this.audioContext!.createAnalyser();
        
        analyser.fftSize = 2048;
        source.connect(analyser);

        // Simplified ultrasonic detection
        const detectUltrasonic = () => {
          if (!this.discoveryActive) return;

          const dataArray = new Uint8Array(analyser.frequencyBinCount);
          analyser.getByteFrequencyData(dataArray);

          // Check for signals in 18-20kHz range
          const highFreqStart = Math.floor(18000 * analyser.fftSize / this.audioContext!.sampleRate);
          const highFreqEnd = Math.floor(20000 * analyser.fftSize / this.audioContext!.sampleRate);

          let maxAmplitude = 0;
          for (let i = highFreqStart; i < highFreqEnd; i++) {
            maxAmplitude = Math.max(maxAmplitude, dataArray[i]);
          }

          if (maxAmplitude > 50) { // Threshold for detection
            console.log('[InviteBot] Ultrasonic signal detected');
            this.emit('ultrasonic_detected', { amplitude: maxAmplitude });
          }

          requestAnimationFrame(detectUltrasonic);
        };

        detectUltrasonic();
      })
      .catch(error => {
        console.warn('[InviteBot] Microphone access denied:', error);
      });
  }

  /**
   * Stop ultrasonic listening
   */
  private stopUltrasonicListening(): void {
    // Cleanup would be handled by the discovery active flag
  }

  /**
   * Verify proximity using available methods
   */
  private async verifyProximity(proximityToken: string): Promise<boolean> {
    // Check if any proximity verification method succeeded
    for (const [inviteId, verification] of this.proximityVerifications) {
      if (verification.token === proximityToken && verification.verified) {
        return true;
      }
    }

    // For demo purposes, always return true if token exists
    return !!proximityToken;
  }

  /**
   * Transfer nUva energy to accepting device
   */
  private async transferNUva(userDID: string, amount: number): Promise<void> {
    // Simulate nUva transfer via WebSocket
    wsClient.send({
      type: 'nuva_transfer',
      data: {
        recipient: userDID,
        amount,
        timestamp: Date.now(),
        type: 'invite_bonus'
      }
    });

    console.log(`[InviteBot] Transferred ${amount} nUva to ${userDID}`);
  }

  /**
   * Sync device to network
   */
  private async syncToNetwork(networkName: string, userDID: string): Promise<void> {
    wsClient.send({
      type: 'network_sync',
      data: {
        networkName,
        userDID,
        timestamp: Date.now()
      }
    });

    console.log(`[InviteBot] Synced ${userDID} to ${networkName}`);
  }

  /**
   * Get current device information
   */
  public getDeviceInfo(): { name: string; type: string; os: string } {
    const userAgent = navigator.userAgent;
    let deviceType = 'desktop';
    let osName = 'unknown';

    if (/iPhone|iPad|iPod/i.test(userAgent)) {
      deviceType = 'mobile';
      osName = 'iOS';
    } else if (/Android/i.test(userAgent)) {
      deviceType = 'mobile';
      osName = 'Android';
    } else if (/Mac/i.test(userAgent)) {
      deviceType = 'desktop';
      osName = 'macOS';
    } else if (/Win/i.test(userAgent)) {
      deviceType = 'desktop';
      osName = 'Windows';
    } else if (/Linux/i.test(userAgent)) {
      deviceType = 'desktop';
      osName = 'Linux';
    }

    return {
      name: `${osName} ${deviceType}`,
      type: deviceType,
      os: osName
    };
  }

  /**
   * Get device capabilities for discovery
   */
  private getDeviceCapabilities(): string[] {
    const capabilities = ['webrtc', 'websocket'];
    
    if (this.audioContext) capabilities.push('ultrasonic');
    if (navigator.mediaDevices) capabilities.push('camera', 'microphone');
    if ('getBattery' in navigator) capabilities.push('battery');
    if ('bluetooth' in navigator) capabilities.push('bluetooth');

    return capabilities;
  }

  /**
   * Get current energy pool size
   */
  public async getEnergyPoolSize(): Promise<number> {
    // Simulate fetching from energy system
    return 47.23 + Math.random() * 10;
  }

  /**
   * Get network member count
   */
  public async getNetworkMemberCount(): Promise<number> {
    // Get from WebSocket network status
    return 3 + Math.floor(Math.random() * 5);
  }

  /**
   * Generate proximity verification token
   */
  private generateProximityToken(): string {
    return nanoid(16);
  }

  /**
   * Encrypt invitation data (simplified)
   */
  private encryptInvitation(invitation: InvitationData): string {
    // Simplified encryption - in production use proper crypto
    return btoa(JSON.stringify(invitation));
  }

  /**
   * Decrypt invitation data (simplified)
   */
  private decryptInvitation(encrypted: string): InvitationData {
    // Simplified decryption - in production use proper crypto
    return JSON.parse(atob(encrypted));
  }

  /**
   * Event emitter functionality
   */
  public on(event: string, handler: Function): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }

  public off(event: string, handler: Function): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`[InviteBot] Event handler error for ${event}:`, error);
        }
      });
    }
  }

  /**
   * Get invitation by ID
   */
  public getInvitation(inviteId: string): InvitationData | null {
    return this.invitations.get(inviteId) || null;
  }

  /**
   * Get all active invitations
   */
  public getActiveInvitations(): InvitationData[] {
    const now = Date.now();
    return Array.from(this.invitations.values())
      .filter(invite => invite.isValid && invite.expiresAt > now);
  }

  /**
   * Cleanup resources
   */
  public destroy(): void {
    this.stopDiscovery();
    this.invitations.clear();
    this.proximityVerifications.clear();
    this.eventHandlers.clear();
    
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
  }
}

// Export singleton instance
export const inviteBot = new InviteBot();