[RealTimeDeviceManager] Starting real-time monitoring...
[IoTManager] USB device detection initialized, found 0 devices
[IoTManager] Bluetooth discovery available
[IoTManager] Added device: Living Room Apple TV (speaker)
[IoTManager] Added device: Kitchen HomePod (speaker)
[IoTManager] Added device: Printer (hub)
[IoTManager] UPnP discovery initialized
[IoTManager] Network scanning initialized
[IoTManager] Zigbee discovery initialized
[IoTManager] Z-Wave discovery initialized
[IoTManager] Added device: Smart Thermostat (thermostat)
[IoTManager] Added device: Smart Light Bulbs (dimmer)
[IoTManager] Added device: Google Nest Hub (hub)
[IoTManager] Added device: Amazon Echo (speaker)
[IoTManager] Added device: Motion Sensor (sensor)
[IoTManager] Added device: Energy Monitor (sensor)
[NuCore] IoT Manager initialized successfully
[AuthenticEnergyData] Starting authentic data collection...
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[SpUnderBot] Issue reported: wallet_not_updating - Wallet API endpoints not responding correctly
[SpUnderBot] Repair task created: wallet_update - Fix wallet API connectivity and update mechanisms
[PWA] Service Worker registration failed
[EnergySyncController] ✅ Batch processed successfully: 
Object {success: true, batchSize: 1, totalAmount: 0.18725269067027914, totalGenerated: 0.18725269067027914, source: "authentic_device_energy_batch"}
[SpUnderBot] Issue reported: extension_download_failed - Browser extension download endpoint not working
[SpUnderBot] Repair task created: extension_fix - Fix browser extension download and file serving
[SpUnderBot] Issue reported: buttons_not_working - Energy generation buttons not found in DOM
[SpUnderBot] Repair task created: ui_enhancement - Repair and enhance UI button functionality
[IPFSStorage] IPFS not available, using local storage fallback
[IPFSStorage] Encryption initialized
[IPFSStorage] Storage initialized (Local mode)
[IPFSStorage] IPFS not available, using local storage fallback