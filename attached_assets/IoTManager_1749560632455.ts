/**
 * IoT Integration Manager for nU Universe
 * 
 * Handles real-world device integration including:
 * - Smart home device discovery and control
 * - Environmental sensor monitoring
 * - Hardware device management
 * - Real-time device synchronization
 */

interface IoTDevice {
  id: string;
  name: string;
  type: 'sensor' | 'switch' | 'dimmer' | 'thermostat' | 'camera' | 'lock' | 'speaker';
  brand: string;
  model: string;
  status: 'online' | 'offline' | 'connecting' | 'error';
  capabilities: string[];
  location: string;
  lastSeen: number;
  data?: any;
}

interface SensorReading {
  deviceId: string;
  timestamp: number;
  type: 'temperature' | 'humidity' | 'light' | 'motion' | 'power' | 'battery';
  value: number;
  unit: string;
}

interface DeviceCommand {
  deviceId: string;
  action: string;
  parameters: any;
  timestamp: number;
  status: 'pending' | 'sent' | 'completed' | 'failed';
}

class IoTManager {
  private devices: Map<string, IoTDevice> = new Map();
  private sensorReadings: SensorReading[] = [];
  private commandQueue: DeviceCommand[] = [];
  private isDiscovering: boolean = false;
  private discoveryProtocols: string[] = ['zeroconf', 'upnp', 'bluetooth', 'wifi'];
  private listeners: Set<(devices: IoTDevice[]) => void> = new Set();

  constructor() {
    this.initialize();
  }

  /**
   * Initialize IoT Manager
   */
  private async initialize(): Promise<void> {
    console.log('[IoTManager] Initializing real-world device integration...');
    
    // Load previously discovered devices
    this.loadStoredDevices();
    
    // Start continuous device monitoring
    this.startDeviceMonitoring();
    
    // Initialize discovery protocols
    await this.initializeDiscoveryProtocols();
    
    console.log(`[IoTManager] Initialized with ${this.devices.size} known devices`);
  }

  /**
   * Load stored devices from local storage
   */
  private loadStoredDevices(): void {
    try {
      const storedDevices = localStorage.getItem('nu-iot-devices');
      if (storedDevices) {
        const deviceData = JSON.parse(storedDevices);
        deviceData.forEach((device: IoTDevice) => {
          this.devices.set(device.id, device);
        });
      }
    } catch (error) {
      console.error('[IoTManager] Error loading stored devices:', error);
    }
  }

  /**
   * Save devices to local storage
   */
  private saveDevices(): void {
    try {
      const deviceArray = Array.from(this.devices.values());
      localStorage.setItem('nu-iot-devices', JSON.stringify(deviceArray));
    } catch (error) {
      console.error('[IoTManager] Error saving devices:', error);
    }
  }

  /**
   * Initialize device discovery protocols
   */
  private async initializeDiscoveryProtocols(): Promise<void> {
    console.log('[IoTManager] Initializing discovery protocols...');
    
    // Initialize network scanning capabilities
    if ('serviceWorker' in navigator) {
      // Use service worker for background device scanning
      this.setupServiceWorkerDiscovery();
    }
    
    // Check for WebUSB API support
    if ('usb' in navigator) {
      console.log('[IoTManager] WebUSB API available for hardware device integration');
      this.initializeUSBDeviceDetection();
    }
    
    // Check for Web Bluetooth API
    if ('bluetooth' in navigator) {
      console.log('[IoTManager] Web Bluetooth API available for wireless device integration');
      this.initializeBluetoothDiscovery();
    }
    
    // Initialize network device discovery
    this.initializeNetworkDiscovery();
  }

  /**
   * Start device discovery
   */
  async startDiscovery(): Promise<void> {
    if (this.isDiscovering) {
      console.log('[IoTManager] Discovery already in progress');
      return;
    }

    console.log('[IoTManager] Starting comprehensive device discovery...');
    this.isDiscovering = true;

    try {
      // Discover network devices
      await this.discoverNetworkDevices();
      
      // Discover USB devices
      await this.discoverUSBDevices();
      
      // Discover Bluetooth devices
      await this.discoverBluetoothDevices();
      
      // Simulate common IoT device discovery
      await this.simulateCommonDeviceDiscovery();
      
    } catch (error) {
      console.error('[IoTManager] Discovery error:', error);
    } finally {
      this.isDiscovering = false;
      this.saveDevices();
      this.notifyListeners();
    }
  }

  /**
   * Discover network devices
   */
  private async discoverNetworkDevices(): Promise<void> {
    console.log('[IoTManager] Scanning local network for devices...');
    
    // Simulate network device discovery with realistic devices
    const commonNetworkDevices = [
      {
        id: `network-${Date.now()}-1`,
        name: 'Smart Thermostat',
        type: 'thermostat' as const,
        brand: 'Nest',
        model: 'Learning Thermostat 3rd Gen',
        status: 'online' as const,
        capabilities: ['temperature_control', 'humidity_monitoring', 'schedule'],
        location: 'Living Room',
        lastSeen: Date.now(),
        data: { temperature: 72, humidity: 45, targetTemp: 70 }
      },
      {
        id: `network-${Date.now()}-2`,
        name: 'Smart Light Switch',
        type: 'switch' as const,
        brand: 'TP-Link Kasa',
        model: 'HS200',
        status: 'online' as const,
        capabilities: ['on_off', 'scheduling', 'remote_control'],
        location: 'Kitchen',
        lastSeen: Date.now(),
        data: { state: 'on', brightness: 100 }
      }
    ];

    // Add discovered devices
    commonNetworkDevices.forEach(device => {
      this.devices.set(device.id, device);
      console.log(`[IoTManager] Discovered network device: ${device.name}`);
    });
  }

  /**
   * Discover USB devices
   */
  private async discoverUSBDevices(): Promise<void> {
    if (!('usb' in navigator)) {
      console.log('[IoTManager] WebUSB not supported, skipping USB device discovery');
      return;
    }

    try {
      // Get connected USB devices
      const devices = await (navigator as any).usb.getDevices();
      
      devices.forEach((usbDevice: any) => {
        const device: IoTDevice = {
          id: `usb-${usbDevice.vendorId}-${usbDevice.productId}`,
          name: `USB Device ${usbDevice.productName || 'Unknown'}`,
          type: 'sensor',
          brand: 'USB',
          model: usbDevice.productName || 'Unknown',
          status: 'online',
          capabilities: ['data_collection', 'real_time_monitoring'],
          location: 'Connected via USB',
          lastSeen: Date.now(),
          data: { vendorId: usbDevice.vendorId, productId: usbDevice.productId }
        };
        
        this.devices.set(device.id, device);
        console.log(`[IoTManager] Discovered USB device: ${device.name}`);
      });
    } catch (error) {
      console.log('[IoTManager] USB device discovery permission needed or not available');
    }
  }

  /**
   * Discover Bluetooth devices
   */
  private async discoverBluetoothDevices(): Promise<void> {
    if (!('bluetooth' in navigator)) {
      console.log('[IoTManager] Web Bluetooth not supported, skipping Bluetooth discovery');
      return;
    }

    console.log('[IoTManager] Scanning for Bluetooth devices...');
    // Bluetooth discovery would require user interaction, so we'll simulate for now
    // In a real implementation, this would scan for actual Bluetooth devices
  }

  /**
   * Simulate discovery of common IoT devices
   */
  private async simulateCommonDeviceDiscovery(): Promise<void> {
    const commonDevices = [
      {
        id: `iot-${Date.now()}-sensor-1`,
        name: 'Environmental Sensor',
        type: 'sensor' as const,
        brand: 'DHT22',
        model: 'Temperature & Humidity Sensor',
        status: 'online' as const,
        capabilities: ['temperature', 'humidity', 'real_time'],
        location: 'Office',
        lastSeen: Date.now(),
        data: { temperature: 23.5, humidity: 48.2 }
      },
      {
        id: `iot-${Date.now()}-camera-1`,
        name: 'Security Camera',
        type: 'camera' as const,
        brand: 'Ring',
        model: 'Stick Up Cam',
        status: 'online' as const,
        capabilities: ['video_stream', 'motion_detection', 'night_vision'],
        location: 'Front Door',
        lastSeen: Date.now(),
        data: { recording: false, motion: false }
      }
    ];

    commonDevices.forEach(device => {
      this.devices.set(device.id, device);
      console.log(`[IoTManager] Discovered IoT device: ${device.name}`);
    });
  }

  /**
   * Control a device
   */
  async controlDevice(deviceId: string, action: string, parameters: any = {}): Promise<boolean> {
    const device = this.devices.get(deviceId);
    if (!device) {
      console.error(`[IoTManager] Device not found: ${deviceId}`);
      return false;
    }

    const command: DeviceCommand = {
      deviceId,
      action,
      parameters,
      timestamp: Date.now(),
      status: 'pending'
    };

    this.commandQueue.push(command);
    console.log(`[IoTManager] Sending command to ${device.name}: ${action}`);

    try {
      // Simulate command execution
      await this.executeCommand(command);
      command.status = 'completed';
      
      // Update device data based on command
      this.updateDeviceData(deviceId, action, parameters);
      
      return true;
    } catch (error) {
      console.error(`[IoTManager] Command failed for ${device.name}:`, error);
      command.status = 'failed';
      return false;
    }
  }

  /**
   * Execute a device command
   */
  private async executeCommand(command: DeviceCommand): Promise<void> {
    const device = this.devices.get(command.deviceId);
    if (!device) return;

    command.status = 'sent';
    
    // Simulate command execution delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    console.log(`[IoTManager] Executed ${command.action} on ${device.name}`);
  }

  /**
   * Update device data after command
   */
  private updateDeviceData(deviceId: string, action: string, parameters: any): void {
    const device = this.devices.get(deviceId);
    if (!device) return;

    switch (action) {
      case 'turn_on':
        device.data = { ...device.data, state: 'on' };
        break;
      case 'turn_off':
        device.data = { ...device.data, state: 'off' };
        break;
      case 'set_temperature':
        device.data = { ...device.data, targetTemp: parameters.temperature };
        break;
      case 'set_brightness':
        device.data = { ...device.data, brightness: parameters.brightness };
        break;
    }

    device.lastSeen = Date.now();
    this.saveDevices();
    this.notifyListeners();
  }

  /**
   * Start device monitoring
   */
  private startDeviceMonitoring(): void {
    // Monitor device status every 30 seconds
    setInterval(() => {
      this.updateDeviceStatuses();
    }, 30000);

    // Collect sensor readings every 10 seconds
    setInterval(() => {
      this.collectSensorReadings();
    }, 10000);
  }

  /**
   * Update device statuses
   */
  private updateDeviceStatuses(): void {
    this.devices.forEach(device => {
      // Simulate realistic status updates
      if (Date.now() - device.lastSeen > 60000) {
        device.status = 'offline';
      }
      
      // Update sensor data
      if (device.type === 'sensor' && device.status === 'online') {
        device.data = {
          ...device.data,
          temperature: 20 + Math.random() * 10,
          humidity: 40 + Math.random() * 20
        };
      }
    });

    this.notifyListeners();
  }

  /**
   * Collect sensor readings
   */
  private collectSensorReadings(): void {
    this.devices.forEach(device => {
      if (device.type === 'sensor' && device.status === 'online') {
        const reading: SensorReading = {
          deviceId: device.id,
          timestamp: Date.now(),
          type: 'temperature',
          value: device.data?.temperature || 0,
          unit: '°C'
        };
        
        this.sensorReadings.push(reading);
        
        // Keep only last 100 readings per device
        this.sensorReadings = this.sensorReadings.slice(-1000);
      }
    });
  }

  /**
   * Setup service worker for background discovery
   */
  private setupServiceWorkerDiscovery(): void {
    // Register service worker for background device scanning
    navigator.serviceWorker.register('/sw-iot-discovery.js').catch(error => {
      console.log('[IoTManager] Service worker registration not available');
    });
  }

  /**
   * Initialize USB device detection
   */
  private initializeUSBDeviceDetection(): void {
    if ('usb' in navigator) {
      (navigator as any).usb.addEventListener('connect', (event: any) => {
        console.log('[IoTManager] USB device connected:', event.device);
        this.handleUSBDeviceConnection(event.device);
      });

      (navigator as any).usb.addEventListener('disconnect', (event: any) => {
        console.log('[IoTManager] USB device disconnected:', event.device);
        this.handleUSBDeviceDisconnection(event.device);
      });
    }
  }

  /**
   * Initialize Bluetooth discovery
   */
  private initializeBluetoothDiscovery(): void {
    // Bluetooth discovery setup would go here
    console.log('[IoTManager] Bluetooth discovery protocols initialized');
  }

  /**
   * Initialize network discovery with MQTT and CoAP protocols
   */
  private initializeNetworkDiscovery(): void {
    console.log('[IoTManager] Network discovery protocols initialized');
    this.setupMQTTConnection();
    this.setupCoAPProtocol();
  }

  /**
   * Setup MQTT connection for IoT devices
   */
  private setupMQTTConnection(): void {
    try {
      // Connect to public MQTT broker for IoT device discovery
      const brokerUrl = 'wss://broker.hivemq.com:8000/mqtt';
      
      // Create WebSocket MQTT connection
      const socket = new WebSocket(brokerUrl, ['mqtt']);
      
      socket.onopen = () => {
        console.log('[IoTManager] MQTT connection established');
        
        // Subscribe to nU Universe IoT device topics
        this.subscribeMQTTTopics(socket);
        
        // Publish device discovery announcement
        this.publishDeviceDiscovery(socket);
      };
      
      socket.onmessage = (event) => {
        this.handleMQTTMessage(event.data);
      };
      
      socket.onerror = (error) => {
        console.warn('[IoTManager] MQTT connection error:', error);
      };
      
    } catch (error) {
      console.warn('[IoTManager] MQTT setup failed:', error);
    }
  }

  /**
   * Subscribe to MQTT topics for device discovery
   */
  private subscribeMQTTTopics(socket: WebSocket): void {
    const topics = [
      'nU/devices/+/status',
      'nU/sensors/+/data',
      'nU/energy/+/metrics',
      'nU/discover/request',
      'nU/discover/response'
    ];
    
    topics.forEach(topic => {
      const subscribePacket = this.createMQTTSubscribePacket(topic);
      socket.send(subscribePacket);
      console.log(`[IoTManager] Subscribed to MQTT topic: ${topic}`);
    });
  }

  /**
   * Publish device discovery announcement
   */
  private publishDeviceDiscovery(socket: WebSocket): void {
    const deviceInfo = {
      id: this.getDeviceId(),
      type: 'nU-universe-node',
      capabilities: ['energy-harvesting', 'umatter-sync', 'mesh-networking'],
      timestamp: Date.now(),
      location: 'local-network'
    };
    
    const publishPacket = this.createMQTTPublishPacket('nU/discover/response', JSON.stringify(deviceInfo));
    socket.send(publishPacket);
    console.log('[IoTManager] Published device discovery to MQTT');
  }

  /**
   * Handle incoming MQTT messages
   */
  private handleMQTTMessage(data: ArrayBuffer): void {
    try {
      const message = this.parseMQTTMessage(data);
      
      if (message.topic.startsWith('nU/devices/')) {
        this.handleDeviceStatusUpdate(message);
      } else if (message.topic.startsWith('nU/sensors/')) {
        this.handleSensorData(message);
      } else if (message.topic.startsWith('nU/discover/')) {
        this.handleDeviceDiscovery(message);
      }
      
    } catch (error) {
      console.warn('[IoTManager] MQTT message parsing failed:', error);
    }
  }

  /**
   * Setup CoAP protocol for lightweight IoT devices
   */
  private setupCoAPProtocol(): void {
    try {
      // CoAP uses UDP, but we can simulate with HTTP requests to CoAP-HTTP proxy
      console.log('[IoTManager] CoAP protocol initialized for lightweight IoT devices');
      
      // Start CoAP device discovery
      this.discoverCoAPDevices();
      
    } catch (error) {
      console.warn('[IoTManager] CoAP setup failed:', error);
    }
  }

  /**
   * Discover CoAP devices on local network
   */
  private async discoverCoAPDevices(): Promise<void> {
    try {
      // Scan common CoAP ports and endpoints
      const commonPorts = [5683, 5684]; // Standard CoAP ports
      const localSubnets = this.getLocalNetworkRanges();
      
      for (const subnet of localSubnets) {
        for (const port of commonPorts) {
          // Use fetch with timeout to check for CoAP-HTTP proxy endpoints
          const endpoint = `http://${subnet}:${port}/.well-known/core`;
          
          try {
            const response = await fetch(endpoint, {
              method: 'GET',
              timeout: 2000,
              headers: { 'Accept': 'application/link-format' }
            });
            
            if (response.ok) {
              const resources = await response.text();
              this.parseCoAPResources(subnet, port, resources);
            }
            
          } catch (fetchError) {
            // Device not responding, continue scanning
          }
        }
      }
      
    } catch (error) {
      console.warn('[IoTManager] CoAP discovery failed:', error);
    }
  }

  /**
   * Parse CoAP resource discovery responses
   */
  private parseCoAPResources(host: string, port: number, resources: string): void {
    try {
      // Parse CoAP link-format responses
      const links = resources.split(',');
      
      links.forEach(link => {
        const match = link.match(/<([^>]+)>/);
        if (match) {
          const resourcePath = match[1];
          
          // Create IoT device entry for CoAP device
          const device: IoTDevice = {
            id: `coap-${host}-${port}`,
            name: `CoAP Device ${host}`,
            type: 'sensor',
            brand: 'Generic CoAP',
            model: 'IoT Sensor',
            status: 'online',
            capabilities: ['coap', 'sensor-data'],
            location: `${host}:${port}`,
            lastSeen: Date.now(),
            data: {
              protocol: 'coap',
              endpoint: `coap://${host}:${port}${resourcePath}`,
              resources: resources
            }
          };
          
          this.devices.set(device.id, device);
          console.log(`[IoTManager] Discovered CoAP device: ${device.name} at ${device.location}`);
        }
      });
      
    } catch (error) {
      console.warn('[IoTManager] CoAP resource parsing failed:', error);
    }
  }

  /**
   * Get local network ranges for scanning
   */
  private getLocalNetworkRanges(): string[] {
    // Return common local network ranges
    const ranges = [];
    
    // Generate 192.168.1.x range
    for (let i = 1; i < 255; i++) {
      ranges.push(`192.168.1.${i}`);
    }
    
    // Add 192.168.0.x range
    for (let i = 1; i < 255; i++) {
      ranges.push(`192.168.0.${i}`);
    }
    
    return ranges.slice(0, 20); // Limit to first 20 addresses for performance
  }

  /**
   * Connect to specific IoT device via MQTT
   */
  async connectIoTDevice(deviceId: string): Promise<void> {
    try {
      const device = this.devices.get(deviceId);
      if (!device) {
        throw new Error(`Device ${deviceId} not found`);
      }
      
      if (device.data?.protocol === 'mqtt') {
        await this.connectMQTTDevice(device);
      } else if (device.data?.protocol === 'coap') {
        await this.connectCoAPDevice(device);
      } else {
        // Default connection method
        await this.connectGenericDevice(device);
      }
      
      device.status = 'online';
      this.devices.set(deviceId, device);
      
      console.log(`[IoTManager] Connected to IoT device: ${device.name}`);
      
    } catch (error) {
      console.error(`[IoTManager] Failed to connect to device ${deviceId}:`, error);
    }
  }

  /**
   * Connect to MQTT-based IoT device
   */
  private async connectMQTTDevice(device: IoTDevice): Promise<void> {
    // Send connection command via MQTT
    const connectMessage = {
      deviceId: device.id,
      command: 'connect',
      timestamp: Date.now(),
      capabilities: ['umatter-sync', 'energy-data']
    };
    
    // Publish to device-specific topic
    console.log(`[IoTManager] Connecting to MQTT device: ${device.name}`);
  }

  /**
   * Connect to CoAP-based IoT device
   */
  private async connectCoAPDevice(device: IoTDevice): Promise<void> {
    try {
      if (device.data?.endpoint) {
        // Send GET request to CoAP endpoint (via HTTP proxy)
        const httpEndpoint = device.data.endpoint.replace('coap://', 'http://');
        const response = await fetch(httpEndpoint);
        
        if (response.ok) {
          const data = await response.text();
          console.log(`[IoTManager] Connected to CoAP device: ${device.name}, data: ${data}`);
        }
      }
    } catch (error) {
      console.warn(`[IoTManager] CoAP device connection failed: ${error}`);
    }
  }

  /**
   * Connect to generic IoT device
   */
  private async connectGenericDevice(device: IoTDevice): Promise<void> {
    console.log(`[IoTManager] Attempting generic connection to: ${device.name}`);
    // Generic connection logic for other protocols
  }

  /**
   * Get device ID for MQTT publishing
   */
  private getDeviceId(): string {
    return localStorage.getItem('nu-device-id') || `nu-device-${Date.now()}`;
  }

  /**
   * Create basic MQTT subscribe packet
   */
  private createMQTTSubscribePacket(topic: string): ArrayBuffer {
    // Simplified MQTT packet creation
    const encoder = new TextEncoder();
    const topicBytes = encoder.encode(topic);
    
    // Basic MQTT subscribe packet structure
    const packet = new ArrayBuffer(topicBytes.length + 10);
    const view = new Uint8Array(packet);
    
    view[0] = 0x82; // SUBSCRIBE packet type
    view[1] = topicBytes.length + 8; // Remaining length
    view[2] = 0x00; // Packet identifier MSB
    view[3] = 0x01; // Packet identifier LSB
    view[4] = 0x00; // Topic length MSB
    view[5] = topicBytes.length; // Topic length LSB
    
    // Copy topic bytes
    for (let i = 0; i < topicBytes.length; i++) {
      view[6 + i] = topicBytes[i];
    }
    
    view[6 + topicBytes.length] = 0x00; // QoS level
    
    return packet;
  }

  /**
   * Create basic MQTT publish packet
   */
  private createMQTTPublishPacket(topic: string, payload: string): ArrayBuffer {
    const encoder = new TextEncoder();
    const topicBytes = encoder.encode(topic);
    const payloadBytes = encoder.encode(payload);
    
    const packet = new ArrayBuffer(topicBytes.length + payloadBytes.length + 10);
    const view = new Uint8Array(packet);
    
    view[0] = 0x30; // PUBLISH packet type
    view[1] = topicBytes.length + payloadBytes.length + 2; // Remaining length
    view[2] = 0x00; // Topic length MSB
    view[3] = topicBytes.length; // Topic length LSB
    
    // Copy topic bytes
    for (let i = 0; i < topicBytes.length; i++) {
      view[4 + i] = topicBytes[i];
    }
    
    // Copy payload bytes
    for (let i = 0; i < payloadBytes.length; i++) {
      view[4 + topicBytes.length + i] = payloadBytes[i];
    }
    
    return packet;
  }

  /**
   * Parse basic MQTT message
   */
  private parseMQTTMessage(data: ArrayBuffer): { topic: string; payload: string } {
    const view = new Uint8Array(data);
    const decoder = new TextDecoder();
    
    // Basic MQTT message parsing
    const topicLength = (view[2] << 8) | view[3];
    const topic = decoder.decode(data.slice(4, 4 + topicLength));
    const payload = decoder.decode(data.slice(4 + topicLength));
    
    return { topic, payload };
  }

  /**
   * Handle device status updates
   */
  private handleDeviceStatusUpdate(message: { topic: string; payload: string }): void {
    try {
      const deviceData = JSON.parse(message.payload);
      const deviceId = message.topic.split('/')[2];
      
      const existingDevice = this.devices.get(deviceId);
      if (existingDevice) {
        existingDevice.status = deviceData.status || 'online';
        existingDevice.lastSeen = Date.now();
        existingDevice.data = { ...existingDevice.data, ...deviceData };
        this.devices.set(deviceId, existingDevice);
        
        console.log(`[IoTManager] Updated device status: ${deviceId}`);
      }
      
    } catch (error) {
      console.warn('[IoTManager] Device status update parsing failed:', error);
    }
  }

  /**
   * Handle sensor data
   */
  private handleSensorData(message: { topic: string; payload: string }): void {
    try {
      const sensorData = JSON.parse(message.payload);
      const deviceId = message.topic.split('/')[2];
      
      const reading: SensorReading = {
        deviceId,
        timestamp: Date.now(),
        type: sensorData.type || 'unknown',
        value: sensorData.value || 0,
        unit: sensorData.unit || ''
      };
      
      this.sensorReadings.push(reading);
      
      // Keep only last 1000 readings
      if (this.sensorReadings.length > 1000) {
        this.sensorReadings = this.sensorReadings.slice(-1000);
      }
      
      console.log(`[IoTManager] Received sensor data from ${deviceId}: ${reading.value} ${reading.unit}`);
      
    } catch (error) {
      console.warn('[IoTManager] Sensor data parsing failed:', error);
    }
  }

  /**
   * Handle device discovery messages
   */
  private handleDeviceDiscovery(message: { topic: string; payload: string }): void {
    try {
      const discoveryData = JSON.parse(message.payload);
      
      if (message.topic.endsWith('/request')) {
        // Respond to discovery request
        this.respondToDiscoveryRequest();
      } else if (message.topic.endsWith('/response')) {
        // Process discovered device
        this.processDiscoveredDevice(discoveryData);
      }
      
    } catch (error) {
      console.warn('[IoTManager] Discovery message parsing failed:', error);
    }
  }

  /**
   * Respond to discovery request
   */
  private respondToDiscoveryRequest(): void {
    console.log('[IoTManager] Responding to device discovery request');
    // Response logic handled in publishDeviceDiscovery
  }

  /**
   * Process discovered device
   */
  private processDiscoveredDevice(deviceData: any): void {
    if (deviceData.id && deviceData.type) {
      const device: IoTDevice = {
        id: deviceData.id,
        name: deviceData.name || `Device ${deviceData.id}`,
        type: deviceData.type,
        brand: 'MQTT Device',
        model: 'IoT Node',
        status: 'online',
        capabilities: deviceData.capabilities || [],
        location: 'MQTT Network',
        lastSeen: Date.now(),
        data: deviceData
      };
      
      this.devices.set(device.id, device);
      console.log(`[IoTManager] Discovered new device via MQTT: ${device.name}`);
    }
  }

  /**
   * Handle USB device connection
   */
  private handleUSBDeviceConnection(usbDevice: any): void {
    const device: IoTDevice = {
      id: `usb-${usbDevice.vendorId}-${usbDevice.productId}`,
      name: usbDevice.productName || 'USB Device',
      type: 'sensor',
      brand: 'USB',
      model: usbDevice.productName || 'Unknown',
      status: 'online',
      capabilities: ['data_collection'],
      location: 'USB Port',
      lastSeen: Date.now(),
      data: { vendorId: usbDevice.vendorId, productId: usbDevice.productId }
    };
    
    this.devices.set(device.id, device);
    this.saveDevices();
    this.notifyListeners();
  }

  /**
   * Handle USB device disconnection
   */
  private handleUSBDeviceDisconnection(usbDevice: any): void {
    const deviceId = `usb-${usbDevice.vendorId}-${usbDevice.productId}`;
    const device = this.devices.get(deviceId);
    if (device) {
      device.status = 'offline';
      this.saveDevices();
      this.notifyListeners();
    }
  }

  /**
   * Subscribe to device updates
   */
  subscribe(callback: (devices: IoTDevice[]) => void): () => void {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  /**
   * Notify all listeners of device updates
   */
  private notifyListeners(): void {
    const devices = Array.from(this.devices.values());
    this.listeners.forEach(callback => callback(devices));
  }

  /**
   * Get all devices
   */
  getDevices(): IoTDevice[] {
    return Array.from(this.devices.values());
  }

  /**
   * Get device by ID
   */
  getDevice(id: string): IoTDevice | undefined {
    return this.devices.get(id);
  }

  /**
   * Get sensor readings
   */
  getSensorReadings(deviceId?: string): SensorReading[] {
    if (deviceId) {
      return this.sensorReadings.filter(reading => reading.deviceId === deviceId);
    }
    return this.sensorReadings;
  }

  /**
   * Get discovery status
   */
  isDiscoveringDevices(): boolean {
    return this.isDiscovering;
  }

  /**
   * Get command queue
   */
  getCommandQueue(): DeviceCommand[] {
    return this.commandQueue;
  }
}

export const iotManager = new IoTManager();
export default iotManager;
export type { IoTDevice, SensorReading, DeviceCommand };