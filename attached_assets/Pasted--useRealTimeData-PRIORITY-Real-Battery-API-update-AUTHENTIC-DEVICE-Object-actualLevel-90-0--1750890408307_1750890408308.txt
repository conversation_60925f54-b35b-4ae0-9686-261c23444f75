[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "90.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "90.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "90.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[PWA] Service Worker registration failed
[MetaAIOrchestrator] Processing task ai_task_1750889699020_xq5xwakax with nU Quantum AI
[MetaAIOrchestrator] Processing task ai_task_1750889699024_vru09dc8k with nU Quantum AI
[IoTManager] No real devices discovered on network
[RealWorldEnergyAPI] SolarEdge not available
[RealHardwareConnector] AUTHENTIC: 0.001339 UMatter from real Node.js hardware
[RealHardwareConnector] Real metrics: CPU 12.11%, Memory 171.0MB, Power 0.20W
[useRealTimeData] REAL HARDWARE ENERGY: 0.001339 UMatter from Node.js APIs
[useRealTimeData] Real hardware metrics: CPU 12.11%, Memory 171.0MB, Power 0.20W
[EnergySyncController] Added 0.001339 UMatter from real_nodejs_hardware, accumulator size: 1
[IoTManager] No real devices discovered on network
[RealHardwareConnector] AUTHENTIC: 0.001681 UMatter from real Node.js hardware
[RealHardwareConnector] Real metrics: CPU 12.17%, Memory 175.8MB, Power 0.20W
[useRealTimeData] REAL HARDWARE ENERGY: 0.001681 UMatter from Node.js APIs
[useRealTimeData] Real hardware metrics: CPU 12.17%, Memory 175.8MB, Power 0.20W
[EnergySyncController] Added 0.001681 UMatter from real_nodejs_hardware, accumulator size: 2
[RealHardwareConnector] AUTHENTIC: 0.001378 UMatter from real Node.js hardware
[RealHardwareConnector] Real metrics: CPU 12.19%, Memory 177.5MB, Power 0.20W
[useRealTimeData] REAL HARDWARE ENERGY: 0.001378 UMatter from Node.js APIs
[useRealTimeData] Real hardware metrics: CPU 12.19%, Memory 177.5MB, Power 0.20W
[EnergySyncController] Added 0.001378 UMatter from real_nodejs_hardware, accumulator size: 3
[RealWorldEnergyAPI] Enphase not available
[RealHardwareConnector] AUTHENTIC: 0.001317 UMatter from real Node.js hardware
[RealHardwareConnector] Real metrics: CPU 12.23%, Memory 179.7MB, Power 0.20W
[useRealTimeData] REAL HARDWARE ENERGY: 0.001317 UMatter from Node.js APIs
[useRealTimeData] Real hardware metrics: CPU 12.23%, Memory 179.7MB, Power 0.20W
[EnergySyncController] Added 0.001317 UMatter from real_nodejs_hardware, accumulator size: 4
[EnergySyncController] ✅ Batch processed successfully: 1 items
[RealWorldEnergyAPI] OpenWeatherSolar not available
[RealWorldEnergyAPI] Real solar API unavailable, using physics-based system
this.calculateEnhancedSolarOutput is not a function
[IPFSStorage] IPFS not available, using local storage fallback
[IPFSStorage] Encryption initialized
[IPFSStorage] Storage initialized (Local mode)
[IPFSStorage] IPFS not available, using local storage fallback
[IPFSStorage] Encryption initialized
[IPFSStorage] Storage initialized (Local mode)
[SystemInitializer] Initializing nuCore...
[SystemInitializer] Initializing spUnderBot...
[SystemInitializer] Initializing nuosSocialControl...
[SystemInitializer] Initializing ghostBots...
[SystemInitializer] Initializing iotManager...
[SystemInitializer] Initializing metaAI...
[SystemInitializer] Initializing realWorldEnergy...
[SystemInitializer] Initializing sbuTokens...
[SystemInitializer] Starting SpUnderBot system diagnostics...
[SystemInitializer] SpUnderBot status: 
Object {isRunning: true, diagnosticMode: true, totalTasks: 0, pendingRepairs: 0, systemIssues: 0, …}
[SpUnderBot] Force system repair initiated
[SpUnderBot] Running comprehensive system diagnostics...
[IPFSStorage] IPFS not available, using local storage fallback
[IPFSStorage] Encryption initialized
[IPFSStorage] Storage initialized (Local mode)
[NuCore] IPFS Storage initialized successfully
[NuCore] Complete system initialization successful
[IPFSStorage] IPFS not available, using local storage fallback