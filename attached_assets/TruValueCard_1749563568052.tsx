import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  ArrowUp,
  ArrowDown,
  Battery,
  BatteryCharging,
  Zap,
  ArrowUpRight,
  BarChart,
  Gauge,
  RefreshCw,
  Clock,
  DollarSign
} from 'lucide-react';
import { useGhost } from '@/components/GhostProvider';
import { formatTRU, formatCurrency } from '@/lib/utils';
import { AnimatedBalance } from '@/components/AnimatedBalance';
import { motion, AnimatePresence } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Progress } from '@/components/ui/progress';
interface TruValueCardProps {
  compact?: boolean;
  showDetails?: boolean;
  className?: string;
}
export function TruValueCard({ compact = false, showDetails = true, className = '' }: TruValueCardProps) {
  const { state: ghostState } = useGhost();
  const [lastUpdate, setLastUpdate] = useState(Date.now());
  const [priceChange, setPriceChange] = useState<{ direction: 'up' | 'down' | 'neutral', percent: number }>({ 
    direction: 'neutral', 
    percent: 0 
  });
  // Update price change indicator based on real data from our updater
  useEffect(() => {
    if (ghostState) {
      const now = Date.now();
      // Import the function to get TRU value change
      import('@/lib/tru-value-updater').then(({ getTruValueChange }) => {
        // Get the change data from our utility
        const changeData = getTruValueChange(ghostState);
        // Only update if we have a valid direction and value
        if (changeData.direction !== 'neutral' || changeData.percent > 0) {
          setPriceChange(changeData);
          setLastUpdate(now);
        }
      });
    }
  }, [ghostState?.truValue, ghostState?.previousTruValue]);
  // Format time since last update
  const getTimeSinceUpdate = () => {
    const seconds = Math.floor((Date.now() - lastUpdate) / 1000);
    if (seconds < 60) return `${seconds}s ago`;
    return `${Math.floor(seconds / 60)}m ago`;
  };
  if (!ghostState) {
    return (
      <Card className={`w-full h-32 ${className}`}>
        <CardContent className="flex items-center justify-center h-full">
          <RefreshCw className="animate-spin h-5 w-5 text-muted-foreground" />
          <span className="ml-2 text-muted-foreground">Loading trU value data...</span>
        </CardContent>
      </Card>
    );
  }
  // Show compact version for dashboard integration
  if (compact) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className={`bg-black/30 rounded-lg p-3 border border-red-500/10 ${className}`}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Zap className="h-4 w-4 text-red-400 mr-2" />
            <span className="font-medium text-sm">trU Value</span>
          </div>
          <Badge 
            variant="outline" 
            className={`${priceChange.direction === 'up' ? 'bg-green-500/10 text-green-400' : 
                          priceChange.direction === 'down' ? 'bg-red-500/10 text-red-400' : 
                          'bg-red-500/10 text-red-400'}`}
          >
            <span className="flex items-center text-xs">
              {priceChange.direction === 'up' ? <ArrowUp className="h-3 w-3 mr-1" /> : 
               priceChange.direction === 'down' ? <ArrowDown className="h-3 w-3 mr-1" /> : null}
              {priceChange.percent.toFixed(2)}%
            </span>
          </Badge>
        </div>
        <div className="mt-2 flex items-baseline">
          <span className="text-xl font-bold">${(ghostState?.truValue || 0).toFixed(6)}</span>
          <span className="text-xs ml-2 text-muted-foreground">per trU</span>
        </div>
        <div className="mt-3 grid grid-cols-2 gap-2">
          <div className="flex flex-col">
            <span className="text-xs text-muted-foreground">UMatter Rate</span>
            <span className="text-sm">1 UM = {(0.01 * ghostState.truValue).toFixed(6)} trU</span>
          </div>
          <div className="flex flex-col">
            <span className="text-xs text-muted-foreground">Last Updated</span>
            <span className="text-sm flex items-center">
              <Clock className="h-3 w-3 mr-1 text-muted-foreground" />
              {getTimeSinceUpdate()}
            </span>
          </div>
        </div>
      </motion.div>
    );
  }
  // Full detailed version for wallet page
  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center">
              <Zap className="mr-2 h-5 w-5 text-red-500" />
              trU Real-Time Value
            </CardTitle>
            <CardDescription>
              Current value synced with nU soUrce and market conditions
            </CardDescription>
          </div>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge 
                  variant="outline" 
                  className={`${priceChange.direction === 'up' ? 'bg-green-500/10 text-green-400 border-green-500/20' : 
                              priceChange.direction === 'down' ? 'bg-red-500/10 text-red-400 border-red-500/20' : 
                              'bg-blue-500/10 text-blue-400 border-blue-500/20'}`}
                >
                  <span className="flex items-center">
                    {priceChange.direction === 'up' ? <ArrowUpRight className="h-3 w-3 mr-1" /> : 
                    priceChange.direction === 'down' ? <ArrowDown className="h-3 w-3 mr-1" /> : null}
                    {priceChange.percent.toFixed(2)}%
                  </span>
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>Real-time value change</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-end mb-6">
          <div>
            <AnimatePresence mode="wait">
              <motion.div
                key={ghostState.truValue?.toString()}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
                className="flex items-baseline"
              >
                <DollarSign className="h-5 w-5 text-red-400 mr-1" />
                <span className="text-3xl font-bold">{(ghostState?.truValue || 0).toFixed(6)}</span>
                <span className="text-sm ml-2 text-muted-foreground">per trU</span>
              </motion.div>
            </AnimatePresence>
            <div className="flex items-center mt-1 text-sm text-muted-foreground">
              <Clock className="h-3 w-3 mr-1" />
              <span>Last updated {getTimeSinceUpdate()}</span>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm">Your Balance</div>
            <div className="text-2xl font-bold flex items-center justify-end">
              <AnimatedBalance currency="TRU" value={ghostState?.tru} showIcon={false} />
              <span className="text-sm ml-2 text-muted-foreground">trU</span>
            </div>
            <div className="text-sm text-muted-foreground">
              ≈ ${((ghostState?.tru || 0) * (ghostState?.truValue || 0)).toFixed(2)}
            </div>
          </div>
        </div>
        {showDetails && (
          <>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="bg-black/30 p-3 rounded-lg">
                <div className="flex justify-between items-center">
                  <div className="text-sm font-medium">UMatter Rate</div>
                  <Badge variant="outline" className="bg-blue-500/10 text-blue-400 border-blue-500/20">
                    <BatteryCharging className="h-3 w-3 mr-1" /> Energy
                  </Badge>
                </div>
                <div className="text-xl font-bold mt-1">{(0.01 * ghostState.truValue).toFixed(6)}</div>
                <div className="text-xs text-muted-foreground">trU per 1 UMatter</div>
              </div>
              <div className="bg-black/30 p-3 rounded-lg">
                <div className="flex justify-between items-center">
                  <div className="text-sm font-medium">System Health</div>
                  <Badge variant="outline" className="bg-green-500/10 text-green-400 border-green-500/20">
                    <Gauge className="h-3 w-3 mr-1" /> Strong
                  </Badge>
                </div>
                <div className="mt-2">
                  <div className="flex justify-between items-center text-xs mb-1">
                    <span>Network Strength</span>
                    <span>{Math.round((ghostState.systemHealth?.networkStrength || 0) * 100)}%</span>
                  </div>
                  <Progress 
                    value={Math.round((ghostState.systemHealth?.networkStrength || 0) * 100)} 
                    max={100}
                    className="h-1 bg-black/50 [&>div]:bg-green-500"
                  />
                </div>
              </div>
            </div>
            <div className="bg-gradient-to-r from-red-950/20 to-black/20 rounded-lg p-3 border border-red-500/10">
              <div className="flex items-center mb-2">
                <BarChart className="h-4 w-4 text-red-400 mr-2" />
                <span className="font-medium">Market Factors</span>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Battery Influence</span>
                  <span>{ghostState.charging ? '+2.4%' : '-0.8%'}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Peer Activity</span>
                  <span>+{(ghostState.peers * 0.1).toFixed(1)}%</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Quantum Entropy</span>
                  <span>+{((ghostState.systemHealth?.quantumEntropy || 0) * 3).toFixed(1)}%</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Pool Stability</span>
                  <span>+{((ghostState.systemHealth?.poolStability || 0) * 5).toFixed(1)}%</span>
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
      <CardFooter className="pt-0">
        <div className="text-sm text-muted-foreground w-full">
          <div className="flex items-center justify-between">
            <span>Based on real-time nU energy contribUtions</span>
            <RefreshCw className="h-3 w-3 animate-spin opacity-70" />
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}