[vite] connecting...
DOMException {}
stack: "SyntaxError: Failed to construct 'WebSocket': The URL 'wss://localhost:undefined/?token=pHEH9xh6jtmN' is invalid.↵ at new t (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218737)↵ at setupWebSocket (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@vite/client:536:19)↵ at fallback (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@vite/client:509:16)↵ at WebSocket.<anonymous> (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@vite/client:555:7)"
get stack: ƒ ()
set stack: ƒ ()
[[Prototype]]: DOMException

at t.value (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:17465)
at new t (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:12630)
at t.value (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:32766)
at https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:34400
[RealTimeDeviceManager] Initializing real device APIs...
[RealTimeDeviceManager] Battery API connected
[RealTimeDeviceManager] Current device added: 
Object {id: "device-MTkyMHgxMDgw", name: "MacBook", type: "laptop", battery: 100, isCharging: true, …}
[RealTimeDeviceManager] USB API initialized with 0 devices
[RealTimeDeviceManager] Bluetooth API available
[RealTimeDeviceManager] WebRTC P2P initialized
[RealTimeDeviceManager] Starting real-time monitoring...
[Extension] No extension detected
[RealTimeEnergyDisplay] DISABLED - No DOM interaction tracking
[Extension] No extension detected
[NeuralEnergyMonitor] Real-time update: 
Object {battery: "100.0", performance: "15.9", charging: true}
[InteractionTracking] Started session: spunder_mc6x96gp_77ik5mj09
[NeuralEnergyMonitor] Real-time update: 
Object {battery: "100.0", performance: "15.9", charging: true}
[NeuralEnergyMonitor] Real-time update: 
Object {battery: "100.0", performance: "15.9", charging: true}
[NeuralEnergyMonitor] Real-time update: 
Object {battery: "100.0", performance: "15.9", charging: true}
[NeuralEnergyMonitor] Real-time update: 
Object {battery: "100.0", performance: "15.9", charging: true}
[NeuralEnergyMonitor] Real-time update: 
Object {battery: "100.0", performance: "15.9", charging: true}
[NeuralEnergyMonitor] Real-time update: 
Object {battery: "100.0", performance: "15.9", charging: true}
