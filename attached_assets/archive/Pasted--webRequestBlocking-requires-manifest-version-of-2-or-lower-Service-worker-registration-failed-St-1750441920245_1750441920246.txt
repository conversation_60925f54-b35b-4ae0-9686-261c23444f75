'webRequestBlocking' requires manifest version of 2 or lower.
Service worker registration failed. Status code: 15
Uncaught TypeError: Cannot read properties of undefined (reading 'create')
Context
background.js
Stack Trace
background.js:88 (anonymous function)
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70
71
72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90
91
92
93
94
95
96
97
98
99
100
101
102
103
104
105
106
107
108
109
110
111
112
113
114
115
116
117
118
119
120
121
122
// nU SpUnder Web - Background Service Worker
// Manages web ad interception and UMatter conversion

chrome.runtime.onInstalled.addListener(() => {
  console.log('nU SpUnder Web extension installed');
  
  // Initialize storage with default values
  chrome.storage.local.set({
    totalUMatter: 0,
    totalInterceptions: 0,
    isActive: true,
    authToken: null
  });

  // Set up badge
  chrome.action.setBadgeBackgroundColor({ color: '#00ffff' });
  chrome.action.setBadgeText({ text: '0' });
});

// Listen for messages from content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  switch (message.type) {
    case 'update-badge':
      chrome.action.setBadgeText({ 
        text: message.count.toString(),
        tabId: sender.tab.id 
      });
      
      // Update stored stats
      chrome.storage.local.get(['totalUMatter', 'totalInterceptions'], (result) => {
        chrome.storage.local.set({
          totalUMatter: (result.totalUMatter || 0) + (message.umatter || 0),
          totalInterceptions: (result.totalInterceptions || 0) + 1
        });
      });
      break;
      
    case 'get-auth-token':
      chrome.storage.local.get(['authToken'], (result) => {
        sendResponse({ authToken: result.authToken });
      });
      return true; // Keep channel open for async response
      
    case 'set-auth-token':
      chrome.storage.local.set({ authToken: message.token });
      sendResponse({ success: true });
      break;
  }
});

// Web request interception for ad networks
chrome.webRequest.onBeforeRequest.addListener(
  (details) => {
    // Check if this is an ad request
    const adDomains = [
      'googlesyndication.com',
      'doubleclick.net',
      'googleadservices.com',
      'facebook.com/tr',
      'amazon-adsystem.com'
    ];
    
    const isAdRequest = adDomains.some(domain => 
      details.url.includes(domain)
    );
    
    if (isAdRequest) {
      // Send message to content script about intercepted ad request
      chrome.tabs.sendMessage(details.tabId, {
        type: 'ad-request-intercepted',
        url: details.url,
        timestamp: Date.now()
      }).catch(() => {
        // Ignore errors if content script isn't ready
      });
    }
    
    return { cancel: false }; // Don't block, just track
  },
  {
    urls: ["<all_urls>"],
    types: ["image", "script", "xmlhttprequest", "sub_frame"]
  },
  ["requestBody"]
);

// Context menu for manual UMatter claiming
chrome.contextMenus.create({
  id: "claim-umatter",
  title: "Claim UMatter from this element",
  contexts: ["all"]
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "claim-umatter") {
    chrome.tabs.sendMessage(tab.id, {
      type: 'manual-umatter-claim',
      timestamp: Date.now()
    });
  }
});

// Periodic stats sync with nU Universe backend
setInterval(() => {
  chrome.storage.local.get(['totalUMatter', 'totalInterceptions', 'authToken'], (result) => {
    if (result.authToken && result.totalUMatter > 0) {
      // Sync with backend (placeholder URL)
      fetch('https://your-repl-url.replit.app/api/extension/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${result.authToken}`
        },
        body: JSON.stringify({
          totalUMatter: result.totalUMatter,
          totalInterceptions: result.totalInterceptions,
          timestamp: Date.now()
        })
      }).catch(console.error);
    }
  });
}, 60000); // Every minute