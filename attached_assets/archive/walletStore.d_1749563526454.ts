// Type declarations for walletStore.ts
declare module '@/lib/stores/walletStore' { 
  export interface WalletBalance {
    TRU: number;
    UMATTER: number;
    NUVA: number;
    SBU: number;
    [key: string]: number;
  }
  export interface WalletItem {
    id: string;
    type: string;
    name: string;
    description?: string;
    amount: number;
    value?: number;
    imageUrl?: string;
    timestamp?: number;
    status?: string;
    metadata?: Record<string, any>;
  }
  export interface WalletState {
    balance: WalletBalance;
    items: WalletItem[];
    transactions: any[];
    isLoading: boolean;
    error: string | null;
    lastSync: number;
  }
  export interface WalletStore extends WalletState {
    addTransaction: (transaction: any) => void;
    updateBalance: (currency: string, amount: number) => void;
    setBalance: (balance: Partial<WalletBalance>) => void;
    addItem: (item: WalletItem) => void;
    removeItem: (id: string) => void;
    updateItem: (id: string, updates: Partial<WalletItem>) => void;
    setItems: (items: WalletItem[]) => void;
    setError: (error: string | null) => void;
    setLoading: (isLoading: boolean) => void;
    syncWallet: () => Promise<void>;
    reset: () => void;
  }
  export function useWalletStore(selector?: (state: WalletStore) => any): any;
}
