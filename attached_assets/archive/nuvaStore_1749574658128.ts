import { create } from "zustand";
import { nanoid } from "nanoid";
import { NuvaEnergy } from "@/components/NuvaEnergyStorage";
// Add Window interface extension for TypeScript to recognize window.bot
declare global {
  interface Window {
    bot?: any;
  }
}
interface NuvaState {
  // Storage
  nuvaEnergy: NuvaEnergy[];
  maxStorageCapacity: number;
  currentTotalStored: number;
  pendingShares: Map<string, string>; // Map of share codes to energy IDs
  // Actions
  storeEnergy: (amount: number, source: NuvaEnergy["source"]) => Promise<boolean>;
  useEnergy: (energyId: string, amount: number) => Promise<boolean>;
  shareEnergy: (energyId: string) => Promise<string | null>;
  combineEnergy: (friendCode: string) => Promise<boolean>;
  redeemSharedEnergy: (shareCode: string) => Promise<boolean>;
  // Helpers
  getAvailableCapacity: () => number;
  getTotalStoredEnergy: () => number;
}
export const useNuvaStore = create<NuvaState>((set, get) => ({
  nuvaEnergy: [],
  maxStorageCapacity: 100, // Maximum nUva that can be stored
  currentTotalStored: 0,
  pendingShares: new Map(),
  storeEnergy: async (amount, source) => {
    // Check if there's enough capacity
    const currentTotal = get().currentTotalStored;
    const maxCapacity = get().maxStorageCapacity;
    if (currentTotal + amount > maxCapacity) {
      console.error(`[nUva] Cannot store energy: capacity exceeded (${currentTotal}/${maxCapacity})`);
      return false;
    }
    try {
      // Generate a unique ID for this energy
      const id = nanoid();
      const now = Date.now();
      // Create the energy object
      const energy: NuvaEnergy = {
        id,
        amount,
        source,
        createdAt: now,
        expiresAt: null, // nUva energy doesn't expire
        isShared: false
      };
      // Update the store
      set(state => ({
        nuvaEnergy: [...state.nuvaEnergy, energy],
        currentTotalStored: state.currentTotalStored + amount
      }));
      console.log(`[nUva] Stored ${amount} nUva energy from ${source}`);
      return true;
    } catch (error) {
      console.error("[nUva] Error storing energy:", error);
      return false;
    }
  },
  useEnergy: async (energyId, amount) => {
    const { nuvaEnergy } = get();
    // Find the energy by ID
    const energyIndex = nuvaEnergy.findIndex(e => e.id === energyId);
    if (energyIndex === -1) {
      console.error(`[nUva] Energy with ID ${energyId} not found`);
      return false;
    }
    const energy = nuvaEnergy[energyIndex];
    // Check if there's enough energy to use
    if (energy.amount < amount) {
      console.error(`[nUva] Not enough energy: requested ${amount}, available ${energy.amount}`);
      return false;
    }
    try {
      // Calculate battery boost based on energy amount
      // 0.01 nUva = 1% battery
      const batteryBoost = amount * 100;
      // Apply the battery boost (simulate charging)
      // Access GhostBot to update the battery
      try {
        // Get the bot instance directly rather than using getState()
        const bot = window.bot || null;
        if (bot) {
          // If this is a SUpernUva, we get 50% more battery charging efficiency
          const boostedAmount = energy.friendId ? amount * 1.5 : amount;
          const batteryBoostPercentage = Math.min(100, Math.round(boostedAmount * 100));
          // Update the battery level
          await bot.updateBatteryState(
            Math.min(100, (bot.getState().batteryLevel || 0) + batteryBoostPercentage),
            true // Set as charging
          );
          console.log(`[nUva] Used ${amount} nUva energy to boost battery by ${batteryBoostPercentage}%`);
        }
      } catch (err) {
        console.error("[nUva] Error updating battery:", err);
      }
      // Update the energy storage
      const updatedEnergy = [...nuvaEnergy];
      if (energy.amount <= amount) {
        // Remove the energy if used completely
        updatedEnergy.splice(energyIndex, 1);
      } else {
        // Reduce the energy amount
        updatedEnergy[energyIndex] = {
          ...energy,
          amount: energy.amount - amount
        };
      }
      // Update the store
      set(state => ({
        nuvaEnergy: updatedEnergy,
        currentTotalStored: state.currentTotalStored - amount
      }));
      return true;
    } catch (error) {
      console.error("[nUva] Error using energy:", error);
      return false;
    }
  },
  shareEnergy: async (energyId) => {
    const { nuvaEnergy, pendingShares } = get();
    // Find the energy by ID
    const energyIndex = nuvaEnergy.findIndex(e => e.id === energyId);
    if (energyIndex === -1) {
      console.error(`[nUva] Energy with ID ${energyId} not found`);
      return null;
    }
    const energy = nuvaEnergy[energyIndex];
    // Check if already shared
    if (energy.isShared) {
      console.error(`[nUva] Energy is already shared`);
      return null;
    }
    try {
      // Generate a share code
      const shareCode = nanoid(8);
      // Mark the energy as shared
      const updatedEnergy = [...nuvaEnergy];
      updatedEnergy[energyIndex] = {
        ...energy,
        isShared: true,
        shareCode
      };
      // Update the pending shares map
      const updatedPendingShares = new Map(pendingShares);
      updatedPendingShares.set(shareCode, energyId);
      // Update the store
      set({
        nuvaEnergy: updatedEnergy,
        pendingShares: updatedPendingShares
      });
      console.log(`[nUva] Energy shared with code: ${shareCode}`);
      return shareCode;
    } catch (error) {
      console.error("[nUva] Error sharing energy:", error);
      return null;
    }
  },
  combineEnergy: async (friendCode) => {
    const { nuvaEnergy, pendingShares } = get();
    // Check if the share code exists
    if (!pendingShares.has(friendCode)) {
      console.error(`[nUva] Invalid share code: ${friendCode}`);
      return false;
    }
    try {
      // Get the energy ID from the share code
      const friendEnergyId = pendingShares.get(friendCode)!;
      // Find the friend's energy
      const friendEnergy = nuvaEnergy.find(e => e.id === friendEnergyId);
      if (!friendEnergy) {
        console.error(`[nUva] Friend's energy not found`);
        return false;
      }
      // Create a SUpernUva energy (combined energy)
      const id = nanoid();
      const now = Date.now();
      // If the user has no energy of their own, we'll create a new one
      // Otherwise we'll find their largest energy to combine with
      let userEnergy = nuvaEnergy
        .filter(e => !e.isShared && !e.friendId)
        .sort((a, b) => b.amount - a.amount)[0];
      const supernovaAmount = userEnergy 
        ? userEnergy.amount + friendEnergy.amount 
        : friendEnergy.amount; // If no user energy, just use friend's
      const supernova: NuvaEnergy = {
        id,
        amount: supernovaAmount,
        source: "friend",
        createdAt: now,
        expiresAt: null,
        isShared: false,
        friendId: friendEnergyId,
        friendName: "Friend" // In a real implementation, we'd get the friend's name
      };
      // Remove the user's energy if it was used
      const updatedEnergy = userEnergy
        ? nuvaEnergy.filter(e => e.id !== userEnergy.id)
        : [...nuvaEnergy];
      // Remove the friend's energy from pendingShares
      const updatedPendingShares = new Map(pendingShares);
      updatedPendingShares.delete(friendCode);
      // Update the store
      set({
        nuvaEnergy: [...updatedEnergy, supernova],
        pendingShares: updatedPendingShares,
        // The total stored energy doesn't change, as we're just combining existing energy
      });
      console.log(`[nUva] Created SUpernUva with ${supernovaAmount} nUva energy`);
      return true;
    } catch (error) {
      console.error("[nUva] Error combining energy:", error);
      return false;
    }
  },
  redeemSharedEnergy: async (shareCode) => {
    // Implement this function if needed to redeem shared energy
    // For now, we'll use the combineEnergy function
    return get().combineEnergy(shareCode);
  },
  getAvailableCapacity: () => {
    const { maxStorageCapacity, currentTotalStored } = get();
    return maxStorageCapacity - currentTotalStored;
  },
  getTotalStoredEnergy: () => {
    return get().currentTotalStored;
  }
}));