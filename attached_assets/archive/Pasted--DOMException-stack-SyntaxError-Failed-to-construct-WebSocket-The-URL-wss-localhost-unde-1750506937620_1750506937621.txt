
DOMException {}
stack: "SyntaxError: Failed to construct 'WebSocket': The URL 'wss://localhost:undefined/?token=Q8tS6Im8W9Hy' is invalid.↵ at new t (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:218737)↵ at setupWebSocket (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@vite/client:536:19)↵ at fallback (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@vite/client:509:16)↵ at WebSocket.<anonymous> (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@vite/client:555:7)"
get stack: ƒ ()
length: 0
name: ""
[[Prototype]]: ƒ ()
apply: ƒ apply()
arguments: "'caller', 'callee', and 'arguments' properties may not be accessed on strict mode functions or the arguments objects for calls to them"
get arguments: ƒ ()
set arguments: ƒ ()
bind: ƒ bind()
call: ƒ call()
caller: "'caller', 'callee', and 'arguments' properties may not be accessed on strict mode functions or the arguments objects for calls to them"
get caller: ƒ ()
set caller: ƒ ()
constructor: ƒ Function()
length: 0
name: ""
toString: ƒ toString()
Symbol(Symbol.hasInstance): undefined
[[Prototype]]: Object
set stack: ƒ ()
length: 1
name: ""
[[Prototype]]: ƒ ()
apply: ƒ apply()
arguments: "'caller', 'callee', and 'arguments' properties may not be accessed on strict mode functions or the arguments objects for calls to them"
get arguments: ƒ ()
set arguments: ƒ ()
bind: ƒ bind()
call: ƒ call()
caller: "'caller', 'callee', and 'arguments' properties may not be accessed on strict mode functions or the arguments objects for calls to them"
get caller: ƒ ()
set caller: ƒ ()
constructor: ƒ Function()
length: 0
name: ""
toString: ƒ toString()
Symbol(Symbol.hasInstance): undefined
[[Prototype]]: Object
[[Prototype]]: DOMException
ABORT_ERR: 20
DATA_CLONE_ERR: 25
DOMSTRING_SIZE_ERR: 2
HIERARCHY_REQUEST_ERR: 3
INDEX_SIZE_ERR: 1
INUSE_ATTRIBUTE_ERR: 10
INVALID_ACCESS_ERR: 15
INVALID_CHARACTER_ERR: 5
INVALID_MODIFICATION_ERR: 13
INVALID_NODE_TYPE_ERR: 24
INVALID_STATE_ERR: 11
NAMESPACE_ERR: 14
NETWORK_ERR: 19
NOT_FOUND_ERR: 8
NOT_SUPPORTED_ERR: 9
NO_DATA_ALLOWED_ERR: 6
NO_MODIFICATION_ALLOWED_ERR: 7
QUOTA_EXCEEDED_ERR: 22
SECURITY_ERR: 18
SYNTAX_ERR: 12
TIMEOUT_ERR: 23
TYPE_MISMATCH_ERR: 17
URL_MISMATCH_ERR: 21
VALIDATION_ERR: 16
WRONG_DOCUMENT_ERR: 4
code: 12
get code: ƒ get code()
message: "Failed to construct 'WebSocket': The URL 'wss://localhost:undefined/?token=Q8tS6Im8W9Hy' is invalid."
get message: ƒ get message()
name: "SyntaxError"
get name: ƒ get name()
constructor: ƒ DOMException()
Symbol(Symbol.toStringTag): undefined
[[Prototype]]: Object

[RealTimeDeviceManager] Initializing real device APIs...
[RealExtensionDashboard] Extension dashboard integration active
[CommunityNumentum] Real community tracking initialized
[IoT] Philips Hue discovery requires backend proxy for CORS
[IoT] Added device: Smart Bulbs (Simulated)
[IoT] Fitbit not available, using device motion sensors
