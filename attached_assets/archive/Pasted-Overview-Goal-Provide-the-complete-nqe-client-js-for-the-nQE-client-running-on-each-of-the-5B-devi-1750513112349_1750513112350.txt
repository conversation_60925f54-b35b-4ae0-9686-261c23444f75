Overview
Goal: Provide the complete nqe-client.js for the nQE client running on each of the 5B devices and the database migration scripts for PostgreSQL to support nQE tasks and results.

Context: nQE emulates Shor’s (factoring via distributed continued fractions) and Grover’s (search via distributed Monte Carlo) algorithms classically, using nU Web’s P2P network and neural energy (20W/device, 0.000018 UMatter/2s).

Integration: The client works with the backend (server.js), frontend (NuQuantum.tsx), and Chrome extension (background.js) from the previous response, and the database stores task states and results.

1. Full nqe-client.js
The nqe-client.js is a Node.js microservice running on each nU Web device (e.g., user laptops, phones, or IoT nodes). It processes task chunks for nUShor (factoring) or nUGrover (search), communicates via a P2P protocol (using libp2p), and integrates with the nUmentum engine for energy tracking. Below is the complete implementation, expanding on the earlier snippet.
javascript

const { Libp2p } = require('libp2p');
const { noise } = require('@chainsafe/libp2p-noise');
const { mplex } = require('@libp2p/mplex');
const { tcp } = require('@libp2p/tcp');
const crypto = require('crypto');

// Mock nUmentum client for energy tracking (replace with actual nU Web API)
const nUmentum = require('./numentum-client'); // Hypothetical SDK

// Client configuration
const CONFIG = {
  peerId: crypto.randomBytes(32).toString('hex'),
  port: 0, // Auto-select
  energyCost: { factor: 0.001, search: 0.0005 }, // UMatter per task
  biometricBoost: 1.25, // Premium user boost
};

// Start nQE client
async function startClient() {
  // Initialize libp2p node
  const node = await Libp2p.create({
    addresses: { listen: ['/ip4/0.0.0.0/tcp/0'] },
    modules: {
      transport: [tcp()],
      connEncryption: [noise()],
      streamMuxer: [mplex()],
    },
    peerId: CONFIG.peerId,
  });

  await node.start();
  console.log(`nQE Client started: ${node.peerId.toString()}`);

  // Handle incoming tasks
  node.handle('/nqe/task/1.0.0', ({ stream }) => {
    stream.on('data', async (data) => {
      try {
        const { taskId, type, chunk, userId } = JSON.parse(data.toString());
        const boost = await getBiometricBoost(userId); // Check premium status
        const result = await processTask(type, chunk);
        const energyCost = CONFIG.energyCost[type] / boost;

        // Deduct UMatter
        await nUmentum.deductUMatter(userId, energyCost);

        // Report result
        await reportResult(node, taskId, result);
        stream.write(JSON.stringify({ status: 'success', taskId }));
      } catch (error) {
        console.error(`Task error: ${error.message}`);
        stream.write(JSON.stringify({ status: 'error', taskId }));
      }
    });
  });

  // Handle result aggregation (for coordinator feedback)
  node.handle('/nqe/result/1.0.0', ({ stream }) => {
    stream.on('data', (data) => {
      console.log(`Result feedback: ${data.toString()}`);
    });
  });

  return node;
}

// Process task chunk (nUShor or nUGrover)
async function processTask(type, chunk) {
  if (type === 'factor') {
    return computeModExp(chunk); // nUShor: Modular exponentiation
  } else if (type === 'search') {
    return monteCarloSearch(chunk); // nUGrover: Probabilistic search
  }
  throw new Error(`Unknown task type: ${type}`);
}

// Modular exponentiation for nUShor
function computeModExp({ a, x, N }) {
  // Efficient modular exponentiation (square-and-multiply)
  let result = 1n;
  a = BigInt(a) % BigInt(N);
  x = BigInt(x);
  const n = BigInt(N);
  while (x > 0n) {
    if (x & 1n) result = (result * a) % n;
    a = (a * a) % n;
    x >>= 1n;
  }
  return result.toString();
}

// Monte Carlo search for nUGrover
function monteCarloSearch({ query, data, weights }) {
  // Probabilistic search with amplification
  const amplified = amplifyWeights(data, weights, query);
  for (const item of amplified) {
    if (matchQuery(item, query)) return item;
  }
  return null;
}

// Amplify weights (mimics Grover’s diffusion)
function amplifyWeights(data, weights, query) {
  const totalWeight = weights.reduce((sum, w) => sum + w, 0);
  const avgWeight = totalWeight / weights.length;
  const amplified = data.map((item, i) => ({
    item,
    weight: weights[i] + (avgWeight - weights[i]) * 0.5, // Simplified diffusion
  }));
  // Sort by weight for probabilistic selection
  return amplified
    .sort((a, b) => b.weight - a.weight)
    .map((entry) => entry.item);
}

// Match query (simplified, customize for nU Web marketplace)
function matchQuery(item, query) {
  return item.toString().includes(query); // Example: string match
}

// Report result to coordinator
async function reportResult(node, taskId, result) {
  const coordinator = await node.dialProtocol('/nqe/result/1.0.0');
  await coordinator.stream.write(JSON.stringify({ taskId, result }));
}

// Get biometric boost (mock, integrate with nU Web auth)
async function getBiometricBoost(userId) {
  // Check user’s premium status via nUmentum
  const user = await nUmentum.getUser(userId);
  return user.isPremium ? CONFIG.biometricBoost : 1.0;
}

// Main
startClient().catch((error) => {
  console.error(`Client failed: ${error.message}`);
});

Key Features:
P2P Communication: Uses libp2p for task receiving (/nqe/task/1.0.0) and result reporting (/nqe/result/1.0.0).

Task Processing:
nUShor: Computes modular exponentiation (computeModExp) for period-finding, using BigInt for large numbers.

nUGrover: Runs a Monte Carlo search (monteCarloSearch) with weight amplification to mimic Grover’s diffusion.

Energy Integration: Deducts UMatter (0.001 for factoring, 0.0005 for search, adjusted by 1.25x biometric boost) via a mock nUmentum SDK.

Scalability: Lightweight (~200 lines), runs on low-power devices, processes small chunks (e.g., one exponentiation or 0.2 records).

Error Handling: Catches and reports errors, ensuring fault tolerance.

Dependencies:
libp2p, @chainsafe/libp2p-noise, @libp2p/mplex, @libp2p/tcp (P2P networking).

crypto (Node.js built-in, for peer ID).

numentum-client (hypothetical, replace with nU Web’s actual energy SDK).

Deployment:
Install: npm install libp2p @chainsafe/libp2p-noise @libp2p/mplex @libp2p/tcp.

Run: node nqe-client.js on each device, auto-joins nU Web’s P2P network.

Distribute via nU Web’s update system (e.g., Chrome extension pushes).

2. Database Migrations
The PostgreSQL database needs tables to track nQE tasks and results, integrated with nU Web’s existing 25+ tables (e.g., marketplace, user wallets). Below are the migration scripts to create nqe_tasks and nqe_results, plus a rollback script.
Migration Script (migrations/20250621_create_nqe_tables.sql):
sql

-- Create nqe_tasks table
CREATE TABLE nqe_tasks (
  task_id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL, -- References nU Web user
  type ENUM('factor', 'search') NOT NULL,
  input JSONB NOT NULL, -- Stores number or query
  status ENUM('submitted', 'running', 'complete', 'failed') NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(user_id) -- Assumes users table exists
);

-- Create nqe_results table
CREATE TABLE nqe_results (
  task_id VARCHAR(36) PRIMARY KEY,
  output JSONB NOT NULL, -- Stores factors or search result
  energy_cost DECIMAL(10,6) NOT NULL, -- UMatter
  completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (task_id) REFERENCES nqe_tasks(task_id) ON DELETE CASCADE
);

-- Index for performance
CREATE INDEX idx_nqe_tasks_user_id ON nqe_tasks(user_id);
CREATE INDEX idx_nqe_tasks_status ON nqe_tasks(status);

Rollback Script (migrations/20250621_drop_nqe_tables.sql):
sql

-- Drop tables in reverse order due to foreign key
DROP TABLE IF EXISTS nqe_results;
DROP TABLE IF EXISTS nqe_tasks;

Integration:
Schema Details:
nqe_tasks: Stores task metadata (ID, user, type, input, status). JSONB for flexible inputs (e.g., { "number": 2047 } or { "query": "NUVA123" }).

nqe_results: Stores outputs (e.g., { "factors": [23, 89] } or { "result": "NUVA123" }) and energy cost (e.g., 0.1 UMatter).

Links to users table (assumed in nU Web) for user authentication and energy tracking.

Apply Migration:
Run: psql -U nUweb -d nUweb_db -f migrations/20250621_create_nqe_tables.sql.

Use a migration tool like knex or sequelize if nU Web uses one:
javascript

// Example with knex
exports.up = async (knex) => {
  await knex.schema.createTable('nqe_tasks', (table) => {
    table.string('task_id', 36).primary();
    table.string('user_id', 36).notNullable().references('user_id').inTable('users');
    table.enum('type', ['factor', 'search']).notNullable();
    table.jsonb('input').notNullable();
    table.enum('status', ['submitted', 'running', 'complete', 'failed']).notNullable();
    table.timestamp('created_at').defaultTo(knex.fn.now());
  });
  await knex.schema.createTable('nqe_results', (table) => {
    table.string('task_id', 36).primary().references('task_id').inTable('nqe_tasks').onDelete('CASCADE');
    table.jsonb('output').notNullable();
    table.decimal('energy_cost', 10, 6).notNullable();
    table.timestamp('completed_at').defaultTo(knex.fn.now());
  });
};

exports.down = async (knex) => {
  await knex.schema.dropTableIfExists('nqe_results');
  await knex.schema.dropTableIfExists('nqe_tasks');
};

Performance: Indexes on user_id and status optimize queries for task tracking and user dashboards.

Integration with nU Web
Backend Coordination:
The server.js from the previous response distributes tasks to clients via libp2p and updates nqe_tasks/nqe_results.

Example query to track tasks:
sql

SELECT t.task_id, t.type, t.status, r.output, r.energy_cost
FROM nqe_tasks t
LEFT JOIN nqe_results r ON t.task_id = r.task_id
WHERE t.user_id = 'user123';

Frontend:
The NuQuantum.tsx component displays tasks and results, querying /nqe/status and /nqe/results.

Energy costs (e.g., 0.1 UMatter) are shown in the dashboard, linked to the user’s TrU/NUVA wallet.

Chrome Extension:
Triggers tasks from marketplace queries, inserting into nqe_tasks via /nqe/submit.

Energy Economy:
nqe-client.js deducts UMatter via nUmentum.deductUMatter, syncing with nU Web’s wallet system.

Example: A factoring task (0.1 UMatter) costs 1 TrU (0.1 UMatter = 1 TrU), offset by marketplace earnings.

Example Workflow
User Submits Task:
Via React dashboard: “Factor 2047” or “Search NUVA123 in marketplace.”

Backend inserts into nqe_tasks:
sql

INSERT INTO nqe_tasks (task_id, user_id, type, input, status)
VALUES ('task-uuid', 'user123', 'factor', '{"number": 2047}', 'submitted');

Task Distribution:
Backend shards task (e.g., 2047 → 5B modular exponentiations).

nqe-client.js on each device processes a chunk (e.g., 2xmod  20472^x \mod 20472^x \mod 2047
).

Processing:
Client computes (e.g., 210mod  2047=10242^{10} \mod 2047 = 10242^{10} \mod 2047 = 1024
), deducts 0.001 UMatter.

Reports result via /nqe/result/1.0.0.

Result Aggregation:
Backend aggregates (e.g., finds period, factors 2047 = 23 × 89).

Inserts into nqe_results:
sql

INSERT INTO nqe_results (task_id, output, energy_cost)
VALUES ('task-uuid', '{"factors": [23, 89]}', 0.1);

User Views Result:
Dashboard shows: “Factors: 23, 89, Cost: 0.1 UMatter.”

Tagged Details (DetailTag Tracker)
Existing tag:
“nU Web is the nU internet” [Category: nU Web Core].
Suggested tags:

“Tag this: nqe-client.js runs nUShor and nUGrover on 5B nU Web devices.” [Category: nQE]

“Tag this: nQE database uses nqe_tasks and nqe_results with JSONB for flexibility.” [Category: nQE]
Want to tag these? E.g., “Tag this: [detail].”

Testing & Deployment
Test Client:
Run locally: node nqe-client.js.

Simulate tasks with a mock coordinator sending { taskId: "test1", type: "factor", chunk: { a: 2, x: 10, N: 2047 } }.

Verify UMatter deductions and results.

Apply Migrations:
Run psql -f migrations/20250621_create_nqe_tables.sql.

Test inserts:
sql

INSERT INTO nqe_tasks (task_id, user_id, type, input, status)
VALUES ('test1', 'user123', 'search', '{"query": "NUVA123"}', 'submitted');

Scale: Deploy to 5B devices via nU Web’s P2P update system (e.g., extension pushes).

Limitations
Classical Limits: Without quantum hardware, nUShor and nUGrover lose exponential/quadratic speedups, relying on 5B-device parallelism.

Network Latency: P2P gossip protocol (~100ms) may delay large tasks; optimize with regional coordinators.

Energy Tracking: Assumes nUmentum SDK; implement actual wallet integration.

