[vite] connecting...
[vite] connected.
Warning: You provided a &#x60;checked&#x60; prop to a form field without an &#x60;onChange&#x60; handler. This will render a read-only field. If the field should be mutable use &#x60;defaultChecked&#x60;. Otherwise, set either &#x60;onChange&#x60; or &#x60;readOnly&#x60;.
    at input
    at label
    at div
    at div
    at nav
    at aside
    at div
    at div
    at Dashboard (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/src/pages/Dashboard.tsx:46:20)
    at Route (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=f1f3659c:323:16)
    at Switch (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=f1f3659c:379:17)
    at Router (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/src/App.tsx?v=Q3QPaq5TA3ymjog8gruBi:32:42)
    at Provider (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-PLT6GTVM.js?v=f1f3659c:38:15)
    at TooltipProvider (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=f1f3659c:63:5)
    at QueryClientProvider (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@tanstack_react-query.js?v=f1f3659c:2805:3)
    at App
at input
at label
at div
at div
at nav
at aside
at div
at div
at Dashboard (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/src/pages/Dashboard.tsx:46:20)
at Route (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=f1f3659c:323:16)
at Switch (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/wouter.js?v=f1f3659c:379:17)
at Router (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/src/App.tsx?v=Q3QPaq5TA3ymjog8gruBi:32:42)
at Provider (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-PLT6GTVM.js?v=f1f3659c:38:15)
at TooltipProvider (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@radix-ui_react-tooltip.js?v=f1f3659c:63:5)
at QueryClientProvider (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/@tanstack_react-query.js?v=f1f3659c:2805:3)
at App
at t.value (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:17465)
at new t (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:12630)
at t.value (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:32766)
at https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/__replco/static/devtools/eruda/3.2.3/eruda.js:2:34400
[InteractionTracking] Started session: spunder_mbr3fdiz_vq0qz6pcm
