Refused to execute inline script because it violates the following Content Security Policy directive: "script-src 'self'". Either the 'unsafe-inline' keyword, a hash ('sha256-1sgsvV7RqhZEU57uUj1HcUsN4yHkBRXs1Ubie0S9E3A='), or a nonce ('nonce-...') is required to enable inline execution.
Context
popup.html
Stack Trace
popup.html:61 (anonymous function)
1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18
19
20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37
38
39
40
41
42
43
44
45
46
47
48
49
50
51
52
53
54
55
56
57
58
59
60
61
62
63
64
65
66
67
68
<!DOCTYPE html>
<html>
<head>
  <style>
    body {
      width: 250px;
      padding: 15px;
      font-family: Arial, sans-serif;
      background: #1a1a2e;
      color: white;
      margin: 0;
    }
    .header {
      text-align: center;
      margin-bottom: 15px;
    }
    .logo {
      width: 30px;
      height: 30px;
      background: linear-gradient(45deg, #00ff88, #00ccff);
      border-radius: 50%;
      margin: 0 auto 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }
    .umatter {
      background: linear-gradient(45deg, #00ff88, #00ccff);
      padding: 10px;
      border-radius: 8px;
      text-align: center;
      margin-bottom: 15px;
      font-size: 18px;
      font-weight: bold;
    }
    .btn {
      width: 100%;
      padding: 8px;
      background: #00ff88;
      color: black;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo">nU</div>
    <h3>nU Universe</h3>
  </div>
  
  <div class="umatter">
    <div id="umatter">0.00 UMatter</div>
  </div>
  
  <button class="btn" id="openApp">Open App</button>
  
  <script>
    document.getElementById('openApp').onclick = () => {
      chrome.tabs.create({url: 'http://localhost:5000'});
      window.close();
    };
  </script>
</body>
</html>
Refused to execute inline script because it violates the following Content Security Policy directive: "script-src 'self' 'wasm-unsafe-eval' 'inline-speculation-rules' http://localhost:* http://127.0.0.1:*". Either the 'unsafe-inline' keyword, a hash ('sha256-1sgsvV7RqhZEU57uUj1HcUsN4yHkBRXs1Ubie0S9E3A='), or a nonce ('nonce-...') is required to enable inline execution.