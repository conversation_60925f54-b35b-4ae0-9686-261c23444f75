
6:12:52 PM [express] serving on port 5000
Error: Failed to deserialize user out of session
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:359:19)
    at Authenticator.deserializeUser (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:377:5)
    at SessionStrategy.authenticate (/home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:112:10)
    at attempt (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:378:16)
    at authenticate (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:379:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
Error: Failed to deserialize user out of session
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:359:19)
    at Authenticator.deserializeUser (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:377:5)
    at SessionStrategy.authenticate (/home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:112:10)
    at attempt (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:378:16)
    at authenticate (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:379:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
Error: Failed to deserialize user out of session
    at pass (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:359:19)
    at Authenticator.deserializeUser (/home/<USER>/workspace/node_modules/passport/lib/authenticator.js:377:5)
    at SessionStrategy.authenticate (/home/<USER>/workspace/node_modules/passport/lib/strategies/session.js:112:10)
    at attempt (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:378:16)
    at authenticate (/home/<USER>/workspace/node_modules/passport/lib/middleware/authenticate.js:379:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
Browserslist: browsers data (caniuse-lite) is 8 months old. Please run:
  npx update-browserslist-db@latest
  Why you should do it regularly: https://github.com/browserslist/update-db#readme
6:13:04 PM [express] GET /api/auth/user 200 in 109ms :: {"id":"dev-user-123","email":"<EMAIL>…
6:13:04 PM [express] GET /api/interactions/nodes 304 in 111ms :: []
6:13:04 PM [express] GET /api/sessions/active 200 in 105ms
6:13:04 PM [express] GET /api/privacy/alerts 304 in 119ms :: []
6:13:04 PM [express] GET /api/interactions/connections 304 in 126ms :: []
6:13:04 PM [express] GET /api/memvid/chunks 304 in 120ms :: []
6:13:04 PM [express] GET /api/stats/system 200 in 176ms :: {"totalInteractions":"0","encryptedSessio…
