[Extensions] Status data: 
Object {connected: true, version: "3.0.0", lastSync: "2025-06-24T10:40:09.235Z", totalUMatter: 7.020363, adsIntercepted: 165, …}
[Extensions] Connected status: true
[Extensions] UMatter value: 7.020363
[AuthenticDeviceManager] REAL MacBook Battery: 
Object {realLevel: "71.0%", realCharging: "YES", source: "AUTHENTIC_DEVICE_API"}
[AuthenticDeviceManager] AUTHENTIC MacBook METRICS: 
Object {battery: "71.0% CHARGING", network: "10Mbps WIFI", memory: "22.8MB used of 25.3MB", cores: 8, platform: "MacIntel"}
[AuthenticDeviceManager] UMatter generated: 0.460445 from authentic_device_sync
[Extensions] Status data: 
Object {connected: true, version: "3.0.0", lastSync: "2025-06-24T10:40:11.283Z", totalUMatter: 7.749733, adsIntercepted: 165, …}
[Extensions] Connected status: true
[Extensions] UMatter value: 7.749733
[AuthenticDeviceManager] REAL MacBook Battery: 
Object {realLevel: "71.0%", realCharging: "YES", source: "AUTHENTIC_DEVICE_API"}
[AuthenticDeviceManager] AUTHENTIC MacBook METRICS: 
Object {battery: "71.0% CHARGING", network: "10Mbps WIFI", memory: "23.4MB used of 25.7MB", cores: 8, platform: "MacIntel"}
[AuthenticDeviceManager] UMatter generated: 0.46083 from authentic_device_sync
[NativeBatteryDetector] REAL-TIME UPDATE: 
Object {level: "71.0%", charging: "YES", source: "LIVE_BATTERY_API"}
[Extensions] Status data: 
Object {connected: true, version: "3.0.0", lastSync: "2025-06-24T10:40:13.463Z", totalUMatter: 9.823429, adsIntercepted: 165, …}
[Extensions] Connected status: true
[Extensions] UMatter value: 9.823429
[AuthenticDeviceManager] REAL MacBook Battery: 
Object {realLevel: "71.0%", realCharging: "YES", source: "AUTHENTIC_DEVICE_API"}
[AuthenticDeviceManager] AUTHENTIC MacBook METRICS: 
Object {battery: "71.0% CHARGING", network: "10Mbps WIFI", memory: "23.3MB used of 26.3MB", cores: 8, platform: "MacIntel"}
[AuthenticDeviceManager] UMatter generated: 0.45884 from authentic_device_sync
[Extensions] Status data: 
Object {connected: true, version: "3.0.0", lastSync: "2025-06-24T10:40:15.515Z", totalUMatter: 3.979835, adsIntercepted: 165, …}
[Extensions] Connected status: true
[Extensions] UMatter value: 3.979835
[AuthenticDeviceManager] REAL MacBook Battery: 
Object {realLevel: "71.0%", realCharging: "YES", source: "AUTHENTIC_DEVICE_API"}
[AuthenticDeviceManager] AUTHENTIC MacBook METRICS: 
Object {battery: "71.0% CHARGING", network: "10Mbps WIFI", memory: "23.3MB used of 25.7MB", cores: 8, platform: "MacIntel"}
[AuthenticDeviceManager] UMatter generated: 0.460558 from authentic_device_sync
[NativeBatteryDetector] REAL-TIME UPDATE: 
Object {level: "71.0%", charging: "YES", source: "LIVE_BATTERY_API"}
[RealTimeDeviceManager] Hardware metrics updated: 
Object {batteryDrain: 0, cpuUsage: "NaN", memoryUsage: 23.427417755126953, networkActivity: 0.5998046875}