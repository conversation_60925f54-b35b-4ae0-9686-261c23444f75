[RealMarketData] Failed to fetch ethereum: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:71:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 1)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:36:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch solana: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:71:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 2)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:36:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch bitcoin: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:71:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 0)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:36:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[Banking] LIVE TOKEN CALCULATIONS - TRU:0 NUVA:0 INU:0 UBITS:0
9:26:22 PM [express] GET /api/banking/balance 304 in 96ms :: {"umatter":0,"tru":0,"nuva":0,"inurtia"…
[Storage] Real balance from 0 transactions: 0 UMatter
[Storage] Real balance: 0 from 0 transactions
[Storage] Real balance from 0 transactions: 0 UMatter
[Wallet] Real balance: 0 UMatter from 0 transactions
9:26:22 PM [express] GET /api/wallet/balance 304 in 90ms :: {"umatter":0,"tru":0,"nuva":0,"inurtia":…
[RealDeviceMessenger] Scanning 10.0.0.1-254 for devices...
[Storage] Retrieved ALL 0 transactions for user anonymous
[Banking] Real balance: 0 UMatter from 0 transactions
[Storage] Real balance from 0 transactions: 0 UMatter
[Storage] Real balance: 0 from 0 transactions
[RealMarketData] Failed to fetch bitcoin: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:71:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 0)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:36:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch ethereum: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:71:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 1)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:36:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch solana: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:71:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 2)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:36:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[Banking] LIVE TOKEN CALCULATIONS - TRU:0 NUVA:0 INU:0 UBITS:0
9:26:22 PM [express] GET /api/banking/balance 304 in 83ms :: {"umatter":0,"tru":0,"nuva":0,"inurtia"…
[Extension Download] Attempting to serve: /home/<USER>/workspace/browser-extension/nu-universe-quantum-extension.zip
[Extension Download] File found, serving download
[Extension Download] File sent successfully
9:26:23 PM [express] GET /api/extension/download 200 in 10ms
[Storage] Real balance from 0 transactions: 0 UMatter
[Wallet] Real balance: 0 UMatter from 0 transactions
9:26:23 PM [express] GET /api/wallet/balance 304 in 112ms :: {"umatter":0,"tru":0,"nuva":0,"inurtia"…
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 0.62%, Memory: 246.1MB, Power: 15.68W
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 0.31%, Memory: 244.1MB, Power: 15.58W
9:26:23 PM [express] GET /api/energy/metrics 200 in 128ms :: {"neuralPower":15.**************,"devic…
[Storage] Retrieved ALL 0 transactions for user anonymous
[Banking] Real balance: 0 UMatter from 0 transactions
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 0.39%, Memory: 246.0MB, Power: 15.61W
[RealMarketData] Failed to fetch ethereum: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:71:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 1)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:36:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch bitcoin: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:71:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 0)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:36:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch solana: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:71:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 2)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:36:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[Banking] LIVE TOKEN CALCULATIONS - TRU:0 NUVA:0 INU:0 UBITS:0
9:26:23 PM [express] GET /api/banking/balance 304 in 75ms :: {"umatter":0,"tru":0,"nuva":0,"inurtia"…
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 0.43%, Memory: 247.0MB, Power: 15.62W
9:26:23 PM [express] GET /api/energy/metrics 200 in 104ms :: {"neuralPower":15.***************,"devi…
[Storage] Real balance from 0 transactions: 0 UMatter
[Storage] Real balance: 0 from 0 transactions
[Storage] Total UMatter from real transactions: 0
9:26:23 PM [express] GET /api/extension/status 200 in 23ms :: {"connected":true,"version":"3.0.0","l…
[Trading] Real crypto prices: BTC=$50000, ETH=$3000, Energy multiplier=1.033250
9:26:23 PM [express] GET /api/trading/prices 200 in 22ms :: [{"symbol":"TRU","name":"Truth Energy","…
[Storage] Real balance from 0 transactions: 0 UMatter
[Wallet] Real balance: 0 UMatter from 0 transactions
9:26:23 PM [express] GET /api/wallet/balance 304 in 90ms :: {"umatter":0,"tru":0,"nuva":0,"inurtia":…
[Storage] Retrieved ALL 0 transactions for user anonymous
[Banking] Real balance: 0 UMatter from 0 transactions
[RealMarketData] Failed to fetch bitcoin: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:71:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 0)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:36:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch ethereum: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:71:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 1)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:36:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 0.30%, Memory: 249.8MB, Power: 15.59W
[RealMarketData] Failed to fetch solana: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:71:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 2)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:36:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[Banking] LIVE TOKEN CALCULATIONS - TRU:0 NUVA:0 INU:0 UBITS:0
9:26:23 PM [express] GET /api/banking/balance 304 in 116ms :: {"umatter":0,"tru":0,"nuva":0,"inurtia…
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 0.11%, Memory: 250.6MB, Power: 15.53W
9:26:23 PM [express] GET /api/energy/metrics 200 in 104ms :: {"neuralPower":15.***************,"devi…
[RealDeviceMessenger] Scanning 172.16.0.1-254 for devices...
[Storage] Total UMatter from real transactions: 0
9:26:23 PM [express] GET /api/extension/status 200 in 41ms :: {"connected":true,"version":"3.0.0","l…
[WebSocket] Client disconnected: { code: 1001, reason: '' }
[WebSocket] Client disconnected: { code: 1001, reason: '' }
[WebSocket] Client disconnected: { code: 1001, reason: '' }
[WebSocket] Client disconnected: { code: 1001, reason: '' }
[RealDeviceMessenger] TCP scan complete - no devices found on 192.168.x.x networks
[RealDeviceMessenger] Network appears isolated or devices not responding on scanned ports
[RealDeviceMessenger] Device discovery complete - found 0 devices
