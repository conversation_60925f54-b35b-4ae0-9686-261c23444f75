[Storage] Error updating energy balance: PostgresError: invalid input syntax for type uuid: "bal_1750887158460"
    at ErrorResponse (file:///home/<USER>/workspace/node_modules/postgres/src/connection.js:794:26)
    at handle (file:///home/<USER>/workspace/node_modules/postgres/src/connection.js:480:6)
    at TLSSocket.data (file:///home/<USER>/workspace/node_modules/postgres/src/connection.js:315:9)
    at TLSSocket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Readable.push (node:internal/streams/readable:392:5)
    at TLSWrap.onStreamRead (node:internal/stream_base_commons:191:23) {
  severity_local: 'ERROR',
  severity: 'ERROR',
  code: '22P02',
  where: "unnamed portal parameter $1 = '...'",
  file: 'uuid.c',
  line: '133',
  routine: 'string_to_uuid'
}
[Energy Batch] Deposited 0.357519 UMatter for user anonymous
9:32:38 PM [express] POST /api/energy/deposit-batch 200 in 261ms :: {"success":true,"deposited":0.35…
[RealDeviceMessenger] Scanning 192.168.0.1-254 for devices...
[RealDeviceMessenger] Scanning 10.0.0.1-254 for devices...
[Storage] Real balance from 2 transactions: 3.92130745 UMatter
[Storage] Real balance: 3.92130745 from 2 transactions
[RealDeviceMessenger] Scanning 172.16.0.1-254 for devices...
[Storage] Error updating energy balance: PostgresError: invalid input syntax for type uuid: "bal_1750887159641"
    at ErrorResponse (file:///home/<USER>/workspace/node_modules/postgres/src/connection.js:794:26)
    at handle (file:///home/<USER>/workspace/node_modules/postgres/src/connection.js:480:6)
    at TLSSocket.data (file:///home/<USER>/workspace/node_modules/postgres/src/connection.js:315:9)
    at TLSSocket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Readable.push (node:internal/streams/readable:392:5)
    at TLSWrap.onStreamRead (node:internal/stream_base_commons:191:23) {
  severity_local: 'ERROR',
  severity: 'ERROR',
  code: '22P02',
  where: "unnamed portal parameter $1 = '...'",
  file: 'uuid.c',
  line: '133',
  routine: 'string_to_uuid'
}
[Energy Batch] Deposited 3.563789 UMatter for user anonymous
9:32:39 PM [express] POST /api/energy/deposit-batch 200 in 192ms :: {"success":true,"deposited":3.56…
9:32:40 PM [express] GET /api/web-ads/recent 304 in 1ms :: []
[RealDeviceMessenger] TCP scan complete - no devices found on 192.168.x.x networks
[RealDeviceMessenger] Network appears isolated or devices not responding on scanned ports
[RealDeviceMessenger] Device discovery complete - found 0 devices
9:32:40 PM [express] POST /api/devices/scan 200 in 2271ms :: {"success":true,"message":"Real network…
[Storage] Real balance from 0 transactions: 0 UMatter
[Storage] Real balance: 0 from 0 transactions
[Storage] Real balance from 0 transactions: 0 UMatter
[Wallet] Real balance: 0 UMatter from 0 transactions
9:32:40 PM [express] GET /api/wallet/balance 200 in 2302ms :: {"umatter":0,"tru":0,"nuva":0,"inurtia…
[Storage] Real balance from 0 transactions: 0 UMatter
[Storage] Real balance: 0 from 0 transactions
[Storage] Total UMatter from real transactions: 3.92130745
9:32:40 PM [express] GET /api/extension/status 200 in 22ms :: {"connected":true,"version":"3.0.0","l…
[Storage] Real balance from 0 transactions: 0 UMatter
[Wallet] Real balance: 0 UMatter from 0 transactions
9:32:40 PM [express] GET /api/wallet/balance 304 in 91ms :: {"umatter":0,"tru":0,"nuva":0,"inurtia":…
[Storage] Real balance from 3 transactions: 6.98175101 UMatter
[Storage] Real balance: 6.98175101 from 3 transactions
[Storage] Error updating energy balance: PostgresError: invalid input syntax for type uuid: "bal_1750887160875"
    at ErrorResponse (file:///home/<USER>/workspace/node_modules/postgres/src/connection.js:794:26)
    at handle (file:///home/<USER>/workspace/node_modules/postgres/src/connection.js:480:6)
    at TLSSocket.data (file:///home/<USER>/workspace/node_modules/postgres/src/connection.js:315:9)
    at TLSSocket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Readable.push (node:internal/streams/readable:392:5)
    at TLSWrap.onStreamRead (node:internal/stream_base_commons:191:23) {
  severity_local: 'ERROR',
  severity: 'ERROR',
  code: '22P02',
  where: "unnamed portal parameter $1 = '...'",
  file: 'uuid.c',
  line: '133',
  routine: 'string_to_uuid'
}
[Energy Batch] Deposited 3.060444 UMatter for user anonymous
9:32:40 PM [express] POST /api/energy/deposit-batch 200 in 177ms :: {"success":true,"deposited":3.06…
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 0.00%, Memory: 317.0MB, Power: 15.64W
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 0.00%, Memory: 317.0MB, Power: 15.64W
9:32:41 PM [express] GET /api/energy/metrics 200 in 102ms :: {"neuralPower":15.31723613116455,"devic…
9:32:42 PM [express] GET /api/web-ads/recent 304 in 1ms :: []
[Storage] Total UMatter from real transactions: 6.98175101
9:32:42 PM [express] GET /api/extension/status 200 in 23ms :: {"connected":true,"version":"3.0.0","l…
[Real Hardware API] Generated 0.002954 UMatter from Node.js process metrics
9:32:43 PM [express] GET /api/energy/real-hardware-metrics 200 in 1ms :: {"authenticEnergy":0.002953…
[Real Hardware API] Generated 0.002956 UMatter from Node.js process metrics
9:32:43 PM [express] GET /api/energy/real-hardware-metrics 200 in 1ms :: {"authenticEnergy":0.002955…
[Real Hardware API] Generated 0.002936 UMatter from Node.js process metrics
9:32:43 PM [express] GET /api/energy/real-hardware-metrics 200 in 0ms :: {"authenticEnergy":0.002935…
[Real Hardware API] Generated 0.003030 UMatter from Node.js process metrics
9:32:43 PM [express] GET /api/energy/real-hardware-metrics 200 in 0ms :: {"authenticEnergy":0.003029…
[Storage] Real balance from 4 transactions: 6.9943863 UMatter
[Storage] Real balance: 6.9943863 from 4 transactions
[Storage] Error updating energy balance: PostgresError: invalid input syntax for type uuid: "bal_1750887163393"
    at ErrorResponse (file:///home/<USER>/workspace/node_modules/postgres/src/connection.js:794:26)
    at handle (file:///home/<USER>/workspace/node_modules/postgres/src/connection.js:480:6)
    at TLSSocket.data (file:///home/<USER>/workspace/node_modules/postgres/src/connection.js:315:9)
    at TLSSocket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)
    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)
    at Readable.push (node:internal/streams/readable:392:5)
    at TLSWrap.onStreamRead (node:internal/stream_base_commons:191:23) {
  severity_local: 'ERROR',
  severity: 'ERROR',
  code: '22P02',
  where: "unnamed portal parameter $1 = '...'",
  file: 'uuid.c',
  line: '133',
  routine: 'string_to_uuid'
}
[Energy Batch] Deposited 0.012635 UMatter for user anonymous
9:32:43 PM [express] POST /api/energy/deposit-batch 200 in 180ms :: {"success":true,"deposited":0.01…
9:32:44 PM [express] GET /api/web-ads/recent 304 in 1ms :: []
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 0.00%, Memory: 317.8MB, Power: 15.64W
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 0.00%, Memory: 317.8MB, Power: 15.64W
9:32:44 PM [express] GET /api/energy/metrics 200 in 102ms :: {"neuralPower":15.317995201538086,"devi…
[Storage] Total UMatter from real transactions: 6.9943863
9:32:44 PM [express] GET /api/extension/status 200 in 23ms :: {"connected":true,"version":"3.0.0","l…
9:32:46 PM [express] GET /api/web-ads/recent 304 in 1ms :: []
[Storage] Total UMatter from real transactions: 6.9943863
9:32:46 PM [express] GET /api/extension/status 200 in 24ms :: {"connected":true,"version":"3.0.0","l…
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 0.00%, Memory: 318.0MB, Power: 15.64W
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 0.00%, Memory: 318.0MB, Power: 15.64W
9:32:47 PM [express] GET /api/energy/metrics 200 in 102ms :: {"neuralPower":15.31820152520752,"devic…
[Real Hardware API] Generated 0.002913 UMatter from Node.js process metrics
9:32:48 PM [express] GET /api/energy/real-hardware-metrics 200 in 0ms :: {"authenticEnergy":0.002912…
[Storage] Real balance from 5 transactions: 6.99543246 UMatter
[Storage] Real balance: 6.99543246 from 5 transactions
[Real Hardware API] Generated 0.002680 UMatter from Node.js process metrics
9:32:48 PM [express] GET /api/energy/real-hardware-metrics 200 in 1ms :: {"authenticEnergy":0.002679…
[Real Hardware API] Generated 0.002770 UMatter from Node.js process metrics
9:32:48 PM [express] GET /api/energy/real-hardware-metrics 200 in 1ms :: {"authenticEnergy":0.002769…
[Storage] Error updating energy balance: PostgresError: invalid input syntax for type uuid: "bal_1750887168166"
    at ErrorResponse (file:///home/<USER>/workspace/node_modules/postgres/src/connection.js:794:26)
    at handle (file:///home/<USER>/workspace/node_modules/postgres/src/connection.js:480:6)
    at TLSSocket.data (file:///home/<USER>/workspace/node_modules/postgres/src/connection.js:315:9)
    at TLSSocket.emit (node:events:518:28)
    at addChunk (node:internal/streams/readable:561:12)