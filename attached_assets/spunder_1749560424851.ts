/**
 * SpUnder - Silent Guardian DID Interaction Tracker
 * Non-intrusive interaction logging with HashBot encryption for nUOS integration
 */

import { HashBot, TimestampedData } from './hashbot';

export interface SpUnderInteraction {
  did: string;
  hashedDID: string;
  type: string;
  target: string;
  timestamp: number;
  data: string;
  sessionId: string;
  hash: string;
}

export interface SpUnderWeb {
  did: string;
  interactions: SpUnderInteraction[];
  merkleRoot: string;
  bundleHash: string;
  encrypted: boolean;
  timestamp: number;
}

export class SpUnder {
  private did: string;
  private nUOSendpoint: string;
  private web: SpUnderInteraction[] = [];
  private hashBot: HashBot;
  private encryptionKey: string;
  private sessionId: string;
  private sendQueue: SpUnderWeb[] = [];
  private maxWebSize: number = 50;
  private sendInterval: number = 30000; // 30 seconds
  private isActive: boolean = true;

  constructor(did: string, nUOSendpoint: string = '/api/spunder/web') {
    this.did = did;
    this.nUOSendpoint = nUOSendpoint;
    this.hashBot = new HashBot();
    this.encryptionKey = this.hashBot.generateSecureKey(did);
    this.sessionId = this.generateSessionId();
    
    console.log(`[SpUnder] Guardian initialized for DID: ${this.hashBot.hashDID(did).substring(0, 8)}...`);
    this.init();
  }

  private generateSessionId(): string {
    return this.hashBot.recursiveSha256(`${this.did}_${Date.now()}_${Math.random()}`).substring(0, 16);
  }

  /**
   * Initialize event listeners for interaction tracking
   */
  private init(): void {
    if (!this.isActive) return;

    // Track comprehensive interaction events
    const interactionEvents = [
      'click', 'dblclick', 'mousedown', 'mouseup',
      'input', 'change', 'submit', 'focus', 'blur',
      'keydown', 'keyup', 'scroll', 'resize',
      'message', 'beforeunload'
    ];

    interactionEvents.forEach(event => {
      document.addEventListener(event, this.logInteraction.bind(this), { 
        capture: true, 
        passive: true 
      });
    });

    // Set up periodic sending
    setInterval(() => {
      if (this.web.length > 0) {
        this.encryptAndSend();
      }
    }, this.sendInterval);

    // Send on page unload
    window.addEventListener('beforeunload', () => {
      if (this.web.length > 0) {
        this.encryptAndSend(true); // Synchronous send on unload
      }
    });

    // Periodic cache cleanup
    setInterval(() => {
      this.hashBot.clearCache();
    }, 300000); // 5 minutes
  }

  /**
   * Log user interactions with enhanced security
   */
  private logInteraction(event: Event): void {
    if (!this.isActive) return;

    try {
      const target = event.target as HTMLElement;
      const interaction: SpUnderInteraction = {
        did: this.did,
        hashedDID: this.hashBot.hashDID(this.did),
        type: event.type,
        target: this.getTargetIdentifier(target),
        timestamp: Date.now(),
        data: this.extractEventData(event),
        sessionId: this.sessionId,
        hash: ''
      };

      // Add timestamped hash for immutability
      const timestamped = this.hashBot.timestampData(JSON.stringify(interaction));
      interaction.hash = timestamped.hash;

      this.web.push(interaction);

      // Send when web reaches max size
      if (this.web.length >= this.maxWebSize) {
        this.encryptAndSend();
      }
    } catch (error) {
      console.warn('[SpUnder] Interaction logging failed:', error);
    }
  }

  /**
   * Get secure target identifier without exposing sensitive data
   */
  private getTargetIdentifier(target: HTMLElement): string {
    if (!target) return 'unknown';

    // Priority order for identification
    const identifiers = [
      target.id && `id:${target.id}`,
      target.className && `class:${target.className.split(' ')[0]}`,
      target.tagName && `tag:${target.tagName.toLowerCase()}`,
      target.getAttribute('data-testid') && `testid:${target.getAttribute('data-testid')}`,
      'unknown'
    ].filter(Boolean);

    return identifiers[0] as string;
  }

  /**
   * Extract relevant event data while preserving privacy
   */
  private extractEventData(event: Event): string {
    const target = event.target as HTMLInputElement;
    
    switch (event.type) {
      case 'input':
      case 'change':
        // Hash sensitive input data
        if (target.type === 'password' || target.type === 'email') {
          return this.hashBot.recursiveSha256(target.value || '').substring(0, 8);
        }
        return target.value ? 'has-value' : 'no-value';
      
      case 'click':
      case 'dblclick':
        const clickEvent = event as MouseEvent;
        return `x:${clickEvent.clientX},y:${clickEvent.clientY}`;
      
      case 'keydown':
      case 'keyup':
        const keyEvent = event as KeyboardEvent;
        return keyEvent.key === 'Enter' ? 'enter' : 'key-press';
      
      case 'scroll':
        return `y:${window.scrollY}`;
      
      default:
        return 'interaction';
    }
  }

  /**
   * Encrypt web and send to nUOS with enhanced security
   */
  private async encryptAndSend(sync: boolean = false): Promise<void> {
    if (this.web.length === 0) return;

    try {
      // Bundle interactions with Merkle tree
      const bundle = this.hashBot.bundleInteractions(this.web);
      
      const spunderWeb: SpUnderWeb = {
        did: this.hashBot.hashDID(this.did),
        interactions: this.web,
        merkleRoot: bundle.merkleRoot,
        bundleHash: bundle.bundleHash,
        encrypted: true,
        timestamp: bundle.timestamp
      };

      // Encrypt the entire web
      const encryptedWeb = this.hashBot.encryptWebData(spunderWeb, this.encryptionKey);

      const payload = {
        did: this.hashBot.hashDID(this.did),
        sessionId: this.sessionId,
        web: encryptedWeb,
        merkleRoot: bundle.merkleRoot,
        timestamp: bundle.timestamp,
        interactionCount: this.web.length
      };

      if (sync) {
        // Synchronous send using sendBeacon for page unload
        const blob = new Blob([JSON.stringify(payload)], { type: 'application/json' });
        navigator.sendBeacon(this.nUOSendpoint, blob);
      } else {
        // Asynchronous send
        await fetch(this.nUOSendpoint, {
          method: 'POST',
          headers: { 
            'Content-Type': 'application/json',
            'X-SpUnder-Version': '1.0',
            'X-Session-ID': this.sessionId
          },
          body: JSON.stringify(payload)
        });
      }

      console.log(`[SpUnder] Web sent: ${this.web.length} interactions, Merkle: ${bundle.merkleRoot.substring(0, 8)}...`);
      
      // Clear web after successful send
      this.web = [];
      
    } catch (error) {
      console.warn('[SpUnder] Send failed, queuing for retry:', error);
      // Queue for retry without clearing web
      this.queueForRetry();
    }
  }

  /**
   * Queue failed sends for retry
   */
  private queueForRetry(): void {
    if (this.web.length > 0) {
      const bundle = this.hashBot.bundleInteractions(this.web);
      
      this.sendQueue.push({
        did: this.hashBot.hashDID(this.did),
        interactions: [...this.web],
        merkleRoot: bundle.merkleRoot,
        bundleHash: bundle.bundleHash,
        encrypted: true,
        timestamp: bundle.timestamp
      });

      // Limit queue size
      if (this.sendQueue.length > 10) {
        this.sendQueue.shift();
      }

      // Clear current web
      this.web = [];

      // Retry after delay
      setTimeout(() => {
        this.retryQueuedSends();
      }, 60000); // 1 minute
    }
  }

  /**
   * Retry queued sends
   */
  private async retryQueuedSends(): Promise<void> {
    if (this.sendQueue.length === 0) return;

    const queued = this.sendQueue.shift();
    if (!queued) return;

    try {
      const encryptedWeb = this.hashBot.encryptWebData(queued, this.encryptionKey);
      
      await fetch(this.nUOSendpoint, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'X-SpUnder-Version': '1.0',
          'X-Session-ID': this.sessionId,
          'X-Retry': 'true'
        },
        body: JSON.stringify({
          did: queued.did,
          sessionId: this.sessionId,
          web: encryptedWeb,
          merkleRoot: queued.merkleRoot,
          timestamp: queued.timestamp,
          interactionCount: queued.interactions.length
        })
      });

      console.log(`[SpUnder] Queued web sent: ${queued.interactions.length} interactions`);
      
    } catch (error) {
      console.warn('[SpUnder] Retry failed, requeuing:', error);
      this.sendQueue.unshift(queued); // Put back at front
    }
  }

  /**
   * Pause SpUnder tracking
   */
  pause(): void {
    this.isActive = false;
    console.log('[SpUnder] Guardian paused');
  }

  /**
   * Resume SpUnder tracking
   */
  resume(): void {
    this.isActive = true;
    console.log('[SpUnder] Guardian resumed');
  }

  /**
   * Get current web statistics
   */
  getStats(): {
    currentWebSize: number;
    queueSize: number;
    sessionId: string;
    isActive: boolean;
    hashedDID: string;
  } {
    return {
      currentWebSize: this.web.length,
      queueSize: this.sendQueue.length,
      sessionId: this.sessionId,
      isActive: this.isActive,
      hashedDID: this.hashBot.hashDID(this.did).substring(0, 8)
    };
  }

  /**
   * Destroy SpUnder instance
   */
  destroy(): void {
    this.isActive = false;
    if (this.web.length > 0) {
      this.encryptAndSend(true);
    }
    this.web = [];
    this.sendQueue = [];
    console.log('[SpUnder] Guardian destroyed');
  }
}

/**
 * Global SpUnder manager for multiple DIDs
 */
class SpUnderManager {
  private instances: Map<string, SpUnder> = new Map();

  /**
   * Attach SpUnder to a DID
   */
  attachSpUnder(did: string, endpoint?: string): SpUnder {
    if (!this.instances.has(did)) {
      const spunder = new SpUnder(did, endpoint);
      this.instances.set(did, spunder);
      console.log(`[SpUnderManager] Guardian attached for DID: ${did.substring(0, 8)}...`);
    }
    return this.instances.get(did)!;
  }

  /**
   * Get SpUnder instance for DID
   */
  getSpUnder(did: string): SpUnder | undefined {
    return this.instances.get(did);
  }

  /**
   * Remove SpUnder for DID
   */
  removeSpUnder(did: string): void {
    const spunder = this.instances.get(did);
    if (spunder) {
      spunder.destroy();
      this.instances.delete(did);
      console.log(`[SpUnderManager] Guardian removed for DID: ${did.substring(0, 8)}...`);
    }
  }

  /**
   * Get all active SpUnders
   */
  getAllSpUnders(): Map<string, SpUnder> {
    return new Map(this.instances);
  }

  /**
   * Get manager statistics
   */
  getManagerStats(): {
    activeGuardians: number;
    totalInteractions: number;
    totalQueueSize: number;
  } {
    let totalInteractions = 0;
    let totalQueueSize = 0;

    for (const spunder of this.instances.values()) {
      const stats = spunder.getStats();
      totalInteractions += stats.currentWebSize;
      totalQueueSize += stats.queueSize;
    }

    return {
      activeGuardians: this.instances.size,
      totalInteractions,
      totalQueueSize
    };
  }
}

// Global SpUnder manager instance
export const spunderManager = new SpUnderManager();

// Auto-attach function for easy integration
export function attachSpUnder(did: string, endpoint?: string): SpUnder {
  return spunderManager.attachSpUnder(did, endpoint);
}

// Initialize SpUnder for current user (call this in app initialization)
export function initializeSpUnder(userDID?: string): void {
  // Generate a DID if none provided
  const did = userDID || `did:nU:${Date.now()}_${Math.random().toString(36).substring(2)}`;
  
  // Attach SpUnder guardian
  attachSpUnder(did);
  
  console.log(`[SpUnder] System initialized for DID: ${did.substring(0, 8)}...`);
}