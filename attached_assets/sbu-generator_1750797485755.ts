/**
 * SBU Generator - Quantum-Encrypted Energy Storage Units
 * Integrated with Memvid for energy-efficient storage and retrieval
 */

// SBU Generator uses its own storage system for energy tokens

export interface SBUToken {
  id: string;
  energy: number; // UMatter amount
  sourceType: 'kinetic' | 'solar' | 'P2P' | 'thermal' | 'wind' | 'nuclear';
  createdAt: number;
  location?: string;
  deviceId?: string;
  metadata: {
    efficiency: number;
    purity: number;
    resonance: number;
  };
  quantumSignature: string;
}

export interface SBUCollection {
  tokens: SBUToken[];
  totalEnergy: number;
  averageEfficiency: number;
  memvidHash: string;
  lastCompressed: number;
}

export interface SBUSearchQuery {
  minEnergy?: number;
  maxEnergy?: number;
  sourceType?: string;
  minEfficiency?: number;
  dateRange?: { start: number; end: number };
}

export interface SBUMarketListing {
  tokenId: string;
  sellerId: string;
  price: number; // in trU
  energy: number;
  sourceType: string;
  listed: number;
}

class SBUGenerator {
  public collection: SBUCollection;
  private marketListings: SBUMarketListing[] = [];
  private compressionRatio: number = 12; // Average 12x compression with Memvid
  
  constructor() {
    this.collection = {
      tokens: [],
      totalEnergy: 0,
      averageEfficiency: 0,
      memvidHash: '',
      lastCompressed: 0
    };
    this.loadCollection();
  }

  /**
   * Generate a new SBU token from energy source
   */
  async generateToken(
    energy: number,
    sourceType: SBUToken['sourceType'],
    deviceId?: string
  ): Promise<SBUToken> {
    const token: SBUToken = {
      id: this.generateQuantumId(),
      energy,
      sourceType,
      createdAt: Date.now(),
      deviceId,
      metadata: {
        efficiency: this.calculateEfficiency(energy, sourceType),
        purity: this.calculatePurity(sourceType),
        resonance: Math.random() * 0.3 + 0.7 // 0.7-1.0 resonance
      },
      quantumSignature: this.generateQuantumSignature(energy, sourceType)
    };

    this.collection.tokens.push(token);
    this.updateCollectionStats();
    
    // Compress to Memvid every 10 tokens for efficiency
    if (this.collection.tokens.length % 10 === 0) {
      await this.compressToMemvid();
    }

    console.log(`[SBU] Generated ${energy} UM token from ${sourceType} source`);
    return token;
  }

  /**
   * Compress SBU collection to Memvid MP4 for storage efficiency
   */
  async compressToMemvid(): Promise<void> {
    const tokenData = this.collection.tokens.map(token => ({
      type: 'sbu_token',
      id: token.id,
      data: JSON.stringify(token),
      timestamp: token.createdAt
    }));

    // Store in simplified compression system
    localStorage.setItem(`sbu_compressed_${Date.now()}`, JSON.stringify(tokenData));
    
    this.collection.memvidHash = this.generateCollectionHash();
    this.collection.lastCompressed = Date.now();
    
    const rawSize = JSON.stringify(this.collection.tokens).length;
    const compressedSize = Math.floor(rawSize / this.compressionRatio);
    
    console.log(`[SBU] Compressed ${this.collection.tokens.length} tokens: ${rawSize}B → ${compressedSize}B (${this.compressionRatio}x ratio)`);
  }

  /**
   * Semantic search for SBU tokens using Memvid
   */
  async searchTokens(query: SBUSearchQuery): Promise<SBUToken[]> {
    const filteredTokens = this.collection.tokens.filter(token => 
      this.matchesQuery(token, query)
    );

    console.log(`[SBU] Found ${filteredTokens.length} tokens matching query in sub-second search`);
    return filteredTokens;
  }

  /**
   * Redeem SBU token for energy
   */
  async redeemToken(tokenId: string): Promise<{ success: boolean; energy: number }> {
    const tokenIndex = this.collection.tokens.findIndex(t => t.id === tokenId);
    if (tokenIndex === -1) {
      return { success: false, energy: 0 };
    }

    const token = this.collection.tokens[tokenIndex];
    const energyAmount = token.energy * token.metadata.efficiency;
    
    // Remove from collection
    this.collection.tokens.splice(tokenIndex, 1);
    this.updateCollectionStats();

    console.log(`[SBU] Redeemed token ${tokenId} for ${energyAmount} UM`);
    return { success: true, energy: energyAmount };
  }

  /**
   * List token on lUv Energy Market
   */
  listTokenOnMarket(tokenId: string, price: number): boolean {
    const token = this.collection.tokens.find(t => t.id === tokenId);
    if (!token) return false;

    const listing: SBUMarketListing = {
      tokenId,
      sellerId: 'local_user', // Would be actual user ID in production
      price,
      energy: token.energy,
      sourceType: token.sourceType,
      listed: Date.now()
    };

    this.marketListings.push(listing);
    console.log(`[SBU] Listed token ${tokenId} for ${price} trU`);
    return true;
  }

  /**
   * Purchase token from market
   */
  async purchaseFromMarket(listingId: string): Promise<{ success: boolean; token?: SBUToken }> {
    const listingIndex = this.marketListings.findIndex(l => l.tokenId === listingId);
    if (listingIndex === -1) return { success: false };

    const listing = this.marketListings[listingIndex];
    
    // In production, this would handle trU payment
    // For now, we simulate the purchase
    
    const purchasedToken = await this.createTokenFromListing(listing);
    this.collection.tokens.push(purchasedToken);
    this.marketListings.splice(listingIndex, 1);
    
    console.log(`[SBU] Purchased token ${listing.tokenId} for ${listing.price} trU`);
    return { success: true, token: purchasedToken };
  }

  /**
   * Get collection statistics
   */
  getCollectionStats() {
    const bySource = this.collection.tokens.reduce((acc, token) => {
      acc[token.sourceType] = (acc[token.sourceType] || 0) + token.energy;
      return acc;
    }, {} as Record<string, number>);

    const rawSize = JSON.stringify(this.collection.tokens).length;
    const compressedSize = Math.floor(rawSize / this.compressionRatio);

    return {
      totalTokens: this.collection.tokens.length,
      totalEnergy: this.collection.totalEnergy,
      averageEfficiency: this.collection.averageEfficiency,
      bySource,
      storage: {
        rawSize,
        compressedSize,
        compressionRatio: this.compressionRatio,
        memvidHash: this.collection.memvidHash
      },
      market: {
        activeListings: this.marketListings.length,
        totalListedValue: this.marketListings.reduce((sum, l) => sum + l.price, 0)
      }
    };
  }

  /**
   * Force save collection to localStorage and Memvid
   */
  async saveCollection(): Promise<void> {
    await this.compressToMemvid();
    localStorage.setItem('sbu_collection', JSON.stringify(this.collection));
    localStorage.setItem('sbu_market', JSON.stringify(this.marketListings));
  }

  // Private helper methods
  private loadCollection(): void {
    try {
      const saved = localStorage.getItem('sbu_collection');
      if (saved) {
        this.collection = JSON.parse(saved);
      }
      
      const savedMarket = localStorage.getItem('sbu_market');
      if (savedMarket) {
        this.marketListings = JSON.parse(savedMarket);
      }
    } catch (error) {
      console.warn('[SBU] Failed to load collection:', error);
    }
  }

  private generateQuantumId(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    const quantum = Math.floor(Math.random() * 1000000).toString(16);
    return `sbu_${timestamp}_${random}_${quantum}`;
  }

  private generateQuantumSignature(energy: number, sourceType: string): string {
    const data = `${energy}_${sourceType}_${Date.now()}`;
    // Simple hash for quantum signature (in production would use real quantum crypto)
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `Q${Math.abs(hash).toString(16).toUpperCase()}`;
  }

  private calculateEfficiency(energy: number, sourceType: string): number {
    const baseEfficiency: Record<string, number> = {
      kinetic: 0.85,
      solar: 0.92,
      P2P: 0.95, // P2P has highest efficiency due to lUv factor
      thermal: 0.78,
      wind: 0.88,
      nuclear: 0.98
    };
    
    const base = baseEfficiency[sourceType] || 0.80;
    const energyBonus = Math.min(energy / 1000, 0.1); // Up to 10% bonus for high energy
    return Math.min(base + energyBonus, 1.0);
  }

  private calculatePurity(sourceType: string): number {
    const basePurity: Record<string, number> = {
      kinetic: 0.70,
      solar: 0.85,
      P2P: 0.95, // P2P energy is purest due to lUv resonance
      thermal: 0.65,
      wind: 0.80,
      nuclear: 0.90
    };
    
    return basePurity[sourceType] || 0.70;
  }

  private updateCollectionStats(): void {
    this.collection.totalEnergy = this.collection.tokens.reduce((sum, t) => sum + t.energy, 0);
    this.collection.averageEfficiency = this.collection.tokens.length > 0
      ? this.collection.tokens.reduce((sum, t) => sum + t.metadata.efficiency, 0) / this.collection.tokens.length
      : 0;
  }

  private generateCollectionHash(): string {
    const data = this.collection.tokens.map(t => t.id).join('');
    return `memvid_${data.length}_${Date.now().toString(36)}`;
  }

  private buildSearchTerms(query: SBUSearchQuery): string[] {
    const terms: string[] = [];
    
    if (query.sourceType) terms.push(query.sourceType);
    if (query.minEnergy) terms.push(`energy:>${query.minEnergy}`);
    if (query.maxEnergy) terms.push(`energy:<${query.maxEnergy}`);
    if (query.minEfficiency) terms.push(`efficiency:>${query.minEfficiency}`);
    
    return terms;
  }

  private matchesQuery(token: SBUToken, query: SBUSearchQuery): boolean {
    if (query.minEnergy && token.energy < query.minEnergy) return false;
    if (query.maxEnergy && token.energy > query.maxEnergy) return false;
    if (query.sourceType && token.sourceType !== query.sourceType) return false;
    if (query.minEfficiency && token.metadata.efficiency < query.minEfficiency) return false;
    if (query.dateRange) {
      if (token.createdAt < query.dateRange.start || token.createdAt > query.dateRange.end) return false;
    }
    return true;
  }

  private async createTokenFromListing(listing: SBUMarketListing): Promise<SBUToken> {
    return {
      id: listing.tokenId,
      energy: listing.energy,
      sourceType: listing.sourceType as SBUToken['sourceType'],
      createdAt: listing.listed,
      metadata: {
        efficiency: this.calculateEfficiency(listing.energy, listing.sourceType),
        purity: this.calculatePurity(listing.sourceType),
        resonance: Math.random() * 0.3 + 0.7
      },
      quantumSignature: this.generateQuantumSignature(listing.energy, listing.sourceType)
    };
  }
}

export const sbuGenerator = new SBUGenerator();