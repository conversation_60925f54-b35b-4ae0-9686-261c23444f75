[RealMarketData] Failed to fetch bitcoin: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 0)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch ethereum: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 1)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch bitcoin: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 0)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch solana: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 2)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch ethereum: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 1)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch solana: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 2)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[Banking] LIVE TOKEN CALCULATIONS - TRU:17 NUVA:8 INU:4 UBITS:66
12:35:08 AM [express] GET /api/banking/balance 200 in 11984ms :: {"umatter":536.*************,"tru":1…
[RealMarketData] Failed to fetch bitcoin: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 0)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch ethereum: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 1)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch solana: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 2)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch solana: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 2)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[Banking] LIVE TOKEN CALCULATIONS - TRU:17 NUVA:8 INU:4 UBITS:66
12:35:08 AM [express] GET /api/banking/balance 200 in 11913ms :: {"umatter":536.*************,"tru":1…
[RealMarketData] Failed to fetch ethereum: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 1)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[Banking] LIVE TOKEN CALCULATIONS - TRU:17 NUVA:8 INU:4 UBITS:66
12:35:08 AM [express] GET /api/banking/balance 200 in 11976ms :: {"umatter":536.*************,"tru":1…
[RealMarketData] Failed to fetch bitcoin: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 0)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch ethereum: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 1)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch solana: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 2)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch bitcoin: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 0)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)
[RealMarketData] Failed to fetch ethereum: Error: HTTP 429
    at RealMarketDataConnector.fetchCoinGeckoPrice (/home/<USER>/workspace/server/real-market-data.ts:88:31)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async Promise.all (index 1)
    at async RealMarketDataConnector.getRealTokenPricing (/home/<USER>/workspace/server/real-market-data.ts:38:43)
    at async <anonymous> (/home/<USER>/workspace/server/energy-banking-routes.ts:39:23)