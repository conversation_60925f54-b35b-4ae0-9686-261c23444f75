[MobileDeviceManager] Initializing with capabilities: 
Object {battery: true, deviceMotion: true, deviceOrientation: true, geolocation: true, camera: true, …}
[PerformanceController] Individual API call blocking enabled - batching only
[ProximityDiscovery] Bluetooth API available
[ProximityDiscovery] Network discovery initialized
[AuthenticDeviceManager] ✅ Real MacBook Battery Connected: 
Object {level: "100.0%", charging: "YES"}
[AuthenticDeviceManager] ✅ Real Network API Connected
[NativeBatteryDetector] SUCCESS: getBattery API returned real data: 
Object {level: "100.0%", charging: true}
[RealBatteryAPI] SUCCESS: Connected to real device battery: 
Object {level: "100.0%", charging: "YES", source: "AUTHENTIC_DEVICE_BATTERY"}
[RealTimeDeviceManager] Battery API connected
[MobileDeviceManager] Battery API connected: 
Object {level: 1, charging: true}
[AuthenticDeviceManager] ✅ AUTHENTIC MacBook HARDWARE SYNC ACTIVE - Zero hardcoded data, only real device metrics
[RealTimeDeviceManager] Current device added: 
Object {id: "device-MTkyMHgxMDgw", name: "MacBook", type: "laptop", battery: 100, isCharging: true, …}
[MobileDeviceManager] Motion tracking initialized
[MobileDeviceManager] Orientation tracking initialized
[MobileDeviceManager] Touch tracking initialized
[MobileDeviceManager] Network monitoring initialized
[MobileDeviceManager] Memory monitoring initialized
[MobileDeviceManager] Initialization complete
[AuthenticEnergyData] Starting authentic data collection...
[useRealTimeData] Connecting to real device manager...
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] Real device manager is ready
energySyncController.start is not a function
at https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/src/hooks/useRealTimeData.ts:85:26
at commitHookEffectListMount (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=1696976c:16915:34)
at commitPassiveMountOnFiber (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=1696976c:18156:19)
at commitPassiveMountEffects_complete (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=1696976c:18129:17)
at commitPassiveMountEffects_begin (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=1696976c:18119:15)
at commitPassiveMountEffects (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=1696976c:18109:11)
at flushPassiveEffectsImpl (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=1696976c:19490:11)
at flushPassiveEffects (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=1696976c:19447:22)
at performSyncWorkOnRoot (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=1696976c:18868:11)
at flushSyncCallbacks (https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/@fs/home/<USER>/workspace/node_modules/.vite/deps/chunk-WERSD76P.js?v=1696976c:9119:30)
[useRealTimeData] Connecting to real device manager...
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] Real device manager is ready
energySyncController.start is not a function
[useRealTimeData] Connecting to real device manager...
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] Real device manager is ready
energySyncController.start is not a function
[AuthenticEnergyData] Starting authentic data collection...
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
