 {/* Real Hardware Metrics */}
            <Card className="glass-panel">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Cpu className="h-5 w-5 text-neon-cyan" />
                  <span>Hardware Energy Generation</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-gray-800/50 rounded-lg">
                    <div className="text-2xl font-bold text-neon-cyan">
                      {hardwareMetrics?.authenticEnergy?.toFixed(6) || '0.000000'}
                    </div>
                    <div className="text-sm text-gray-400">Current Generation (UMatter/5s)</div>
                  </div>
                  <div className="text-center p-4 bg-gray-800/50 rounded-lg">
                    <div className="text-2xl font-bold text-green-400">
                      {realDailyGeneration.toFixed(2)}
                    </div>
                    <div className="text-sm text-gray-400">Projected Daily</div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-300">CPU Usage</span>
                    <span className="text-sm font-mono">{hardwareMetrics?.cpuUsage?.toFixed(1) || '0.0'}%</span>
                  </div>
                  <Progress value={hardwareMetrics?.cpuUsage || 0} className="h-2" />
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-300">Memory Usage</span>
                    <span className="text-sm font-mono">{hardwareMetrics?.memoryUsage?.toFixed(1) || '0.0'}%</span>
                  </div>
                  <Progress value={hardwareMetrics?.memoryUsage || 0} className="h-2" />