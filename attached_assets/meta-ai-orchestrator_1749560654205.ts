/**
 * Meta-AI Orchestrator for nUOS
 * Aggregates responses from multiple AI models (<PERSON><PERSON> <PERSON>, <PERSON>, <PERSON><PERSON> Face)
 * Uses parallel processing and intelligent scoring to deliver optimal answers
 */

export interface AIModelResponse {
  model: 'Grok3' | 'Claude' | 'HuggingFace' | 'GPT-4';
  response: string;
  confidence: number;
  latency: number;
  timestamp: number;
  contextRelevance: number;
  coherenceScore: number;
}

export interface ResponseAggregation {
  optimalResponse: AIModelResponse;
  allResponses: AIModelResponse[];
  aggregationScore: number;
  processingTime: number;
  consensusLevel: number;
}

export interface PromptContext {
  type: 'code' | 'ethics' | 'technical' | 'creative' | 'trading' | 'general';
  domain: string;
  complexity: number;
  userPreference?: 'Grok3' | 'Claude' | 'HuggingFace' | 'GPT-4';
}

export interface UserFeedback {
  responseId: string;
  rating: number; // 1-5
  action: 'upvote' | 'downvote' | 'share' | 'ignore';
  timestamp: number;
  context: PromptContext;
}

class MetaAIOrchestrator {
  private static instance: MetaAIOrchestrator;
  private responseHistory: AIModelResponse[] = [];
  private userFeedback: UserFeedback[] = [];
  private modelPerformanceScores: Map<string, number> = new Map();
  private contextualPreferences: Map<string, string> = new Map();

  static getInstance(): MetaAIOrchestrator {
    if (!this.instance) {
      this.instance = new MetaAIOrchestrator();
    }
    return this.instance;
  }

  constructor() {
    this.initializeModelScores();
    this.loadUserPreferences();
  }

  private initializeModelScores() {
    // Initialize baseline performance scores for authentic AI models
    this.modelPerformanceScores.set('Grok3', 0.85);
    this.modelPerformanceScores.set('Claude', 0.90);
    this.modelPerformanceScores.set('HuggingFace', 0.80);
    this.modelPerformanceScores.set('GPT-4', 0.88);

    // Initialize contextual preferences for authentic data processing
    this.contextualPreferences.set('code', 'Grok3');
    this.contextualPreferences.set('ethics', 'Claude');
    this.contextualPreferences.set('technical', 'Claude');
    this.contextualPreferences.set('creative', 'HuggingFace');
    this.contextualPreferences.set('trading', 'Grok3');
    this.contextualPreferences.set('general', 'Claude');
  }

  private loadUserPreferences() {
    try {
      const savedFeedback = localStorage.getItem('nuos_user_feedback');
      if (savedFeedback) {
        this.userFeedback = JSON.parse(savedFeedback);
        this.updateModelScoresFromFeedback();
      }
    } catch (error) {
      console.log('No saved user preferences found');
    }
  }

  private updateModelScoresFromFeedback() {
    const modelFeedback = new Map<string, { total: number; count: number }>();
    
    this.userFeedback.forEach(feedback => {
      const response = this.responseHistory.find(r => r.timestamp === feedback.timestamp);
      if (response) {
        const current = modelFeedback.get(response.model) || { total: 0, count: 0 };
        current.total += feedback.rating;
        current.count += 1;
        modelFeedback.set(response.model, current);
      }
    });

    modelFeedback.forEach((data, model) => {
      const averageScore = data.total / data.count / 5; // Normalize to 0-1
      this.modelPerformanceScores.set(model, averageScore);
    });
  }

  /**
   * Main orchestration method - queries all models in parallel and aggregates responses
   */
  async orchestrateResponse(prompt: string, context?: PromptContext): Promise<ResponseAggregation> {
    const startTime = performance.now();
    
    // Determine context if not provided
    const inferredContext = context || this.inferContext(prompt);
    
    // Query all models in parallel using Promise.all for true parallelism
    const modelQueries = [
      this.queryGrok3(prompt, inferredContext),
      this.queryClaude(prompt, inferredContext),
      this.queryHuggingFace(prompt, inferredContext),
      this.queryGPT4(prompt, inferredContext)
    ];

    try {
      // Execute all queries concurrently with timeout protection
      const responses = await Promise.allSettled(
        modelQueries.map(query => 
          Promise.race([
            query,
            new Promise<AIModelResponse>((_, reject) => 
              setTimeout(() => reject(new Error('Query timeout')), 10000)
            )
          ])
        )
      );

      // Process successful responses
      const validResponses: AIModelResponse[] = responses
        .filter((result): result is PromiseFulfilledResult<AIModelResponse> => 
          result.status === 'fulfilled'
        )
        .map(result => result.value);

      if (validResponses.length === 0) {
        throw new Error('All AI models failed to respond');
      }

      // Store responses for learning
      this.responseHistory.push(...validResponses);
      this.trimResponseHistory();

      // Calculate aggregation scores and select optimal response
      const scoredResponses = validResponses.map(response => ({
        ...response,
        aggregationScore: this.calculateAggregationScore(response, inferredContext)
      }));

      const optimalResponse = scoredResponses.reduce((best, current) => 
        current.aggregationScore > best.aggregationScore ? current : best
      );

      const processingTime = performance.now() - startTime;
      const consensusLevel = this.calculateConsensusLevel(validResponses);

      return {
        optimalResponse,
        allResponses: validResponses,
        aggregationScore: optimalResponse.aggregationScore,
        processingTime,
        consensusLevel
      };

    } catch (error) {
      console.error('Meta-AI orchestration failed:', error);
      throw new Error(`Orchestration failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Query Grok 3 model via X.AI API
   */
  private async queryGrok3(prompt: string, context: PromptContext): Promise<AIModelResponse> {
    const startTime = performance.now();
    
    try {
      const response = await fetch('/api/ai/grok', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          context,
          model: 'grok-beta'
        })
      });

      if (!response.ok) {
        throw new Error(`Grok API error: ${response.status}`);
      }

      const data = await response.json();
      
      return {
        model: 'Grok3',
        response: data.content || data.message || 'No response received',
        confidence: data.confidence || 0.85,
        latency: performance.now() - startTime,
        timestamp: Date.now(),
        contextRelevance: this.calculateContextRelevance(data.content || '', context),
        coherenceScore: this.calculateCoherenceScore(data.content || '')
      };
    } catch (error) {
      throw new Error(`Grok3 query failed: ${error instanceof Error ? error.message : 'API unavailable'}`);
    }
  }

  /**
   * Query Claude model via Anthropic API
   */
  private async queryClaude(prompt: string, context: PromptContext): Promise<AIModelResponse> {
    const startTime = performance.now();
    
    try {
      const response = await fetch('/api/ai/claude', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          context,
          model: 'claude-3-sonnet-20240229'
        })
      });

      if (!response.ok) {
        throw new Error(`Claude API error: ${response.status}`);
      }

      const data = await response.json();
      
      return {
        model: 'Claude',
        response: data.content || data.message || 'No response received',
        confidence: data.confidence || 0.90,
        latency: performance.now() - startTime,
        timestamp: Date.now(),
        contextRelevance: this.calculateContextRelevance(data.content || '', context),
        coherenceScore: this.calculateCoherenceScore(data.content || '')
      };
    } catch (error) {
      throw new Error(`Claude query failed: ${error instanceof Error ? error.message : 'API unavailable'}`);
    }
  }

  /**
   * Query Hugging Face model via Inference API
   */
  private async queryHuggingFace(prompt: string, context: PromptContext): Promise<AIModelResponse> {
    const startTime = performance.now();
    
    try {
      const response = await fetch('/api/ai/huggingface', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          context,
          model: 'microsoft/DialoGPT-large'
        })
      });

      if (!response.ok) {
        throw new Error(`HuggingFace API error: ${response.status}`);
      }

      const data = await response.json();
      
      return {
        model: 'HuggingFace',
        response: data.content || data.generated_text || 'No response received',
        confidence: data.confidence || 0.80,
        latency: performance.now() - startTime,
        timestamp: Date.now(),
        contextRelevance: this.calculateContextRelevance(data.content || '', context),
        coherenceScore: this.calculateCoherenceScore(data.content || '')
      };
    } catch (error) {
      throw new Error(`HuggingFace query failed: ${error instanceof Error ? error.message : 'API unavailable'}`);
    }
  }

  /**
   * Query GPT-4 model via OpenAI API
   */
  private async queryGPT4(prompt: string, context: PromptContext): Promise<AIModelResponse> {
    const startTime = performance.now();
    
    try {
      const response = await fetch('/api/ai/openai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt,
          context,
          model: 'gpt-4-turbo-preview'
        })
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }

      const data = await response.json();
      
      return {
        model: 'GPT-4',
        response: data.content || data.choices?.[0]?.message?.content || 'No response received',
        confidence: data.confidence || 0.88,
        latency: performance.now() - startTime,
        timestamp: Date.now(),
        contextRelevance: this.calculateContextRelevance(data.content || '', context),
        coherenceScore: this.calculateCoherenceScore(data.content || '')
      };
    } catch (error) {
      throw new Error(`GPT-4 query failed: ${error instanceof Error ? error.message : 'API unavailable'}`);
    }
  }

  /**
   * Infer context from prompt content
   */
  private inferContext(prompt: string): PromptContext {
    const lowerPrompt = prompt.toLowerCase();
    
    let type: PromptContext['type'] = 'general';
    let complexity = 0.5;
    
    if (lowerPrompt.includes('code') || lowerPrompt.includes('function') || lowerPrompt.includes('debug')) {
      type = 'code';
      complexity = 0.8;
    } else if (lowerPrompt.includes('ethics') || lowerPrompt.includes('moral') || lowerPrompt.includes('privacy')) {
      type = 'ethics';
      complexity = 0.7;
    } else if (lowerPrompt.includes('technical') || lowerPrompt.includes('system') || lowerPrompt.includes('optimize')) {
      type = 'technical';
      complexity = 0.8;
    } else if (lowerPrompt.includes('creative') || lowerPrompt.includes('idea') || lowerPrompt.includes('design')) {
      type = 'creative';
      complexity = 0.6;
    } else if (lowerPrompt.includes('trade') || lowerPrompt.includes('market') || lowerPrompt.includes('tru')) {
      type = 'trading';
      complexity = 0.9;
    }
    
    return {
      type,
      domain: 'nU ecosystem',
      complexity
    };
  }

  /**
   * Calculate aggregation score for response selection
   */
  private calculateAggregationScore(response: AIModelResponse, context: PromptContext): number {
    const modelScore = this.modelPerformanceScores.get(response.model) || 0.5;
    const preferredModel = this.contextualPreferences.get(context.type);
    const preferenceBonus = preferredModel === response.model ? 0.1 : 0;
    
    return (
      response.confidence * 0.3 +
      response.contextRelevance * 0.3 +
      response.coherenceScore * 0.2 +
      modelScore * 0.15 +
      preferenceBonus +
      (1000 / Math.max(response.latency, 100)) * 0.05 // Faster responses get slight bonus
    );
  }

  /**
   * Calculate context relevance score
   */
  private calculateContextRelevance(response: string, context: PromptContext): number {
    const keywords = {
      code: ['function', 'variable', 'class', 'method', 'algorithm', 'debug'],
      ethics: ['responsible', 'privacy', 'consent', 'transparent', 'ethical'],
      technical: ['system', 'performance', 'optimization', 'architecture', 'scalability'],
      creative: ['innovative', 'design', 'artistic', 'unique', 'imaginative'],
      trading: ['market', 'price', 'trading', 'investment', 'portfolio'],
      general: ['analysis', 'solution', 'approach', 'strategy', 'recommendation']
    };
    
    const contextKeywords = keywords[context.type] || keywords.general;
    const lowerResponse = response.toLowerCase();
    
    const keywordMatches = contextKeywords.filter(keyword => 
      lowerResponse.includes(keyword)
    ).length;
    
    return Math.min(keywordMatches / contextKeywords.length, 1.0);
  }

  /**
   * Calculate coherence score based on response structure
   */
  private calculateCoherenceScore(response: string): number {
    // Basic coherence metrics
    const sentences = response.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const words = response.split(/\s+/).filter(w => w.length > 0);
    
    if (sentences.length === 0 || words.length === 0) return 0;
    
    const avgSentenceLength = words.length / sentences.length;
    const hasStructure = response.includes('\n') || response.includes('•') || response.includes('-');
    const hasNumbers = /\d/.test(response);
    
    let score = 0.5; // Base score
    
    // Optimal sentence length (10-25 words)
    if (avgSentenceLength >= 10 && avgSentenceLength <= 25) score += 0.2;
    
    // Structured content bonus
    if (hasStructure) score += 0.2;
    
    // Data-driven content bonus
    if (hasNumbers) score += 0.1;
    
    return Math.min(score, 1.0);
  }

  /**
   * Calculate consensus level between responses
   */
  private calculateConsensusLevel(responses: AIModelResponse[]): number {
    if (responses.length < 2) return 1.0;
    
    let totalSimilarity = 0;
    let comparisons = 0;
    
    for (let i = 0; i < responses.length; i++) {
      for (let j = i + 1; j < responses.length; j++) {
        totalSimilarity += this.calculateResponseSimilarity(
          responses[i].response,
          responses[j].response
        );
        comparisons++;
      }
    }
    
    return comparisons > 0 ? totalSimilarity / comparisons : 0;
  }

  /**
   * Calculate similarity between two responses
   */
  private calculateResponseSimilarity(response1: string, response2: string): number {
    const words1 = response1.toLowerCase().split(/\s+/);
    const words2 = response2.toLowerCase().split(/\s+/);
    const allWords = new Set([...words1, ...words2]);
    
    let intersection = 0;
    Array.from(allWords).forEach(word => {
      if (words1.includes(word) && words2.includes(word)) {
        intersection++;
      }
    });
    
    return intersection / allWords.size;
  }

  /**
   * Record user feedback for learning
   */
  recordUserFeedback(feedback: UserFeedback): void {
    this.userFeedback.push(feedback);
    
    // Save to localStorage for persistence
    try {
      localStorage.setItem('nuos_user_feedback', JSON.stringify(this.userFeedback));
    } catch (error) {
      console.error('Failed to save user feedback:', error);
    }
    
    // Update model scores based on new feedback
    this.updateModelScoresFromFeedback();
  }

  /**
   * Get model performance analytics
   */
  getModelAnalytics(): {
    modelScores: Map<string, number>;
    totalQueries: number;
    averageLatency: number;
    userSatisfaction: number;
    contextualPreferences: Map<string, string>;
  } {
    const totalQueries = this.responseHistory.length;
    const averageLatency = totalQueries > 0 
      ? this.responseHistory.reduce((sum, r) => sum + r.latency, 0) / totalQueries 
      : 0;
    
    const userSatisfaction = this.userFeedback.length > 0
      ? this.userFeedback.reduce((sum, f) => sum + f.rating, 0) / (this.userFeedback.length * 5)
      : 0;

    return {
      modelScores: new Map(this.modelPerformanceScores),
      totalQueries,
      averageLatency,
      userSatisfaction,
      contextualPreferences: new Map(this.contextualPreferences)
    };
  }

  /**
   * Trim response history to prevent memory bloat
   */
  private trimResponseHistory(): void {
    if (this.responseHistory.length > 500) {
      this.responseHistory = this.responseHistory.slice(-500);
    }
  }

  /**
   * Reset all learning data (for testing purposes)
   */
  resetLearningData(): void {
    this.responseHistory = [];
    this.userFeedback = [];
    this.initializeModelScores();
    localStorage.removeItem('nuos_user_feedback');
  }
}

export const metaAIOrchestrator = MetaAIOrchestrator.getInstance();