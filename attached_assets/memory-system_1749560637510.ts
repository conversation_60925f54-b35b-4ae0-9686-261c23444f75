/**
 * nUOS Memory System
 * Implements long-term memory, historical data logging, and response caching
 */

export interface BotMetrics {
  drainRate: number;
  nodeStrength: number;
  efficiency: number;
  status: 'active' | 'idle';
  lastUpdate: number;
}

export interface SystemMetrics {
  totalEnergy: number;
  peersConnected: number;
  umatterFlow: number;
  truBalance: number;
  nuvaShares: number;
  batteryLevel: number;
  networkHealth: number;
}

export interface UniverseKnowledge {
  physics: {
    energy_conversion: {
      E_b: string;
      U_s: string;
      A_nU: string;
      K_UM: string;
      forms: {
        UMatter: string;
        trU: string;
        nUva: string;
      };
      F_4Ce: string;
    };
  };
  bots: {
    behavior: {
      drainRate: string;
      nodeStrength: string;
      efficiency: string;
    };
  };
  system: {
    networking: string;
    trading: string;
  };
}

export class HistoryLogger {
  private logs: string[] = [];
  private maxLogs: number = 10000; // Prevent memory overflow

  logEvent(event: string, data: any) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${event}: ${JSON.stringify(data)}`;
    this.logs.push(logEntry);
    
    // Maintain log size limit
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
  }

  getLogs(): string[] {
    return [...this.logs];
  }

  getRecentLogs(count: number = 100): string[] {
    return this.logs.slice(-count);
  }

  searchLogs(query: string): string[] {
    const lowerQuery = query.toLowerCase();
    return this.logs.filter(log => 
      log.toLowerCase().includes(lowerQuery)
    );
  }

  exportLogs(): string {
    return this.logs.join('\n');
  }

  clearLogs() {
    this.logs = [];
  }
}

export class ResponseCache {
  private cache: Map<string, { 
    response: string; 
    model: string; 
    timestamp: number;
    confidence?: number;
    context?: string;
  }> = new Map();
  
  private maxCacheSize: number = 1000;
  private cacheExpiryMs: number = 24 * 60 * 60 * 1000; // 24 hours

  cacheResponse(query: string, response: string, model: string, confidence?: number, context?: string) {
    const key = this.normalizeQuery(query);
    this.cache.set(key, { 
      response, 
      model, 
      timestamp: Date.now(),
      confidence,
      context
    });
    
    // Maintain cache size limit
    if (this.cache.size > this.maxCacheSize) {
      const keys = Array.from(this.cache.keys());
      if (keys.length > 0) {
        this.cache.delete(keys[0]);
      }
    }
  }

  getCachedResponse(query: string): { response: string; model: string; confidence?: number } | null {
    const key = this.normalizeQuery(query);
    const entry = this.cache.get(key);
    
    if (entry) {
      // Check if cache entry is still valid
      if (Date.now() - entry.timestamp < this.cacheExpiryMs) {
        return { 
          response: entry.response, 
          model: entry.model,
          confidence: entry.confidence
        };
      } else {
        // Remove expired entry
        this.cache.delete(key);
      }
    }
    return null;
  }

  searchSimilarQueries(query: string, threshold: number = 0.7): Array<{
    query: string;
    response: string;
    model: string;
    similarity: number;
  }> {
    const results: Array<{
      query: string;
      response: string;
      model: string;
      similarity: number;
    }> = [];

    const normalizedQuery = this.normalizeQuery(query);
    
    Array.from(this.cache.entries()).forEach(([cachedQuery, entry]) => {
      const similarity = this.calculateSimilarity(normalizedQuery, cachedQuery);
      if (similarity >= threshold) {
        results.push({
          query: cachedQuery,
          response: entry.response,
          model: entry.model,
          similarity
        });
      }
    });

    return results.sort((a, b) => b.similarity - a.similarity);
  }

  private normalizeQuery(query: string): string {
    return query.toLowerCase().trim();
  }

  private calculateSimilarity(str1: string, str2: string): number {
    const words1 = str1.split(/\s+/);
    const words2 = str2.split(/\s+/);
    const allWords = new Set([...words1, ...words2]);
    
    let intersection = 0;
    Array.from(allWords).forEach(word => {
      if (words1.includes(word) && words2.includes(word)) {
        intersection++;
      }
    });
    
    return intersection / allWords.size;
  }

  exportCache(): string {
    const cacheData = Array.from(this.cache.entries()).map(([query, entry]) => ({
      query,
      ...entry
    }));
    return JSON.stringify(cacheData, null, 2);
  }

  clearCache() {
    this.cache.clear();
  }

  getCacheStats(): {
    size: number;
    oldestEntry: number | null;
    newestEntry: number | null;
  } {
    if (this.cache.size === 0) {
      return { size: 0, oldestEntry: null, newestEntry: null };
    }

    const timestamps = Array.from(this.cache.values()).map(entry => entry.timestamp);
    return {
      size: this.cache.size,
      oldestEntry: Math.min(...timestamps),
      newestEntry: Math.max(...timestamps)
    };
  }
}

export class UniverseKnowledgeManager {
  private knowledge: UniverseKnowledge;

  constructor() {
    this.knowledge = this.initializeUniverseKnowledge();
  }

  private initializeUniverseKnowledge(): UniverseKnowledge {
    return {
      physics: {
        energy_conversion: {
          E_b: "Lithium-ion battery, 4000mAh at 3.7V = 14.8Wh/phone",
          U_s: "Q × V = 14,400C × 3.7V = 14.8Wh/phone",
          A_nU: "Sleep (8h, 5% battery) = 0.74Wh/phone; Scrolling (2h, 2% battery) = 0.296Wh/phone",
          K_UM: "1Wh ≈ 0.675 UM; Sleep = 0.5 UM/phone; Scrolling = 0.2 UM/phone",
          forms: {
            UMatter: "Base unit, 3.5B UM/day from 5B phones",
            trU: "1 UM = 0.1 trU; 350M trU/day tradable units",
            nUva: "1 UM = 1 nUva; 3.5B nUva/day storable units"
          },
          F_4Ce: "350M trU/day = 35MW continuous combustible force"
        }
      },
      bots: {
        behavior: {
          drainRate: "Random between 0.05 and 0.15 for realistic battery consumption",
          nodeStrength: "Random between 0.5 and 1.0 for network connectivity",
          efficiency: "Random between 80 and 100 percent for optimal performance"
        }
      },
      system: {
        networking: "WebSocket-based peer-to-peer connectivity with auto-discovery",
        trading: "trU transactions at $0.0001/Wh with real-time conversion rates"
      }
    };
  }

  getKnowledge(): UniverseKnowledge {
    return this.knowledge;
  }

  updateKnowledge(path: string, value: any) {
    const pathParts = path.split('.');
    let current: any = this.knowledge;
    
    for (let i = 0; i < pathParts.length - 1; i++) {
      if (!current[pathParts[i]]) {
        current[pathParts[i]] = {};
      }
      current = current[pathParts[i]];
    }
    
    current[pathParts[pathParts.length - 1]] = value;
  }

  searchKnowledge(query: string): string[] {
    const results: string[] = [];
    const lowerQuery = query.toLowerCase();
    
    this.searchInObject(this.knowledge, '', lowerQuery, results);
    return results;
  }

  private searchInObject(obj: any, prefix: string, query: string, results: string[]) {
    for (const [key, value] of Object.entries(obj)) {
      const currentPath = prefix ? `${prefix}.${key}` : key;
      
      if (typeof value === 'string') {
        if (key.toLowerCase().includes(query) || value.toLowerCase().includes(query)) {
          results.push(`${currentPath}: ${value}`);
        }
      } else if (typeof value === 'object' && value !== null) {
        this.searchInObject(value, currentPath, query, results);
      }
    }
  }

  exportKnowledge(): string {
    return JSON.stringify(this.knowledge, null, 2);
  }
}

export class MemorySystem {
  private historyLogger: HistoryLogger;
  private responseCache: ResponseCache;
  private knowledgeManager: UniverseKnowledgeManager;

  constructor() {
    this.historyLogger = new HistoryLogger();
    this.responseCache = new ResponseCache();
    this.knowledgeManager = new UniverseKnowledgeManager();
  }

  // History Management
  logEvent(event: string, data: any) {
    this.historyLogger.logEvent(event, data);
  }

  getRecentHistory(count: number = 50): string[] {
    return this.historyLogger.getRecentLogs(count);
  }

  searchHistory(query: string): string[] {
    return this.historyLogger.searchLogs(query);
  }

  // Response Caching
  cacheResponse(query: string, response: string, model: string, confidence?: number) {
    this.responseCache.cacheResponse(query, response, model, confidence);
  }

  getCachedResponse(query: string) {
    return this.responseCache.getCachedResponse(query);
  }

  findSimilarResponses(query: string, threshold: number = 0.7) {
    return this.responseCache.searchSimilarQueries(query, threshold);
  }

  // Knowledge Management
  searchKnowledge(query: string): string[] {
    return this.knowledgeManager.searchKnowledge(query);
  }

  updateKnowledge(path: string, value: any) {
    this.knowledgeManager.updateKnowledge(path, value);
    this.logEvent('KnowledgeUpdate', { path, value });
  }

  // Comprehensive Search
  async comprehensiveSearch(query: string): Promise<{
    knowledge: string[];
    history: string[];
    cachedResponses: Array<{
      query: string;
      response: string;
      model: string;
      similarity: number;
    }>;
  }> {
    return {
      knowledge: this.searchKnowledge(query),
      history: this.searchHistory(query),
      cachedResponses: this.findSimilarResponses(query, 0.6)
    };
  }

  // Memory Statistics
  getMemoryStats(): {
    historyCount: number;
    cacheStats: {
      size: number;
      oldestEntry: number | null;
      newestEntry: number | null;
    };
    knowledgeSize: number;
  } {
    return {
      historyCount: this.historyLogger.getLogs().length,
      cacheStats: this.responseCache.getCacheStats(),
      knowledgeSize: JSON.stringify(this.knowledgeManager.getKnowledge()).length
    };
  }

  // Export/Import
  exportMemory(): {
    history: string;
    cache: string;
    knowledge: string;
    timestamp: string;
  } {
    return {
      history: this.historyLogger.exportLogs(),
      cache: this.responseCache.exportCache(),
      knowledge: this.knowledgeManager.exportKnowledge(),
      timestamp: new Date().toISOString()
    };
  }

  // Cleanup
  cleanupMemory() {
    // Keep only recent logs
    const recentLogs = this.historyLogger.getRecentLogs(1000);
    this.historyLogger.clearLogs();
    recentLogs.forEach(log => {
      const match = log.match(/\[(.*?)\] (.*?): (.*)/);
      if (match) {
        this.historyLogger.logEvent(match[2], JSON.parse(match[3]));
      }
    });

    // Clear old cache entries (done automatically by expiry)
    this.logEvent('MemoryCleanup', { timestamp: Date.now() });
  }
}

// Singleton instance
export const memorySystem = new MemorySystem();