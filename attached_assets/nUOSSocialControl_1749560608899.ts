/**
 * nUOS Social Control System
 * Integrates with SpUnder data for governance, deception detection, and social balance
 */

import { HashBot } from './hashbot';

export interface SocialControlReport {
  reportId: string;
  reporterDID: string;
  targetDID: string;
  reportType: 'deception' | 'harassment' | 'spam' | 'harmful_content' | 'governance_violation';
  evidence: {
    merkleRoot: string;
    interactions: string[];
    timestamp: number;
    confidence: number;
  };
  status: 'pending' | 'investigating' | 'resolved' | 'dismissed';
  createdAt: number;
  resolvedAt?: number;
}

export interface GovernanceProposal {
  proposalId: string;
  proposerDID: string;
  title: string;
  description: string;
  category: 'policy' | 'technical' | 'community' | 'economic';
  votes: {
    for: number;
    against: number;
    abstain: number;
  };
  voters: Map<string, 'for' | 'against' | 'abstain'>;
  evidence: string[];
  status: 'draft' | 'voting' | 'passed' | 'rejected';
  createdAt: number;
  votingEnds: number;
}

export interface DIDTrustScore {
  did: string;
  socialScore: number;
  governanceParticipation: number;
  deceptionFlags: number;
  humanResonanceScore: number;
  trustLevel: 'verified' | 'high' | 'medium' | 'low' | 'flagged';
  lastUpdated: number;
}

export class nUOSSocialControl {
  private hashBot: HashBot;
  private reports: Map<string, SocialControlReport> = new Map();
  private proposals: Map<string, GovernanceProposal> = new Map();
  private trustScores: Map<string, DIDTrustScore> = new Map();
  private bannedDIDs: Set<string> = new Set();
  private stubUsers: Map<string, { reason: string; until: number }> = new Map();

  constructor() {
    this.hashBot = new HashBot();
    console.log('[nUOS Social Control] System initialized');
  }

  /**
   * Report a DID for suspicious behavior using SpUnder evidence
   */
  reportDID(
    reporterDID: string,
    targetDID: string,
    reportType: SocialControlReport['reportType'],
    evidence: string[],
    merkleRoot: string
  ): string {
    const reportId = this.hashBot.recursiveSha256(`${reporterDID}_${targetDID}_${Date.now()}`);
    
    const report: SocialControlReport = {
      reportId,
      reporterDID: this.hashBot.hashDID(reporterDID),
      targetDID: this.hashBot.hashDID(targetDID),
      reportType,
      evidence: {
        merkleRoot,
        interactions: evidence.map(e => this.hashBot.recursiveSha256(e)),
        timestamp: Date.now(),
        confidence: this.calculateEvidenceConfidence(evidence, merkleRoot)
      },
      status: 'pending',
      createdAt: Date.now()
    };

    this.reports.set(reportId, report);
    
    // Update trust score immediately for serious reports
    if (reportType === 'deception' || reportType === 'harmful_content') {
      this.updateTrustScore(targetDID, -10);
    }

    console.log(`[Social Control] DID reported: ${reportType} - Report ID: ${reportId.substring(0, 8)}...`);
    return reportId;
  }

  /**
   * Burn a user DID from the system (permanent ban)
   */
  burnUserDID(did: string, reason: string, evidenceMerkleRoot: string): boolean {
    const hashedDID = this.hashBot.hashDID(did);
    
    // Require multiple reports or severe evidence for burning
    const userReports = Array.from(this.reports.values())
      .filter(r => r.targetDID === hashedDID && r.status === 'resolved');
    
    if (userReports.length < 3 && !this.isSevereEvidence(evidenceMerkleRoot)) {
      console.log(`[Social Control] Burn rejected - insufficient evidence for DID: ${hashedDID.substring(0, 8)}...`);
      return false;
    }

    this.bannedDIDs.add(hashedDID);
    this.trustScores.delete(hashedDID);
    
    // Hash the burn reason for immutable record
    const burnRecord = this.hashBot.timestampData(`BURN:${hashedDID}:${reason}:${evidenceMerkleRoot}`);
    
    console.log(`[Social Control] DID burned: ${hashedDID.substring(0, 8)}... - Reason: ${reason}`);
    return true;
  }

  /**
   * Stub a user temporarily (shadow ban)
   */
  stubUserDID(did: string, reason: string, durationHours: number = 24): boolean {
    const hashedDID = this.hashBot.hashDID(did);
    const until = Date.now() + (durationHours * 60 * 60 * 1000);
    
    this.stubUsers.set(hashedDID, {
      reason: this.hashBot.recursiveSha256(reason),
      until
    });

    this.updateTrustScore(did, -5);
    
    console.log(`[Social Control] DID stubbed: ${hashedDID.substring(0, 8)}... for ${durationHours}h`);
    return true;
  }

  /**
   * Create governance proposal with SpUnder evidence
   */
  createGovernanceProposal(
    proposerDID: string,
    title: string,
    description: string,
    category: GovernanceProposal['category'],
    evidence: string[]
  ): string {
    const proposalId = this.hashBot.recursiveSha256(`${proposerDID}_${title}_${Date.now()}`);
    
    const proposal: GovernanceProposal = {
      proposalId,
      proposerDID: this.hashBot.hashDID(proposerDID),
      title,
      description,
      category,
      votes: { for: 0, against: 0, abstain: 0 },
      voters: new Map(),
      evidence: evidence.map(e => this.hashBot.recursiveSha256(e)),
      status: 'draft',
      createdAt: Date.now(),
      votingEnds: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days
    };

    this.proposals.set(proposalId, proposal);
    
    console.log(`[Governance] Proposal created: ${title.substring(0, 30)}... - ID: ${proposalId.substring(0, 8)}...`);
    return proposalId;
  }

  /**
   * Vote on governance proposal
   */
  voteOnProposal(
    voterDID: string,
    proposalId: string,
    vote: 'for' | 'against' | 'abstain'
  ): boolean {
    const proposal = this.proposals.get(proposalId);
    if (!proposal) return false;

    const hashedDID = this.hashBot.hashDID(voterDID);
    
    // Check if user is eligible to vote
    if (this.bannedDIDs.has(hashedDID) || this.stubUsers.has(hashedDID)) {
      console.log(`[Governance] Vote rejected - DID not eligible: ${hashedDID.substring(0, 8)}...`);
      return false;
    }

    // Check trust score requirement
    const trustScore = this.trustScores.get(hashedDID);
    if (!trustScore || trustScore.socialScore < 30) {
      console.log(`[Governance] Vote rejected - insufficient trust score: ${hashedDID.substring(0, 8)}...`);
      return false;
    }

    // Record vote
    const previousVote = proposal.voters.get(hashedDID);
    if (previousVote) {
      proposal.votes[previousVote]--;
    }
    
    proposal.voters.set(hashedDID, vote);
    proposal.votes[vote]++;
    
    // Hash vote for immutable record
    const voteRecord = this.hashBot.timestampData(`VOTE:${proposalId}:${hashedDID}:${vote}`);
    
    console.log(`[Governance] Vote recorded: ${vote} on proposal ${proposalId.substring(0, 8)}...`);
    return true;
  }

  /**
   * Analyze SpUnder data for deception patterns
   */
  analyzeDeceptionPatterns(spunderData: any[]): {
    deceptionScore: number;
    patterns: string[];
    recommendations: string[];
  } {
    let deceptionScore = 0;
    const patterns: string[] = [];
    const recommendations: string[] = [];

    // Analyze interaction patterns
    if (spunderData.length > 0) {
      const rapidClicks = spunderData.filter(d => d.type === 'click').length;
      const timeSpans = this.calculateTimeSpans(spunderData);
      const repeatPatterns = this.detectRepeatPatterns(spunderData);

      // Check for bot-like behavior
      if (rapidClicks > 100 && timeSpans.avg < 500) {
        deceptionScore += 30;
        patterns.push('Rapid automated clicking detected');
        recommendations.push('Increase CAPTCHA verification');
      }

      // Check for pattern repetition (copy-paste behavior)
      if (repeatPatterns.score > 0.8) {
        deceptionScore += 25;
        patterns.push('Highly repetitive behavior patterns');
        recommendations.push('Manual review recommended');
      }

      // Check for suspicious timing
      const nightTimeActivity = this.analyzeActivityTiming(spunderData);
      if (nightTimeActivity > 0.7) {
        deceptionScore += 15;
        patterns.push('Unusual activity timing patterns');
      }
    }

    return {
      deceptionScore: Math.min(deceptionScore, 100),
      patterns,
      recommendations
    };
  }

  /**
   * Update trust score based on behavior and reports
   */
  private updateTrustScore(did: string, adjustment: number): void {
    const hashedDID = this.hashBot.hashDID(did);
    let trustScore = this.trustScores.get(hashedDID);

    if (!trustScore) {
      trustScore = {
        did: hashedDID,
        socialScore: 50,
        governanceParticipation: 0,
        deceptionFlags: 0,
        humanResonanceScore: 50,
        trustLevel: 'medium',
        lastUpdated: Date.now()
      };
    }

    trustScore.socialScore = Math.max(0, Math.min(100, trustScore.socialScore + adjustment));
    trustScore.lastUpdated = Date.now();

    // Update trust level based on score
    if (trustScore.socialScore >= 90) trustScore.trustLevel = 'verified';
    else if (trustScore.socialScore >= 70) trustScore.trustLevel = 'high';
    else if (trustScore.socialScore >= 40) trustScore.trustLevel = 'medium';
    else if (trustScore.socialScore >= 20) trustScore.trustLevel = 'low';
    else trustScore.trustLevel = 'flagged';

    this.trustScores.set(hashedDID, trustScore);
  }

  /**
   * Calculate evidence confidence using Merkle verification
   */
  private calculateEvidenceConfidence(evidence: string[], merkleRoot: string): number {
    if (evidence.length === 0) return 0;
    
    // Simulate Merkle proof verification
    const verifiedItems = evidence.filter(item => {
      return this.hashBot.recursiveSha256(item).length === 64; // Valid hash format
    });

    return (verifiedItems.length / evidence.length) * 100;
  }

  /**
   * Check if evidence is severe enough for immediate action
   */
  private isSevereEvidence(merkleRoot: string): boolean {
    // Simulate checking against known severe content hashes
    const severePatterns = [
      'harmful_content_pattern',
      'illegal_activity_pattern',
      'severe_harassment_pattern'
    ];

    return severePatterns.some(pattern => 
      merkleRoot.includes(this.hashBot.recursiveSha256(pattern).substring(0, 8))
    );
  }

  /**
   * Calculate time spans between interactions
   */
  private calculateTimeSpans(data: any[]): { avg: number; min: number; max: number } {
    if (data.length < 2) return { avg: 0, min: 0, max: 0 };

    const spans = [];
    for (let i = 1; i < data.length; i++) {
      spans.push(data[i].timestamp - data[i-1].timestamp);
    }

    return {
      avg: spans.reduce((sum, span) => sum + span, 0) / spans.length,
      min: Math.min(...spans),
      max: Math.max(...spans)
    };
  }

  /**
   * Detect repetitive patterns in interactions
   */
  private detectRepeatPatterns(data: any[]): { score: number; patterns: string[] } {
    const patterns: string[] = [];
    let repeatScore = 0;

    // Check for repeated sequences
    const sequences = data.map(d => `${d.type}_${d.target}`);
    const sequenceGroups = new Map<string, number>();

    sequences.forEach(seq => {
      sequenceGroups.set(seq, (sequenceGroups.get(seq) || 0) + 1);
    });

    // Calculate repetition score
    for (const [pattern, count] of sequenceGroups) {
      if (count > 5) {
        patterns.push(pattern);
        repeatScore += count / sequences.length;
      }
    }

    return {
      score: Math.min(repeatScore, 1),
      patterns
    };
  }

  /**
   * Analyze activity timing for suspicious patterns
   */
  private analyzeActivityTiming(data: any[]): number {
    const activityHours = data.map(d => new Date(d.timestamp).getHours());
    const nightHours = activityHours.filter(hour => hour >= 23 || hour <= 5);
    
    return nightHours.length / activityHours.length;
  }

  /**
   * Get social control statistics
   */
  getSystemStats(): {
    totalReports: number;
    pendingReports: number;
    bannedDIDs: number;
    stubUsers: number;
    activeProposals: number;
    totalVotes: number;
  } {
    const pendingReports = Array.from(this.reports.values())
      .filter(r => r.status === 'pending').length;
    
    const activeProposals = Array.from(this.proposals.values())
      .filter(p => p.status === 'voting').length;
    
    const totalVotes = Array.from(this.proposals.values())
      .reduce((sum, p) => sum + p.votes.for + p.votes.against + p.votes.abstain, 0);

    return {
      totalReports: this.reports.size,
      pendingReports,
      bannedDIDs: this.bannedDIDs.size,
      stubUsers: this.stubUsers.size,
      activeProposals,
      totalVotes
    };
  }

  /**
   * Get trust score for a DID
   */
  getTrustScore(did: string): DIDTrustScore | null {
    const hashedDID = this.hashBot.hashDID(did);
    return this.trustScores.get(hashedDID) || null;
  }

  /**
   * Check if DID is banned or stubbed
   */
  isDIDRestricted(did: string): {
    banned: boolean;
    stubbed: boolean;
    stubReason?: string;
    stubUntil?: number;
  } {
    const hashedDID = this.hashBot.hashDID(did);
    const banned = this.bannedDIDs.has(hashedDID);
    const stubInfo = this.stubUsers.get(hashedDID);
    const stubbed = stubInfo ? stubInfo.until > Date.now() : false;

    return {
      banned,
      stubbed,
      stubReason: stubInfo?.reason,
      stubUntil: stubInfo?.until
    };
  }
}

export const socialControl = new nUOSSocialControl();