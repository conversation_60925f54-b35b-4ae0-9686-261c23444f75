/**
 * SpUnderBot - Spider/Angel Rating System
 * Implements ethical content rating, audit trails, and lUvHub routing
 */

import { BrowserEventEmitter } from './browser-events';

// Browser-compatible memory system
const memorySystem = {
  logEvent: (event: string, data: any) => {
    console.log(`[MemorySystem] ${event}:`, data);
    try {
      const logs = JSON.parse(localStorage.getItem('memory_logs') || '[]');
      logs.push({ event, data, timestamp: Date.now() });
      if (logs.length > 1000) logs.splice(0, 100); // Keep only recent 1000 logs
      localStorage.setItem('memory_logs', JSON.stringify(logs));
    } catch (error) {
      console.warn('[MemorySystem] Failed to store log:', error);
    }
  }
};

export interface SpUnderInteraction {
  id: string;
  did: string;
  data: {
    type: 'text' | 'image' | 'video' | 'link';
    content: string;
    metadata?: Record<string, any>;
  };
  timestamp: number;
  rating?: SpUnderRating;
  status?: 'pending' | 'approved' | 'rejected' | 'hubRouted';
  trail?: string[];
}

export interface SpUnderAudit {
  did: string;
  interactionId: string;
  trail: string[];
  hash: string;
  verified: boolean;
  timestamp: number;
  ratingHistory: SpUnderRating[];
}

export type SpUnderRating = 'G' | 'T' | 'M' | 'X' | 'Rejected';

export interface AIRaterConfig {
  onlyFansRules: boolean;
  scale: SpUnderRating[];
  strictMode: boolean;
}

export class AIRater {
  private brainModule: string;
  private config: AIRaterConfig;

  constructor(brainModule: string = 'nuos-brain.js') {
    this.brainModule = brainModule;
    this.config = {
      onlyFansRules: true,
      scale: ['G', 'T', 'M', 'X', 'Rejected'],
      strictMode: true
    };
  }

  public analyze(content: string, config?: Partial<AIRaterConfig>): SpUnderRating {
    const analysisConfig = { ...this.config, ...config };
    
    // Implement content analysis based on OnlyFans 2024 guidelines
    const analysisResult = this.performContentAnalysis(content);
    
    // Log analysis for audit trail
    memorySystem.logEvent('content_analysis', {
      content: content.substring(0, 100) + '...',
      rating: analysisResult.rating,
      confidence: analysisResult.confidence,
      flags: analysisResult.flags
    });

    return analysisResult.rating;
  }

  private performContentAnalysis(content: string): {
    rating: SpUnderRating;
    confidence: number;
    flags: string[];
  } {
    const flags: string[] = [];
    let rating: SpUnderRating = 'G';

    // Check for explicit keywords and patterns
    const rejectedPatterns = [
      /\b(minor|child|underage)\b/i,
      /\b(non-?consensual|forced|rape)\b/i,
      /\b(gore|violence|death)\b/i,
      /\b(drugs|weapons|illegal)\b/i
    ];

    const explicitPatterns = [
      /\b(porn|xxx|hardcore|bdsm)\b/i,
      /\b(explicit|graphic|sexual)\b/i
    ];

    const maturePatterns = [
      /\b(nude|naked|adult|18\+)\b/i,
      /\b(sexy|intimate|erotic)\b/i
    ];

    const teenPatterns = [
      /\b(fuck|shit|damn|hell)\b/i,
      /\b(teen|young adult)\b/i
    ];

    // Check for rejected content first
    for (const pattern of rejectedPatterns) {
      if (pattern.test(content)) {
        flags.push('rejected_content');
        return { rating: 'Rejected', confidence: 0.95, flags };
      }
    }

    // Check for X-rated content
    for (const pattern of explicitPatterns) {
      if (pattern.test(content)) {
        flags.push('explicit_content');
        rating = 'X';
      }
    }

    // Check for M-rated content
    if (rating === 'G') {
      for (const pattern of maturePatterns) {
        if (pattern.test(content)) {
          flags.push('mature_content');
          rating = 'M';
        }
      }
    }

    // Check for T-rated content
    if (rating === 'G') {
      for (const pattern of teenPatterns) {
        if (pattern.test(content)) {
          flags.push('teen_content');
          rating = 'T';
        }
      }
    }

    return {
      rating,
      confidence: flags.length > 0 ? 0.85 : 0.70,
      flags
    };
  }
}

export class SpUnderBot extends BrowserEventEmitter {
  private aiRater: AIRater;
  private hubAccess: Map<string, string[]> = new Map();
  private interactions: Map<string, SpUnderInteraction> = new Map();
  private auditTrails: Map<string, SpUnderAudit> = new Map();

  constructor() {
    super();
    this.aiRater = new AIRater('nuos-brain.js');
    this.setupEventHandlers();
    console.log('[SpUnderBot] Spider/Angel rating system initialized');
  }

  private setupEventHandlers(): void {
    // Handle rating updates
    this.on('ratingUpdate', (data) => {
      console.log(`[SpUnderBot] Rating updated: ${data.id} -> ${data.rating}`);
    });

    // Handle hub routing
    this.on('hubRouted', (data) => {
      console.log(`[SpUnderBot] Content routed to lUvHub: DID ${data.did}`);
    });

    // Handle rejections
    this.on('reject', (interaction) => {
      console.warn(`[SpUnderBot] Content rejected: ${interaction.id}`);
      this.reportToEagleEye(interaction);
    });
  }

  public rateInteraction(interaction: SpUnderInteraction): SpUnderInteraction {
    try {
      const content = interaction.data.content;
      const rating = this.aiRater.analyze(content, {
        onlyFansRules: true,
        scale: ['G', 'T', 'M', 'X', 'Rejected']
      });

      const ratedInteraction = { ...interaction, rating };

      if (rating === 'Rejected') {
        ratedInteraction.status = 'rejected';
        this.emit('reject', ratedInteraction);
        memorySystem.logEvent('content_rejected', {
          id: interaction.id,
          did: interaction.did,
          reason: 'ai_analysis',
          timestamp: Date.now()
        });
        return ratedInteraction;
      }

      if (['M', 'X'].includes(rating)) {
        this.routeToHub(interaction.did, ratedInteraction);
        ratedInteraction.status = 'hubRouted';
      } else {
        ratedInteraction.status = 'approved';
      }

      // Store interaction
      this.interactions.set(interaction.id, ratedInteraction);
      
      // Create audit trail
      this.createAuditTrail(ratedInteraction);

      this.emit('ratingUpdate', { id: interaction.id, rating });

      return ratedInteraction;
    } catch (error) {
      console.error('[SpUnderBot] Error rating interaction:', error);
      return { ...interaction, rating: 'Rejected', status: 'rejected' };
    }
  }

  public auditTrail(did: string, interactionId: string): SpUnderAudit | null {
    const auditKey = `${did}-${interactionId}`;
    const audit = this.auditTrails.get(auditKey);
    
    if (!audit) {
      console.warn(`[SpUnderBot] Audit trail not found: ${auditKey}`);
      return null;
    }

    // Verify audit integrity
    const verified = this.verifyAuditHash(audit);
    return { ...audit, verified };
  }

  private routeToHub(did: string, interaction: SpUnderInteraction): void {
    const trail = this.hubAccess.get(did) || [];
    const clickTrail = `click_${Date.now()}_${interaction.id}`;
    trail.push(clickTrail);
    this.hubAccess.set(did, trail);

    // Log hub access
    memorySystem.logEvent('hub_access', {
      did,
      interactionId: interaction.id,
      rating: interaction.rating,
      trailLength: trail.length,
      timestamp: Date.now()
    });

    this.emit('hubRouted', { did, interaction });
  }

  private createAuditTrail(interaction: SpUnderInteraction): void {
    const auditKey = `${interaction.did}-${interaction.id}`;
    const trail = this.hubAccess.get(interaction.did) || [];
    const hash = this.generateAuditHash(interaction);

    const audit: SpUnderAudit = {
      did: interaction.did,
      interactionId: interaction.id,
      trail: [...trail],
      hash,
      verified: true,
      timestamp: Date.now(),
      ratingHistory: [interaction.rating!]
    };

    this.auditTrails.set(auditKey, audit);
  }

  private generateAuditHash(interaction: SpUnderInteraction): string {
    const data = `${interaction.id}-${interaction.did}-${interaction.timestamp}-${interaction.rating}`;
    // Simple hash function for demo - in production use proper cryptographic hash
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(16);
  }

  private verifyAuditHash(audit: SpUnderAudit): boolean {
    // Reconstruct hash and verify
    const reconstructedData = `${audit.interactionId}-${audit.did}-${audit.timestamp}`;
    // In production, use proper verification
    return audit.hash.length > 0;
  }

  private reportToEagleEye(interaction: SpUnderInteraction): void {
    console.log(`[SpUnderBot] Reporting to EagleEyeBot: ${interaction.id}`);
    memorySystem.logEvent('eagle_eye_report', {
      interactionId: interaction.id,
      did: interaction.did,
      contentType: interaction.data.type,
      reason: 'content_violation',
      timestamp: Date.now()
    });
  }

  public getUserFilters(did: string): {
    allowedRatings: SpUnderRating[];
    hideExplicit: boolean;
    lUvHubAccess: boolean;
  } {
    const trail = this.hubAccess.get(did) || [];
    const hasHubAccess = trail.length >= 5; // Requires 5+ clicks for lUvHub access

    return {
      allowedRatings: ['G', 'T'], // Default safe ratings
      hideExplicit: true,
      lUvHubAccess: hasHubAccess
    };
  }

  public getWebVisualization(): {
    nodes: any[];
    edges: any[];
    stats: Record<string, number>;
  } {
    const nodes = Array.from(this.interactions.values()).map(interaction => ({
      id: interaction.id,
      rating: interaction.rating,
      type: interaction.data.type,
      timestamp: interaction.timestamp,
      status: interaction.status
    }));

    const edges = Array.from(this.hubAccess.entries()).flatMap(([did, trail]) =>
      trail.map((click, index) => ({
        source: did,
        target: click,
        index
      }))
    );

    const stats = {
      totalInteractions: this.interactions.size,
      gRated: Array.from(this.interactions.values()).filter(i => i.rating === 'G').length,
      tRated: Array.from(this.interactions.values()).filter(i => i.rating === 'T').length,
      mRated: Array.from(this.interactions.values()).filter(i => i.rating === 'M').length,
      xRated: Array.from(this.interactions.values()).filter(i => i.rating === 'X').length,
      rejected: Array.from(this.interactions.values()).filter(i => i.rating === 'Rejected').length,
      hubRouted: Array.from(this.interactions.values()).filter(i => i.status === 'hubRouted').length
    };

    return { nodes, edges, stats };
  }

  public simulateAgeVerification(did: string): boolean {
    // Simulate zero-knowledge age verification
    console.log(`[SpUnderBot] Age verification for DID: ${did}`);
    
    // In production, integrate with proper ZK proof system
    const isVerified = Math.random() > 0.1; // 90% verification rate for demo
    
    memorySystem.logEvent('age_verification', {
      did,
      verified: isVerified,
      method: 'zk_proof',
      timestamp: Date.now()
    });

    return isVerified;
  }

  public getHubAccessFlow(did: string): {
    currentStep: number;
    totalSteps: number;
    nextAction: string;
    canAccess: boolean;
  } {
    const trail = this.hubAccess.get(did) || [];
    const steps = [
      'Click "Enter lUvHub"',
      'Verify Age (18+)',
      'Confirm Adult Content',
      'Accept Terms',
      'View Mature Content',
      'Access lUvHub'
    ];

    const currentStep = Math.min(trail.length, steps.length - 1);
    const canAccess = trail.length >= 5;

    return {
      currentStep,
      totalSteps: steps.length,
      nextAction: steps[currentStep] || 'Access Granted',
      canAccess
    };
  }
}

// Export singleton instance
export const spUnderBot = new SpUnderBot();
export default spUnderBot;