[AuthenticDeviceManager] Energy accumulated for batching: 2.19611
[useRealBalance] Authentic balance: 342.96722753 UMatter from 2085 transactions
[AuthenticDeviceManager] REAL MacBook Battery: 
Object {realLevel: "100.0%", realCharging: "YES", source: "AUTHENTIC_DEVICE_API"}
[AuthenticDeviceManager] AUTHENTIC MacBook METRICS: 
Object {battery: "100.0% CHARGING", network: "10Mbps WIFI", memory: "39.0MB used of 51.2MB", cores: 8, platform: "MacIntel"}
[AuthenticDeviceManager] Energy calculation: Base: 2.181321, Variance: 0.003455, Total: 2.184776
[AuthenticDeviceManager] UMatter generated: 2.184776 from authentic_device_sync
[AuthenticDeviceManager] Energy accumulated for batching: 2.184776
[AuthenticDeviceManager] Energy calculation: Base: 2.181320, Variance: -0.031722, Total: 2.149597
[AuthenticDeviceManager] Energy accumulated for batching: 2.149597
[EnergySyncController] Added 0.000760 UMatter from real_world_energy, accumulator size: 1
[useRealBalance] Authentic balance: 342.96722753 UMatter from 2085 transactions
[RealHardwareConnector] AUTHENTIC: 0.001303 UMatter from real Node.js hardware
[RealHardwareConnector] Real metrics: CPU 23.70%, Memory 160.9MB, Power 0.21W
[useRealTimeData] REAL HARDWARE ENERGY: 0.001303 UMatter from Node.js APIs
[useRealTimeData] Real hardware metrics: CPU 23.70%, Memory 160.9MB, Power 0.21W
[EnergySyncController] Added 0.001303 UMatter from real_nodejs_hardware, accumulator size: 2
[useRealBalance] Authentic balance: 342.96722753 UMatter from 2085 transactions
optimizePerformance is not defined
optimizePerformance is not defined
[RealHardwareConnector] AUTHENTIC: 0.001522 UMatter from real Node.js hardware
[RealHardwareConnector] Real metrics: CPU 23.70%, Memory 161.0MB, Power 0.21W
[useRealTimeData] REAL HARDWARE ENERGY: 0.001522 UMatter from Node.js APIs
[useRealTimeData] Real hardware metrics: CPU 23.70%, Memory 161.0MB, Power 0.21W
[EnergySyncController] Added 0.001522 UMatter from real_nodejs_hardware, accumulator size: 3
[RealHardwareConnector] AUTHENTIC: 0.001278 UMatter from real Node.js hardware
[RealHardwareConnector] Real metrics: CPU 23.70%, Memory 161.1MB, Power 0.21W
[useRealTimeData] REAL HARDWARE ENERGY: 0.001278 UMatter from Node.js APIs
[useRealTimeData] Real hardware metrics: CPU 23.70%, Memory 161.1MB, Power 0.21W
[EnergySyncController] Added 0.001278 UMatter from real_nodejs_hardware, accumulator size: 4
[RealHardwareConnector] AUTHENTIC: 0.001465 UMatter from real Node.js hardware
[RealHardwareConnector] Real metrics: CPU 23.71%, Memory 161.1MB, Power 0.21W
[useRealTimeData] REAL HARDWARE ENERGY: 0.001465 UMatter from Node.js APIs
[useRealTimeData] Real hardware metrics: CPU 23.71%, Memory 161.1MB, Power 0.21W
[EnergySyncController] Added 0.001465 UMatter from real_nodejs_hardware, accumulator size: 5
[EnergySyncController] Processing batch: 5 items, 0.006328 UMatter total
[EnergySyncController] ✅ Batch processed successfully: 5 items
runSecurityScan is not defined
runSecurityScan is not defined
[AuthenticDeviceManager] REAL MacBook Battery: 
Object {realLevel: "100.0%", realCharging: "YES", source: "AUTHENTIC_DEVICE_API"}
[AuthenticDeviceManager] AUTHENTIC MacBook METRICS: 
Object {battery: "100.0% CHARGING", network: "10Mbps WIFI", memory: "42.7MB used of 51.2MB", cores: 8, platform: "MacIntel"}
[AuthenticDeviceManager] Energy calculation: Base: 2.179683, Variance: 0.014504, Total: 2.194188
[AuthenticDeviceManager] UMatter generated: 2.194188 from authentic_device_sync
[AuthenticDeviceManager] Energy accumulated for batching: 2.194188
[AuthenticDeviceManager] Energy calculation: Base: 2.179683, Variance: 0.062838, Total: 2.242521
[AuthenticDeviceManager] Energy accumulated for batching: 2.242521
[NativeBatteryDetector] REAL-TIME UPDATE: 
Object {level: "100.0%", charging: "YES", source: "LIVE_BATTERY_API"}
runSystemDiagnostics is not defined
runSystemDiagnostics is not defined
[SpUnder] Failed to connect to real backend data: 
TypeError {}

[WalletFix] Wallet updated: 342.97355506 UMatter
[SpUnder] Failed to connect to real backend data: 
TypeError {}

[SpUnder] Failed to connect to real backend data: 
TypeError {}

[SpUnder] Failed to connect to real backend data: 
TypeError {}
