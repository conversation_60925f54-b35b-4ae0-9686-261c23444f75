ected token '<', "<!DOCTYPE "... is not valid JSON
connectToRealData @ AdvancedSpUnderDashboard.tsx:209Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/wallet/balance 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
fetchRealBalance @ useRealBalance.ts:27
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
getCurrentMetrics @ authentic-device-manager.ts:182
await in getCurrentMetrics
collectAndBroadcastMetrics @ authentic-device-manager.ts:80
(anonymous) @ authentic-device-manager.ts:74Understand this error
useRealBalance.ts:55 [useRealBalance] Error fetching real balance: Error: Failed to fetch real balance: 502
    at fetchRealBalance (useRealBalance.ts:29:15)
fetchRealBalance @ useRealBalance.ts:55
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
getCurrentMetrics @ authentic-device-manager.ts:182
await in getCurrentMetrics
collectAndBroadcastMetrics @ authentic-device-manager.ts:80
(anonymous) @ authentic-device-manager.ts:74Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/wallet/balance 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
fetchRealBalance @ useRealBalance.ts:27
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
dispatchUMatterEvent @ authentic-device-manager.ts:279
collectAndBroadcastMetrics @ authentic-device-manager.ts:84
await in collectAndBroadcastMetrics
(anonymous) @ authentic-device-manager.ts:74Understand this error
useRealBalance.ts:55 [useRealBalance] Error fetching real balance: Error: Failed to fetch real balance: 502
    at fetchRealBalance (useRealBalance.ts:29:15)
fetchRealBalance @ useRealBalance.ts:55
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
dispatchUMatterEvent @ authentic-device-manager.ts:279
collectAndBroadcastMetrics @ authentic-device-manager.ts:84
await in collectAndBroadcastMetrics
(anonymous) @ authentic-device-manager.ts:74Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/banking/balance 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
connectToRealData @ AdvancedSpUnderDashboard.tsx:140Understand this error
AdvancedSpUnderDashboard.tsx:209 [SpUnder] Failed to connect to real backend data: SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON
connectToRealData @ AdvancedSpUnderDashboard.tsx:209Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/wallet/balance 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
fetchRealBalance @ useRealBalance.ts:27Understand this error
useRealBalance.ts:55 [useRealBalance] Error fetching real balance: Error: Failed to fetch real balance: 502
    at fetchRealBalance (useRealBalance.ts:29:15)
fetchRealBalance @ useRealBalance.ts:55Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/banking/balance 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
updateWalletData @ wallet-fix.ts:54
(anonymous) @ wallet-fix.ts:31Understand this error
performance-controller.ts:38 
            
            
           POST https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/energy/deposit-batch 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
processBatch @ energy-sync-controller.ts:76
checkBatchTriggers @ energy-sync-controller.ts:52
addEnergy @ energy-sync-controller.ts:42
(anonymous) @ real-world-energy-api.ts:685
Promise.then
updateEnergyBalance @ real-world-energy-api.ts:684
recordDeviceConsumption @ real-world-energy-api.ts:314
(anonymous) @ real-world-energy-api.ts:291Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/wallet/balance 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
fetchRealBalance @ useRealBalance.ts:27
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
getCurrentMetrics @ authentic-device-manager.ts:182
await in getCurrentMetrics
collectAndBroadcastMetrics @ authentic-device-manager.ts:80
(anonymous) @ authentic-device-manager.ts:74Understand this error
useRealBalance.ts:55 [useRealBalance] Error fetching real balance: Error: Failed to fetch real balance: 502
    at fetchRealBalance (useRealBalance.ts:29:15)
fetchRealBalance @ useRealBalance.ts:55
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
getCurrentMetrics @ authentic-device-manager.ts:182
await in getCurrentMetrics
collectAndBroadcastMetrics @ authentic-device-manager.ts:80
(anonymous) @ authentic-device-manager.ts:74Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/energy/real-hardware-metrics 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
connectToRealHardware @ real-hardware-connector.ts:33
generateFromRealHardware @ real-hardware-connector.ts:68Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/wallet/balance 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
fetchRealBalance @ useRealBalance.ts:27
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
dispatchUMatterEvent @ authentic-device-manager.ts:279
collectAndBroadcastMetrics @ authentic-device-manager.ts:84
await in collectAndBroadcastMetrics
(anonymous) @ authentic-device-manager.ts:74Understand this error
useRealBalance.ts:55 [useRealBalance] Error fetching real balance: Error: Failed to fetch real balance: 502
    at fetchRealBalance (useRealBalance.ts:29:15)
fetchRealBalance @ useRealBalance.ts:55
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
dispatchUMatterEvent @ authentic-device-manager.ts:279
collectAndBroadcastMetrics @ authentic-device-manager.ts:84
await in collectAndBroadcastMetrics
(anonymous) @ authentic-device-manager.ts:74Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/energy/real-hardware-metrics 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
connectToRealHardware @ real-hardware-connector.ts:33
generateFromRealHardware @ real-hardware-connector.ts:68Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/energy/real-hardware-metrics 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
connectToRealHardware @ real-hardware-connector.ts:33
generateFromRealHardware @ real-hardware-connector.ts:68Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/energy/real-hardware-metrics 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
connectToRealHardware @ real-hardware-connector.ts:33
generateFromRealHardware @ real-hardware-connector.ts:68Understand this error
performance-controller.ts:38 
            
            
           POST https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/energy/deposit-batch 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
processBatch @ energy-sync-controller.ts:76
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
processPendingBatches @ energy-sync-controller.ts:59
(anonymous) @ energy-sync-controller.ts:24Understand this error
performance-controller.ts:38 
            
            
           POST https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/energy/deposit-batch 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
processBatch @ energy-sync-controller.ts:76
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
checkBatchTriggers @ energy-sync-controller.ts:52
addEnergy @ energy-sync-controller.ts:42
(anonymous) @ real-world-energy-api.ts:685
Promise.then
updateEnergyBalance @ real-world-energy-api.ts:684
recordDeviceConsumption @ real-world-energy-api.ts:314
(anonymous) @ real-world-energy-api.ts:291Understand this error
performance-controller.ts:38 
            
            
           POST https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/energy/deposit-batch 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
processBatch @ energy-sync-controller.ts:76
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
checkBatchTriggers @ energy-sync-controller.ts:52
addEnergy @ energy-sync-controller.ts:42
(anonymous) @ real-world-energy-api.ts:685
Promise.then
updateEnergyBalance @ real-world-energy-api.ts:684
recordDeviceConsumption @ real-world-energy-api.ts:314
(anonymous) @ real-world-energy-api.ts:291Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/wallet/balance 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
fetchRealBalance @ useRealBalance.ts:27
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
getCurrentMetrics @ authentic-device-manager.ts:182
await in getCurrentMetrics
collectAndBroadcastMetrics @ authentic-device-manager.ts:80
(anonymous) @ authentic-device-manager.ts:74Understand this error
useRealBalance.ts:55 [useRealBalance] Error fetching real balance: Error: Failed to fetch real balance: 502
    at fetchRealBalance (useRealBalance.ts:29:15)
fetchRealBalance @ useRealBalance.ts:55
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
getCurrentMetrics @ authentic-device-manager.ts:182
await in getCurrentMetrics
collectAndBroadcastMetrics @ authentic-device-manager.ts:80
(anonymous) @ authentic-device-manager.ts:74Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/wallet/balance 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
fetchRealBalance @ useRealBalance.ts:27
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
dispatchUMatterEvent @ authentic-device-manager.ts:279
collectAndBroadcastMetrics @ authentic-device-manager.ts:84
await in collectAndBroadcastMetrics
(anonymous) @ authentic-device-manager.ts:74Understand this error
useRealBalance.ts:55 [useRealBalance] Error fetching real balance: Error: Failed to fetch real balance: 502
    at fetchRealBalance (useRealBalance.ts:29:15)
fetchRealBalance @ useRealBalance.ts:55
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
dispatchUMatterEvent @ authentic-device-manager.ts:279
collectAndBroadcastMetrics @ authentic-device-manager.ts:84
await in collectAndBroadcastMetrics
(anonymous) @ authentic-device-manager.ts:74Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/banking/balance 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
connectToRealData @ AdvancedSpUnderDashboard.tsx:140Understand this error
AdvancedSpUnderDashboard.tsx:209 [SpUnder] Failed to connect to real backend data: SyntaxError: Unexpected token '<', "<!DOCTYPE "... is not valid JSON
connectToRealData @ AdvancedSpUnderDashboard.tsx:209Understand this error
performance-controller.ts:38 
            
            
           POST https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/energy/deposit-batch 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
processBatch @ energy-sync-controller.ts:76
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
checkBatchTriggers @ energy-sync-controller.ts:52
addEnergy @ energy-sync-controller.ts:42
(anonymous) @ iot-manager.ts:498
Promise.then
convertPowerToEnergy @ iot-manager.ts:497
(anonymous) @ iot-manager.ts:486Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/wallet/balance 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
fetchRealBalance @ useRealBalance.ts:27
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
getCurrentMetrics @ authentic-device-manager.ts:182
await in getCurrentMetrics
collectAndBroadcastMetrics @ authentic-device-manager.ts:80
(anonymous) @ authentic-device-manager.ts:74Understand this error
useRealBalance.ts:55 [useRealBalance] Error fetching real balance: Error: Failed to fetch real balance: 502
    at fetchRealBalance (useRealBalance.ts:29:15)
fetchRealBalance @ useRealBalance.ts:55
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
getCurrentMetrics @ authentic-device-manager.ts:182
await in getCurrentMetrics
collectAndBroadcastMetrics @ authentic-device-manager.ts:80
(anonymous) @ authentic-device-manager.ts:74Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/wallet/balance 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
fetchRealBalance @ useRealBalance.ts:27
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
dispatchUMatterEvent @ authentic-device-manager.ts:279
collectAndBroadcastMetrics @ authentic-device-manager.ts:84
await in collectAndBroadcastMetrics
(anonymous) @ authentic-device-manager.ts:74Understand this error
useRealBalance.ts:55 [useRealBalance] Error fetching real balance: Error: Failed to fetch real balance: 502
    at fetchRealBalance (useRealBalance.ts:29:15)
fetchRealBalance @ useRealBalance.ts:55
setTimeout
handleEnergyGenerated @ useRealBalance.ts:98
dispatchUMatterEvent @ authentic-device-manager.ts:279
collectAndBroadcastMetrics @ authentic-device-manager.ts:84
await in collectAndBroadcastMetrics
(anonymous) @ authentic-device-manager.ts:74Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/banking/balance 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
updateWalletData @ wallet-fix.ts:54
(anonymous) @ wallet-fix.ts:31Understand this error
performance-controller.ts:38 
            
            
           POST https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/energy/deposit-batch 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
processBatch @ energy-sync-controller.ts:76
processPendingBatches @ energy-sync-controller.ts:59
(anonymous) @ energy-sync-controller.ts:24Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/energy/real-hardware-metrics 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
connectToRealHardware @ real-hardware-connector.ts:33
generateFromRealHardware @ real-hardware-connector.ts:68Understand this error
performance-controller.ts:38 
            
            
           POST https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/energy/deposit-batch 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
processBatch @ energy-sync-controller.ts:76
(anonymous) @ energy-sync-controller.ts:107
setTimeout
processBatch @ energy-sync-controller.ts:107
await in processBatch
checkBatchTriggers @ energy-sync-controller.ts:52
addEnergy @ energy-sync-controller.ts:42
(anonymous) @ real-world-energy-api.ts:685
Promise.then
updateEnergyBalance @ real-world-energy-api.ts:684
recordDeviceConsumption @ real-world-energy-api.ts:314
(anonymous) @ real-world-energy-api.ts:291Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/energy/real-hardware-metrics 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
connectToRealHardware @ real-hardware-connector.ts:33
generateFromRealHardware @ real-hardware-connector.ts:68Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/energy/real-hardware-metrics 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
connectToRealHardware @ real-hardware-connector.ts:33
generateFromRealHardware @ real-hardware-connector.ts:68Understand this error
performance-controller.ts:38 
            
            
           GET https://c7ac58eb-aec7-4047-93c0-9e84b0630fc9-00-1owyq6l91z2yd.picard.replit.dev/api/energy/real-hardware-metrics 502 (Bad Gateway)
window.fetch @ performance-controller.ts:38
connectToRealHardware @ real-hardware-connector.ts:33
generateFromRealHardware @ real-hardware-connector.ts:68Understand this error