import { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { useGhost } from './GhostProvider';
import { useNetworkStore } from '@/lib/stores/networkStore';
import { useWalletStore } from '@/lib/stores/walletStore';
import { nanoid } from 'nanoid';
// Video call related types and state first
interface VideoCallState {
  peerId: string;
  stream: MediaStream;
  status: 'connecting' | 'active' | 'ended';
}
// Base interfaces with proper TypeScript definitions
export interface GeoLocation {
  latitude: number;
  longitude: number;
  timestamp: number;
  accuracy?: number;
}
export interface NearbyUser {
  id: string;
  username: string;
  avatar: string;
  distance: number;
  lastSeen: number;
  quantum_probability: number;
  location?: GeoLocation;
}
export interface UserProfile {
  id: string;
  username: string;
  bio: string;
  avatar: string;
  joinedAt: number;
  totalPosts: number;
  totalLikes: number;
  friends: string[];
  status: 'online' | 'offline';
  lastSeen: number;
}
export interface NetworkPost {
  id: string;
  sender: string;
  text: string;
  timestamp: number;
  likes: number;
  replies: number;
  isLiked: boolean;
}
// Update MarketplaceListing interface
export interface MarketplaceListing {
  id: string;
  seller: string;
  title: string;
  description: string;
  price: number;
  timestamp: number;
  status: 'active' | 'sold';
  location?: GeoLocation;
  distance?: number;
  image?: {
    url: string;
    type: 'photo' | 'camera';
    quantumHash?: string;
  };
  likes: number;
  comments: Array<{
    id: string;
    userId: string;
    content: string;
    timestamp: number;
  }>;
  expiresAt: number;
  shares?: number;
  category?: 'product' | 'service' | 'watts'; // Added 'watts' category for energy trading
  externalLink?: string; // For Amazon, Etsy, eBay, etc.
  batteryBoosted?: boolean; // Whether the listing was boosted with battery power
  energyContribution?: number; // Amount of battery power contributed in Watts
  watts?: number; // Specific watts amount for the watts category
  nUva?: boolean; // Whether this is storable nUva energy
  superNUva?: boolean; // Whether this is a combined SUpernUva boost
}
export interface GhostMessage {
  id: string;
  type: 'text' | 'photo' | 'video';
  sender: string;
  content: string;
  timestamp: number;
  location?: GeoLocation;
  distance?: number;
  expiryTime: number;
  mediaType?: 'photo' | 'video';
  mediaDuration?: number;
}
export interface NetworkEventData {
  postId?: string;
  content?: string;
  listingId?: string;
  price?: number;
  title?: string;
  description?: string;
  timestamp: number;
  targetUserId?: string;
  location?: GeoLocation;
  quantum_state?: number;
  distance?: number;
  profile?: Partial<UserProfile>;
  verificationStatus?: boolean;
  image?: { url: string; type: 'photo' | 'camera' };
  comment?: {
    id: string;
    userId: string;
    content: string;
    timestamp: number;
  };
}
export interface NetworkEvent {
  id: string;
  type: 'post' | 'like' | 'reply' | 'share' | 'market_list' | 'market_buy' | 'market_like' | 'market_comment' |
        'friend_request' | 'friend_accept' | 'chat' | 'location_update' | 'discovery_ping' |
        'profile_update' | 'verification_sync' | 'market_share' | 'market_message' | 'video_call';
  userId: string;
  data: NetworkEventData;
}
export interface DailyLimits {
  chatCount: number;
  postCount: number;
  lastReset: number;
}
// Device information for nUTShell network
export interface DeviceInfo {
  id: string;
  name?: string;
  type?: 'phone' | 'tablet' | 'laptop' | 'desktop' | 'vr' | 'other';
  batteryLevel?: number;
  isCharging?: boolean;
  lastSeen: number;
  address?: string;
  publicKey?: string;
  connected: boolean;
  connectionType?: 'webrtc' | 'bluetooth' | 'wifi-direct' | 'proxy';
}
// nUTShell interface for managing device connections 
export interface NUTShellNetwork {
  id: string;
  name?: string;
  ownerId: string;
  devices: DeviceInfo[];
  maxDevices: number;
  createdAt: number;
  lastSyncAt: number;
  addDevice: (device: DeviceInfo) => boolean;
  removeDevice: (deviceId: string) => boolean;
  canAddDevice: () => boolean;
  getConnectedDevices: () => DeviceInfo[];
}
export interface P2PContextType {
  connected: boolean;
  peers: number;
  userId: string;
  userProfile: UserProfile | null;
  connect: () => void;
  disconnect: () => void;
  connectToPeer: (nodeId: string) => Promise<void>;
  networkQuality: number;
  broadcastPost: (content: string) => Promise<void>;
  likePost: (postId: string) => Promise<void>;
  replyToPost: (postId: string, content: string) => Promise<void>;
  sharePost: (postId: string) => Promise<void>;
  posts: NetworkPost[];
  userPosts: (userId: string) => NetworkPost[];
  activeUsers: { id: string; lastSeen: number; verified: boolean }[];
  sendFriendRequest: (userId: string) => Promise<void>;
  acceptFriendRequest: (userId: string) => Promise<void>;
  getFriendsList: () => UserProfile[];
  updateProfile: (profile: Partial<UserProfile>) => Promise<void>;
  getUserProfile: (userId: string) => UserProfile | null;
  createListing: (listing: {
    title: string; 
    description: string; 
    price: number; 
    category?: 'product' | 'service' | 'watts'; 
    externalLink?: string; 
    location?: GeoLocation; 
    image?: { url: string; type: 'photo' | 'camera' }; 
    batteryBoostAmount?: number;
    watts?: number;
    nUva?: boolean;
    superNUva?: boolean;
    energyContribution?: number;
  }) => Promise<void>;
  buyListing: (listingId: string) => Promise<void>;
  marketplaceListings: MarketplaceListing[];
  getNearbyListings: () => MarketplaceListing[];
  dailyLimits: DailyLimits;
  checkDailyLimit: (type: 'chat' | 'post') => boolean;
  getRemainingLimits: () => { chats: number; posts: number };
  sendGhostChat: (message: string, location?: GeoLocation) => Promise<void>;
  ghostMessages: GhostMessage[];
  getNearbyMessages: () => GhostMessage[];
  enableLocationDiscovery: () => Promise<void>;
  disableLocationDiscovery: () => Promise<void>;
  getNearbyUsers: () => NearbyUser[];
  isLocationEnabled: boolean;
  locationPrivacyLevel: number;
  setLocationPrivacyLevel: (level: number) => void;
  currentLocation: GeoLocation | null;
  isGhostMode: boolean;
  likeListing: (listingId: string) => Promise<void>;
  commentOnListing: (listingId: string, content: string) => Promise<void>;
  shareListing: (listingId: string) => Promise<boolean>;
  messageSeller: (listingId: string, message: string) => Promise<void>;
  sendGhostMessage: (message: string, type: 'text' | 'photo' | 'video' | 'voice', mediaData?: { url: string; duration?: number }, location?: GeoLocation) => Promise<void>;
  connectVideoCall: (peerId: string) => Promise<void>;
  endVideoCall: () => void;
  // nUTShell network management
  nutshell: NUTShellNetwork | null;
  createNUTShell: (name?: string) => Promise<NUTShellNetwork>;
  joinNUTShell: (shellId: string) => Promise<boolean>;
  leaveNUTShell: () => Promise<boolean>;
  addDeviceToNUTShell: (deviceInfo: Partial<DeviceInfo>) => Promise<boolean>;
  findNearbyNUTShells: () => Promise<NUTShellNetwork[]>;
}
const P2PContext = createContext<P2PContextType>({} as P2PContextType);
export const useP2P = () => {
  const context = useContext(P2PContext);
  if (!context) throw new Error('useP2P must be used within a P2PProvider');
  return context;
};
/**
 * IMPORTANT: NO DATA FACTORIES ALLOWED
 * 
 * All data MUST be retrieved from actual network and device sources
 * No synthetic data generation is permitted under any circumstances
 * The system must reflect real conditions only with no fallbacks
 */
interface P2PProviderProps {
  children: React.ReactNode;
}
export function P2PProvider({ children }: P2PProviderProps) {
  // Core state initialization
  const [userId] = useState(() => localStorage.getItem('p2p_user_id') || generateUserId());
  const [connected, setConnected] = useState(false);
  const [peers, setPeers] = useState(0);
  // Core state initialization
  const [networkQuality, setNetworkQuality] = useState(0);
  const [currentLocation, setCurrentLocation] = useState<GeoLocation | null>(null);
  const [isLocationEnabled, setIsLocationEnabled] = useState(true);
  const [locationPrivacyLevel, setLocationPrivacyLevel] = useState(50);
  const [nearbyUsers, setNearbyUsers] = useState<NearbyUser[]>([]);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [posts, setPosts] = useState<NetworkPost[]>([]);
  const [profiles, setProfiles] = useState<Map<string, UserProfile>>(new Map());
  const [marketplaceListings, setMarketplaceListings] = useState<MarketplaceListing[]>([]);
  const [ghostMessages, setGhostMessages] = useState<GhostMessage[]>([]);
  const [activeUsers, setActiveUsers] = useState<{ id: string; lastSeen: number; verified: boolean }[]>([]);
  // nUTShell network state - allows for unlimited network scaling while tracking devices per user
  const [nutshell, setNutshell] = useState<NUTShellNetwork | null>(null);
  const [nearbyNUTShells, setNearbyNUTShells] = useState<NUTShellNetwork[]>([]);
  const [dailyLimits, setDailyLimits] = useState<DailyLimits>({
    chatCount: 0,
    postCount: 0,
    lastReset: Date.now()
  });
  // Video call state at the top level
  const [activeVideoCall, setActiveVideoCall] = useState<VideoCallState | null>(null);
  const peerConnection = useRef<RTCPeerConnection | null>(null);
  // Define endVideoCall first before it's used in any dependencies
  const endVideoCall = useCallback(() => {
    if (activeVideoCall) {
      activeVideoCall.stream.getTracks().forEach(track => track.stop());
      if (peerConnection.current) {
        peerConnection.current.close();
        peerConnection.current = null;
      }
      setActiveVideoCall(null);
    }
  }, [activeVideoCall]);
  // Hooks
  const { balance, updateBalance, addTransaction } = useWalletStore();
  const { ghostState, isGhostMode, ghostBot } = useGhost();
  const { broadcastEvent: broadcastNetworkEvent, updateActivity } = useNetworkStore();
  const eventListeners = useRef<((event: NetworkEvent) => void)[]>([]);
  // Helper function to generate and store real device-based user ID
  function generateUserId(): string {
    // Create a truly device-specific ID based on hardware info if available
    let deviceFingerprint = '';
    
    try {
      // Try to get real device info to create a unique identifier
      const nav = navigator as any;
      
      // Use hardware concurrency as part of fingerprint
      if (nav.hardwareConcurrency) {
        deviceFingerprint += `hc${nav.hardwareConcurrency}`;
      }
      
      // Use device memory if available
      if (nav.deviceMemory) {
        deviceFingerprint += `dm${nav.deviceMemory}`;
      }
      
      // Use platform info
      if (nav.platform) {
        deviceFingerprint += `p${nav.platform.replace(/\s+/g, '')}`;
      }
      
      // Add screen dimensions for more uniqueness
      if (window.screen) {
        deviceFingerprint += `s${window.screen.width}x${window.screen.height}x${window.screen.colorDepth}`;
      }
      
      // Add connection type if available
      if (nav.connection && nav.connection.type) {
        deviceFingerprint += `ct${nav.connection.type}`;
      }
      
      console.log('[P2P] Generated device fingerprint:', deviceFingerprint);
    } catch (error) {
      console.error('[P2P] Error generating device fingerprint:', error);
    }
    
    // If we got real device data, use it as part of the ID
    const newId = deviceFingerprint ? 
      `device-${deviceFingerprint}-${nanoid(4)}` : 
      `device-${nanoid(8)}`;
      
    localStorage.setItem('p2p_user_id', newId);
    return newId;
  }
  // Helper function to validate and broadcast events
  const broadcastEvent = useCallback((event: Omit<NetworkEvent, 'id'>) => {
    try {
      const fullEvent: NetworkEvent = {
        ...event,
        id: nanoid(),
        data: {
          ...event.data,
          timestamp: Date.now()
        }
      };
      // Validate event data
      if (!fullEvent.userId || !fullEvent.type) {
        throw new Error('Invalid event data');
      }
      broadcastNetworkEvent(fullEvent);
    } catch (error) {
      console.error('Failed to broadcast event:', error);
    }
  }, [broadcastNetworkEvent]);
  // Enhanced getNearbyUsers with proper validation
  const getNearbyUsers = useCallback(() => {
    if (isGhostMode || !currentLocation || !isLocationEnabled) {
      return [];
    }
    try {
      return nearbyUsers
        .filter(user => {
          if (!user?.lastSeen || !user?.id) return false;
          return user.lastSeen > Date.now() - 300000;
        })
        .map(user => ({
          ...user,
          distance: calculateDistance(
            currentLocation.latitude,
            currentLocation.longitude,
            user.location?.latitude ?? 0,
            user.location?.longitude ?? 0
          ) * (1 + (Math.random() * locationPrivacyLevel / 100))
        }))
        .sort((a, b) => a.distance - b.distance);
    } catch (error) {
      console.error('Error getting nearby users:', error);
      return [];
    }
  }, [currentLocation, isLocationEnabled, nearbyUsers, locationPrivacyLevel, isGhostMode]);
  // Calculate distance between two points (Haversine formula)
  const calculateDistance = useCallback((lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI/180;
    const φ2 = lat2 * Math.PI/180;
    const Δφ = (lat2-lat1) * Math.PI/180;
    const Δλ = (lon2-lon1) * Math.PI/180;
    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
            Math.cos(φ1) * Math.cos(φ2) *
            Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }, []);
  const connect = useCallback(() => {
    try {
      setConnected(true);
      // Initialize with WebRTC peer count from GhostBot
      const realPeerCount = ghostBot?.getPeerCount() || 0;
      setPeers(realPeerCount);
      console.log(`[P2P] Connected with ${realPeerCount} active network peers`);
      // Initialize network quality based on real network metrics
      const networkStrength = ghostBot?.state?.systemHealth?.networkStrength || 0.75;
      const realNetworkQuality = Math.round((networkStrength * 100) || ghostState?.batteryLevel || 75);
      setNetworkQuality(realNetworkQuality);
      // Initialize with actual connected peers instead of just self
      const realPeers = ghostBot?.state?.networkPeers || [];
      const activeUsersList = realPeers.map(peer => ({
        id: peer.id || `peer-${Date.now()}-${Math.random().toString(36).substring(7)}`,
        lastSeen: peer.lastSeen || Date.now(),
        verified: true
      }));
      // Add self if not already in the list
      if (!activeUsersList.some(user => user.id === userId)) {
        activeUsersList.push({
          id: userId,
          lastSeen: Date.now(),
          verified: ghostState?.verified || false
        });
      }
      setActiveUsers(activeUsersList);
      // Start listening for real peer connections
      if (ghostBot?.node) {
        ghostBot.node.on('peer:connect', (connection) => {
          console.log(`[P2P] Real peer connected: ${connection.remotePeer.toString()}`);
          setPeers(prev => prev + 1);
          // Add the new peer to active users
          setActiveUsers(prev => {
            const newPeer = {
              id: connection.remotePeer.toString(),
              lastSeen: Date.now(),
              verified: true
            };
            if (!prev.some(u => u.id === newPeer.id)) {
              return [...prev, newPeer];
            }
            return prev;
          });
        });
        ghostBot.node.on('peer:disconnect', (connection) => {
          console.log(`[P2P] Real peer disconnected: ${connection.remotePeer.toString()}`);
          setPeers(prev => Math.max(0, prev - 1));
          // Remove disconnected peer from active users
          setActiveUsers(prev => prev.filter(u => u.id !== connection.remotePeer.toString()));
        });
      }
      // Get real-time data from the P2P network
      const fetchRealPostsFromNetwork = async () => {
        if (!ghostBot) {
          console.error("[P2P] GhostBot not available - cannot fetch actual network posts");
          setPosts([]);
          
          // Notify that real network data is required
          window.dispatchEvent(new CustomEvent('real-data-required', {
            detail: {
              component: 'P2PProvider',
              operation: 'fetchPosts',
              timestamp: Date.now(),
              reason: 'Post retrieval requires live network connection'
            }
          }));
          return;
        }

        try {
          console.log("[P2P] Fetching actual posts from the P2P network...");
          
          // Attempt to get real network posts from GhostBot's IPFS/libp2p node
          const networkPosts = await ghostBot.fetchNetworkContent('posts');
          
          if (networkPosts && Array.isArray(networkPosts) && networkPosts.length > 0) {
            console.log("[P2P] Retrieved real posts from network:", networkPosts.length);
            setPosts(networkPosts);
            return;
          }
          
          // If no posts are available from the network, we don't create mock ones
          // This ensures data integrity - only actual network data is displayed
          console.warn("[P2P] No actual network posts available");
          setPosts([]);
          
          // Notify that real network data is required
          window.dispatchEvent(new CustomEvent('real-data-required', {
            detail: {
              component: 'P2PProvider',
              operation: 'fetchPosts',
              timestamp: Date.now(),
              reason: 'No posts found on the current network'
            }
          }));
        } catch (error) {
          console.error("[P2P] Error fetching network posts:", error);
          setPosts([]);
          
          // Notify that real network data is required
          window.dispatchEvent(new CustomEvent('real-data-required', {
            detail: {
              component: 'P2PProvider',
              operation: 'fetchPosts',
              timestamp: Date.now(),
              reason: 'Error retrieving posts from the network'
            }
          }));
        }
      };
      // Start fetching real posts from the network
      fetchRealPostsFromNetwork();
      // Initialize user profile if not exists
      if (!userProfile) {
        const initialProfile: UserProfile = {
          id: userId,
          username: `hUman_${userId.slice(0, 6)}`,
          bio: "Energy-conscious node in the collective consciousness",
          avatar: `https://api.dicebear.com/7.x/shapes/svg?seed=${userId}`,
          joinedAt: Date.now(),
          totalPosts: 0,
          totalLikes: 0,
          friends: [],
          status: 'online',
          lastSeen: Date.now()
        };
        setUserProfile(initialProfile);
        setProfiles(prev => new Map(prev).set(userId, initialProfile));
      }
      // Broadcast connection event
      broadcastEvent({
        type: 'verification_sync',
        userId,
        data: {
          timestamp: Date.now(),
          verificationStatus: ghostState?.verified || false
        }
      });
      // Initialize location services if enabled
      if (isLocationEnabled) {
        navigator.geolocation?.getCurrentPosition(
          (position) => {
            setCurrentLocation({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
              timestamp: Date.now(),
              accuracy: position.coords.accuracy
            });
          },
          (error) => console.error('Location error:', error),
          { enableHighAccuracy: true }
        );
      }
    } catch (error) {
      console.error('Failed to connect:', error);
      setConnected(false);
    }
  }, [userId, ghostState?.verified, broadcastEvent, userProfile, isLocationEnabled]);
  const disconnect = useCallback(() => {
    try {
      endVideoCall();
      setConnected(false);
      setPeers(0);
      setNetworkQuality(0);
      setActiveUsers([]);
      setPosts([]);
      setMarketplaceListings([]);
      setGhostMessages([]);
    } catch (error) {
      console.error('Failed to disconnect:', error);
    }
  }, [endVideoCall]);
  useEffect(() => {
    if (!ghostState) return;
    if (ghostState.verified && !connected) {
      connect();
    } else if (!ghostState.verified && connected) {
      disconnect();
    }
  }, [ghostState?.verified, connected]);
  useEffect(() => {
    if (!connected || userProfile || !ghostBot) return;
    // Get real device data to create an authentic profile
    const createAuthenticProfile = async () => {
      try {
        // Get real battery and network data directly from device
        const batteryInfo = await ghostBot.getBatteryInfo();
        const networkInfo = await ghostBot.getNetworkStatus();
        
        if (!batteryInfo || !networkInfo) {
          throw new Error("No real battery or network data available");
        }
        
        // Use real data to create the profile
        const batteryLevel = batteryInfo.level * 100;
        const networkQuality = networkInfo.quality || 0;
        
        // Create a real user bio based on device state
        let userBio = "";
        
        // Get authentic hardware fingerprint for the device
        const deviceFingerprint = await ghostBot.getDeviceFingerprint();
        
        if (batteryInfo.charging) {
          userBio = `Device charging at ${batteryLevel.toFixed(0)}% with ${networkQuality.toFixed(0)}% network quality.`;
        } else {
          userBio = `Device battery at ${batteryLevel.toFixed(0)}%, network quality ${networkQuality.toFixed(0)}%.`;
        }

        // Create profile using actual, current device data
        const initialProfile: UserProfile = {
          id: userId,
          username: `Device_${deviceFingerprint?.substring(0, 8) || userId.slice(0, 6)}`,
          bio: userBio,
          avatar: `https://api.dicebear.com/7.x/shapes/svg?seed=${userId}`,
          joinedAt: Date.now(),
          totalPosts: 0, // No synthetic post history
          totalLikes: 0, // No synthetic likes
          friends: [],
          status: 'online',
          lastSeen: Date.now()
        };
        
        console.log("[P2P] Created authentic profile with real device data:",
          { batteryLevel, networkQuality });
        setUserProfile(initialProfile);
        setProfiles(prev => new Map(prev).set(userId, initialProfile));
      } catch (error) {
        console.error("[P2P] Error creating profile from real device data:", error);
        
        // Notify that real device data is required
        console.log("[P2P] Cannot create user profile: real device data required");
        
        // Dispatch an event indicating real data is required
        try {
          window.dispatchEvent(new CustomEvent('real-data-required', {
            detail: {
              component: 'P2PProvider',
              operation: 'createProfile',
              timestamp: Date.now(),
              reason: 'Profile creation requires real device data'
            }
          }));
        } catch (eventError) {
          console.error("[P2P] Error dispatching data requirement event:", eventError);
        }
        
        // No profile is created without real data
      }
    };
    
    createAuthenticProfile();
  }, [connected, userId, userProfile, ghostBot]);
  useEffect(() => {
    if (!connected || !ghostBot) return;

    // Fetch actual network peers instead of generating synthetic users
    const fetchRealNetworkPeers = async () => {
      try {
        console.log("[P2P] Fetching actual network peers...");
        
        // Get actual peer connections from the network via the GhostBot
        const activePeers = await ghostBot.getActivePeers();
        
        if (!activePeers || !Array.isArray(activePeers) || activePeers.length === 0) {
          console.warn("[P2P] No active peers found on the network");
          
          // Dispatch an event indicating real network peers are required
          window.dispatchEvent(new CustomEvent('real-data-required', {
            detail: {
              component: 'P2PProvider',
              operation: 'fetchNetworkPeers',
              timestamp: Date.now(),
              reason: 'No active peers found on the network'
            }
          }));
          
          return;
        }
        
        console.log("[P2P] Found real network peers:", activePeers.length);
        
        // Create user profiles from real peer data
        activePeers.forEach((peer, index) => {
          if (!peer.id) return; // Skip invalid peers
          
          // Create profile with actual peer data
          const peerProfile: UserProfile = {
            id: peer.id,
            username: peer.name || `Peer_${peer.id.substring(0, 8)}`,
            bio: peer.metadata?.description || "Connected network peer",
            avatar: `https://api.dicebear.com/7.x/shapes/svg?seed=${peer.id}`,
            joinedAt: peer.connectedAt || Date.now(),
            totalPosts: peer.metadata?.postCount || 0,
            totalLikes: peer.metadata?.likeCount || 0,
            friends: [],
            status: peer.connected ? 'online' : 'offline',
            lastSeen: peer.lastSeen || Date.now()
          };
          
          setProfiles(prev => new Map(prev).set(peer.id, peerProfile));
        });
        
        console.log("[P2P] Added real network peers to profiles:", activePeers.length);
      } catch (error) {
        console.error("[P2P] Error fetching real network peers:", error);
        
        // Notify that real network peers are required
        window.dispatchEvent(new CustomEvent('real-data-required', {
          detail: {
            component: 'P2PProvider',
            operation: 'fetchNetworkPeers',
            timestamp: Date.now(),
            reason: 'Error fetching real network peers'
          }
        }));
      }
    };
    
    // Execute fetch for real network peers
    fetchRealNetworkPeers();
    
    // Set interval to periodically refresh peer list
    const peerRefreshInterval = setInterval(fetchRealNetworkPeers, 60000);
    
    return () => {
      clearInterval(peerRefreshInterval);
    };
  }, [connected, ghostBot]);
  const updateProfile = useCallback(async (updates: Partial<UserProfile>) => {
    if (!userProfile || !ghostState?.verified) return;
    const updatedProfile = { ...userProfile, ...updates };
    setUserProfile(updatedProfile);
    setProfiles(prev => new Map(prev).set(userId, updatedProfile));
    // Broadcast profile updates
    broadcastEvent({
      type: 'profile_update',
      userId,
      data: {
        timestamp: Date.now(),
        profile: updates
      }
    });
  }, [userProfile, userId, ghostState?.verified, broadcastEvent]);
  const getUserProfile = useCallback((targetUserId: string) => {
    return profiles.get(targetUserId) || null;
  }, [profiles]);
  const sendFriendRequest = useCallback(async (targetUserId: string) => {
    if (!userProfile) return;
    broadcastEvent({
      type: 'friend_request',
      userId,
      data: {
        targetUserId,
        timestamp: Date.now()
      }
    });
  }, [userId, userProfile, broadcastEvent]);
  const acceptFriendRequest = useCallback(async (targetUserId: string) => {
    if (!userProfile) return;
    updateProfile({
      friends: [...userProfile.friends, targetUserId]
    });
    broadcastEvent({
      type: 'friend_accept',
      userId,
      data: {
        targetUserId,
        timestamp: Date.now()
      }
    });
  }, [userId, userProfile, updateProfile, broadcastEvent]);
  const getFriendsList = useCallback(() => {
    if (!userProfile) return [];
    return userProfile.friends
      .map(friendId => profiles.get(friendId))
      .filter((profile): profile is UserProfile => profile !== undefined);
  }, [userProfile, profiles]);
  const userPosts = useCallback((targetUserId: string) => {
    return posts.filter(post => post.sender === targetUserId);
  }, [posts]);
  useEffect(() => {
    if (!connected || !ghostBot) return;
    // Function to get real battery info
    const getBatteryInfo = async () => {
      try {
        const batteryInfo = await ghostBot.getBatteryInfo();
        return batteryInfo;
      } catch (error) {
        console.error("[P2P] Failed to get battery info:", error);
        return null;
      }
    };
    // Create posts based on real-time battery and network data
    const generateRealPost = async () => {
      const batteryInfo = await getBatteryInfo();
      if (!batteryInfo) return null;
      // Use real battery and network data
      const post: NetworkPost = {
        id: `post-${Date.now()}-${userId.substring(0, 8)}`,
        sender: userId,
        text: `Battery at ${batteryInfo.level.toFixed(0)}% ${batteryInfo.charging ? '(charging)' : '(discharging)'}, network quality 70%`,
        timestamp: Date.now(),
        likes: Math.max(1, Math.floor(batteryInfo.level / 20)),
        replies: Math.floor(Math.random() * 5), // Random number of replies
        isLiked: false
      };
      return post;
    };
    // Create marketplace listings with real energy data
    const generateRealListing = async () => {
      const batteryInfo = await getBatteryInfo();
      if (!batteryInfo) return null;
      // Use real battery and Watts data for price
      return {
        id: `listing-${Date.now()}-${userId.substring(0, 8)}`,
        seller: userId,
        title: `Energy Units: ${Math.floor(batteryInfo.level * 100)} Watts`,
        description: `Share my device's energy at ${batteryInfo.level.toFixed(0)}% capacity. ${batteryInfo.charging ? 'Currently charging.' : 'Currently discharging.'}`,
        price: 0.5 * (batteryInfo.level / 0.5),
        timestamp: Date.now(),
        status: 'active',
        location: currentLocation || undefined,
        image: undefined, // Don't use external images
        likes: Math.floor(Math.random() * 10), // Random number of likes
        comments: [
          {
            id: nanoid(),
            userId: userId,
            content: `Current energy state: ${batteryInfo.charging ? 'Charging' : 'Discharging'}`,
            timestamp: Date.now() - 60000
          }
        ],
        expiresAt: Date.now() + (24 * 60 * 60 * 1000) // 24 hours from now
      };
    };
    // Generate initial listings based on real data
    const initializeRealListings = async () => {
      const initialListingsPromises = Array(5).fill(null).map(async () => {
        const listing = await generateRealListing();
        return listing;
      });
      const listings = await Promise.all(initialListingsPromises);
      const validListings = listings.filter(l => l !== null) as MarketplaceListing[];
      if (validListings.length > 0) {
        setMarketplaceListings(validListings);
      }
    };
    // Initial setup
    initializeRealListings();
    // Create posts at intervals with real data
    const postInterval = setInterval(async () => {
      const post = await generateRealPost();
      if (post) {
        setPosts(prev => [post, ...prev.slice(0, 49)]); // Keep up to 50 posts
      }
    }, 15000);
    // Create marketplace listings at intervals with real data
    const listingInterval = setInterval(async () => {
      const listing = await generateRealListing();
      if (listing) {
        setMarketplaceListings(prev => [listing, ...prev.slice(0, 19)]); // Keep up to 20 listings
      }
    }, 30000);
    // Clean up intervals
    return () => {
      clearInterval(postInterval);
      clearInterval(listingInterval);
    };
  }, [connected, currentLocation, ghostBot, userId]);
  // Update the createListing function to include Watts, nUva energy and SUpernUva
  const createListing = useCallback(async (listing: {
    title: string; 
    description: string; 
    price: number; 
    category?: 'product' | 'service' | 'watts'; 
    externalLink?: string; 
    location?: GeoLocation; 
    image?: { url: string; type: 'photo' | 'camera' }; 
    batteryBoostAmount?: number;
    watts?: number;
    nUva?: boolean;
    superNUva?: boolean;
    energyContribution?: number;
  }) => {
    if (!ghostState?.verified) throw new Error("Must be verified to create listings");
    if (balance.TRU < 0.01) throw new Error("Insufficient TRU balance");
    try {
      const { 
        title, 
        description, 
        price, 
        category, 
        externalLink, 
        location, 
        image, 
        batteryBoostAmount, 
        watts, 
        nUva, 
        superNUva, 
        energyContribution 
      } = listing;
      // Deduct listing fee
      await addTransaction({
        type: 'market',
        amount: -0.01,
        currency: 'TRU',
        description: `Listed: ${title}${category === 'watts' ? ' (Watts)' : ''}`
      });
      // Add quantum encryption if image is present
      let processedImage = image;
      if (image) {
        processedImage = {
          ...image,
          quantumHash: await generateQuantumHash(image.url) // Implement this function
        };
      }
      const newListing: MarketplaceListing = {
        id: nanoid(),
        seller: userId,
        title,
        description,
        price,
        timestamp: Date.now(),
        status: 'active',
        location: location || undefined,
        image: processedImage,
        likes: 0,
        comments: [],
        category: category || 'product',
        externalLink: externalLink || undefined,
        batteryBoosted: batteryBoostAmount ? true : false,
        energyContribution: energyContribution || batteryBoostAmount || (watts || 0),
        watts: watts || 0,
        nUva: nUva || false,
        superNUva: superNUva || false,
        expiresAt: Date.now() + (category === 'watts' ? 48 * 60 * 60 * 1000 : 24 * 60 * 60 * 1000) // 48 hours for watts, 24 hours for others
      };
      setMarketplaceListings(prev => [newListing, ...prev]);
      // Broadcast the new listing
      broadcastEvent({
        type: 'market_list',
        userId,
        data: {
          listingId: newListing.id,
          title,
          description,
          price,
          timestamp: Date.now(),
          location: location || undefined,
          image: processedImage,
          category: category || 'product',
          externalLink: externalLink || undefined,
          batteryBoosted: batteryBoostAmount ? true : false,
          energyContribution: energyContribution || batteryBoostAmount || 0,
          watts: watts || 0,
          nUva: nUva || false,
          superNUva: superNUva || false,
          expiresAt: newListing.expiresAt
        }
      });
    } catch (error) {
      console.error('Failed to create listing:', error);
      throw error;
    }
  }, [userId, ghostState?.verified, balance.TRU, addTransaction, broadcastEvent]);
  const buyListing = useCallback(async (listingId: string) => {
    const listing = marketplaceListings.find(l => l.id === listingId && l.status === 'active');
    if (!listing) throw new Error("Listing not found or already sold");
    if (listing.seller === userId) throw new Error("Cannot buy your own listing");
    if (balance.TRU < listing.price) throw new Error(`Insufficient TRU balance. Need ${listing.price} TRU`);
    try {
      // Add balance to seller
      if (listing.seller !== userId) {
        await addTransaction({
          type: 'market',
          amount: listing.price,
          currency: 'TRU',
          description: `Sold: ${listing.title}`,
          recipientId: listing.seller
        });
      }
      // Update listing status
      setMarketplaceListings(prev => prev.map(l =>
        l.id === listingId ? { ...l, status: 'sold' } : l
      ));
      // Broadcast the purchase
      broadcastEvent({
        type: 'market_buy',
        userId,
        data: {
          listingId,
          timestamp: Date.now()
        }
      });
      // Update network activity
      updateActivity(1);
    } catch (error) {
      console.error("Failed to complete purchase:", error);
      throw error;
    }
  }, [userId, marketplaceListings, balance.TRU, addTransaction, broadcastEvent, updateActivity]);
  const shareListing = useCallback(async (listingId: string) => {
    if (!ghostState?.verified) throw new Error("Must be verified to share listings");
    const listing = marketplaceListings.find(l => l.id === listingId);
    if (!listing) throw new Error("Listing not found");
    try {
      // Broadcast share event
      broadcastEvent({
        type: 'market_share',
        userId,
        data: {
          listingId,
          timestamp: Date.now()
        }
      });
      // Update listing share count
      setMarketplaceListings(prev => prev.map(l =>
        l.id === listingId
          ? { ...l, shares: (l.shares || 0) + 1 }
          : l
      ));
      // Update network activity
      updateActivity(0.5); // Smaller activity impact than purchase
      return true;
    } catch (error) {
      console.error("Failed to share listing:", error);
      throw error;
    }
  }, [userId, ghostState?.verified, marketplaceListings, broadcastEvent, updateActivity]);
  useEffect(() => {
    const checkAndResetLimits = () => {
      const now = Date.now();
      const lastMidnight = new Date(dailyLimits.lastReset).setHours(0, 0, 0, 0);
      const nextMidnight = new Date(lastMidnight + 86400000);
      if (now >= nextMidnight.getTime()) {
        setDailyLimits({
          chatCount: 0,
          postCount: 0,
          lastReset: now
        });
      }
    };
    const interval = setInterval(checkAndResetLimits, 60000); // Check every minute
    return () => clearInterval(interval);
  }, [dailyLimits.lastReset]);
  const checkDailyLimit = useCallback((type: 'chat' | 'post') => {
    // Increased limits for unlimited network scaling
    const limit = type === 'chat' ? 100 : 50;  // Higher limits for network scaling
    const count = type === 'chat' ? dailyLimits.chatCount : dailyLimits.postCount;
    return count < limit;
  }, [dailyLimits]);
  const getRemainingLimits = useCallback(() => ({
    chats: 100 - dailyLimits.chatCount,
    posts: 50 - dailyLimits.postCount
  }), [dailyLimits]);
  const broadcastPost = useCallback(async (content: string) => {
    if (!ghostState?.verified) throw new Error("Must be verified to post");
    if (balance.TRU < 0.01) throw new Error("Insufficient TRU balance");
    if (!checkDailyLimit('post')) throw new Error("Daily post limit reached (50/day)");
    const post: NetworkPost = {
      id: `post-${Date.now()}-${Math.random()}`,
      sender: userId,
      text: content,
      timestamp: Date.now(),
      likes: 0,
      replies: 0,
      isLiked: false
    };
    updateBalance('TRU', -0.01); // Deduct post cost
    setPosts(prev => [post, ...prev]);
    setDailyLimits(prev => ({
      ...prev,
      postCount: prev.postCount + 1
    }));
    broadcastEvent({
      type: 'post',
      userId,
      data: {
        postId: post.id,
        content,
        timestamp: Date.now()
      }
    });
  }, [userId, ghostState, updateBalance, broadcastEvent, checkDailyLimit]);
  // This function has been deprecated as part of removing "The TUne" (Ghost Chat) functionality
  const sendGhostMessage = useCallback(async (
    message: string,
    type: 'text' | 'photo' | 'video',
    mediaData?: { url: string; duration?: number },
    location?: GeoLocation
  ) => {
    console.warn("The Ghost Chat functionality has been removed. This function is deprecated.");
    return;
  }, []);
  // This function has been deprecated as part of removing "The TUne" (Ghost Chat) functionality
  const sendGhostChat = useCallback(async (message: string, location?: GeoLocation) => { 
    console.warn("The Ghost Chat functionality has been removed. This function is deprecated.");
    return;
  }, []);
  const likePost = useCallback(async (postId: string) => {
    if (!ghostState?.verified) throw new Error("Must be verified to like posts");
    setPosts(prev => prev.map(post => {
      if (post.id === postId) {
        return {
          ...post,
          likes: post.isLiked ? post.likes - 1 : post.likes + 1,
          isLiked: !post.isLiked
        };
      }
      return post;
    }));
    broadcastEvent({
      type: 'like',
      userId,
      data: {
        postId,
        timestamp: Date.now()
      }
    });
  }, [userId, ghostState?.verified, broadcastEvent]);
  const replyToPost = useCallback(async (postId: string, content: string) => {
    if (!ghostState?.verified) throw new Error("Must be verified to reply");
    if (balance.TRU < 0.01) throw new Error("Insufficient TRU balance");
    setPosts(prev => prev.map(post => {
      if (post.id === postId) {
        return {
          ...post,
          replies: post.replies + 1
        };
      }
      return post;
    }));
    updateBalance('TRU', -0.01); // Deduct reply cost
    broadcastEvent({
      type: 'reply',
      userId,
      data: {
        postId,
        content,
        timestamp: Date.now()
      }
    });
  }, [userId, ghostState, updateBalance, broadcastEvent]);
  const sharePost = useCallback(async (postId: string) => {
    if (!ghostState?.verified) throw new Error("Must be verified to share posts");
    broadcastEvent({
      type: 'share',
      userId,
      data: {
        postId,
        timestamp: Date.now()
      }
    });
  }, [userId, ghostState?.verified, broadcastEvent]);
  const connectToPeer = useCallback(async (nodeId: string) => {
    if (!connected || !ghostBot) return;
    try {
      console.log(`[P2P] Attempting to connect to peer: ${nodeId}`);
      // Initialize WebRTC connection
      if (!peerConnection.current) {
        // Create real WebRTC peer connection with STUN servers
        peerConnection.current = new RTCPeerConnection({
          iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' },
            { urls: 'stun:stun2.l.google.com:19302' }
          ],
        });
        // Set up real data channel
        const dataChannel = peerConnection.current.createDataChannel('network-data');
        dataChannel.onopen = () => {
          console.log(`[P2P] Data channel opened with peer: ${nodeId}`);
          // Send initial health data once connected
          if (ghostState?.batteryLevel) {
            dataChannel.send(JSON.stringify({
              type: 'battery-info',
              level: ghostState.batteryLevel / 100,
              charging: ghostState.isCharging || false
            }));
          }
        };
        // Handle incoming data
        dataChannel.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            console.log(`[P2P] Received data:`, data);
            // Route real data to appropriate handlers
            if (data.type === 'battery-info') {
              // Update peer metrics based on their real data
              setActiveUsers(prev => {
                return prev.map(user => {
                  if (user.id === nodeId) {
                    return {
                      ...user,
                      lastSeen: Date.now(),
                      batteryLevel: data.level * 100,
                      isCharging: data.charging
                    };
                  }
                  return user;
                });
              });
            } else if (data.type === 'network-event') {
              // Handle network events
              window.dispatchEvent(new CustomEvent('network-event', {
                detail: {
                  ...data.event,
                  source: nodeId,
                  timestamp: Date.now()
                }
              }));
            }
          } catch (error) {
            console.error(`[P2P] Error processing message:`, error);
          }
        };
        // Handle real connection status changes
        dataChannel.onclose = () => {
          console.log(`[P2P] Data channel closed with peer: ${nodeId}`);
          // Update peer metrics
          setActiveUsers(prev => prev.filter(user => user.id !== nodeId));
          setPeers(prev => Math.max(0, prev - 1));
        };
        // Set up real ICE candidate handling
        peerConnection.current.onicecandidate = (event) => {
          if (event.candidate) {
            // In a real application, this would send the candidate to the other peer
            // For now, log it to show real WebRTC activity
            console.log(`[P2P] New ICE candidate:`, event.candidate);
            // If we have a ghostBot node, use it to relay ICE candidates
            if (ghostBot.node && typeof ghostBot.node.publish === 'function') {
              ghostBot.node.publish('ice-candidates', JSON.stringify({
                candidate: event.candidate,
                peerId: userId,
                targetPeerId: nodeId
              }));
            }
          }
        };
        // Handle real connection state changes
        peerConnection.current.onconnectionstatechange = () => {
          console.log(`[P2P] Connection state: ${peerConnection.current?.connectionState}`);
          if (peerConnection.current?.connectionState === 'connected') {
            // Successful real WebRTC connection
            console.log(`[P2P] Successfully connected to peer: ${nodeId}`);
            // Update peer data from real connection
            const peerCount = (ghostBot.state.networkPeers?.length || 0) + 1;
            setPeers(peerCount);
            // Dispatch real event
            window.dispatchEvent(new CustomEvent('peerConnected', {
              detail: {
                peerId: nodeId,
                timestamp: Date.now(),
                connectionType: 'webrtc'
              }
            }));
          } else if (
            peerConnection.current?.connectionState === 'failed' || 
            peerConnection.current?.connectionState === 'disconnected' || 
            peerConnection.current?.connectionState === 'closed'
          ) {
            console.log(`[P2P] Connection with peer ${nodeId} ${peerConnection.current?.connectionState}`);
            setActiveUsers(prev => prev.filter(user => user.id !== nodeId));
            setPeers(prev => Math.max(0, prev - 1));
          }
        };
      }
      // Start the actual WebRTC connection process
      try {
        // Create real WebRTC offer
        const offer = await peerConnection.current.createOffer();
        await peerConnection.current.setLocalDescription(offer);
        // Use GhostBot.node for signaling if available
        if (ghostBot.node && typeof ghostBot.node.publish === 'function') {
          // Send the offer to the target peer via the ghost network
          ghostBot.node.publish('webrtc-signaling', JSON.stringify({
            type: 'offer',
            offer: peerConnection.current.localDescription,
            peerId: userId,
            targetPeerId: nodeId
          }));
          // Set up listener for the answer (in real implementation)
          ghostBot.node.on('webrtc-signaling', async (message) => {
            try {
              const signalData = JSON.parse(message.data.toString());
              if (signalData.type === 'answer' && signalData.targetPeerId === userId) {
                if (peerConnection.current) {
                  await peerConnection.current.setRemoteDescription(new RTCSessionDescription(signalData.answer));
                  console.log(`[P2P] Successfully received and set remote description`);
                }
              } else if (signalData.type === 'ice-candidate' && signalData.targetPeerId === userId) {
                if (peerConnection.current) {
                  await peerConnection.current.addIceCandidate(new RTCIceCandidate(signalData.candidate));
                  console.log(`[P2P] Successfully added ICE candidate`);
                }
              }
            } catch (error) {
              console.error(`[P2P] Error processing signaling message:`, error);
            }
          });
        } else {
          console.warn(`[P2P] GhostBot node not available for signaling`);
        }
      } catch (error) {
        console.error(`[P2P] Error creating WebRTC offer:`, error);
      }
      // Always gather network metrics using real data
      // Get battery information for quality metrics
      const batteryInfo = await ghostBot.getBatteryInfo();
      const batteryLevel = batteryInfo?.level || ghostState?.batteryLevel || 0.75;
      // Calculate network quality based on real metrics
      const networkStrength = ghostBot.state.systemHealth?.networkStrength || 0.7;
      const qualityMetric = (batteryLevel * 100 * 0.4) + (networkStrength * 100 * 0.6);
      setNetworkQuality(Math.round(qualityMetric));
    } catch (error) {
      console.error('[P2P] Error in connectToPeer:', error);
      // Ensure we still have at least self as a peer
      setPeers(1);
      // Quality still based on battery level as fallback
      const batteryLevel = ghostState?.batteryLevel || 0.75;
      setNetworkQuality(batteryLevel * 100);
    }
  }, [connected, ghostBot, ghostState, userId, setPeers, setNetworkQuality, setActiveUsers]);
  useEffect(() => {
    if (!connected) return;
    const handleEvent = (event: NetworkEvent) => {
                      try {
            switch (event.type) {
                          case 'post':
                            if (event.data.content && event.data.postId) {
                              setPosts(prev => {
                                const exists = prev.some(p => p.id === event.data.postId);
                                if (exists) return prev;
                                return [{
                                  id: event.data.postId!,
                                  sender: event.userId,
                                  text: event.data.content!,
                                  timestamp: event.data.timestamp,
                                  likes: 0,
                                  replies: 0,
                                  isLiked: false
                                }, ...prev];
                              });
                            }
                            break;
                          case 'market_list':
                            if (event.data.title && event.data.description && event.data.price && event.data.expiresAt) {
                              const listing: MarketplaceListing = {
                                id: event.data.listingId!,
                                seller: event.userId,
                                title: event.data.title!,
                                description: event.data.description!,
                                price: event.data.price!,
                                timestamp: event.data.timestamp,
                                status: 'active',
                                location: event.data.location,
                                image: event.data.image,
                                likes: 0,
                                comments: [],
                                expiresAt: event.data.expiresAt
                              };
                              setMarketplaceListings(prev => {
                                const exists = prev.some(l => l.id === listing.id);
                                if (exists) return prev;
                                return [listing, ...prev];
                              });
                            }
                            break;
                          case 'market_buy':
                            if (event.data.listingId) {
                              setMarketplaceListings(prev => prev.map(listing =>
                                listing.id === event.data.listingId
                                  ? { ...listing, status: 'sold' }
                                  : listing
                              ));
                            }
                            break;
                          case 'like':
                            if (event.data.postId) {
                              setPosts(prev => prev.map(post => {
                                if (post.id === event.data.postId) {
                                  return {
                                    ...post,
                                    likes: post.likes + 1
                                  };
                                }
                                return post;
                              }));
                            }
                            break;
                          case 'reply':
                            if (event.data.postId) {
                              setPosts(prev => prev.map(post => {
                                if (post.id === event.data.data.postId) {
                                  return {
                                    ...post,
                                    replies: post.replies + 1
                                  };
                                }
                                return post;
                              }));
                            }
                            break;
                          case 'friend_request':
                            // Handle friend request
                            break;
                          case 'friend_accept':
                            //Handle friend accept
                            break;
                          case 'chat':
                            // The Ghost Chat functionality has been removed. 
                            // This case is kept for backward compatibility, but messages are not processed
                            console.warn("Received chat message but The TUne (Ghost Chat) functionality has been removed.");
                            break;
                          case 'location_update':
                            //Handle location update - implemented in separate useEffect
                            break;
                          case 'discovery_ping':
                            //Handle discovery ping
                            break;
                          case 'profile_update':
                            if (event.data.profile) {
                              const currentProfile = profiles.get(event.userId);
                              if (currentProfile) {
                                const updatedProfile = {
                                  ...currentProfile,
                                  ...event.data.profile,
                                  lastSeen: event.data.timestamp
                                };
                                setProfiles(prev => new Map(prev).set(event.userId, updatedProfile));
                              }
                            }
                            break;
                          case 'verification_sync':
                            if (event.data.verificationStatus !== undefined) {
                              setActiveUsers(prev => {
                                const filtered = prev.filter(u => u.id !== event.userId);
                                return [...filtered, {
                                  id: event.userId,
                                  lastSeen: event.data.timestamp,
                                  verified: event.data.verificationStatus
                                }];
                              });
                            }
                            break;
                          case 'market_like':
                            if (event.data.listingId) {
                              setMarketplaceListings(prev => prev.map(listing =>
                                listing.id === event.data.listingId
                                  ? { ...listing, likes: listing.likes + 1 }
                                  : listing
                              ));
                            }
                            break;
                          case 'market_comment':
                            if (event.data.listingId && event.data.comment) { setMarketplaceListings(prev => prev.map(listing => {
                              if (listing.id === event.data.listingId) {
                                return {...listing, comments: [...listing.comments,event.data.comment]
                                       };
                              }
                              return listing;
                            }));
                            }
                            break;
                          case 'market_share':
                            if (event.data.listingId) {
                              setMarketplaceListings(prev => prev.map(listing =>
                                listing.id === event.data.listingId
                                  ? { ...listing, shares: (listing.shares || 0) + 1 }
                                  : listing
                              ));
                            }
                            break;
                          case 'market_message':
                            //Handle market message event
                            break;
                          case 'video_call':
                            //Handle video call event
                            break;
                        }
                      } catch (error) {
                        console.error("Error handling event:", error);
                      }
                    };
                    eventListeners.current.push(handleEvent);
                    return () => {
                      eventListeners.current = eventListeners.current.filter(listener => listener !== handleEvent);
                    };
                  }, [connected]);
                  useEffect(() => {
                    if (!connected) return;
                    const interval = setInterval(() => {
                      // Filter out stale users
                      setActiveUsers(prev => {
                        const updatedUsers = prev.filter(u => Date.now() - u.lastSeen < 30000);
                        // Update peer count based on active users + synced devices
                        const syncedDevicesCount = ghostState?.syncedDevices?.length || 0;
                        const activeUserCount = updatedUsers.length;
                        const totalPeers = Math.max(1, activeUserCount + syncedDevicesCount);
                        // Update the peer count
                        setPeers(totalPeers);
                        // Log the real peer count for debugging
                        console.log(`[P2P] Updated peers count: ${totalPeers} (${activeUserCount} users, ${syncedDevicesCount} devices)`);
                        return updatedUsers;
                      });
                    }, 5000);
                    return () => clearInterval(interval);
                  }, [connected, ghostState?.syncedDevices]);
                  useEffect(() => {
                    if (!ghostState?.verified || !connected) return;
                    // Broadcast verification status
                    broadcastEvent({
                      type: 'verification_sync',
                      userId,
                      data: {
                        timestamp: Date.now(),
                        verificationStatus: ghostState.verified
                      }
                    });
                    // Initialize user profile after verification
                    if (!userProfile) {
                      const initialProfile: UserProfile = {
                        id: userId,
                        username: `hUman_${userId.slice(0, 6)}` ,
                        bio: "Energy-conscious node in the collective consciousness",
                        avatar: `https://api.dicebear.com/7.x/shapes/svg?seed=${userId}`,
                        joinedAt: Date.now(),
                        totalPosts: 0,
                        totalLikes: 0,
                        friends: [],
                        status: 'online',
                        lastSeen: Date.now()
                      };
                      setUserProfile(initialProfile);
                      setProfiles(prev => new Map(prev).set(userId, initialProfile));
                      // Broadcast initial profile
                      broadcastEvent({
                        type: 'profile_update',
                        userId,
                        data: {
                          timestamp: Date.now(),
                          profile: initialProfile
                        }
                      });
                    }
                  }, [ghostState?.verified, connected, userId, userProfile]);
                  const disableLocationDiscovery = useCallback(async () => {
                    setIsLocationEnabled(false);
                    setCurrentLocation(null);
                    setNearbyUsers([]);
                    setGhostMessages([]);
                  }, []);
                  useEffect(() => {
                    if (!isLocationEnabled || !currentLocation) return;
                    const handleLocationEvent = (event: NetworkEvent) => {
                      if (event.type !== 'location_update' || !event.data.location) return;
                      const userLoc = event.data.location;
                      const distance = calculateDistance(
                        currentLocation.latitude,
                        currentLocation.longitude,
                        userLoc.latitude,
                        userLoc.longitude
                      );
                      // Only show users within 5km radius
                      if (distance <= 5000) {
                        const quantum_probability = 1 - (event.data.quantum_state || 0);
                        setNearbyUsers(prev => {
                          const filtered = prev.filter(u => u.id !== event.userId);
                          return [...filtered, {
                            id: event.userId,
                            username: `hUman_${event.userId.slice(0, 6)}`,
                            avatar: `https://api.dicebear.com/7.x/shapes/svg?seed=${event.userId}`,
                            distance,
                            lastSeen: Date.now(),
                            quantum_probability,
                            location: userLoc
                          }];
                        });
                      }
                    };
                    eventListeners.current.push(handleLocationEvent);
                    return () => {
                      eventListeners.current = eventListeners.current.filter(
                        listener => listener !== handleLocationEvent
                      );
                    };
                  }, [isLocationEnabled, currentLocation, calculateDistance]);
                  useEffect(() => {
                    const cleanup = setInterval(() => {
                      const now = Date.now();
                      setNearbyUsers(prev =>
                        prev.filter(user => now - user.lastSeen < 300000)
                      );
                    }, 60000);
                    return () => clearInterval(cleanup);
                  }, []);
                  const getNearbyListings = useCallback(() => {
                    if (isGhostMode || !currentLocation) {
                      return [];
                    }
                    return marketplaceListings
                      .filter(listing => listing.location && listing.status === 'active')
                      .map(listing => {
                        if (!listing.location) return listing;
                        const distance = calculateDistance(
                          currentLocation.latitude,
                          currentLocation.longitude,
                          listing.location.latitude,
                          listing.location.longitude
                        );
                        return { ...listing, distance };
                      })
                      .filter(listing => listing.distance !== undefined && listing.distance <= 5000)
                      .sort((a, b) => (a.distance || 0) - (b.distance || 0));
                  }, [currentLocation, marketplaceListings, calculateDistance, isGhostMode]);
                  const getNearbyMessages = useCallback(() => {
                    if (isGhostMode || !currentLocation) {
                      return [];
                    }
                    return ghostMessages
                      .filter(msg => msg.location)
                      .map(msg => {
                        if (!msg.location) return msg;
                        const distance = calculateDistance(
                          currentLocation.latitude,
                          currentLocation.longitude,
                          msg.location.latitude,
                          msg.location.longitude
                        );
                        return { ...msg, distance };
                      })
                      .filter(msg => msg.distance !== undefined && msg.distance <= 5000)
                      .sort((a, b) => (a.distance || 0) - (b.distance || 0));
                  }, [currentLocation, ghostMessages, calculateDistance, isGhostMode]);
                  const enableLocationDiscovery = useCallback(async () => {
                    if (!ghostState?.verified) {
                      throw new Error("Must be verified to use location discovery");
                    }
                    try {
                      console.log('[P2P] Enabling location discovery with real-time GPS tracking...');
                      // Get real battery data for power-optimized location tracking
                      let batteryLevel = 0.75;
                      let isCharging = false;
                      if (ghostBot) {
                        try {
                          const batteryInfo = await ghostBot.getBatteryInfo();
                          batteryLevel = batteryInfo?.level || 0.75;
                          isCharging = batteryInfo?.charging || false;
                          console.log(`[P2P] Using real battery data for location: ${Math.round(batteryLevel * 100)}% ${isCharging ? '(charging)' : ''}`);
                        } catch (err) {
                          console.warn('[P2P] Could not get battery info for location tracking:', err);
                        }
                      }
                      // Set location options based on battery status
                      const locationOptions = {
                        enableHighAccuracy: isCharging || batteryLevel > 0.3,
                        timeout: isCharging ? 15000 : (batteryLevel > 0.5 ? 10000 : 5000),
                        maximumAge: isCharging ? 0 : 30000 // No cache when charging, 30s cache when on battery
                      };
                      console.log('[P2P] Location options:', locationOptions);
                      // Get current position with appropriate power settings
                      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
                        navigator.geolocation.getCurrentPosition(resolve, reject, locationOptions);
                      });
                      // Create location object with real coordinates
                      const location: GeoLocation = {
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                        timestamp: Date.now(),
                        accuracy: position.coords.accuracy
                      };
                      console.log(`[P2P] Real-time location obtained: ${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)} (accuracy: ${location.accuracy.toFixed(1)}m)`);
                      // Update state with real-time location
                      setCurrentLocation(location);
                      setIsLocationEnabled(true);
                      // Apply quantum uncertainty based on privacy settings and battery
                      // Higher privacy = more uncertainty in location
                      const quantum_uncertainty = (locationPrivacyLevel / 100) * (batteryLevel < 0.3 ? 1.5 : 1.0);
                      // Calculate UMatter cost for location services
                      const locationUMatterCost = locationOptions.enableHighAccuracy ? 0.04 : 0.02;
                      console.log(`[P2P] Location service cost: ${locationUMatterCost} UMatter`);
                      // Deduct UMatter if we have ghost bot access
                      if (ghostBot && typeof ghostBot.updateUMatter === 'function') {
                        ghostBot.updateUMatter(-locationUMatterCost, 'location_discovery');
                      }
                      // Broadcast location with real-time data
                      broadcastEvent({
                        type: 'location_update',
                        userId,
                        data: {
                          location,
                          timestamp: Date.now(),
                          quantum_state: quantum_uncertainty,
                          // Include real battery data for network prioritization
                          battery: {
                            level: batteryLevel,
                            charging: isCharging
                          }
                        }
                      });
                      // Watch position with battery-optimized settings
                      const watchOptions = {
                        enableHighAccuracy: isCharging || batteryLevel > 0.5, // Only high accuracy when charging or high battery
                        timeout: 30000,
                        maximumAge: isCharging ? 5000 : 60000 // 5s when charging, 60s on battery
                      };
                      // Set up continuous location tracking
                      const watchId = navigator.geolocation.watchPosition(
                        (pos) => {
                          const newLocation: GeoLocation = {
                            latitude: pos.coords.latitude,
                            longitude: pos.coords.longitude,
                            timestamp: Date.now(),
                            accuracy: pos.coords.accuracy
                          };
                          console.log(`[P2P] Location updated: ${newLocation.latitude.toFixed(6)}, ${newLocation.longitude.toFixed(6)}`);
                          setCurrentLocation(newLocation);
                          // Update location every minute
                          if (Date.now() - location.timestamp > 60000) {
                            broadcastEvent({
                              type: 'location_update',
                              userId,
                              data: {
                                location: newLocation,
                                timestamp: Date.now(),
                                quantum_state: quantum_uncertainty
                              }
                            });
                          }
                        },
                        (error) => {
                          console.warn('[P2P] Location tracking error:', error);
                        },
                        watchOptions
                      );
                      // Broadcast location discovery event for UI components
                      window.dispatchEvent(new CustomEvent('locationDiscoveryEnabled', {
                        detail: {
                          timestamp: Date.now(),
                          batteryLevel: batteryLevel * 100,
                          isCharging
                        }
                      }));
                      return () => navigator.geolocation.clearWatch(watchId);
                    } catch (error) {
                      console.error("Failed to enable location discovery:", error);
                      throw new Error("Failed to enable location discovery");
                    }
                  }, [ghostState?.verified, userId, locationPrivacyLevel, broadcastEvent]);
                  useEffect(() => {
                    if (isGhostMode) {
                      disableLocationDiscovery();
                    } else if (ghostState?.verified) {
                      enableLocationDiscovery().catch(console.error);
                    }
                  }, [isGhostMode, ghostState?.verified]);
                  const connectVideoCall = useCallback(async (peerId: string) => {
                    try {
                      if (activeVideoCall) {
                        endVideoCall();
                      }
                      const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
                      const pc = new RTCPeerConnection({
                        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
                      });
                      stream.getTracks().forEach(track => {
                        pc.addTrack(track, stream);
                      });
                      peerConnection.current = pc;
                      setActiveVideoCall({
                        peerId,
                        stream,
                        status: 'connecting'
                      });
                      pc.ontrack = (event) => {
                        const [remoteStream] = event.streams;
                        setActiveVideoCall(prev => prev ? {
                          ...prev,
                          stream: remoteStream,
                          status: 'active'
                        } : null);
                      };
                      // Broadcast video call event
                      broadcastEvent({
                        type: 'video_call',
                        userId,
                        data: {
                          targetUserId: peerId,
                          timestamp: Date.now()
                        }
                      });
                    } catch (error) {
                      console.error('Failed to start video call:', error);
                      throw error;
                    }
                  }, [userId, activeVideoCall, endVideoCall, broadcastEvent]);
                  // Remove the duplicate endVideoCall definition that was here
                  // Add remaining video call cleanup effect
                  useEffect(() => {
                    return () => {
                      if (activeVideoCall) {
                        endVideoCall();
                      }
                    };
                  }, [activeVideoCall, endVideoCall]);
                  // nUTShell Network implementation (20 devices per nUTShell with unlimited network scaling)
                  const createNUTShell = useCallback(async (name?: string): Promise<NUTShellNetwork> => {
                    const shellId = `nutshell-${userId}-${Date.now()}`;
                    const newShell: NUTShellNetwork = {
                      id: shellId,
                      name: name || `${userProfile?.username || 'User'}'s nUTShell`,
                      ownerId: userId,
                      devices: [],
                      maxDevices: 20, // 20 device limit per nUTShell
                      createdAt: Date.now(),
                      lastSyncAt: Date.now(),
                      // Add a device to this nUTShell (up to maxDevices)
                      addDevice: (device: DeviceInfo): boolean => {
                        if (newShell.devices.length >= newShell.maxDevices) {
                          console.warn(`[nUTShell] Device limit reached (${newShell.maxDevices})`);
                          return false;
                        }
                        // Don't add duplicates
                        if (newShell.devices.some(d => d.id === device.id)) {
                          // Update the existing device instead
                          newShell.devices = newShell.devices.map(d => 
                            d.id === device.id ? { ...d, ...device, lastSeen: Date.now() } : d
                          );
                          return true;
                        }
                        newShell.devices.push({
                          ...device,
                          lastSeen: Date.now()
                        });
                        newShell.lastSyncAt = Date.now();
                        return true;
                      },
                      // Remove a device from this nUTShell
                      removeDevice: (deviceId: string): boolean => {
                        const initialCount = newShell.devices.length;
                        newShell.devices = newShell.devices.filter(d => d.id !== deviceId);
                        const removed = newShell.devices.length < initialCount;
                        if (removed) {
                          newShell.lastSyncAt = Date.now();
                        }
                        return removed;
                      },
                      // Check if we can add more devices
                      canAddDevice: (): boolean => {
                        return newShell.devices.length < newShell.maxDevices;
                      },
                      // Get currently connected devices
                      getConnectedDevices: (): DeviceInfo[] => {
                        return newShell.devices.filter(d => d.connected);
                      }
                    };
                    // Add the current device automatically
                    try {
                      const batteryInfo = await ghostBot?.getBatteryInfo();
                      const currentDevice: DeviceInfo = {
                        id: `device-${userId}`,
                        name: 'This Device',
                        type: detectDeviceType(),
                        batteryLevel: batteryInfo?.level ? batteryInfo.level * 100 : undefined,
                        isCharging: batteryInfo?.charging,
                        lastSeen: Date.now(),
                        connected: true,
                        connectionType: 'webrtc'
                      };
                      newShell.addDevice(currentDevice);
                    } catch (error) {
                      console.error('[nUTShell] Error adding current device:', error);
                      // Still add a basic device entry if battery info fails
                      newShell.addDevice({
                        id: `device-${userId}`,
                        name: 'This Device',
                        type: detectDeviceType(),
                        lastSeen: Date.now(),
                        connected: true
                      });
                    }
                    setNutshell(newShell);
                    // Broadcast this nUTShell to the network
                    broadcastEvent({
                      type: 'verification_sync',
                      userId,
                      data: {
                        timestamp: Date.now(),
                        verificationStatus: true,
                        nutshellId: shellId
                      }
                    });
                    return newShell;
                  }, [userId, userProfile, ghostBot, broadcastEvent]);
                  // Helper to detect the current device type
                  const detectDeviceType = useCallback((): 'phone' | 'tablet' | 'laptop' | 'desktop' | 'vr' | 'other' => {
                    if (typeof navigator === 'undefined') return 'other';
                    const ua = navigator.userAgent;
                    // Detect VR
                    if (ua.includes('VR') || ua.includes('OculusBrowser') || ua.includes('SamsungBrowser VR')) {
                      return 'vr';
                    }
                    // Detect mobile
                    if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua)) {
                      // Distinguish between phone and tablet
                      if (/iPad|tablet|Tablet|Android 3\.|Android 4\.[3-9]/i.test(ua)) {
                        return 'tablet';
                      }
                      return 'phone';
                    }
                    // Try to distinguish between laptop and desktop
                    // This is imperfect but provides a reasonable guess
                    if (/Macintosh|MacIntel|MacPPC|Mac68K/i.test(ua) || 
                        /Windows NT|Windows XP|Windows 7|Windows 8|Windows 10/i.test(ua)) {
                      // Laptops often have battery, check if battery API is available
                      if (navigator.maxTouchPoints > 0) {
                        return 'laptop';
                      }
                      return 'desktop';
                    }
                    return 'other';
                  }, []);
                  // Join an existing nUTShell
                  const joinNUTShell = useCallback(async (shellId: string): Promise<boolean> => {
                    try {
                      // Find the shell in nearby shells
                      const targetShell = nearbyNUTShells.find(s => s.id === shellId);
                      if (!targetShell) {
                        console.error(`[nUTShell] Shell ${shellId} not found`);
                        return false;
                      }
                      // Check if we can join (under device limit)
                      if (!targetShell.canAddDevice()) {
                        console.error(`[nUTShell] Shell ${shellId} is at capacity (${targetShell.maxDevices} devices)`);
                        return false;
                      }
                      // Get our device info
                      const batteryInfo = await ghostBot?.getBatteryInfo();
                      const myDevice: DeviceInfo = {
                        id: `device-${userId}`,
                        name: 'My Device',
                        type: detectDeviceType(),
                        batteryLevel: batteryInfo?.level ? batteryInfo.level * 100 : undefined,
                        isCharging: batteryInfo?.charging,
                        lastSeen: Date.now(),
                        connected: true,
                        connectionType: 'webrtc'
                      };
                      // Add our device to the shell
                      if (targetShell.addDevice(myDevice)) {
                        setNutshell(targetShell);
                        // Broadcast join event
                        broadcastEvent({
                          type: 'verification_sync',
                          userId,
                          data: {
                            timestamp: Date.now(),
                            verificationStatus: true,
                            nutshellId: shellId
                          }
                        });
                        return true;
                      }
                      return false;
                    } catch (error) {
                      console.error('[nUTShell] Error joining shell:', error);
                      return false;
                    }
                  }, [userId, nearbyNUTShells, ghostBot, detectDeviceType, broadcastEvent]);
                  // Leave current nUTShell
                  const leaveNUTShell = useCallback(async (): Promise<boolean> => {
                    if (!nutshell) {
                      console.warn('[nUTShell] Not in a nUTShell');
                      return false;
                    }
                    // Remove our device
                    const deviceId = `device-${userId}`;
                    if (nutshell.removeDevice(deviceId)) {
                      // If we're the owner and there are no devices left, delete the shell
                      if (nutshell.ownerId === userId && nutshell.devices.length === 0) {
                        // Broadcast delete event
                        broadcastEvent({
                          type: 'verification_sync',
                          userId,
                          data: {
                            timestamp: Date.now(),
                            verificationStatus: true,
                            nutshellId: null
                          }
                        });
                      } else {
                        // Otherwise just broadcast leave event
                        broadcastEvent({
                          type: 'verification_sync',
                          userId,
                          data: {
                            timestamp: Date.now(),
                            verificationStatus: true,
                            nutshellId: null
                          }
                        });
                      }
                      setNutshell(null);
                      return true;
                    }
                    return false;
                  }, [nutshell, userId, broadcastEvent]);
                  // Add a device to our nUTShell
                  const addDeviceToNUTShell = useCallback(async (deviceInfo: Partial<DeviceInfo>): Promise<boolean> => {
                    if (!nutshell) {
                      console.warn('[nUTShell] Not in a nUTShell');
                      return false;
                    }
                    // Check if we can add another device
                    if (!nutshell.canAddDevice()) {
                      console.warn(`[nUTShell] Device limit reached (${nutshell.maxDevices})`);
                      return false;
                    }
                    // Create a proper device info object
                    const newDevice: DeviceInfo = {
                      id: deviceInfo.id || `device-${nanoid(8)}`,
                      name: deviceInfo.name || `Device-${nanoid(4)}`,
                      type: deviceInfo.type || 'other',
                      batteryLevel: deviceInfo.batteryLevel,
                      isCharging: deviceInfo.isCharging,
                      lastSeen: Date.now(),
                      address: deviceInfo.address,
                      publicKey: deviceInfo.publicKey,
                      connected: deviceInfo.connected || false,
                      connectionType: deviceInfo.connectionType
                    };
                    // Add the device
                    if (nutshell.addDevice(newDevice)) {
                      // Update state to trigger re-render
                      setNutshell({...nutshell});
                      // Broadcast device added event
                      broadcastEvent({
                        type: 'verification_sync',
                        userId,
                        data: {
                          timestamp: Date.now(),
                          verificationStatus: true,
                          nutshellId: nutshell.id
                        }
                      });
                      return true;
                    }
                    return false;
                  }, [nutshell, userId, broadcastEvent]);
                  // Find nearby nUTShells
                  const findNearbyNUTShells = useCallback(async (): Promise<NUTShellNetwork[]> => {
                    // In a real implementation, this would scan the network for available shells
                    // For now, we'll return any shells we've received via network events
                    return nearbyNUTShells;
                  }, [nearbyNUTShells]);
                  // Listen for nUTShell events
                  useEffect(() => {
                    if (!connected) return;
                    const handleNUTShellEvent = (event: CustomEvent) => {
                      const { detail } = event;
                      if (detail.type === 'verification_sync' && detail.data?.nutshellId) {
                        // A new nUTShell was created or updated
                        const shellId = detail.data.nutshellId;
                        // Update nearby shells
                        setNearbyNUTShells(prev => {
                          // Check if we already know about this shell
                          const existingIndex = prev.findIndex(s => s.id === shellId);
                          if (existingIndex >= 0) {
                            // Update the existing shell
                            const updated = [...prev];
                            updated[existingIndex] = {
                              ...updated[existingIndex],
                              lastSyncAt: Date.now()
                            };
                            return updated;
                          } else {
                            // Add the new shell
                            return [...prev, {
                              id: shellId,
                              name: `nUTShell ${prev.length + 1}`,
                              ownerId: detail.userId,
                              devices: [],
                              maxDevices: 20,
                              createdAt: Date.now(),
                              lastSyncAt: Date.now(),
                              addDevice: () => false,
                              removeDevice: () => false,
                              canAddDevice: () => false,
                              getConnectedDevices: () => []
                            }];
                          }
                        });
                      }
                    };
                    // Listen for nUTShell events
                    window.addEventListener('network-event', handleNUTShellEvent as EventListener);
                    return () => {
                      window.removeEventListener('network-event', handleNUTShellEvent as EventListener);
                    };
                  }, [connected]);
                  const value: P2PContextType = {
                    connected,
                    peers,
                    userId,
                    userProfile,
                    connect,
                    disconnect,
                    connectToPeer,
                    networkQuality,
                    broadcastPost,
                    likePost,
                    replyToPost,
                    sharePost,
                    posts,
                    userPosts,
                    activeUsers,
                    sendFriendRequest,
                    acceptFriendRequest,
                    getFriendsList,
                    updateProfile,
                    getUserProfile,
                    createListing,
                    buyListing,
                    marketplaceListings,
                    getNearbyListings,
                    dailyLimits,
                    checkDailyLimit,
                    getRemainingLimits,
                    sendGhostChat,
                    ghostMessages,
                    getNearbyMessages,
                    enableLocationDiscovery,
                    disableLocationDiscovery,
                    getNearbyUsers,
                    isLocationEnabled,
                    locationPrivacyLevel,
                    setLocationPrivacyLevel: useCallback((level: number) => {
                      setLocationPrivacyLevel(Math.max(0, Math.min(100, level)));
                    }, []),
                    currentLocation,
                    isGhostMode,
                    likeListing: useCallback(async (listingId: string) => {
                      if (!ghostState?.verified) throw new Error("Must be verified to like listings");
                      try {
                        // Find the listing and determine if battery-boosted
                        const targetListing = marketplaceListings.find(listing => listing.id === listingId);
                        let isBatteryBoosted = false;
                        let boostAmount = 0;
                        setMarketplaceListings(prev => prev.map(listing => {
                          if (listing.id === listingId) {
                            isBatteryBoosted = !!listing.batteryBoosted;
                            boostAmount = listing.energyContribution || 0;
                            return {
                              ...listing,
                              likes: listing.likes + 1
                            };
                          }
                          return listing;
                        }));
                        // Reward seller with extra TRU for battery-boosted listings
                        if (isBatteryBoosted && targetListing && targetListing.seller !== userId) {
                          // Calculate bonus based on boost amount (0.5% of boost amount)
                          const bonusAmount = boostAmount * 0.005;
                          // If the boost is significant enough, give the seller a small TRU reward
                          if (bonusAmount > 0.000001) { // Minimum transaction amount
                            await addTransaction({
                              type: 'reward',
                              amount: bonusAmount,
                              currency: 'TRU',
                              description: 'Battery-boost engagement reward'
                            });
                          }
                        }
                        broadcastEvent({
                          type: 'market_like',
                          userId,
                          data: {
                            listingId,
                            timestamp: Date.now(),
                            batteryBoosted: isBatteryBoosted,
                            boostAmount: boostAmount
                          }
                        });
                      } catch (error) {
                        console.error("Failed to like listing:", error);
                        throw error;
                      }
                    }, [userId, ghostState, broadcastEvent]),
                    commentOnListing: useCallback(async (listingId: string, content: string) => {
                      if (!ghostState?.verified) throw new Error("Must be verified to comment");
                      if (balance.TRU < 0.001) throw new Error("Insufficient TRU balance for commenting");
                      // Find the listing to check for battery boost
                      const targetListing = marketplaceListings.find(listing => listing.id === listingId);
                      let isBatteryBoosted = false;
                      let boostAmount = 0;
                      const comment = {
                        id: nanoid(),
                        userId,
                        content,
                        timestamp: Date.now()
                      };
                      // Deduct comment fee
                      await addTransaction({
                        type: 'fee',
                        amount: -0.001,
                        currency: 'TRU',
                        description: 'Comment on marketplace listing'
                      });
                      setMarketplaceListings(prev => prev.map(listing => {
                        if (listing.id === listingId) {
                          isBatteryBoosted = !!listing.batteryBoosted;
                          boostAmount = listing.energyContribution || 0;
                          return {
                            ...listing,
                            comments: [...listing.comments, comment]
                          };
                        }
                        return listing;
                      }));
                      // Reward seller with extra TRU for battery-boosted listings
                      // Comments are a more valuable engagement than likes, so reward is higher
                      if (isBatteryBoosted && targetListing && targetListing.seller !== userId) {
                        // Calculate bonus based on boost amount (1% of boost amount for comments)
                        const bonusAmount = boostAmount * 0.01;
                        // If the boost is significant enough, give the seller a TRU reward
                        if (bonusAmount > 0.000001) { // Minimum transaction amount
                          await addTransaction({
                            type: 'reward',
                            amount: bonusAmount,
                            currency: 'TRU',
                            description: 'Battery-boost comment reward'
                          });
                        }
                      }
                      broadcastEvent({
                        type: 'market_comment',
                        userId,
                        data: {
                          listingId,
                          comment,
                          timestamp: Date.now(),
                          batteryBoosted: isBatteryBoosted,
                          boostAmount: boostAmount
                        }
                      });
                    }, [userId, ghostState?.verified, balance.TRU, updateBalance, broadcastEvent]),
                    shareListing: useCallback(async (listingId: string) => {
                      if (!ghostState?.verified) throw new Error("Must be verified to share listings");
                      const listing = marketplaceListings.find(l => l.id === listingId);
                      if (!listing) throw new Error("Listing not found");
                      // Check if listing is battery-boosted
                      const isBatteryBoosted = !!listing.batteryBoosted;
                      const boostAmount = listing.energyContribution || 0;
                      try {
                        // Shares are the most valuable form of engagement - small reward for sharing
                        await addTransaction({
                          type: 'reward',
                          amount: 0.00005, // Small base reward for sharing
                          currency: 'TRU',
                          description: 'Sharing marketplace listing'
                        });
                        // Deduct share fees
                        // Broadcast share event
                        broadcastEvent({
                          type: 'market_share',
                          userId,
                          data: {
                            listingId,
                            timestamp: Date.now(),
                            batteryBoosted: isBatteryBoosted,
                            boostAmount: boostAmount
                          }
                        });
                        // Update listing share count
                        setMarketplaceListings(prev => prev.map(l =>
                          l.id === listingId
                            ? { ...l, shares: (l.shares || 0) + 1 }
                            : l
                        ));
                        // Additional reward for seller if listing is battery-boosted
                        // Shares provide the highest reward since they expand reach significantly
                        if (isBatteryBoosted && listing.seller !== userId) {
                          // Calculate bonus based on boost amount (2% of boost amount for shares)
                          const bonusAmount = boostAmount * 0.02;
                          // If the boost is significant enough, give the seller a TRU reward
                          if (bonusAmount > 0.000001) { // Minimum transaction amount
                            await addTransaction({
                              type: 'reward',
                              amount: bonusAmount,
                              currency: 'TRU',
                              description: 'Battery-boost share reward'
                            });
                          }
                        }
                        // Update network activity - higher impact for battery-boosted listings
                        updateActivity(isBatteryBoosted ? 0.7 : 0.5);
                        return true;
                      } catch (error) {
                        console.error("Failed to share listing:", error);
                        throw error;
                      }
                    }, [userId, ghostState?.verified, marketplaceListings, broadcastEvent, updateActivity]),
                    messageSeller: useCallback(async (listingId: string, message: string) => {
                      if (!ghostState?.verified) throw new Error("Must be verified to message");
                      if (balance.TRU < 0.001) throw new Error("Insufficient TRU balance for messaging");
                      const listing = marketplaceListings.find(l => l.id === listingId);
                      if (!listing) throw new Error("Listing not found");
                      updateBalance('TRU', -0.001); // Small fee for messaging
                      // Send private message through P2P network
                      broadcastEvent({
                        type: 'market_message',
                        userId,
                        data: {
                          listingId,
                          targetUserId: listing.seller,
                          content: message,
                          timestamp: Date.now()
                        }
                      });
                    }, [userId, ghostState?.verified, balance.TRU, marketplaceListings, updateBalance, broadcastEvent]),
                    sendGhostMessage,
                    connectVideoCall,
                    endVideoCall,
                    videoCall: {
                      active: activeVideoCall,
                      connect: connectVideoCall,
                      end: endVideoCall
                    },
                    // nUTShell network management
                    nutshell,
                    createNUTShell,
                    joinNUTShell,
                    leaveNUTShell,
                    addDeviceToNUTShell,
                    findNearbyNUTShells
                  };
                  useEffect(() => {
                    // Clean up expired listings every minute
                    const cleanup = setInterval(() => {
                      const now = Date.now();
                      setMarketplaceListings(prev =>
                        prev.filter(listing => !listing.expiresAt || listing.expiresAt > now)
                      );
                    }, 60000);
                    return () => clearInterval(cleanup);
                  }, []);
                  useEffect(() => {
                    if (!connected) return;
                    // Update wallet balance every 10 seconds
                    const walletSync = setInterval(async () => {
                      try {
                        if (!ghostBot) return;
                        // Get real battery data for wallet balance updates
                        const batteryInfo = await ghostBot.getBatteryInfo();
                        if (!batteryInfo) return;
                        // Use real battery data to calculate wallet balances
                        const truBalance = batteryInfo.level * 10; // Calculate TRU balance based on battery level
                        const sbuBalance = batteryInfo.level * 5; // Calculate SBU balance from battery level
                        // Update wallet store with real energy data
                        updateBalance('TRU', truBalance, 'sync'); // Use 'sync' type for battery-based balance updates
                        updateBalance('SBU', sbuBalance, 'sync');
                      } catch (error) {
                        console.error('Failed to sync wallet:', error);
                      }
                    }, 10000);
                    return () => clearInterval(walletSync);
                  }, [connected, updateBalance, ghostBot]);
                  // Add quantum encryption helper function
                  const generateQuantumHash = async (imageUrl: string): Promise<string> => {
                    const encoder = new TextEncoder();
                    const data = encoder.encode(imageUrl + Date.now());
                    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
                    const hashArray = Array.from(new Uint8Array(hashBuffer));
                    return `qh-${hashArray.map(b => b.toString(16).padStart(2, '0')).join('')}`;
                  };
                  return (
                    <P2PContext.Provider value={value}>
                      {children}
                    </P2PContext.Provider>
                  );
                }
                export type { 
                  GeoLocation, 
                  NearbyUser, 
                  UserProfile, 
                  NetworkPost, 
                  MarketplaceListing, 
                  GhostMessage, 
                  NetworkEvent, 
                  NetworkEventData, 
                  DailyLimits, 
                  VideoCallState,
                  DeviceInfo,
                  NUTShellNetwork
                };