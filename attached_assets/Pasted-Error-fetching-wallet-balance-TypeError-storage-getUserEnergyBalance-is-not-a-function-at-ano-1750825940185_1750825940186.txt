Error fetching wallet balance: TypeError: storage.getUserEnergyBalance is not a function
    at <anonymous> (/home/<USER>/workspace/server/wallet-routes.ts:25:37)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at isAuthenticated (/home/<USER>/workspace/server/wallet-routes.ts:16:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:61:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at urlencodedParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/urlencoded.js:94:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:34:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at expressInit (/home/<USER>/workspace/node_modules/express/lib/middleware/init.js:40:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at query (/home/<USER>/workspace/node_modules/express/lib/middleware/query.js:45:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at Function.handle (/home/<USER>/workspace/node_modules/express/lib/router/index.js:175:3)
    at Function.handle (/home/<USER>/workspace/node_modules/express/lib/application.js:181:10)
    at Server.app (/home/<USER>/workspace/node_modules/express/lib/express.js:39:9)
4:18:12 AM [express] GET /api/wallet/balance 500 in 9ms :: {"message":"Failed to fetch wallet balanc…
4:18:12 AM [express] GET /api/wallet/staking-pools 304 in 1ms :: [{"id":"pool-tru","token":"TRU","ap…
4:18:12 AM [express] GET /api/wallet/transactions 200 in 0ms :: [{"id":"tx-001","type":"receive","to…
4:18:12 AM [express] GET /api/wallet/conversion-rates 304 in 1ms :: [{"from":"UMATTER","to":"TRU","r…
Error fetching wallet balance: TypeError: storage.getUserEnergyBalance is not a function
    at <anonymous> (/home/<USER>/workspace/server/wallet-routes.ts:25:37)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at isAuthenticated (/home/<USER>/workspace/server/wallet-routes.ts:16:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:61:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at urlencodedParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/urlencoded.js:94:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:34:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at expressInit (/home/<USER>/workspace/node_modules/express/lib/middleware/init.js:40:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at query (/home/<USER>/workspace/node_modules/express/lib/middleware/query.js:45:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at Function.handle (/home/<USER>/workspace/node_modules/express/lib/router/index.js:175:3)
    at Function.handle (/home/<USER>/workspace/node_modules/express/lib/application.js:181:10)
    at Server.app (/home/<USER>/workspace/node_modules/express/lib/express.js:39:9)
4:18:22 AM [express] GET /api/wallet/balance 500 in 2ms :: {"message":"Failed to fetch wallet balanc…
Error fetching wallet balance: TypeError: storage.getUserEnergyBalance is not a function
    at <anonymous> (/home/<USER>/workspace/server/wallet-routes.ts:25:37)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at isAuthenticated (/home/<USER>/workspace/server/wallet-routes.ts:16:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:61:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at urlencodedParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/urlencoded.js:94:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:34:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at expressInit (/home/<USER>/workspace/node_modules/express/lib/middleware/init.js:40:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at query (/home/<USER>/workspace/node_modules/express/lib/middleware/query.js:45:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at Function.handle (/home/<USER>/workspace/node_modules/express/lib/router/index.js:175:3)
    at Function.handle (/home/<USER>/workspace/node_modules/express/lib/application.js:181:10)
    at Server.app (/home/<USER>/workspace/node_modules/express/lib/express.js:39:9)
4:18:32 AM [express] GET /api/wallet/balance 500 in 2ms :: {"message":"Failed to fetch wallet balanc…
4:18:42 AM [express] GET /api/wallet/conversion-rates 304 in 0ms :: [{"from":"UMATTER","to":"TRU","r…
Error fetching wallet balance: TypeError: storage.getUserEnergyBalance is not a function
    at <anonymous> (/home/<USER>/workspace/server/wallet-routes.ts:25:37)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at isAuthenticated (/home/<USER>/workspace/server/wallet-routes.ts:16:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:61:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at urlencodedParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/urlencoded.js:94:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:34:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at expressInit (/home/<USER>/workspace/node_modules/express/lib/middleware/init.js:40:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at query (/home/<USER>/workspace/node_modules/express/lib/middleware/query.js:45:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at Function.handle (/home/<USER>/workspace/node_modules/express/lib/router/index.js:175:3)
    at Function.handle (/home/<USER>/workspace/node_modules/express/lib/application.js:181:10)
    at Server.app (/home/<USER>/workspace/node_modules/express/lib/express.js:39:9)
4:18:42 AM [express] GET /api/wallet/balance 500 in 1ms :: {"message":"Failed to fetch wallet balanc…
Error fetching wallet balance: TypeError: storage.getUserEnergyBalance is not a function
    at <anonymous> (/home/<USER>/workspace/server/wallet-routes.ts:25:37)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at isAuthenticated (/home/<USER>/workspace/server/wallet-routes.ts:16:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:61:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at urlencodedParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/urlencoded.js:94:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js