rror fetching real system metrics: TypeError: Cannot read properties of undefined (reading 'getRealSystemMetrics')
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:866:51)
3:16:39 PM [express] GET /api/energy/metrics 500 in 13ms :: {"message":"Failed to fetch real system …
[Storage] Total UMatter from real transactions: 31953.615
3:16:39 PM [express] GET /api/extension/status 200 in 26ms :: {"connected":true,"version":"3.0.0","l…
3:16:39 PM [express] GET /api/stats/system 304 in 1ms :: {"totalInteractions":0,"encryptedSessions":…
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 1.35%, Memory: 269.5MB, Power: 15.94W
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 0.20%, Memory: 274.8MB, Power: 15.61W
[RealHardwareAPI] AUTHENTIC: 2.783300 UMatter from real Node.js APIs
3:16:39 PM [express] GET /api/energy/real-hardware-metrics 200 in 109ms :: {"success":true,"authenti…
[Banking] Processing batch: 0 items, 0.***************** UMatter
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 1.22%, Memory: 275.1MB, Power: 15.92W
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 0.69%, Memory: 274.9MB, Power: 15.76W
[RealHardwareAPI] AUTHENTIC: 2.833290 UMatter from real Node.js APIs
3:16:39 PM [express] GET /api/energy/real-hardware-metrics 200 in 117ms :: {"success":true,"authenti…
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 2.57%, Memory: 276.8MB, Power: 16.32W
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 1.55%, Memory: 280.0MB, Power: 16.02W
[RealHardwareAPI] AUTHENTIC: 2.970479 UMatter from real Node.js APIs
3:16:40 PM [express] GET /api/energy/real-hardware-metrics 200 in 159ms :: {"success":true,"authenti…
[RealDeviceMessenger] Scanning 192.168.0.1-254 for devices...
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 2.16%, Memory: 291.7MB, Power: 16.23W
[Storage] Retrieved ALL 16046 transactions for user dev-user-123
[Banking] Real balance: 31955.************ UMatter from 16046 transactions
[Banking] LIVE TOKEN CALCULATIONS - TRU:1035 NUVA:498 INU:284 UBITS:3943
3:16:40 PM [express] GET /api/banking/balance 200 in 842ms :: {"umatter":31955.************,"tru":10…
3:16:40 PM [express] GET /api/energy/solar-integration-check 200 in 302ms
[RealDataConnector] AUTHENTIC HARDWARE: CPU: 3.23%, Memory: 302.4MB, Power: 16.57W
[RealHardwareAPI] AUTHENTIC: 3.363063 UMatter from real Node.js APIs
3:16:40 PM [express] GET /api/energy/real-hardware-metrics 200 in 246ms :: {"success":true,"authenti…
3:16:40 PM [express] GET /api/energy/solar-integration-check 200 in 150ms
[Storage] Retrieved ALL 16047 transactions for user dev-user-123
[Banking] Calculated actual balance from 16047 transactions: 31955.************
3:16:40 PM [express] POST /api/banking/deposit-umatter-batch 200 in 836ms :: {"success":true,"batchS…
[RealDeviceMessenger] Scanning ********-254 for devices...
[Storage] Retrieved ALL 16047 transactions for user dev-user-123
[Banking] Real balance: 31955.************ UMatter from 16047 transactions
[Banking] LIVE TOKEN CALCULATIONS - TRU:1035 NUVA:498 INU:284 UBITS:3943
3:16:41 PM [express] GET /api/banking/balance 200 in 630ms :: {"umatter":31955.************,"tru":10…
[Extension Download] Attempting to serve: /home/<USER>/workspace/browser-extension/nu-universe-quantum-extension.zip
3:16:41 PM [express] GET /api/extension/download 500 in 1ms :: {"message":"require is not defined"}
ReferenceError: require is not defined
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:431:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/route.js:149:13)
    at Route.dispatch (/home/<USER>/workspace/node_modules/express/lib/router/route.js:119:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:284:15
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:63:3)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at urlencodedParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/urlencoded.js:94:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at jsonParser (/home/<USER>/workspace/node_modules/body-parser/lib/types/json.js:113:7)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at <anonymous> (/home/<USER>/workspace/server/index.ts:36:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at expressInit (/home/<USER>/workspace/node_modules/express/lib/middleware/init.js:40:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at query (/home/<USER>/workspace/node_modules/express/lib/middleware/query.js:45:5)
    at Layer.handle [as handle_request] (/home/<USER>/workspace/node_modules/express/lib/router/layer.js:95:5)
    at trim_prefix (/home/<USER>/workspace/node_modules/express/lib/router/index.js:328:13)
    at /home/<USER>/workspace/node_modules/express/lib/router/index.js:286:9
    at Function.process_params (/home/<USER>/workspace/node_modules/express/lib/router/index.js:346:12)
    at next (/home/<USER>/workspace/node_modules/express/lib/router/index.js:280:10)
    at Function.handle (/home/<USER>/workspace/node_modules/express/lib/router/index.js:175:3)
    at Function.handle (/home/<USER>/workspace/node_modules/express/lib/application.js:181:10)
    at Server.app (/home/<USER>/workspace/node_modules/express/lib/express.js:39:9)
    at Server.emit (node:events:518:28)
    at parserOnIncoming (node:_http_server:1141:12)
    at HTTPParser.parserOnHeadersComplete (node:_http_common:118:17)
Error fetching real system metrics: TypeError: Cannot read properties of undefined (reading 'getRealSystemMetrics')
    at <anonymous> (/home/<USER>/workspace/server/routes.ts:866:51)
3:16:41 PM [express] GET /api/energy/metrics 500 in 2ms :: {"message":"Failed to fetch real system m…
[Storage] Real balance from 16047 transactions: 31953.9 UMatter
[Storage] Real balance: 31953.9 from 16047 transactions
[Storage] Real balance from 16047 transactions: 31953.9 UMatter
[Wallet] Real balance: 31953.9 UMatter from 16047 transactions
3:16:41 PM [express] GET /api/wallet/balance 200 in 900ms :: {"umatter":31953.9,"tru":0,"nuva":0,"in…
[RealDeviceMessenger] Scanning 172.16.0.1-254 for devices...
[Storage] Retrieved ALL 16047 transactions for user dev-user-123
[Banking] Real balance: 31955.************ UMatter from 16047 transactions
[Banking] LIVE TOKEN CALCULATIONS - TRU:1035 NUVA:498 INU:284 UBITS:3943
[RealDeviceMessenger] TCP scan complete - no devices found on 192.168.x.x networks
[RealDeviceMessenger] Network appears isolated or devices not responding on scanned ports
[RealDeviceMessenger] Device discovery complete - found 0 devices
