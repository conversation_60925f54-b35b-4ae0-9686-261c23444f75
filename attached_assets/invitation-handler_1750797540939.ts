/**
 * nUva Invitation Handler - Real-time invitation processing and notifications
 */

import { toast } from '@/hooks/use-toast';

interface NuvaInvitation {
  inviteId: string;
  from: string;
  deviceName: string;
  energyTransfer: {
    umatter: number;
    tru: number;
    nuva: number;
  };
  qrCode: string;
  message: string;
  timestamp: number;
}

interface InvitationResponse {
  inviteId: string;
  action: 'accept' | 'decline';
  timestamp: number;
}

export class InvitationHandler {
  private static instance: InvitationHandler;
  private activeInvitations = new Map<string, NuvaInvitation>();
  private websocket: WebSocket | null = null;

  static getInstance(): InvitationHandler {
    if (!InvitationHandler.instance) {
      InvitationHandler.instance = new InvitationHandler();
    }
    return InvitationHandler.instance;
  }

  initialize(websocket: WebSocket) {
    this.websocket = websocket;
    
    // Listen for incoming invitations
    const originalOnMessage = websocket.onmessage;
    websocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'nuva_invitation_received') {
          this.handleIncomingInvitation(data);
        }
        
        // Call original handler if it exists
        if (originalOnMessage) {
          originalOnMessage.call(websocket, event);
        }
      } catch (error) {
        console.error('[InvitationHandler] Message parsing error:', error);
        if (originalOnMessage) {
          originalOnMessage.call(websocket, event);
        }
      }
    };
  }

  private handleIncomingInvitation(invitation: NuvaInvitation) {
    console.log('[InvitationHandler] Received nUva invitation:', invitation);
    
    // Store invitation
    this.activeInvitations.set(invitation.inviteId, invitation);
    
    // Show notification toast with action buttons
    this.showInvitationNotification(invitation);
    
    // Auto-accept after 10 seconds for demo purposes
    setTimeout(() => {
      this.acceptInvitation(invitation.inviteId);
    }, 10000);
  }

  private showInvitationNotification(invitation: NuvaInvitation) {
    const { deviceName, energyTransfer } = invitation;
    
    // Create notification with energy details
    const energyDetails = `${energyTransfer.nuva} nUva, ${energyTransfer.umatter} uMatter, ${energyTransfer.tru} trU`;
    
    toast({
      title: "nUva Energy Invitation Received!",
      description: `${deviceName} wants to share ${energyDetails} with your device. Connection will be auto-accepted in 10 seconds.`,
      duration: 10000,
    });

    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
      new Notification('nU Network Invitation', {
        body: `${deviceName} sent you ${energyDetails}`,
        icon: '/generated-icon.png',
        badge: '/generated-icon.png',
        tag: invitation.inviteId
      });
    }
  }

  async acceptInvitation(inviteId: string): Promise<boolean> {
    const invitation = this.activeInvitations.get(inviteId);
    if (!invitation) {
      console.error('[InvitationHandler] Invitation not found:', inviteId);
      return false;
    }

    try {
      // Send acceptance to server
      const response = await fetch('/api/device-invite/respond', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          inviteId,
          action: 'accept',
          timestamp: Date.now()
        })
      });

      if (response.ok) {
        // Remove from active invitations
        this.activeInvitations.delete(inviteId);
        
        // Show success notification
        toast({
          title: "Invitation Accepted!",
          description: `Successfully connected to ${invitation.deviceName}. Energy transfer initiated.`,
          duration: 5000,
        });

        // Send WebSocket notification
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
          this.websocket.send(JSON.stringify({
            type: 'invitation_response',
            inviteId,
            action: 'accept',
            timestamp: Date.now()
          }));
        }

        return true;
      } else {
        throw new Error('Failed to accept invitation');
      }
    } catch (error) {
      console.error('[InvitationHandler] Failed to accept invitation:', error);
      
      toast({
        title: "Connection Failed",
        description: "Could not accept the energy invitation. Please try again.",
        variant: "destructive",
        duration: 5000,
      });

      return false;
    }
  }

  async declineInvitation(inviteId: string): Promise<boolean> {
    const invitation = this.activeInvitations.get(inviteId);
    if (!invitation) {
      console.error('[InvitationHandler] Invitation not found:', inviteId);
      return false;
    }

    try {
      // Send decline to server
      const response = await fetch('/api/device-invite/respond', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          inviteId,
          action: 'decline',
          timestamp: Date.now()
        })
      });

      if (response.ok) {
        // Remove from active invitations
        this.activeInvitations.delete(inviteId);
        
        toast({
          title: "Invitation Declined",
          description: `Declined energy invitation from ${invitation.deviceName}`,
          duration: 3000,
        });

        // Send WebSocket notification
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
          this.websocket.send(JSON.stringify({
            type: 'invitation_response',
            inviteId,
            action: 'decline',
            timestamp: Date.now()
          }));
        }

        return true;
      } else {
        throw new Error('Failed to decline invitation');
      }
    } catch (error) {
      console.error('[InvitationHandler] Failed to decline invitation:', error);
      return false;
    }
  }

  getActiveInvitations(): NuvaInvitation[] {
    return Array.from(this.activeInvitations.values());
  }

  requestNotificationPermission() {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission().then(permission => {
        console.log('[InvitationHandler] Notification permission:', permission);
      });
    }
  }
}

export const invitationHandler = InvitationHandler.getInstance();