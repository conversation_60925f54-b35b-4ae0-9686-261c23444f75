// constants.ts
// Centralized constants for the entire application
// Single source of truth for all conversion rates and constants
export const CONSTANTS = {
  // Initial balances and limits
  INITIAL_BALANCE: "4.313600", // Initial TRU balance for verified users
  MIN_TRANSFER: 0.01, // Minimum TRU transfer
  DECIMAL_PRECISION: 6, // Full precision for calculations
  DISPLAY_PRECISION: 2, // Reduced precision for display
  BLOCK_TIME: 1000, // Block time in milliseconds
  SYNC_INTERVAL: 1000, // Sync interval in milliseconds
  // Transaction fees
  FEES: {
    TRANSFER: 0.02, // 2% TRU fee
    BURN: 0.05, // Burn fee
    LOCK: 0.03, // Lock fee
    QUANTUM: 0.01, // Quantum zap fee
    MARKET: 0.001, // Market fee
    PROPOSAL: 1.0, // Proposal fee
    DONATE: 0.01 // Donate fee
  },
  // Rewards
  REWARDS: {
    QUANTUM_MESSAGE: 0.05, // 5% TRU for quantum zaps
    QUANTUM_BONUS: 0.5, // 0.5 TRU bonus
    TAX_CLAIM_MAX: 1000, // Max tax claim
    TAX_CLAIM_INTERVAL: 7 * 24 * 60 * 60 * 1000, // Weekly claims
  },
  // Pools
  POOLS: {
    ECOSYSTEM_RESERVE: 10000, // 10K TRU
    FREEDOM_POOL: 5000000, // 5M TRU
    MAX_MINT_PER_YEAR: 20000000, // 20M TRU/year
    MAX_BURN_PER_YEAR: 5000, // 5K TRU/year
  },
  // nUva energy system
  NUVA: {
    MAX_STORAGE: 1000, // 1,000 nUva cap
    CONVERSION_RATE: 1, // 1 nUva = 1% battery
    UMATTER_RATE: 0.1, // 1 nUva = 0.1 UMatter
    TRANSFER_FEE: 0.02, // 2% nUva fee
    PULSE_INTERVAL: 300, // 300ms pulse
    SIGNAL_BOOST_FACTOR: 0.3, // 30% boost
  },
  // TRU Constants
  TRU: {
    MICRO_TRU_PER_TRU: 1_000_000, // 1 TRU = 1,000,000 microTRU
    MIN_AMOUNT: 0.000001, // Minimum TRU unit (1 microTRU)
    DECIMALS: 6, // Full precision for calculations
    DISPLAY_DECIMALS: 2, // Reduced precision for display
    USD_VALUE: 0.001036, // Value per TRU in USD
    // Transaction amounts
    TRANSACTION_AMOUNTS: {
      VOTE_COST: 0.001, // 0.001 TRU
      COMMENT_COST: 0.001, // 0.001 TRU
      BOOST_COST: 0.067, // 0.067 TRU
      TRADE_MIN: 0.01, // 0.01 TRU
      MIN_TRADE: 0.000001 // Smallest tradeable unit
    }
  },
  // UMatter Constants
  UMATTER: {
    DECIMALS: 2, // Full precision for calculations
    DISPLAY_DECIMALS: 1, // Reduced precision for display
    TRU_PER_UM: 0.01, // 1 UMatter = 0.01 TRU
    MICRO_TRU_PER_UM: 10_000, // 1 UMatter = 10,000 microTRU
  },
  // Battery and Energy Conversion - PRODUCTION RATES
  BATTERY: {
    // Core conversion rates - EXACT PHYSICS MODEL: E_b → U_s → A_nU → K_UM
    TO_UMATTER_BASE_RATE: 1.0, // 1% battery = 1.0 UMatter base rate
    TO_TRU_BASE_RATE: 1.0, // 1% battery = 1.0 TRU base rate  
    
    // Efficiency multipliers
    CHARGING_BONUS_MULTIPLIER: 1.2, // 20% bonus when charging
    NETWORK_QUALITY_MULTIPLIER: 1.15, // 15% bonus for good network
    DEVICE_EFFICIENCY_MULTIPLIER: 0.675, // Base device efficiency (0.675-0.725 range)
    
    // Safety limits - CRITICAL FOR DEVICE PROTECTION
    MIN_BATTERY_LEVEL: 20, // Never drain below 20%
    MIN_DRAIN_PERCENT: 1, // Minimum 1% drain
    MAX_DRAIN_PERCENT: 15, // Maximum 15% drain per session
    MAX_DAILY_DRAIN: 50, // Maximum 50% total daily drain
    
    // Hardware specifications
    MAH_PER_PERCENT: 40, // 40mAh per 1% (4000mAh battery standard)
    WH_PER_MAH: 0.0037, // 3.7V standard * 1mAh = 0.0037Wh
    WH_PER_PERCENT: 0.148, // 0.148Wh per 1% battery
    
    // Precision settings
    DECIMALS: 6, // Internal calculation precision
    DISPLAY_DECIMALS: 2, // UI display precision
  },
  // Network and Market Constants
  NETWORK: {
    MIN_RATE: 0.8, // Minimum network rate
    MAX_MULTIPLIER: 1.2, // Maximum network multiplier
    BOOST_THRESHOLD: 10, // Network boost threshold
    CONNECTION_QUALITY: {
      WIFI: 1.1,
      CELLULAR_4G: 1.0,
      CELLULAR_3G: 0.9,
      CELLULAR_2G: 0.8,
      OFFLINE: 0.6
    }
  },

  // Watts Trading Constants
  WATTS: {
    PER_TRU: 0.1, // 1 TRU = 0.1W
    BOOST_COST: 1000, // 100W boost = 1000 TRU
    NETWORK_FEE: 5 // 5 TRU network fee
  }
};

// Service Status Constants
export enum ServiceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SYNCING = 'syncing',
  ERROR = 'error',
  PENDING = 'pending'
}

// Service Type Constants
export enum ServiceType {
  CORE = 'core',
  ENERGY = 'energy', 
  INTEL = 'intel',
  MESH = 'mesh',
  UTILITY = 'utility'
}

// Energy conversion rates - centralized for consistency
export const BATTERY_TO_ENERGY_RATE = CONSTANTS.BATTERY.TO_UMATTER_BASE_RATE; // Battery to energy conversion rate
export const ENERGY_TO_TRU_RATE = CONSTANTS.UMATTER.TRU_PER_UM; // Energy to TRU conversion rate

// Validation functions for transfers and conversions
// Validate TRU transfers
export const validateTransfer = (amount: number): boolean => {
  if (isNaN(amount) || amount <= 0) return false; // No negative or NaN values
  if (amount < CONSTANTS.MIN_TRANSFER) return false; // Respect min transfer
  const fixed = Number(amount.toFixed(CONSTANTS.DECIMAL_PRECISION));
  return fixed >= CONSTANTS.MIN_TRANSFER; // Ensure valid precision
};

// Validate nUva transfers
export const validateNuvaTransfer = (amount: number): boolean => {
  if (isNaN(amount) || amount <= 0) return false;
  if (amount > CONSTANTS.NUVA.MAX_STORAGE) return false; // Respect storage cap
  return amount >= 0.1; // Minimum 0.1 nUva
};

// Unified conversion system
export const ConversionService = {
  // Battery to UMatter conversion
  batteryToUMatter: (batteryPercent: number, isCharging: boolean = false, networkQuality: number = 1.0): number => {
    if (batteryPercent <= 0) return 0;
    // Apply charging bonus if applicable
    const chargingMultiplier = isCharging ? CONSTANTS.BATTERY.CHARGING_BONUS_MULTIPLIER : 1.0;
    // Apply network quality multiplier
    const networkMultiplier = Math.min(Math.max(networkQuality, CONSTANTS.NETWORK.MIN_RATE), CONSTANTS.NETWORK.MAX_MULTIPLIER);
    // Calculate UMatter based on real battery metrics
    return batteryPercent * CONSTANTS.BATTERY.TO_UMATTER_BASE_RATE * chargingMultiplier * networkMultiplier;
  },

  // UMatter to TRU conversion
  uMatterToTRU: (uMatterAmount: number, networkQuality: number = 1.0): number => {
    if (uMatterAmount <= 0) return 0;
    // Apply network quality multiplier
    const networkMultiplier = Math.min(Math.max(networkQuality, CONSTANTS.NETWORK.MIN_RATE), CONSTANTS.NETWORK.MAX_MULTIPLIER);
    // Calculate TRU based on UMatter amount
    return Number((uMatterAmount * CONSTANTS.UMATTER.TRU_PER_UM * networkMultiplier).toFixed(CONSTANTS.TRU.DECIMALS));
  },

  // Battery to TRU direct conversion
  batteryToTRU: (batteryPercent: number, isCharging: boolean = false, networkQuality: number = 1.0): number => {
    if (batteryPercent <= 0) return 0;
    // Apply charging bonus if applicable
    const chargingMultiplier = isCharging ? CONSTANTS.BATTERY.CHARGING_BONUS_MULTIPLIER : 1.0;
    // Apply network quality multiplier
    const networkMultiplier = Math.min(Math.max(networkQuality, CONSTANTS.NETWORK.MIN_RATE), CONSTANTS.NETWORK.MAX_MULTIPLIER);
    // Calculate TRU based on real battery metrics
    return Number((batteryPercent * CONSTANTS.BATTERY.TO_TRU_BASE_RATE * chargingMultiplier * networkMultiplier).toFixed(CONSTANTS.TRU.DECIMALS));
  },

  // TRU to UMatter conversion
  truToUMatter: (truAmount: number): number => {
    if (truAmount <= 0) return 0;
    // Calculate UMatter based on TRU amount
    return Number((truAmount / CONSTANTS.UMATTER.TRU_PER_UM).toFixed(CONSTANTS.UMATTER.DECIMALS));
  },

  // TRU to microTRU conversion
  truToMicroTRU: (truAmount: number): number => {
    if (truAmount <= 0) return 0;
    // Convert TRU to microTRU
    return Math.round(truAmount * CONSTANTS.TRU.MICRO_TRU_PER_TRU);
  },

  // microTRU to TRU conversion
  microTRUToTRU: (microTRUAmount: number): number => {
    if (microTRUAmount <= 0) return 0;
    // Convert microTRU to TRU
    return Number((microTRUAmount / CONSTANTS.TRU.MICRO_TRU_PER_TRU).toFixed(CONSTANTS.TRU.DECIMALS));
  },

  // Calculate network quality factor based on connection type
  getNetworkQualityFactor: (connectionType: string, isOnline: boolean = true): number => {
    if (!isOnline) return CONSTANTS.NETWORK.CONNECTION_QUALITY.OFFLINE;
    switch (connectionType) {
      case 'wifi':
      case 'ethernet':
        return CONSTANTS.NETWORK.CONNECTION_QUALITY.WIFI;
      case '4g':
        return CONSTANTS.NETWORK.CONNECTION_QUALITY.CELLULAR_4G;
      case '3g':
        return CONSTANTS.NETWORK.CONNECTION_QUALITY.CELLULAR_3G;
      case '2g':
        return CONSTANTS.NETWORK.CONNECTION_QUALITY.CELLULAR_2G;
      default:
        return CONSTANTS.NETWORK.CONNECTION_QUALITY.CELLULAR_4G; // Default to 4G
    }
  },

  // Format TRU for display
  formatTRU: (amount: number, precision?: number): string => {
    const displayPrecision = precision ?? CONSTANTS.TRU.DISPLAY_DECIMALS;
    return Number(amount).toFixed(displayPrecision);
  },

  // Format UMatter for display
  formatUMatter: (amount: number, precision?: number): string => {
    const displayPrecision = precision ?? CONSTANTS.UMATTER.DISPLAY_DECIMALS;
    return Number(amount).toFixed(displayPrecision);
  },

  // Calculate transaction fee
  calculateTransactionFee: (amount: number, feeType: keyof typeof CONSTANTS.FEES): number => {
    const feeRate = CONSTANTS.FEES[feeType];
    return Number((amount * feeRate).toFixed(CONSTANTS.TRU.DECIMALS));
  }
};