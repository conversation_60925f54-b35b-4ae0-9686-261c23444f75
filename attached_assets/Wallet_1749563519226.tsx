import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { motion } from "framer-motion";
import { useLocation } from "wouter";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useWalletStore, WalletItem } from "@/lib/stores/walletStore";
import { useGhost } from "@/components/GhostProvider";
import {
  Zap,
  ArrowUpRight,
  ArrowDownRight,
  RefreshCw,
  Shield,
  Battery,
  Send,
  Share2,
  History,
  Lock,
  Flame,
  Heart,
  Package,
  Star,
  Sparkles,
  BadgeCheck,
  LayoutGrid
} from "lucide-react";
// Use Zap instead of Bolt (renamed in newer lucide-react versions)
const Bolt = Zap;
// Define local formatTRU and formatCurrency functions since the imports are having issues
const formatTRU = (amount: number): string => {
  if (amount === undefined || amount === null || isNaN(amount)) {
    return '0.00';
  }
  // Format with 2 decimal places
  return amount.toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
    useGrouping: true
  });
};
const fromMicroTRU = (microTRU: number): number => {
  return microTRU / 1000000;
};
const formatCurrency = (amount: number): string => {
  return '$' + amount.toLocaleString(undefined, {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};
import { PageHero } from "@/components/PageHero";
import { BatteryDrainAnimation } from "@/components/BatteryDrainAnimation";
import { CapsoUL } from "@/components/WalletHero";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "@/lib/hooks/use-toast";
import { TransactionModal } from "@/components/TransactionModal";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
// Using our own local transaction visualizer
interface WalletTransaction {
  id: string;
  amount: number;
  type: string;
  timestamp: number;
  status: 'pending' | 'completed' | 'failed'; // Only these three statuses are supported
  source?: string;
  destination?: string;
}
function LocalTransactionVisualizer({ transactions = [] }: { transactions?: any[] }) {
  return (
    <div className="space-y-2 mt-4">
      {transactions.map((tx) => (
        <div
          key={tx.id}
          className={`p-3 rounded-lg flex justify-between items-center ${
            tx.type === 'bUy' || tx.type === 'deposit' || tx.type === 'receive'
              ? 'bg-green-950/30 border border-green-600/30'
              : 'bg-red-950/30 border border-red-600/30'
          }`}
        >
          <div className="flex items-center gap-2">
            {tx.type === 'bUy' || tx.type === 'deposit' || tx.type === 'receive' ? (
              <ArrowDownRight className="h-5 w-5 text-green-500" />
            ) : (
              <ArrowUpRight className="h-5 w-5 text-red-500" />
            )}
            <div>
              <p className="text-sm font-medium">{tx.type}</p>
              <p className="text-xs text-gray-400">
                {new Date(tx.timestamp).toLocaleString()}
              </p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm font-bold">
              {tx.type === 'bUy' || tx.type === 'deposit' || tx.type === 'receive'
                ? '+'
                : '-'}
              {formatTRU(tx.amount || 0) + ' trU'}
            </p>
            <Badge
              variant={tx.status === 'completed' ? 'default' : 'outline'}
              className={tx.status === 'completed' ? 'bg-green-600/20 text-green-400 hover:bg-green-600/30' : ''}
            >
              {tx.status}
            </Badge>
          </div>
        </div>
      ))}
      {transactions.length === 0 && (
        <div className="text-center p-4 bg-gray-900/50 rounded-lg border border-gray-800">
          <p className="text-gray-400 text-sm">No transactions yet</p>
        </div>
      )}
    </div>
  );
}
// Balance Section component
// ITUms Collection Component
export function MyITUmsCollection({ ghost }: { ghost: any }) {
  const {
    walletItems,
    activateItem,
    deactivateItem,
    removeItem,
    convertNUvaToUMatter,
    convertNUvaToBattery,
    transferNUvaToDevice,
    sendNUvaToUser,
    executeConvert
  } = useWalletStore();
  const [activeTab, setActiveTab] = useState<'nuva' | 'flux' | 'marketplace' | 'cosmic'>('nuva');
  const [selectedItem, setSelectedItem] = useState<WalletItem | null>(null);
  const [isConverting, setIsConverting] = useState(false);
  const [conversionAmount, setConversionAmount] = useState(1);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const filteredItems = useMemo(() => {
    return walletItems.filter((item: WalletItem) => item.type === activeTab);
  }, [walletItems, activeTab]);
  const handleActivate = (item: WalletItem) => {
    // Use type assertion to fix TypeScript error
    if ((item as any).isActive) {
      deactivateItem(item.id);
      toast({
        title: "Item Deactivated",
        description: `${item.name} is now inactive`,
        variant: "default",
      });
    } else {
      activateItem(item.id);
      toast({
        title: "Item Activated",
        description: `${item.name} is now active in your nUniverse`,
        variant: "default",
      });
    }
  };
  const handleItemClick = (item: WalletItem) => {
    setSelectedItem(item);
    setIsDialogOpen(true);
  };
  const handleRemove = (itemId: string) => {
    removeItem(itemId);
    setIsDialogOpen(false);
    toast({
      title: "Item Removed",
      description: "The item has been removed from your collection",
      variant: "default",
    });
  };
  const handleConvertNUva = async () => {
    if (isConverting) return;
    setIsConverting(true);
    try {
      const convertedAmount = await convertNUvaToUMatter(conversionAmount);
      toast({
        title: "Conversion Successful",
        description: `${conversionAmount} nUva converted to ${convertedAmount} UMatter`,
        variant: "default",
      });
      setIsDialogOpen(false);
    } catch (error) {
      console.error("Error converting nUva:", error);
      toast({
        title: "Conversion Failed",
        description: "There was an error converting your nUva energy",
        variant: "destructive",
      });
    } finally {
      setIsConverting(false);
    }
  };
  return (
    <div className="space-y-6">
      <Tabs defaultValue="nuva" className="w-full" onValueChange={(value) => setActiveTab(value as any)}>
        <TabsList className="grid grid-cols-4 w-full bg-zinc-900">
          <TabsTrigger value="nuva" className="data-[state=active]:bg-blue-900/60">
            <Sparkles className="h-4 w-4 mr-2" /> nUva
          </TabsTrigger>
          <TabsTrigger value="flux" className="data-[state=active]:bg-indigo-900/60">
            <Zap className="h-4 w-4 mr-2" /> FLUx
          </TabsTrigger>
          <TabsTrigger value="marketplace" className="data-[state=active]:bg-purple-900/60">
            <Package className="h-4 w-4 mr-2" /> Market
          </TabsTrigger>
          <TabsTrigger value="cosmic" className="data-[state=active]:bg-cyan-900/60">
            <Star className="h-4 w-4 mr-2" /> Cosmic
          </TabsTrigger>
        </TabsList>
        <TabsContent value="nuva" className="mt-4">
          <div className="rounded-lg bg-blue-950/30 border border-blue-500/20 p-4 mb-4">
            <h3 className="font-medium text-blue-400 flex items-center">
              <Sparkles className="h-4 w-4 mr-2" /> nUva Energy
            </h3>
            <p className="text-sm text-blue-300/70 mt-1">
              Convertible cosmic energy that powers your device and the nU hUman Universe
            </p>
          </div>
          <ScrollArea className="h-[300px] pr-4">
            {filteredItems.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {filteredItems.map((item: WalletItem) => (
                  <div
                    key={item.id}
                    onClick={() => handleItemClick(item)}
                    className="rounded-lg bg-zinc-900 border border-zinc-800 p-4 cursor-pointer transition-all hover:border-blue-500/50 hover:bg-zinc-800"
                  >
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium text-blue-400">{item.name}</h3>
                      <Badge variant={(item as any).isActive ? "default" : "outline"} className={(item as any).isActive ? "bg-blue-800/40 text-blue-300" : ""}>
                        {(item as any).isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-400 mt-1">{item.description}</p>
                    <div className="mt-3 flex justify-between items-center">
                      <p className="text-sm text-blue-300 font-medium">Power: {(item as any).power} nUva</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleActivate(item);
                        }}
                      >
                        {(item as any).isActive ? "Deactivate" : "Activate"}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-10 px-4 bg-zinc-900/50 rounded-lg border border-zinc-800">
                <div className="flex justify-center mb-4">
                  <Sparkles className="h-8 w-8 text-blue-400 opacity-50" />
                </div>
                <h3 className="text-lg font-medium text-gray-300">No nUva Energy Items</h3>
                <p className="text-sm text-gray-400 mt-2">
                  Visit "The FLUx" to acquire nUva energy items for your collection
                </p>
              </div>
            )}
          </ScrollArea>
        </TabsContent>
        <TabsContent value="flux" className="mt-4">
          <div className="rounded-lg bg-indigo-950/30 border border-indigo-500/20 p-4 mb-4">
            <h3 className="font-medium text-indigo-400 flex items-center">
              <Zap className="h-4 w-4 mr-2" /> FLUx Items
            </h3>
            <p className="text-sm text-indigo-300/70 mt-1">
              Special items acquired from "The FLUx" - the cosmic energy marketplace
            </p>
          </div>
          <ScrollArea className="h-[300px] pr-4">
            {filteredItems.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {filteredItems.map((item: WalletItem) => (
                  <div
                    key={item.id}
                    onClick={() => handleItemClick(item)}
                    className="rounded-lg bg-zinc-900 border border-zinc-800 p-4 cursor-pointer transition-all hover:border-indigo-500/50 hover:bg-zinc-800"
                  >
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium text-indigo-400">{item.name}</h3>
                      <Badge variant={(item as any).isActive ? "default" : "outline"} className={(item as any).isActive ? "bg-indigo-800/40 text-indigo-300" : ""}>
                        {(item as any).isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-400 mt-1">{item.description}</p>
                    <div className="mt-3 flex justify-between items-center">
                      <p className="text-sm text-indigo-300 font-medium">Type: {(item as any).category || "Basic"}</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleActivate(item);
                        }}
                      >
                        {(item as any).isActive ? "Deactivate" : "Activate"}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-10 px-4 bg-zinc-900/50 rounded-lg border border-zinc-800">
                <div className="flex justify-center mb-4">
                  <Zap className="h-8 w-8 text-indigo-400 opacity-50" />
                </div>
                <h3 className="text-lg font-medium text-gray-300">No FLUx Items</h3>
                <p className="text-sm text-gray-400 mt-2">
                  Explore "The FLUx" to acquire special items for your collection
                </p>
              </div>
            )}
          </ScrollArea>
        </TabsContent>
        <TabsContent value="marketplace" className="mt-4">
          <div className="rounded-lg bg-purple-950/30 border border-purple-500/20 p-4 mb-4">
            <h3 className="font-medium text-purple-400 flex items-center">
              <Package className="h-4 w-4 mr-2" /> Marketplace Items
            </h3>
            <p className="text-sm text-purple-300/70 mt-1">
              Items you've acquired through the P2P marketplace from other users
            </p>
          </div>
          <ScrollArea className="h-[300px] pr-4">
            {filteredItems.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {filteredItems.map((item: WalletItem) => (
                  <div
                    key={item.id}
                    onClick={() => handleItemClick(item)}
                    className="rounded-lg bg-zinc-900 border border-zinc-800 p-4 cursor-pointer transition-all hover:border-purple-500/50 hover:bg-zinc-800"
                  >
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium text-purple-400">{item.name}</h3>
                      <Badge variant={(item as any).isActive ? "default" : "outline"} className={(item as any).isActive ? "bg-purple-800/40 text-purple-300" : ""}>
                        {(item as any).isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-400 mt-1">{item.description}</p>
                    <div className="mt-3 flex justify-between items-center">
                      <p className="text-sm text-purple-300 font-medium">Acquired: {new Date((item as any).acquiredAt).toLocaleDateString()}</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleActivate(item);
                        }}
                      >
                        {(item as any).isActive ? "Deactivate" : "Activate"}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-10 px-4 bg-zinc-900/50 rounded-lg border border-zinc-800">
                <div className="flex justify-center mb-4">
                  <Package className="h-8 w-8 text-purple-400 opacity-50" />
                </div>
                <h3 className="text-lg font-medium text-gray-300">No Marketplace Items</h3>
                <p className="text-sm text-gray-400 mt-2">
                  Visit the P2P Marketplace to acquire items from other users
                </p>
              </div>
            )}
          </ScrollArea>
        </TabsContent>
        <TabsContent value="cosmic" className="mt-4">
          <div className="rounded-lg bg-cyan-950/30 border border-cyan-500/20 p-4 mb-4">
            <h3 className="font-medium text-cyan-400 flex items-center">
              <Star className="h-4 w-4 mr-2" /> Cosmic Items
            </h3>
            <p className="text-sm text-cyan-300/70 mt-1">
              Rare items from cosmic events, missions, and special opportunities
            </p>
          </div>
          <ScrollArea className="h-[300px] pr-4">
            {filteredItems.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {filteredItems.map((item: WalletItem) => (
                  <div
                    key={item.id}
                    onClick={() => handleItemClick(item)}
                    className="rounded-lg bg-zinc-900 border border-zinc-800 p-4 cursor-pointer transition-all hover:border-cyan-500/50 hover:bg-zinc-800"
                  >
                    <div className="flex justify-between items-start">
                      <h3 className="font-medium text-cyan-400">{item.name}</h3>
                      <Badge variant={(item as any).isActive ? "default" : "outline"} className={(item as any).isActive ? "bg-cyan-800/40 text-cyan-300" : ""}>
                        {(item as any).isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-400 mt-1">{item.description}</p>
                    <div className="mt-3 flex justify-between items-center">
                      <p className="text-sm text-cyan-300 font-medium">
                        {(item as any).expiresAt ? `Expires: ${new Date((item as any).expiresAt).toLocaleDateString()}` : "Never expires"}
                      </p>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-7 text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleActivate(item);
                        }}
                      >
                        {(item as any).isActive ? "Deactivate" : "Activate"}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-10 px-4 bg-zinc-900/50 rounded-lg border border-zinc-800">
                <div className="flex justify-center mb-4">
                  <Star className="h-8 w-8 text-cyan-400 opacity-50" />
                </div>
                <h3 className="text-lg font-medium text-gray-300">No Cosmic Items</h3>
                <p className="text-sm text-gray-400 mt-2">
                  Complete cosmic missions and special events to earn rare items
                </p>
              </div>
            )}
          </ScrollArea>
        </TabsContent>
      </Tabs>
      {/* Item Detail Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="bg-zinc-950 border border-zinc-800 text-white max-w-lg">
          {selectedItem && (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center">
                  {selectedItem.type === 'nuva' && <Sparkles className="h-5 w-5 mr-2 text-blue-400" />}
                  {selectedItem.type === 'flux' && <Zap className="h-5 w-5 mr-2 text-indigo-400" />}
                  {selectedItem.type === 'marketplace' && <Package className="h-5 w-5 mr-2 text-purple-400" />}
                  {selectedItem.type === 'cosmic' && <Star className="h-5 w-5 mr-2 text-cyan-400" />}
                  {selectedItem.name}
                </DialogTitle>
                <DialogDescription className="text-gray-400">
                  {selectedItem.description}
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-3">
                  <div className="rounded-lg bg-zinc-900 p-3">
                    <p className="text-xs text-gray-400">Type</p>
                    <p className="text-sm font-medium text-white">{selectedItem.type.charAt(0).toUpperCase() + selectedItem.type.slice(1)}</p>
                  </div>
                  <div className="rounded-lg bg-zinc-900 p-3">
                    <p className="text-xs text-gray-400">Status</p>
                    <p className="text-sm font-medium text-white">{(selectedItem as any).isActive ? "Active" : "Inactive"}</p>
                  </div>
                  <div className="rounded-lg bg-zinc-900 p-3">
                    <p className="text-xs text-gray-400">Acquired</p>
                    <p className="text-sm font-medium text-white">{new Date((selectedItem as any).acquiredAt).toLocaleDateString()}</p>
                  </div>
                  <div className="rounded-lg bg-zinc-900 p-3">
                    <p className="text-xs text-gray-400">Category</p>
                    <p className="text-sm font-medium text-white">{(selectedItem as any).category || "General"}</p>
                  </div>
                </div>
                {(selectedItem as any).power && selectedItem.type === 'nuva' && (
                  <div className="space-y-4">
                    <div className="rounded-lg bg-blue-950/30 border border-blue-500/20 p-4">
                      <h3 className="text-sm font-medium text-blue-400 flex items-center">
                        <Sparkles className="h-4 w-4 mr-2" /> nUva Energy Conversion
                      </h3>
                      <p className="text-xs text-blue-300/70 mt-1">
                        Convert nUva energy to UMatter for ecosystem utilities
                      </p>
                      <div className="mt-3 flex items-center gap-2">
                        <input
                          type="number"
                          value={conversionAmount}
                          onChange={(e) => setConversionAmount(Math.min(Number(e.target.value), (selectedItem as any).power || 0))}
                          min={1}
                          max={(selectedItem as any).power}
                          className="w-16 rounded bg-blue-950/50 border border-blue-500/30 px-2 py-1 text-sm"
                        />
                        <span className="text-xs text-blue-300">of {(selectedItem as any).power} nUva</span>
                        <Button
                          size="sm"
                          onClick={handleConvertNUva}
                          disabled={isConverting || conversionAmount <= 0 || conversionAmount > ((selectedItem as any).power || 0)}
                          className="ml-auto bg-blue-700 hover:bg-blue-600 text-white"
                        >
                          Convert to UMatter
                        </Button>
                      </div>
                    </div>
                    <div className="rounded-lg bg-emerald-950/30 border border-emerald-500/20 p-4">
                      <h3 className="text-sm font-medium text-emerald-400 flex items-center">
                        <Battery className="h-4 w-4 mr-2" /> Charge Battery
                      </h3>
                      <p className="text-xs text-emerald-300/70 mt-1">
                        Use nUva energy to recharge your device's battery
                      </p>
                      <div className="mt-3 flex items-center gap-2">
                        <input
                          type="number"
                          value={conversionAmount}
                          onChange={(e) => setConversionAmount(Math.min(Number(e.target.value), (selectedItem as any).power || 0))}
                          min={1}
                          max={(selectedItem as any).power}
                          className="w-16 rounded bg-emerald-950/50 border border-emerald-500/30 px-2 py-1 text-sm"
                        />
                        <span className="text-xs text-emerald-300">of {(selectedItem as any).power} nUva</span>
                        <Button
                          size="sm"
                          onClick={() => {
                            if (isConverting) return;
                            setIsConverting(true);
                            convertNUvaToBattery(conversionAmount)
                              .then((batteryPercent: number) => {
                                toast({
                                  title: "Battery Charged",
                                  description: `${conversionAmount} nUva converted to ${batteryPercent}% battery charge`,
                                  variant: "default",
                                });
                                setIsDialogOpen(false);
                              })
                              .catch((error: Error) => {
                                console.error("Error charging battery:", error);
                                toast({
                                  title: "Charging Failed",
                                  description: "There was an error charging your battery",
                                  variant: "destructive",
                                });
                              })
                              .finally(() => {
                                setIsConverting(false);
                              });
                          }}
                          disabled={isConverting || conversionAmount <= 0 || conversionAmount > ((selectedItem as any).power || 0)}
                          className="ml-auto bg-emerald-700 hover:bg-emerald-600 text-white"
                        >
                          Charge Battery
                        </Button>
                      </div>
                    </div>
                    <div className="rounded-lg bg-purple-950/30 border border-purple-500/20 p-4">
                      <h3 className="text-sm font-medium text-purple-400 flex items-center">
                        <Send className="h-4 w-4 mr-2" /> Transfer nUva
                      </h3>
                      <p className="text-xs text-purple-300/70 mt-1">
                        Send nUva energy to another user or device
                      </p>
                      <div className="mt-3 space-y-3">
                        <div className="flex items-center gap-2">
                          <input
                            type="number"
                            value={conversionAmount}
                            onChange={(e) => setConversionAmount(Math.min(Number(e.target.value), (selectedItem as any).power || 0))}
                            min={1}
                            max={(selectedItem as any).power}
                            className="w-16 rounded bg-purple-950/50 border border-purple-500/30 px-2 py-1 text-sm"
                          />
                          <span className="text-xs text-purple-300">of {(selectedItem as any).power} nUva</span>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => {
                              if (isConverting) return;
                              // Get recipient user ID from prompt
                              const userId = prompt("Enter recipient's user ID");
                              if (!userId) return;
                              setIsConverting(true);
                              sendNUvaToUser(conversionAmount, userId)
                                .then((success: boolean) => {
                                  if (success) {
                                    toast({
                                      title: "nUva Sent",
                                      description: `${conversionAmount} nUva sent to user ${userId}`,
                                      variant: "default",
                                    });
                                    setIsDialogOpen(false);
                                  } else {
                                    throw new Error("Transfer failed");
                                  }
                                })
                                .catch((error: Error) => {
                                  console.error("Error sending nUva:", error);
                                  toast({
                                    title: "Transfer Failed",
                                    description: "There was an error sending nUva to the user",
                                    variant: "destructive",
                                  });
                                })
                                .finally(() => {
                                  setIsConverting(false);
                                });
                            }}
                            disabled={isConverting || conversionAmount <= 0 || conversionAmount > ((selectedItem as any).power || 0)}
                            className="bg-purple-700 hover:bg-purple-600 text-white"
                          >
                            Send to User
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => {
                              if (isConverting) return;
                              // Get device ID from prompt
                              const deviceId = prompt("Enter device ID");
                              if (!deviceId) return;
                              setIsConverting(true);
                              transferNUvaToDevice(conversionAmount, deviceId)
                                .then((success: boolean) => {
                                  if (success) {
                                    toast({
                                      title: "nUva Transferred",
                                      description: `${conversionAmount} nUva transferred to device ${deviceId}`,
                                      variant: "default",
                                    });
                                    setIsDialogOpen(false);
                                  } else {
                                    throw new Error("Transfer failed");
                                  }
                                })
                                .catch((error: Error) => {
                                  console.error("Error transferring nUva:", error);
                                  toast({
                                    title: "Transfer Failed",
                                    description: "There was an error transferring nUva to the device",
                                    variant: "destructive",
                                  });
                                })
                                .finally(() => {
                                  setIsConverting(false);
                                });
                            }}
                            disabled={isConverting || conversionAmount <= 0 || conversionAmount > ((selectedItem as any).power || 0)}
                            className="bg-indigo-700 hover:bg-indigo-600 text-white"
                          >
                            Send to Device
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                <div className="flex justify-between pt-3">
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => handleRemove(selectedItem.id)}
                  >
                    Remove Item
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    onClick={() => handleActivate(selectedItem)}
                    className="bg-blue-700 hover:bg-blue-600 text-white"
                  >
                    {(selectedItem as any).isActive ? "Deactivate" : "Activate"}
                  </Button>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
function BalanceSection({ ghost, syncWithNetwork }: { ghost: any, syncWithNetwork: () => void }) {
  const { balance } = useWalletStore();
  const [isProcessing, setIsProcessing] = useState(false);
  // Enhanced deposit function with better error handling
  const handleDeposit = async () => {
    if (isProcessing) return;
    setIsProcessing(true);
    try {
      // Sync with network before deposit
      let syncSuccess = false;
      if (ghost) {
        try {
          if (ghost.bot) {
            await ghost.bot.syncWalletAndNetwork();
            syncSuccess = true;
          } else {
            console.warn('[BalanceSection] GhostBot not available for sync during deposit');
          }
        } catch (walletErr) {
          console.warn('[BalanceSection] Wallet sync warning (deposit):', walletErr);
        }
      }
      // Always sync network state regardless of GhostBot availability
      syncWithNetwork();
      const amount = 1;
      const result = await useWalletStore.getState().executeBuy(amount, "Deposit from external source");
      if (result) {
        toast({
          title: "Deposit Successful",
          description: `${amount} trU added to your wallet`,
          variant: "default",
        });
        // Refresh to show updated balance
        syncWithNetwork();
        // Notify the network about the deposit
        window.dispatchEvent(new CustomEvent("deposit_completed", {
          detail: { amount, syncSuccess }
        }));
      } else {
        toast({
          title: "Deposit Processing",
          description: "Deposit initiated, waiting for network confirmation",
          variant: "default",
        });
        // Still sync with network to ensure UI is up-to-date
        syncWithNetwork();
      }
    } catch (error) {
      console.error("[BalanceSection] Deposit error:", error);
      // Even in error case, try to update network state
      syncWithNetwork();
      toast({
        title: "Deposit Status Uncertain",
        description: "Transaction submitted but status unknown. Please check your balance.",
        variant: "default", // Changed from destructive to prevent user alarm
      });
    } finally {
      setIsProcessing(false);
    }
  };
  // Enhanced send function with better error handling
  const handleSend = async () => {
    if (isProcessing) return;
    setIsProcessing(true);
    try {
      // Check balance first to avoid unnecessary processing
      const amount = 0.5;
      if (balance.TRU < amount) {
        toast({
          title: "Insufficient Balance",
          description: `You need at least ${amount} trU to complete this transaction`,
          variant: "default", // Changed from destructive
        });
        setIsProcessing(false);
        return;
      }
      // Sync with network before sending
      let syncSuccess = false;
      if (ghost) {
        try {
          if (ghost.bot) {
            await ghost.bot.syncWalletAndNetwork();
            syncSuccess = true;
          } else {
            console.warn('[BalanceSection] GhostBot not available for sync during send');
          }
        } catch (walletErr) {
          console.warn('[BalanceSection] Wallet sync warning (withdraw):', walletErr);
        }
      }
      // Always sync network state regardless of GhostBot availability
      syncWithNetwork();
      const result = await useWalletStore.getState().executeSell(amount, "Send to external wallet");
      if (result) {
        toast({
          title: "Send Successful",
          description: `${amount} trU sent successfully`,
          variant: "default",
        });
        // Refresh to show updated balance
        syncWithNetwork();
        // Notify network about the send operation
        window.dispatchEvent(new CustomEvent("send_completed", {
          detail: { amount, syncSuccess }
        }));
      } else {
        toast({
          title: "Send Processing",
          description: "Send operation initiated, waiting for network confirmation",
          variant: "default",
        });
        // Still sync with network to ensure UI is up-to-date
        syncWithNetwork();
      }
    } catch (error) {
      console.error("[BalanceSection] Send error:", error);
      // Even in error case, try to update network state
      syncWithNetwork();
      toast({
        title: "Send Status Uncertain",
        description: "Transaction submitted but status unknown. Please check your balance.",
        variant: "default", // Changed from destructive to prevent user alarm
      });
    } finally {
      setIsProcessing(false);
    }
  };
  return (
    <div className="relative overflow-hidden rounded-xl bg-black p-6 shadow-2xl mt-6">
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:20px_20px]" />
      <div className="relative z-10">
        <h2 className="text-2xl font-bold text-white">Your Balance</h2>
        <div className="mt-8 flex items-center gap-x-3">
          <div className="rounded-full bg-indigo-500/20 p-2">
            <Zap className="h-6 w-6 text-indigo-400" />
          </div>
          <div>
            <p className="text-sm text-gray-400">Available trU</p>
            <p className="text-3xl font-bold text-white">
              {formatTRU(balance.TRU)} <span className="text-lg font-semibold">trU</span>
            </p>
          </div>
        </div>
        <div className="mt-6 flex items-center gap-x-3">
          <div className="rounded-full bg-purple-500/20 p-2">
            <Battery className="h-6 w-6 text-purple-400" />
          </div>
          <div>
            <p className="text-sm text-gray-400">Staked Balance (SBU)</p>
            <p className="text-2xl font-bold text-white">
              {formatTRU(balance.SBU)} <span className="text-sm font-semibold">SBU</span>
            </p>
          </div>
        </div>
        <div className="mt-6 flex items-center gap-x-3">
          <div className="rounded-full bg-cyan-500/20 p-2">
            <Shield className="h-6 w-6 text-cyan-400" />
          </div>
          <div>
            <p className="text-sm text-gray-400">UMatter</p>
            <p className="text-2xl font-bold text-white">
              {balance.UMATTER} <span className="text-sm font-semibold">Ü</span>
            </p>
          </div>
        </div>
        <div className="mt-6 flex items-center gap-x-3">
          <div className="rounded-full bg-blue-500/20 p-2">
            <Sparkles className="h-6 w-6 text-blue-400" />
          </div>
          <div>
            <p className="text-sm text-gray-400">nUva Energy</p>
            <p className="text-2xl font-bold text-white">
              {balance.NUVA} <span className="text-sm font-semibold">nV</span>
            </p>
          </div>
        </div>
        <div className="mt-8 flex gap-4">
          <Button
            onClick={handleDeposit}
            className="flex-1 gap-1 bg-indigo-600 hover:bg-indigo-700 text-white"
            disabled={isProcessing}
          >
            <ArrowDownRight className="h-4 w-4" />
            {isProcessing ? "Processing..." : "Deposit"}
          </Button>
          <Button
            onClick={handleSend}
            className="flex-1 gap-1 bg-indigo-600 hover:bg-indigo-700 text-white"
            disabled={isProcessing || balance.TRU < 0.5}
          >
            <ArrowUpRight className="h-4 w-4" />
            {isProcessing ? "Processing..." : "Send"}
          </Button>
        </div>
      </div>
    </div>
  );
}
export default function WalletPage() {
  const [location, setLocation] = useLocation();
  const { balance, transactions, syncWithNetwork } = useWalletStore();
  const [activeTab, setActiveTab] = useState("balance");
  const ghost = useGhost();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showBatteryDrainAnimation, setShowBatteryDrainAnimation] = useState(false);
  const [openModal, setOpenModal] = useState<'lock' | 'burn' | 'donate' | 'convert' | null>(null);
  const [batteryDrainAmount, setBatteryDrainAmount] = useState(0);
  const [batteryLevel, setBatteryLevel] = useState<number | null>(null);
  const [isCharging, setIsCharging] = useState<boolean | null>(null);
  const [isVerified, setIsVerified] = useState(false);
  // Add market trade details listener
  useEffect(() => {
    window.addEventListener('market_update', (e: any) =>
      console.log(`[WalletPage] Market update: trU/UMatter=${e.detail.truPerUmatter}, sbU/trU=${e.detail.sbuPerTru}`)
    );
    return () => {
      window.removeEventListener('market_update', (e: any) => {});
    };
  }, []);
  // Get network status from SyncBot and GhostBot
  const [networkStatus, setNetworkStatus] = useState<{
    peers: number;
    health: number;
    lastSync: number;
  }>({
    peers: 0,
    health: 0,
    lastSync: 0
  });
  // Sync with network using both SyncBot and GhostBot - with enhanced error handling
  // and more robust retry mechanism
  const refreshBalance = useCallback(async () => {
    if (isRefreshing || !ghost) return;
    console.log("[Wallet] Refreshing wallet balance and syncing with network...");
    setIsRefreshing(true);
    try {
      // Check verification status first from multiple sources
      const ghostVerified = localStorage.getItem("u-ghost-verified") === "true";
      const syncbotVerified = localStorage.getItem("u-SyncBot-verified") === "true";
      const sessionVerified = sessionStorage.getItem("verified") === "true";
      const nutshellVerified = localStorage.getItem("nutshell-verified") === "true";
      const isUserVerified = ghostVerified || syncbotVerified || sessionVerified || nutshellVerified;
      setIsVerified(isUserVerified);
      if (!isUserVerified) {
        console.warn("[Wallet] User verification not found in any storage location");
        toast({
          title: "Verification Required",
          description: "Please complete nU verification to access full wallet features",
          variant: "destructive",
        });
      } else {
        console.log("[Wallet] User verification confirmed:", {
          ghostVerified,
          syncbotVerified,
          sessionVerified,
          nutshellVerified
        });
      }
      // First attempt to get latest network info from SyncBot if available
      // This is a new step to increase the robustness of network info gathering
      let syncBotInfo = null;
      let networkInfo = null;
      try {
        // Dynamic import to avoid circular references
        const syncBotModule = await import('../lib/sync-bot');
        // Handle different module formats
        const SyncBot = 'default' in syncBotModule ? syncBotModule.default : syncBotModule.SyncBot;
        if (SyncBot) {
          // Try to get existing instance
          // Use type assertion to fix constructor issue
          const syncBot = new (SyncBot as any)(ghost.bot);
          syncBotInfo = await syncBot.getNetworkStatus();
          console.log("[Wallet] Retrieved network info from SyncBot:", syncBotInfo);
        }
      } catch (superSyncError) {
        console.warn("[Wallet] Could not get network info from SyncBot:", superSyncError);
        // Continue with GhostBot as fallback
      }
      // Then try to get network info from GhostBot
      try {
        // Check if GhostBot is available
        if (!ghost.bot) {
          throw new Error("GhostBot not available, check initialization");
        }
        networkInfo = await ghost.bot.getNetworkInfo();
        console.log("[Wallet] Retrieved network info from GhostBot:", networkInfo);
        setNetworkStatus({
          peers: networkInfo.peers,
          health: networkInfo.networkHealth,
          lastSync: networkInfo.lastUpdate !== undefined ? networkInfo.lastUpdate : Date.now()
        });
      } catch (ghostBotError) {
        console.error("[Wallet] Error fetching network info from GhostBot:", ghostBotError);
        // Only use real data, no fallbacks
        if (syncBotInfo && syncBotInfo.peers !== undefined && syncBotInfo.health !== undefined) {
          // Only use the data if both required values are available
          setNetworkStatus({
            peers: syncBotInfo.peers,
            health: syncBotInfo.health,
            lastSync: syncBotInfo.lastUpdate !== undefined ? syncBotInfo.lastUpdate : Date.now()
          });
        } else {
          console.error("[Wallet] Missing essential network data from SyncBot");
          toast({
            title: "Network Data Unavailable",
            description: "Unable to retrieve real-time network metrics. Please sync with the network.",
            variant: "destructive"
          });          // No fallbacks - only use real data
          setNetworkStatus({
            peers: -1, // Special value indicating unavailable
            health: -1, // Special value indicating unavailable
            lastSync: -1 // Special value indicating never synced
          });
        }
      }
      // Perform the complete wallet sync process with better error isolation
      try {
        // First, apply the SyncBot tax for synchronization
        setBatteryDrainAmount(2); // Standard 2% tax from SyncBot
        setShowBatteryDrainAnimation(true);
        // Small delay to show animation
        await new Promise(resolve => setTimeout(resolve, 1000));
        // Try multiple sync approaches with individual error handling
        let syncSuccess = false;
        // 1. First try GhostBot's direct sync
        try {
          if (ghost.bot && typeof ghost.bot.syncWalletAndNetwork === 'function') {
            await ghost.bot.syncWalletAndNetwork();
            syncSuccess = true;
            console.log("[Wallet] Direct GhostBot sync successful");
          } else {
            console.warn('[Wallet] GhostBot not available for direct sync');
          }
        } catch (ghostSyncErr) {
          console.warn('[Wallet] GhostBot sync warning:', ghostSyncErr);
          // Continue to next approach
        }
        // 2. If GhostBot sync failed, try SyncBot's approach
        if (!syncSuccess) {
          try {
            const syncBotModule = await import('../lib/sync-bot');
            // Handle different module formats
            const SyncBot = 'default' in syncBotModule ? syncBotModule.default : syncBotModule.SyncBot;
            if (SyncBot) {
              // Use type assertion to fix constructor issue
              const syncBot = new (SyncBot as any)(ghost.bot);
              await syncBot.syncNetwork();
              syncSuccess = true;
              console.log("[Wallet] SyncBot network sync successful");
            }
          } catch (superSyncErr) {
            console.warn('[Wallet] SyncBot sync warning:', superSyncErr);
            // Continue to fallback
          }
        }
        // 3. Always sync network state through Zustand store as a fallback
        syncWithNetwork();
        console.log("[Wallet] Store-based network sync completed");
        // Show appropriate toast based on sync success level
        if (syncSuccess) {
          toast({
            title: "Wallet Synced with Network",
            description: networkInfo?.peers !== undefined ?
              `Connected with ${networkInfo.peers} peers at ${networkInfo.networkHealth !== undefined ? networkInfo.networkHealth : 'unknown'}% health` :
              (syncBotInfo?.peers !== undefined ?
               `Connected with ${syncBotInfo.peers} peers at ${syncBotInfo.health !== undefined ? syncBotInfo.health : 'unknown'}% health` :
               "Connected to the network. Data integrity maintained."),
            variant: "default",
          });
        } else {
          toast({
            title: "Partial Sync Completed",
            description: "Wallet data updated with limited network connectivity",
            variant: "default",
          });
        }
        // Notify network about sync event with SyncBot tax
        window.dispatchEvent(new CustomEvent("wallet_synced", {
          detail: {
            batteryDonation: 2,
            verified: isUserVerified,
            success: syncSuccess,
            peers: networkInfo?.peers !== undefined
              ? networkInfo.peers
              : (syncBotInfo?.peers !== undefined ? syncBotInfo.peers : -1), // -1 indicates no valid peer data
            timestamp: Date.now()
          }
        }));
        // Hide the animation after a few seconds
        setTimeout(() => setShowBatteryDrainAnimation(false), 3000);
      } catch (syncError) {
        console.error("[Wallet] Complete sync process error:", syncError);
        // Ensure we still sync the network state
        syncWithNetwork();
        toast({
          title: "Limited Sync Completed",
          description: "Local wallet data updated, network sync limited",
          variant: "default", // Changed from destructive to avoid user alarm
        });
        // Hide the animation after a moment
        setTimeout(() => setShowBatteryDrainAnimation(false), 1500);
      }
    } catch (error) {
      console.error("[Wallet] Critical error in refresh process:", error);
      // Still attempt to sync network state as last resort
      syncWithNetwork();
      toast({
        title: "Refresh Completed with Warnings",
        description: "Wallet updated with local data only",
        variant: "default", // Changed from destructive to avoid user alarm
      });
      setTimeout(() => setShowBatteryDrainAnimation(false), 1000);
    } finally {
      setIsRefreshing(false);
    }
  }, [ghost, syncWithNetwork, isRefreshing]);
  useEffect(() => {
    // Check if the user is verified
    const checkVerificationStatus = () => {
      // Check localStorage and sessionStorage for verification from both GhostBot and SyncBot
      const ghostVerified = localStorage.getItem("u-ghost-verified") === "true";
      const syncbotVerified = localStorage.getItem("u-SyncBot-verified") === "true";
      const sessionVerified = sessionStorage.getItem("verified") === "true";
      console.log("Verification status:", {
        ghostVerified,
        syncbotVerified,
        sessionVerified
      });
      setIsVerified(ghostVerified || syncbotVerified || sessionVerified);
      if (!ghostVerified && !syncbotVerified && !sessionVerified) {
        toast({
          title: "Verification Required",
          description: "Please complete the nU verification process to fully interact with the wallet.",
          variant: "destructive",
        });
      }
    };
    checkVerificationStatus();
    if (ghost && ghost.bot && !isRefreshing) {
      refreshBalance();
    }
    const getBatteryData = async () => {
      try {
        // Try to get real battery data from the Battery API
        if ('getBattery' in navigator) {
          const battery = await (navigator as any).getBattery();
          setBatteryLevel(battery.level * 100);
          setIsCharging(battery.charging);
          battery.addEventListener('levelchange', () => setBatteryLevel(battery.level * 100));
          battery.addEventListener('chargingchange', () => setIsCharging(battery.charging));
          console.log(`[Wallet] Using real battery data: ${battery.level * 100}%, charging: ${battery.charging}`);
          return () => {
            battery.removeEventListener('levelchange', () => {});
            battery.removeEventListener('chargingchange', () => {});
          };
        }
        // Try to get real battery data from GhostBot as a second option
        else if (ghost && ghost.bot) {
          const batteryInfo = await ghost.bot.getBatteryInfo();
          if (batteryInfo && batteryInfo.level !== undefined && batteryInfo.charging !== undefined) {
            setBatteryLevel(batteryInfo.level * 100);
            setIsCharging(batteryInfo.charging);
            console.log(`[Wallet] Using GhostBot battery data: ${batteryInfo.level * 100}%, charging: ${batteryInfo.charging}`);
            return;
          }
        }
        // If we reach here, we couldn't get real battery data
        console.error("[Wallet] Could not access real battery data from any source");
        toast({
          title: "Battery Data Unavailable",
          description: "Unable to access real-time battery data. Some features may be limited.",
          variant: "destructive"
        });
        // Keep battery values as null to indicate they're unavailable
      } catch (error) {
        console.error("[Wallet] Error accessing battery data:", error);
        toast({
          title: "Battery Data Error",
          description: "An error occurred while accessing battery data. Some features may be limited.",
          variant: "destructive"
        });
        // Keep battery values as null to indicate they're unavailable
      }
    };
    getBatteryData();
  }, []);
  return (
    <>
      <motion.div className="container mx-auto p-4 max-w-4xl mb-8 bg-black text-white">
        {/* Verification status banner */}
        {!isVerified && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-yellow-900/30 border border-yellow-600/30 rounded-lg flex items-center"
          >
            <div className="w-10 h-10 mr-4 rounded-full bg-yellow-500/20 flex items-center justify-center">
              <Shield className="h-5 w-5 text-yellow-500" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-yellow-400">
                Verification Required
              </h3>
              <p className="text-sm text-yellow-400/70">
                Complete verification to access all wallet features
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="ml-2 border-yellow-500/50 text-yellow-500 hover:bg-yellow-500/10"
              onClick={() => window._setLocation && window._setLocation("/verify")}
            >
              Verify Now
            </Button>
          </motion.div>
        )}
        {isVerified && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-green-900/30 border border-green-600/30 rounded-lg flex items-center"
          >
            <div className="w-10 h-10 mr-4 rounded-full bg-green-500/20 flex items-center justify-center">
              <Shield className="h-5 w-5 text-green-500" />
            </div>
            <div>
              <h3 className="font-medium text-green-400">
                Verification Complete
              </h3>
              <p className="text-sm text-green-400/70">
                Your identity is securely linked to the nU SyncBot network
              </p>
            </div>
          </motion.div>
        )}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <CapsoUL title="The JUIce" subtitle="Your nU ecosystem energy capsule" />
            <BalanceSection ghost={ghost} syncWithNetwork={syncWithNetwork} />
            <Card className="mt-6 bg-black text-white border border-zinc-800">
              <CardHeader className="border-b border-zinc-800">
                <CardTitle className="flex justify-between text-white">
                  <span>Transaction History</span>
                  <Button variant="ghost" size="icon" onClick={() => refreshBalance()} className="text-gray-300 hover:text-white">
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Your recent activity in the nU ecosystem
                </CardDescription>
              </CardHeader>
              <CardContent className="bg-black">
                <ScrollArea className="h-[400px] pr-4">
                  <LocalTransactionVisualizer transactions={transactions} />
                </ScrollArea>
              </CardContent>
            </Card>
            {/* My ITUms Collection */}
            <Card className="mt-6 bg-black text-white border border-zinc-800">
              <CardHeader className="border-b border-zinc-800">
                <CardTitle className="flex justify-between text-white">
                  <span>My ITUms</span>
                  <Button variant="ghost" size="icon" onClick={() => refreshBalance()} className="text-gray-300 hover:text-white">
                    <RefreshCw className="h-4 w-4" />
                  </Button>
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Your collection of energy and marketplace items
                </CardDescription>
              </CardHeader>
              <CardContent className="bg-black">
                <MyITUmsCollection ghost={ghost} />
              </CardContent>
            </Card>
          </div>
          <div className="md:col-span-1 space-y-6">
            <Card className="bg-black text-white border border-zinc-800">
              <CardHeader className="border-b border-zinc-800">
                <CardTitle className="text-white">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 bg-black">
                <Button
                  onClick={() => toast({ title: "Wallet Link Generated", description: "Wallet link copied to clipboard", variant: "default" })}
                  className="w-full justify-start gap-2 bg-zinc-800 hover:bg-zinc-700 text-white"
                >
                  <Share2 className="h-4 w-4" /> Share Wallet
                </Button>
                <Button
                  onClick={() => window.dispatchEvent(new CustomEvent("open_activity_log"))}
                  className="w-full justify-start gap-2 bg-zinc-800 hover:bg-zinc-700 text-white"
                >
                  <History className="h-4 w-4" /> Full Activity Log
                </Button>
                <Button
                  onClick={() => window.dispatchEvent(new CustomEvent("open_security_settings"))}
                  className="w-full justify-start gap-2 bg-zinc-800 hover:bg-zinc-700 text-white"
                >
                  <Lock className="h-4 w-4" /> Security Settings
                </Button>
              </CardContent>
            </Card>
            <Card className="bg-black text-white border border-zinc-800">
              <CardHeader className="border-b border-zinc-800">
                <CardTitle className="text-white">Advanced Operations</CardTitle>
                <CardDescription className="text-gray-400">
                  Manage your assets in the nU ecosystem
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 bg-black">
                <Button
                  onClick={() => setOpenModal('lock')}
                  className="w-full justify-start gap-2 bg-blue-800/70 hover:bg-blue-700 text-white"
                  disabled={isRefreshing}
                >
                  <Lock className="h-4 w-4" /> Lock TRU
                </Button>
                <Button
                  onClick={() => setOpenModal('burn')}
                  className="w-full justify-start gap-2 bg-red-800/70 hover:bg-red-700 text-white"
                  disabled={isRefreshing}
                >
                  <Flame className="h-4 w-4" /> Burn TRU
                </Button>
                <Button
                  onClick={() => setOpenModal('donate')}
                  className="w-full justify-start gap-2 bg-green-800/70 hover:bg-green-700 text-white"
                  disabled={isRefreshing}
                >
                  <Heart className="h-4 w-4" /> Donate Energy
                </Button>
                <Button
                  onClick={() => setOpenModal('convert')}
                  className="w-full justify-start gap-2 bg-cyan-800/70 hover:bg-cyan-700 text-white"
                  disabled={isRefreshing || balance.UMATTER < 1}
                >
                  <RefreshCw className="h-4 w-4" /> Convert UMatter
                </Button>
              </CardContent>
            </Card>
            <Card className="bg-black text-white border border-zinc-800">
              <CardHeader className="border-b border-zinc-800">
                <CardTitle className="text-white">nU Ecosystem Benefits</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 bg-black">
                <div className="rounded-lg border border-zinc-800 p-3 bg-black">
                  <h3 className="text-white font-semibold">Battery Donations</h3>
                  <p className="text-xs text-gray-400 mt-1">
                    Convert your device's battery power to trU when charging
                  </p>
                </div>
                <div className="rounded-lg border border-zinc-800 p-3 bg-black">
                  <h3 className="text-white font-semibold">Staking Rewards</h3>
                  <p className="text-xs text-gray-400 mt-1">
                    Earn passive income by staking your trU in the ecosystem
                  </p>
                </div>
                <div className="rounded-lg border border-zinc-800 p-3 bg-black">
                  <h3 className="text-white font-semibold">UMatter Voting</h3>
                  <p className="text-xs text-gray-400 mt-1">
                    Use UMatter to influence ecosystem decisions and directions
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </motion.div>
      {/* Battery Drain Animation */}
      {showBatteryDrainAnimation && batteryLevel !== null && isCharging !== null && (
        <BatteryDrainAnimation
          isVisible={true}
          donationAmount={batteryDrainAmount}
          batteryLevel={batteryLevel}
          isCharging={isCharging}
          onComplete={() => setShowBatteryDrainAnimation(false)}
          className="fixed inset-0 z-50"
        />
      )}
      {showBatteryDrainAnimation && (batteryLevel === null || isCharging === null) && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80">
          <div className="bg-zinc-900 p-6 rounded-lg max-w-md text-center">
            <h3 className="text-xl font-bold text-white mb-4">Battery Data Unavailable</h3>
            <p className="text-gray-300 mb-6">Unable to access real-time battery data. Please allow battery access to continue.</p>
            <Button onClick={() => setShowBatteryDrainAnimation(false)}>Close</Button>
          </div>
        </div>
      )}
      {/* Transaction Modals */}
      {openModal === 'lock' && (
        <TransactionModal
          type="lock"
          isOpen={openModal === 'lock'}
          onClose={() => setOpenModal(null)}
        />
      )}
      {openModal === 'burn' && (
        <TransactionModal
          type="burn"
          isOpen={openModal === 'burn'}
          onClose={() => setOpenModal(null)}
        />
      )}
      {openModal === 'donate' && (
        <TransactionModal
          type="donate"
          isOpen={openModal === 'donate'}
          onClose={() => setOpenModal(null)}
        />
      )}
      {openModal === 'convert' && (
        <TransactionModal
          type="convert"
          isOpen={openModal === 'convert'}
          onClose={() => setOpenModal(null)}
        />
      )}
    </>
  );
}