/**
 * nUTShell Network Implementation
 * 
 * This file implements the core functionality of the nUTShell network,
 * focusing on real-time device discovery, battery synchronization,
 * and peer-to-peer communication.
 */

// Import websocket client from our implementation
import websocketClient from './websocket';
import type { WebSocketClient } from './websocket';

// Import device manager for Bluetooth and hardware access
import { getDrainBot } from './drainBot';
import syncBot from './syncBot';
import { GhostBot } from './ghostBot';

// Device types and interfaces
export interface DeviceInfo {
  id: string;
  type: string;
  name: string;
  osType?: 'ios' | 'android' | 'windows' | 'macos' | 'linux' | 'other';
  batteryLevel: number;
  isCharging?: boolean;
  lastSeen: number;
  connectionType?: 'bluetooth' | 'webrtc' | 'local' | 'ipfs' | 'unknown';
  isOnline: boolean;
  capabilities?: {
    canSync?: boolean;
    canDonate?: boolean;
    canReceive?: boolean;
    supportsBluetooth?: boolean;
    supportsWebRTC?: boolean;
  };
  metadata?: Record<string, any>;
}

export interface BluetoothDevice {
  id: string;
  name?: string;
  type?: string;
  batteryLevel?: number;
}

export interface DeviceSync {
  deviceId: string;
  timestamp: number;
  syncType: 'full' | 'partial' | 'battery-only' | 'network-only';
  success: boolean;
  error?: string;
}

export interface NetworkStatus {
  isConnected: boolean;
  activeConnections: number;
  lastSync: number;
  discoveryEnabled: boolean;
  hasBluetooth: boolean;
  hasWebRTC: boolean;
  hasBatteryAPI: boolean;
}

export interface NUTShellPeer {
  id: string;
  deviceType?: string;
  deviceName?: string;
  name?: string;
  battery?: number;
  batteryLevel?: number;
  isCharging?: boolean;
  lastSeen: number;
  umatterBalance?: number;
  truBalance?: number;
  connectionStrength?: number;
  capacity?: number;
  connectionType?: 'bluetooth' | 'wifi' | 'webrtc' | 'router' | 'unknown';
  bluetoothId?: string;
  ipAddress?: string;
  online?: boolean;
  macAddress?: string;
  // Enhanced metrics
  pingLatency?: number;
  lastPingSent?: number;
  discoveryLatency?: number;
  lastSyncTime?: number;
  syncLatency?: number;
}

export class NUTShellNetwork {
  private devices: Map<string, DeviceInfo> = new Map();
  private currentDeviceId: string = '';
  private networkStatus: NetworkStatus = {
    isConnected: false,
    activeConnections: 0,
    lastSync: 0,
    discoveryEnabled: false,
    hasBluetooth: false,
    hasWebRTC: false,
    hasBatteryAPI: false
  };
  
  private discoveryActive: boolean = false;
  private syncActive: boolean = false;
  private eventListeners: Map<string, Array<(event: any) => void>> = new Map();
  private ghostBot?: GhostBot;
  private syncBot?: SyncBot;
  private drainBot?: DrainBot;
  private bluetoothDevices: Map<string, BluetoothDevice> = new Map();
  private syncInterval?: number;
  private discoveryInterval?: number;
  private webSocketClient: WebSocketClient;
  private peers: Map<string, NUTShellPeer> = new Map();
  private genesisHash: string;
  
  constructor(ghostBot?: GhostBot, webSocketClient?: WebSocketClient) {
    this.ghostBot = ghostBot;
    this.webSocketClient = webSocketClient || new WebSocketClient();
    this.genesisHash = this.generateDeviceSignature();
    this.initializeCapabilities();
    this.setupBatteryListeners();
    this.initializeDevice();
    
    // Setup WebSocket connection handlers
    this.setupWebSocketHandlers();
  }
  
  /**
   * Initialize capability detection for this device
   */
  private initializeCapabilities(): void {
    // Check for Bluetooth API
    this.networkStatus.hasBluetooth = !!navigator.bluetooth;
    
    // Check for WebRTC support
    this.networkStatus.hasWebRTC = !!(window.RTCPeerConnection);
    
    // Check for Battery API
    this.networkStatus.hasBatteryAPI = !!(navigator as any).getBattery;
    
    console.log(`[NUTShellNetwork] Device capabilities: Bluetooth=${this.networkStatus.hasBluetooth}, WebRTC=${this.networkStatus.hasWebRTC}, Battery=${this.networkStatus.hasBatteryAPI}`);
  }
  
  /**
   * Set up battery change listeners
   */
  private setupBatteryListeners(): void {
    if ((navigator as any).getBattery) {
      (navigator as any).getBattery().then((battery: any) => {
        // Update current battery level
        this.updateCurrentDeviceBattery(battery.level * 100, battery.charging);
        
        // Listen for battery changes
        battery.addEventListener('levelchange', () => {
          this.updateCurrentDeviceBattery(battery.level * 100, battery.charging);
        });
        
        battery.addEventListener('chargingchange', () => {
          this.updateCurrentDeviceBattery(battery.level * 100, battery.charging);
        });
      });
    } else {
      // Fallback - use cached battery value or default to 100%
      const cachedBattery = localStorage.getItem('device-battery');
      if (cachedBattery) {
        try {
          const battery = JSON.parse(cachedBattery);
          this.updateCurrentDeviceBattery(battery.level, battery.charging);
        } catch (e) {
          this.updateCurrentDeviceBattery(100, true);
        }
      } else {
        this.updateCurrentDeviceBattery(100, true);
      }
    }
  }
  
  /**
   * Update current device's battery information
   */
  private updateCurrentDeviceBattery(level: number, charging: boolean): void {
    const device = this.devices.get(this.currentDeviceId);
    if (device) {
      device.batteryLevel = level;
      device.isCharging = charging;
      device.lastSeen = Date.now();
      this.devices.set(this.currentDeviceId, device);
      
      // Cache for offline access
      localStorage.setItem('device-battery', JSON.stringify({
        level,
        charging,
        lastUpdated: Date.now()
      }));
      
      // Notify sync and drain systems of battery changes
      if (this.drainBot) {
        this.drainBot.setChargingState(charging);
      }
      
      // Broadcast device update if WebSocket is connected
      this.sendDeviceUpdate();
    }
  }
  
  /**
   * Generate a unique device signature
   * This creates a stable and unique identifier for this device
   */
  private generateDeviceSignature(): string {
    const deviceInfo = [
      navigator.userAgent,
      navigator.language,
      screen.width,
      screen.height,
      navigator.hardwareConcurrency || 2,
      navigator.platform,
      window.devicePixelRatio
    ].join('|');
    
    // Generate a stable hash that will be consistent across sessions
    let hash = 0;
    for (let i = 0; i < deviceInfo.length; i++) {
      const char = deviceInfo.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    
    // Format the hash as a hex string prefixed with 'NU'
    return 'NU-' + Math.abs(hash).toString(16).substring(0, 12).toUpperCase();
  }
  
  /**
   * Initialize the current device information
   */
  private async initializeDevice(): Promise<void> {
    // Get or create device ID
    this.currentDeviceId = localStorage.getItem('nu-did') || this.genesisHash;
    
    // Store device ID persistently
    if (!localStorage.getItem('nu-did')) {
      localStorage.setItem('nu-did', this.currentDeviceId);
    }
    
    // Get device OS type
    const osType = this.detectOsType();
    
    // Create device info
    const deviceInfo: DeviceInfo = {
      id: this.currentDeviceId,
      type: this.getEnhancedDeviceName('device', osType),
      name: this.getDeviceName('device', osType),
      osType,
      batteryLevel: 100,
      isCharging: true,
      lastSeen: Date.now(),
      isOnline: true,
      capabilities: {
        canSync: true,
        canDonate: true,
        canReceive: true,
        supportsBluetooth: this.networkStatus.hasBluetooth,
        supportsWebRTC: this.networkStatus.hasWebRTC
      }
    };
    
    // Store device info
    this.devices.set(this.currentDeviceId, deviceInfo);
    
    // Initialize bots if available
    await this.initializeBots();
    
    // Add online/offline event listeners
    window.addEventListener('online', this.handleOnline);
    window.addEventListener('offline', this.handleOffline);
    
    // Update connection state
    this.networkStatus.isConnected = navigator.onLine;
    
    // Set up network event listeners
    if ((navigator as any).connection) {
      (navigator as any).connection.addEventListener('change', () => {
        this.sendDeviceUpdate();
      });
    }
    
    console.log(`[NUTShellNetwork] Device initialized: ${this.currentDeviceId}`);
  }
  
  /**
   * Detect OS type from user agent
   */
  private detectOsType(): 'ios' | 'android' | 'windows' | 'macos' | 'linux' | 'other' {
    const userAgent = navigator.userAgent.toLowerCase();
    
    if (/iphone|ipad|ipod/.test(userAgent)) {
      return 'ios';
    } else if (/android/.test(userAgent)) {
      return 'android';
    } else if (/windows/.test(userAgent)) {
      return 'windows';
    } else if (/macintosh|mac os x/.test(userAgent)) {
      return 'macos';
    } else if (/linux/.test(userAgent)) {
      return 'linux';
    } else {
      return 'other';
    }
  }
  
  /**
   * Get a human-readable device name based on OS and device type
   */
  private getDeviceName(type: string, os?: DeviceInfo['osType']): string {
    switch (os) {
      case 'ios':
        return 'iPhone/iPad';
      case 'android':
        return 'Android Device';
      case 'windows':
        return 'Windows Device';
      case 'macos':
        return 'Mac Device';
      case 'linux':
        return 'Linux Device';
      default:
        return 'Unknown Device';
    }
  }
  
  /**
   * Get an enhanced human-readable device name with more detailed information
   */
  private getEnhancedDeviceName(type: string, os?: DeviceInfo['osType']): string {
    const userAgent = navigator.userAgent;
    let deviceName = 'nUTShell Device';
    
    // iOS device detection
    if (os === 'ios') {
      if (/iPhone/.test(userAgent)) {
        // Detect iPhone model based on screen size and pixel ratio
        const { width, height } = screen;
        const ratio = window.devicePixelRatio;
        
        if (width === 375 && height === 812 && ratio === 3) {
          deviceName = 'iPhone X/XS';
        } else if (width === 414 && height === 896 && ratio === 2) {
          deviceName = 'iPhone XR';
        } else if (width === 414 && height === 896 && ratio === 3) {
          deviceName = 'iPhone XS Max';
        } else if (width === 390 && height === 844 && ratio === 3) {
          deviceName = 'iPhone 12/13/14';
        } else if (width === 428 && height === 926 && ratio === 3) {
          deviceName = 'iPhone 12/13/14 Pro Max';
        } else {
          deviceName = 'iPhone';
        }
      } else if (/iPad/.test(userAgent)) {
        deviceName = 'iPad';
      } else {
        deviceName = 'iOS Device';
      }
    }
    // Android device detection
    else if (os === 'android') {
      // Extract manufacturer and model from user agent
      const match = userAgent.match(/android.*?;\s([^;)]+)/i);
      if (match && match[1]) {
        deviceName = match[1].trim();
      } else {
        deviceName = 'Android Device';
      }
    }
    // Desktop operating systems
    else if (os === 'windows') {
      const versionMatch = userAgent.match(/Windows NT (\d+\.\d+)/i);
      if (versionMatch && versionMatch[1]) {
        const version = parseFloat(versionMatch[1]);
        if (version === 10.0) {
          deviceName = 'Windows 10/11';
        } else if (version === 6.3) {
          deviceName = 'Windows 8.1';
        } else if (version === 6.2) {
          deviceName = 'Windows 8';
        } else if (version === 6.1) {
          deviceName = 'Windows 7';
        } else {
          deviceName = 'Windows';
        }
      } else {
        deviceName = 'Windows';
      }
    } else if (os === 'macos') {
      deviceName = 'Mac';
    } else if (os === 'linux') {
      deviceName = 'Linux';
    }
    
    return deviceName;
  }
  
  /**
   * Handle online event
   */
  private handleOnline = (): void => {
    this.networkStatus.isConnected = true;
    console.log('[NUTShellNetwork] Device online');
    
    // Update device status
    const device = this.devices.get(this.currentDeviceId);
    if (device) {
      device.isOnline = true;
      device.lastSeen = Date.now();
      this.devices.set(this.currentDeviceId, device);
    }
    
    // Reconnect to WebSocket
    this.webSocketClient.connect();
    
    // Send device update
    this.sendDeviceUpdate();
    
    // Restart discovery and sync if they were active
    if (this.discoveryActive) {
      this.startDiscovery();
    }
    if (this.syncActive) {
      this.startSync();
    }
  };
  
  /**
   * Handle offline event
   */
  private handleOffline = (): void => {
    this.networkStatus.isConnected = false;
    console.log('[NUTShellNetwork] Device offline');
    
    // Update device status
    const device = this.devices.get(this.currentDeviceId);
    if (device) {
      device.isOnline = false;
      device.lastSeen = Date.now();
      this.devices.set(this.currentDeviceId, device);
    }
    
    // Notify listeners of offline status
    this.dispatchEvent('network_status', { isConnected: false });
  };
  
  /**
   * Set up WebSocket event handlers
   */
  private setupWebSocketHandlers(): void {
    // Register message handler for discovery responses
    this.webSocketClient.registerMessageHandler('discovery_response', (data: any) => {
      this.handleDiscoveryResponse(data);
    });
    
    // Register message handler for sync responses
    this.webSocketClient.registerMessageHandler('sync_response', (data: any) => {
      this.handleSyncResponse(data);
    });
    
    // Register message handler for connection status
    this.webSocketClient.registerMessageHandler('connection_status', (data: any) => {
      this.networkStatus.isConnected = data.connected;
      this.dispatchEvent('network_status', { isConnected: data.connected });
    });
    
    // Register message handler for peer count updates
    this.webSocketClient.registerMessageHandler('peer_count', (data: any) => {
      this.networkStatus.activeConnections = data.count;
      console.log('Network now has ' + data.count + ' connected peers');
      this.dispatchEvent('network_metrics', { activeConnections: data.count });
    });
  }
  
  /**
   * Start automatic device discovery
   */
  public startDiscovery(intervalMs: number = 30000): void {
    this.discoveryActive = true;
    
    // Clear existing interval if any
    if (this.discoveryInterval) {
      clearInterval(this.discoveryInterval);
    }
    
    // Perform initial discovery
    this.discoverDevices();
    
    // Set up interval for periodic discovery
    this.discoveryInterval = window.setInterval(() => {
      console.log('[SyncBot] Performing device discovery');
      this.discoverDevices();
    }, intervalMs);
  }
  
  /**
   * Stop automatic discovery
   */
  public stopDiscovery(): void {
    this.discoveryActive = false;
    
    if (this.discoveryInterval) {
      clearInterval(this.discoveryInterval);
      this.discoveryInterval = undefined;
    }
  }
  
  /**
   * Start automatic device sync
   */
  public startSync(intervalMs: number = 60000): void {
    this.syncActive = true;
    
    // Clear existing interval if any
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    
    // Perform initial sync
    this.syncDevices();
    
    // Set up interval for periodic sync
    this.syncInterval = window.setInterval(() => {
      console.log('[SyncBot] Performing sync');
      this.syncDevices();
    }, intervalMs);
  }
  
  /**
   * Stop automatic sync
   */
  public stopSync(): void {
    this.syncActive = false;
    
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = undefined;
    }
  }
  
  /**
   * Send current device information to WebSocket server
   */
  private sendDeviceUpdate(): void {
    const device = this.devices.get(this.currentDeviceId);
    if (!device || !this.webSocketClient) return;
    
    this.webSocketClient.sendDeviceInfo({
      id: device.id,
      name: device.name,
      type: device.type,
      batteryLevel: device.batteryLevel,
      isCharging: device.isCharging
    });
  }
  
  /**
   * Discover devices using WebSocket and Bluetooth
   */
  public async discoverDevices(): Promise<DeviceInfo[]> {
    // Start a WebSocket discovery request
    if (this.webSocketClient) {
      this.webSocketClient.requestDiscovery();
    }
    
    // Discover Bluetooth devices if available
    if (this.networkStatus.hasBluetooth) {
      try {
        const btDevices = await this.discoverBluetoothDevices();
        
        // Add each Bluetooth device to the devices map
        btDevices.forEach(btDevice => {
          if (btDevice.name) {
            const deviceInfo: DeviceInfo = {
              id: btDevice.id,
              name: btDevice.name,
              type: 'Bluetooth Device',
              batteryLevel: btDevice.batteryLevel || 100,
              lastSeen: Date.now(),
              isOnline: true,
              connectionType: 'bluetooth'
            };
            
            this.devices.set(btDevice.id, deviceInfo);
            this.bluetoothDevices.set(btDevice.id, btDevice);
          }
        });
      } catch (error) {
        console.error('[NUTShellNetwork] Error discovering Bluetooth devices:', error);
      }
    }
    
    // Return all discovered devices
    return Array.from(this.devices.values());
  }
  
  /**
   * Discover Bluetooth devices using Web Bluetooth API
   */
  private async discoverBluetoothDevices(): Promise<BluetoothDevice[]> {
    const devices: BluetoothDevice[] = [];
    
    if (!navigator.bluetooth) {
      return devices;
    }
    
    try {
      // Request Bluetooth device - this will prompt the user
      const device = await navigator.bluetooth.requestDevice({
        acceptAllDevices: true
      }).catch(() => null);
      
      if (device) {
        const btDevice: BluetoothDevice = {
          id: `bt-${Date.now().toString(16).substring(0, 6)}`,
          name: device.name || 'Bluetooth Device',
          type: 'bluetooth'
        };
        
        devices.push(btDevice);
        
        console.log('[NUTShellNetwork] Discovered Bluetooth device:', device.name);
      }
    } catch (error) {
      console.error('[NUTShellNetwork] Bluetooth discovery error:', error);
    }
    
    return devices;
  }
  
  /**
   * Handle discovery response from WebSocket
   */
  private handleDiscoveryResponse(data: any): void {
    if (!data.devices) return;
    
    // Update network metrics
    this.networkStatus.lastSync = Date.now();
    
    // Process each discovered device
    data.devices.forEach((device: any) => {
      const peer: NUTShellPeer = {
        id: device.id,
        deviceName: device.name,
        deviceType: device.type,
        batteryLevel: device.batteryLevel,
        isCharging: device.isCharging,
        lastSeen: Date.now(),
        online: true,
        connectionType: 'webrtc',
        pingLatency: device.latency || 0
      };
      
      // Add to peers map
      this.peers.set(device.id, peer);
      
      // Create device info
      const deviceInfo: DeviceInfo = {
        id: device.id,
        name: device.name,
        type: device.type,
        batteryLevel: device.batteryLevel,
        isCharging: device.isCharging,
        lastSeen: Date.now(),
        isOnline: true,
        connectionType: 'webrtc',
        capabilities: device.capabilities || {
          canSync: true,
          canDonate: device.batteryLevel > 30,
          canReceive: true,
          supportsWebRTC: true
        }
      };
      
      // Add to devices map
      this.devices.set(device.id, deviceInfo);
    });
    
    // Update network metrics
    if (data.metrics) {
      this.dispatchEvent('network_metrics', data.metrics);
    }
    
    // Notify listeners of discovery completion
    this.dispatchEvent('discovery_complete', {
      devices: Array.from(this.devices.values()),
      timestamp: Date.now()
    });
  }
  
  /**
   * Sync devices to ensure up-to-date information
   */
  public async syncDevices(): Promise<DeviceSync[]> {
    const results: DeviceSync[] = [];
    
    // Only sync if we're online
    if (!this.networkStatus.isConnected) {
      console.log('[NUTShellNetwork] Cannot sync - offline');
      return results;
    }
    
    // Initiate sync via WebSocket
    if (this.webSocketClient) {
      this.webSocketClient.send({
        type: 'sync_init',
        timestamp: Date.now(),
        deviceId: this.currentDeviceId
      });
    }
    
    // Return success indicator
    const syncResult = {
      deviceId: this.currentDeviceId,
      timestamp: Date.now(),
      syncType: 'full' as const,
      success: true
    };
    
    results.push(syncResult);
    return results;
  }
  
  /**
   * Handle sync response from WebSocket
   */
  private handleSyncResponse(data: any): void {
    // Update last sync time
    this.networkStatus.lastSync = Date.now();
    
    // Notify listeners of sync completion
    this.dispatchEvent('sync_complete', {
      success: data.success,
      timestamp: Date.now(),
      deviceId: data.deviceId
    });
  }
  
  /**
   * Initialize required bots if they don't exist
   */
  private async initializeBots(): Promise<void> {
    if (!this.ghostBot && window.ghostBot) {
      this.ghostBot = window.ghostBot;
    }
    
    if (!this.syncBot) {
      this.syncBot = new SyncBot(this.ghostBot);
    }
    
    if (!this.drainBot) {
      this.drainBot = new DrainBot();
    }
  }
  
  /**
   * Donate battery power from the current device
   */
  public async donateBatteryPower(percentage: number): Promise<{
    success: boolean;
    amountDonated: number;
    convertedUMatter: number;
  }> {
    if (!this.drainBot) {
      await this.initializeBots();
    }
    
    if (!this.drainBot) {
      return {
        success: false,
        amountDonated: 0,
        convertedUMatter: 0
      };
    }
    
    // Use DrainBot to handle the battery drain
    const umatterGenerated = await this.drainBot.donateBatteryPower(percentage);
    
    return {
      success: true,
      amountDonated: percentage,
      convertedUMatter: umatterGenerated
    };
  }
  
  /**
   * Get all known devices
   */
  public getDevices(): DeviceInfo[] {
    return Array.from(this.devices.values());
  }
  
  /**
   * Get a specific device by ID
   */
  public getDevice(deviceId: string): DeviceInfo | undefined {
    return this.devices.get(deviceId);
  }
  
  /**
   * Get current device
   */
  public async getCurrentDevice(): Promise<DeviceInfo | undefined> {
    return this.devices.get(this.currentDeviceId);
  }
  
  /**
   * Get current network status
   */
  public getNetworkStatus(): NetworkStatus {
    return this.networkStatus;
  }
  
  /**
   * Add an event listener
   */
  public addEventListener(event: string, callback: (event: any) => void): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.push(callback);
    }
  }
  
  /**
   * Remove an event listener
   */
  public removeEventListener(event: string, callback: (event: any) => void): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  }
  
  /**
   * Dispatch an event to listeners
   */
  private dispatchEvent(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`[NUTShellNetwork] Error in event listener for ${event}:`, error);
        }
      });
    }
  }
  
  /**
   * Clean up resources when done
   */
  public destroy(): void {
    // Stop discovery and sync
    this.stopDiscovery();
    this.stopSync();
    
    // Remove event listeners
    window.removeEventListener('online', this.handleOnline);
    window.removeEventListener('offline', this.handleOffline);
    
    // Clear device maps
    this.devices.clear();
    this.bluetoothDevices.clear();
    this.peers.clear();
  }
}

// Add to window for global access
declare global {
  interface Window {
    ghostBot?: any;
    nutshellNetwork?: NUTShellNetwork;
  }
}

// Create singleton instance
let networkInstance: NUTShellNetwork | null = null;

export function getNUTShellNetwork(ghostBot?: GhostBot, webSocketClient?: WebSocketClient): NUTShellNetwork {
  if (!networkInstance) {
    networkInstance = new NUTShellNetwork(ghostBot, webSocketClient);
    
    // Add to window for debugging/global access
    if (typeof window !== 'undefined') {
      window.nutshellNetwork = networkInstance;
    }
  }
  
  return networkInstance;
}