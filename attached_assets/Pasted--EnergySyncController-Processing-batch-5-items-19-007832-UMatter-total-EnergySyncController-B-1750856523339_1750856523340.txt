[EnergySyncController] Processing batch: 5 items, 19.007832 UMatter total
[EnergySyncController] ❌ Batch processing failed, re-adding to accumulator
[AuthenticDeviceManager] REAL MacBook Battery: 
Object {realLevel: "100.0%", realCharging: "YES", source: "AUTHENTIC_DEVICE_API"}
[AuthenticDeviceManager] AUTHENTIC MacBook METRICS: 
Object {battery: "100.0% CHARGING", network: "7.25Mbps WIFI", memory: "37.9MB used of 46.1MB", cores: 8, platform: "MacIntel"}
[AuthenticDeviceManager] UMatter generated: 2.226316 from authentic_device_sync
[useRealTimeData] UMatter received: 2.226316 from authentic_device_sync
[AuthenticDeviceManager] Energy accumulated for batching: 2.226316
[useRealTimeData] UMatter received: 2.226316 from authentic_device_sync
[useRealTimeData] Network/Memory/CPU updated: 
Object {network: "7.25Mbps", memory: "37.9MB", cores: 8}
[useRealTimeData] Network/Memory/CPU updated: 
Object {network: "7.25Mbps", memory: "37.9MB", cores: 8}
[AuthenticEnergyData] Update: 
Object {battery: "100.0%", charging: "YES", memory: "37.9MB", network: "7.25Mbps", cores: 8, …}
[EnergySyncController] Added 0.000760 UMatter from real_world_energy, accumulator size: 6
[EnergySyncController] Processing batch: 6 items, 19.008592 UMatter total
[RealBatteryAPI] PERIODIC CHECK: Battery state changed
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[useRealTimeData] PRIORITY: Real Battery API update (AUTHENTIC DEVICE): 
Object {actualLevel: "100.0%", actualCharging: true, source: "REAL_BATTERY_API_PRIMARY", isRealDevice: true}
[EnergySyncController] ❌ Batch processing failed, re-adding to accumulator
[WalletFix] Wallet updated: 12028.912 UMatter
[AuthenticDeviceManager] REAL MacBook Battery: 
Object {realLevel: "100.0%", realCharging: "YES", source: "AUTHENTIC_DEVICE_API"}
[AuthenticDeviceManager] AUTHENTIC MacBook METRICS: 
Object {battery: "100.0% CHARGING", network: "7.25Mbps WIFI", memory: "38.1MB used of 48.5MB", cores: 8, platform: "MacIntel"}
[AuthenticDeviceManager] UMatter generated: 2.226321 from authentic_device_sync
[useRealTimeData] UMatter received: 2.226321 from authentic_device_sync
[AuthenticDeviceManager] Energy accumulated for batching: 2.226321
[useRealTimeData] UMatter received: 2.226321 from authentic_device_sync
[useRealTimeData] Network/Memory/CPU updated: 
Object {network: "7.25Mbps", memory: "38.1MB", cores: 8}
[useRealTimeData] Network/Memory/CPU updated: 
Object {network: "7.25Mbps", memory: "38.1MB", cores: 8}
[AuthenticEnergyData] Update: 
Object {battery: "100.0%", charging: "YES", memory: "38.1MB", network: "7.25Mbps", cores: 8, …}
[NativeBatteryDetector] REAL-TIME UPDATE: 
Object {level: "100.0%", charging: "YES", source: "LIVE_BATTERY_API"}
[SpUnderBot] Executing repair task: extension_fix
[SpUnderBot] Repairing browser extension download...
[SpUnderBot] Extension endpoint not working, attempting repair...
[SpUnderBot] Extension repair requires backend intervention
[SpUnderBot] Repair task completed: extension_fix
[AuthenticDeviceManager] REAL MacBook Battery: 