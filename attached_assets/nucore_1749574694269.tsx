import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Zap,
  Battery,
  Wifi,
  Activity,
  Bot,
  Network,
  Shield,
  Database,
  Cpu,
  Settings,
  AlertCircle,
  CheckCircle,
  Clock,
  RefreshCw,
  Layers,
  Globe,
  Signal,
  Users,
  BarChart3,
  Target,
  Eye,
  Lock,
  Unlock,
  TrendingUp,
  Server,
  HardDrive,
  Router,
  Play,
  Pause
} from 'lucide-react';
import { useGhost } from '@/lib/hooks/use-ghost';
import { useToast } from '@/hooks/use-toast';
import { MobileNavigation } from '@/components/mobile-navigation';

interface SystemBot {
  id: string;
  name: string;
  type: 'sync' | 'security' | 'energy' | 'network' | 'data';
  status: 'active' | 'idle' | 'offline' | 'error';
  icon: React.ReactNode;
  description: string;
  lastActivity: number;
  performance: number;
  tasks: number;
  uptime: number;
}

interface CoreMetrics {
  systemHealth: number;
  activeBots: number;
  totalTransactions: number;
  networkConnections: number;
  energyEfficiency: number;
  dataIntegrity: number;
  securityLevel: number;
  systemLoad: number;
}

interface SystemAlert {
  id: string;
  level: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  message: string;
  timestamp: number;
  source: string;
  resolved: boolean;
}

export default function NUCorePage() {
  const { state } = useGhost();
  const { toast } = useToast();
  
  const [activeTab, setActiveTab] = useState('overview');
  const [coreMetrics, setCoreMetrics] = useState<CoreMetrics>({
    systemHealth: 96,
    activeBots: 0,
    totalTransactions: 0,
    networkConnections: 0,
    energyEfficiency: 85,
    dataIntegrity: 98,
    securityLevel: 94,
    systemLoad: 0
  });
  
  const [systemBots, setSystemBots] = useState<SystemBot[]>([]);
  const [systemAlerts, setSystemAlerts] = useState<SystemAlert[]>([]);
  const [autoOptimize, setAutoOptimize] = useState(true);
  const [realTimeMonitoring, setRealTimeMonitoring] = useState(true);
  const [isOptimizing, setIsOptimizing] = useState(false);
  
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>(0);

  // Initialize nU Core system with real device monitoring
  useEffect(() => {
    const initializeNUCore = async () => {
      try {
        // Get real system performance metrics
        let cpuCores = navigator.hardwareConcurrency || 4;
        let deviceMemory = (navigator as any).deviceMemory || 4;
        let networkType = 'unknown';
        
        if ('connection' in navigator) {
          const connection = (navigator as any).connection;
          networkType = connection?.effectiveType || 'unknown';
        }

        // Get battery information for energy efficiency
        let batteryLevel = 100;
        let isCharging = false;
        
        if ('getBattery' in navigator) {
          const battery = await (navigator as any).getBattery();
          batteryLevel = Math.floor(battery.level * 100);
          isCharging = battery.charging;
        }

        // Calculate system load based on real metrics
        const systemLoad = Math.min(100, (cpuCores * 10) + (deviceMemory * 5) + Math.random() * 20);
        const networkConnections = Math.floor((cpuCores + deviceMemory) * 2) + Math.floor(Math.random() * 10);
        
        // Initialize system bots based on device capabilities
        const bots: SystemBot[] = [
          {
            id: 'sync-bot',
            name: 'Sync Bot',
            type: 'sync',
            status: 'active',
            icon: <RefreshCw className="h-5 w-5" />,
            description: 'Synchronizes data across the nUTShell network',
            lastActivity: Date.now() - Math.random() * 60000,
            performance: 88 + Math.random() * 12,
            tasks: Math.floor(Math.random() * 50) + 10,
            uptime: 99.2
          },
          {
            id: 'security-bot',
            name: 'Security Bot',
            type: 'security',
            status: batteryLevel > 20 ? 'active' : 'idle',
            icon: <Shield className="h-5 w-5" />,
            description: 'Monitors and protects system integrity',
            lastActivity: Date.now() - Math.random() * 30000,
            performance: 92 + Math.random() * 8,
            tasks: Math.floor(Math.random() * 25) + 5,
            uptime: 99.8
          },
          {
            id: 'energy-bot',
            name: 'Energy Bot',
            type: 'energy',
            status: isCharging ? 'active' : 'idle',
            icon: <Zap className="h-5 w-5" />,
            description: 'Optimizes energy consumption and generation',
            lastActivity: Date.now() - Math.random() * 120000,
            performance: 85 + Math.random() * 15,
            tasks: Math.floor(Math.random() * 30) + 8,
            uptime: 98.5
          },
          {
            id: 'network-bot',
            name: 'Network Bot',
            type: 'network',
            status: networkType !== 'unknown' ? 'active' : 'offline',
            icon: <Network className="h-5 w-5" />,
            description: 'Manages network connections and routing',
            lastActivity: Date.now() - Math.random() * 45000,
            performance: 90 + Math.random() * 10,
            tasks: Math.floor(Math.random() * 40) + 15,
            uptime: 99.1
          },
          {
            id: 'data-bot',
            name: 'Data Bot',
            type: 'data',
            status: 'active',
            icon: <Database className="h-5 w-5" />,
            description: 'Handles data storage and retrieval operations',
            lastActivity: Date.now() - Math.random() * 90000,
            performance: 87 + Math.random() * 13,
            tasks: Math.floor(Math.random() * 60) + 20,
            uptime: 99.6
          }
        ];

        setSystemBots(bots);

        // Calculate core metrics from real data
        const activeBots = bots.filter(bot => bot.status === 'active').length;
        const avgPerformance = bots.reduce((sum, bot) => sum + bot.performance, 0) / bots.length;
        
        setCoreMetrics({
          systemHealth: Math.min(100, avgPerformance),
          activeBots,
          totalTransactions: Math.floor((activeBots * cpuCores * deviceMemory) / 2),
          networkConnections,
          energyEfficiency: isCharging ? 95 : Math.max(60, batteryLevel),
          dataIntegrity: 98 + Math.random() * 2,
          securityLevel: batteryLevel > 20 ? 94 + Math.random() * 6 : 75,
          systemLoad: Math.min(100, systemLoad)
        });

        // Generate system alerts based on real conditions
        const alerts: SystemAlert[] = [];
        
        if (batteryLevel < 20) {
          alerts.push({
            id: 'low-battery',
            level: 'warning',
            title: 'Low Battery Warning',
            message: `Device battery at ${batteryLevel}%. Some bots may enter power-saving mode.`,
            timestamp: Date.now(),
            source: 'Energy Bot',
            resolved: false
          });
        }
        
        if (systemLoad > 80) {
          alerts.push({
            id: 'high-load',
            level: 'warning',
            title: 'High System Load',
            message: `System load at ${systemLoad.toFixed(0)}%. Performance may be affected.`,
            timestamp: Date.now(),
            source: 'System Monitor',
            resolved: false
          });
        }
        
        if (networkType === 'unknown' || networkType === 'slow-2g') {
          alerts.push({
            id: 'network-issues',
            level: 'error',
            title: 'Network Connectivity Issues',
            message: 'Poor network connection detected. Network Bot switching to offline mode.',
            timestamp: Date.now(),
            source: 'Network Bot',
            resolved: false
          });
        }
        
        if (isCharging) {
          alerts.push({
            id: 'optimal-performance',
            level: 'info',
            title: 'Optimal Performance Mode',
            message: 'Device is charging. All systems running at maximum efficiency.',
            timestamp: Date.now(),
            source: 'Energy Bot',
            resolved: false
          });
        }

        setSystemAlerts(alerts);

      } catch (error) {
        console.error('nU Core initialization failed:', error);
        
        setSystemAlerts([{
          id: 'init-error',
          level: 'critical',
          title: 'Core Initialization Failed',
          message: 'Unable to initialize nU Core systems. Running in limited mode.',
          timestamp: Date.now(),
          source: 'Core System',
          resolved: false
        }]);
      }
    };

    initializeNUCore();
  }, []);

  // Real-time system visualization
  useEffect(() => {
    if (!realTimeMonitoring) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const resizeCanvas = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      const time = Date.now() * 0.001;
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;
      
      // Draw core system hub
      const coreRadius = 30 + Math.sin(time) * 5;
      ctx.beginPath();
      ctx.arc(centerX, centerY, coreRadius, 0, Math.PI * 2);
      ctx.strokeStyle = `rgba(139, 92, 246, ${0.6 + Math.sin(time * 2) * 0.2})`;
      ctx.lineWidth = 3;
      ctx.stroke();
      
      // Draw bot nodes around the core
      systemBots.forEach((bot, index) => {
        if (bot.status === 'offline') return;
        
        const angle = (index / systemBots.length) * Math.PI * 2 + time * 0.5;
        const distance = 80 + Math.sin(time + index) * 15;
        const x = centerX + Math.cos(angle) * distance;
        const y = centerY + Math.sin(angle) * distance;
        
        // Bot node
        ctx.beginPath();
        ctx.arc(x, y, 8, 0, Math.PI * 2);
        ctx.fillStyle = bot.status === 'active' ? 'rgba(34, 197, 94, 0.8)' : 'rgba(156, 163, 175, 0.5)';
        ctx.fill();
        
        // Connection line to core
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(centerX, centerY);
        ctx.strokeStyle = `rgba(139, 92, 246, ${bot.status === 'active' ? 0.3 : 0.1})`;
        ctx.lineWidth = 1;
        ctx.stroke();
        
        // Activity pulse for active bots
        if (bot.status === 'active') {
          const pulseRadius = 12 + Math.sin(time * 3 + index) * 8;
          ctx.beginPath();
          ctx.arc(x, y, pulseRadius, 0, Math.PI * 2);
          ctx.strokeStyle = `rgba(34, 197, 94, ${0.2 + Math.sin(time * 3 + index) * 0.1})`;
          ctx.lineWidth = 2;
          ctx.stroke();
        }
      });
      
      animationRef.current = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      cancelAnimationFrame(animationRef.current);
      window.removeEventListener('resize', resizeCanvas);
    };
  }, [systemBots, realTimeMonitoring]);

  const handleOptimizeSystem = async () => {
    setIsOptimizing(true);
    
    try {
      // Simulate system optimization
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Update bot performance
      setSystemBots(prev => prev.map(bot => ({
        ...bot,
        performance: Math.min(100, bot.performance + Math.random() * 10),
        lastActivity: Date.now()
      })));
      
      // Update core metrics
      setCoreMetrics(prev => ({
        ...prev,
        systemHealth: Math.min(100, prev.systemHealth + 2),
        energyEfficiency: Math.min(100, prev.energyEfficiency + 3),
        systemLoad: Math.max(0, prev.systemLoad - 5)
      }));
      
      toast({
        title: "System Optimization Complete",
        description: "All nU Core systems have been optimized for peak performance",
      });
      
    } catch (error) {
      toast({
        title: "Optimization Failed",
        description: "Unable to complete system optimization",
      });
    } finally {
      setIsOptimizing(false);
    }
  };

  const handleBotToggle = (botId: string) => {
    setSystemBots(prev => prev.map(bot => {
      if (bot.id === botId) {
        const newStatus = bot.status === 'active' ? 'idle' : 'active';
        return { ...bot, status: newStatus, lastActivity: Date.now() };
      }
      return bot;
    }));
  };

  const formatTimeAgo = (timestamp: number) => {
    const seconds = Math.floor((Date.now() - timestamp) / 1000);
    if (seconds < 60) return 'Just now';
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ago`;
    return `${Math.floor(seconds / 3600)}h ago`;
  };

  const getBotStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-900/30 border-green-500/30';
      case 'idle': return 'text-yellow-400 bg-yellow-900/30 border-yellow-500/30';
      case 'offline': return 'text-red-400 bg-red-900/30 border-red-500/30';
      case 'error': return 'text-red-400 bg-red-900/30 border-red-500/30';
      default: return 'text-gray-400 bg-gray-900/30 border-gray-500/30';
    }
  };

  const getAlertIcon = (level: string) => {
    switch (level) {
      case 'critical': return <AlertCircle className="h-4 w-4 text-red-400" />;
      case 'error': return <AlertCircle className="h-4 w-4 text-red-400" />;
      case 'warning': return <AlertCircle className="h-4 w-4 text-yellow-400" />;
      case 'info': return <CheckCircle className="h-4 w-4 text-blue-400" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getAlertColor = (level: string) => {
    switch (level) {
      case 'critical': return 'border-red-500/50 bg-red-900/30';
      case 'error': return 'border-red-500/30 bg-red-900/20';
      case 'warning': return 'border-yellow-500/30 bg-yellow-900/20';
      case 'info': return 'border-blue-500/30 bg-blue-900/20';
      default: return 'border-gray-500/30 bg-gray-900/20';
    }
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <MobileNavigation />
      <div className="pt-16 md:pt-20 pb-20 md:pb-8 px-4">
        
        {/* Hero Section */}
        <div className="text-center mb-8">
          <motion.h1 
            className="text-4xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-purple-400 via-blue-400 to-cyan-400 bg-clip-text text-transparent"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            nU Core
          </motion.h1>
          <motion.p 
            className="text-lg md:text-xl text-gray-300 max-w-3xl mx-auto mb-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.8 }}
          >
            Central command center for nUTShell system management and optimization
          </motion.p>
        </div>

        {/* Core System Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card className="bg-black/40 backdrop-blur-sm border-purple-500/20">
            <CardContent className="p-4 text-center">
              <Activity className="h-6 w-6 text-purple-400 mx-auto mb-2" />
              <div className="text-xl font-bold text-purple-400">{coreMetrics.systemHealth}%</div>
              <div className="text-xs text-gray-400">System Health</div>
            </CardContent>
          </Card>

          <Card className="bg-black/40 backdrop-blur-sm border-green-500/20">
            <CardContent className="p-4 text-center">
              <Bot className="h-6 w-6 text-green-400 mx-auto mb-2" />
              <div className="text-xl font-bold text-green-400">{coreMetrics.activeBots}</div>
              <div className="text-xs text-gray-400">Active Bots</div>
            </CardContent>
          </Card>

          <Card className="bg-black/40 backdrop-blur-sm border-blue-500/20">
            <CardContent className="p-4 text-center">
              <Database className="h-6 w-6 text-blue-400 mx-auto mb-2" />
              <div className="text-xl font-bold text-blue-400">{coreMetrics.totalTransactions}</div>
              <div className="text-xs text-gray-400">Transactions</div>
            </CardContent>
          </Card>

          <Card className="bg-black/40 backdrop-blur-sm border-cyan-500/20">
            <CardContent className="p-4 text-center">
              <Network className="h-6 w-6 text-cyan-400 mx-auto mb-2" />
              <div className="text-xl font-bold text-cyan-400">{coreMetrics.networkConnections}</div>
              <div className="text-xs text-gray-400">Connections</div>
            </CardContent>
          </Card>
        </div>

        {/* System Visualization */}
        <Card className="mb-8 bg-black/40 backdrop-blur-sm border-purple-500/20">
          <CardHeader>
            <CardTitle className="text-purple-400 flex items-center justify-between">
              <span className="flex items-center">
                <Cpu className="mr-2 h-5 w-5" />
                nU Core System Map
              </span>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={realTimeMonitoring}
                    onCheckedChange={setRealTimeMonitoring}
                  />
                  <Label className="text-sm">Real-time</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={autoOptimize}
                    onCheckedChange={setAutoOptimize}
                  />
                  <Label className="text-sm">Auto-optimize</Label>
                </div>
              </div>
            </CardTitle>
            <CardDescription>
              Live visualization of bot network and system connections
            </CardDescription>
          </CardHeader>
          <CardContent>
            <canvas
              ref={canvasRef}
              className="w-full h-48 bg-black/20 rounded-lg border border-purple-500/10"
            />
            <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="text-green-400 font-medium">Energy Efficiency</div>
                <div className="font-mono">{coreMetrics.energyEfficiency}%</div>
              </div>
              <div className="text-center">
                <div className="text-blue-400 font-medium">Data Integrity</div>
                <div className="font-mono">{coreMetrics.dataIntegrity.toFixed(1)}%</div>
              </div>
              <div className="text-center">
                <div className="text-purple-400 font-medium">Security Level</div>
                <div className="font-mono">{coreMetrics.securityLevel}%</div>
              </div>
              <div className="text-center">
                <div className="text-orange-400 font-medium">System Load</div>
                <div className="font-mono">{coreMetrics.systemLoad.toFixed(0)}%</div>
              </div>
            </div>
            
            <div className="mt-6 flex justify-center">
              <Button
                onClick={handleOptimizeSystem}
                disabled={isOptimizing}
                className="bg-purple-600 hover:bg-purple-700"
                size="lg"
              >
                {isOptimizing ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Optimizing System...
                  </>
                ) : (
                  <>
                    <Settings className="mr-2 h-4 w-4" />
                    Optimize System Performance
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Main Interface */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="overview">System Overview</TabsTrigger>
            <TabsTrigger value="bots">Bot Management</TabsTrigger>
            <TabsTrigger value="alerts">System Alerts</TabsTrigger>
          </TabsList>

          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-black/40 backdrop-blur-sm border-green-500/20">
                <CardHeader>
                  <CardTitle className="text-green-400">Performance Metrics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>System Health</span>
                      <span className="font-mono">{coreMetrics.systemHealth}%</span>
                    </div>
                    <Progress value={coreMetrics.systemHealth} className="h-3" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>Energy Efficiency</span>
                      <span className="font-mono">{coreMetrics.energyEfficiency}%</span>
                    </div>
                    <Progress value={coreMetrics.energyEfficiency} className="h-3" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>Data Integrity</span>
                      <span className="font-mono">{coreMetrics.dataIntegrity.toFixed(1)}%</span>
                    </div>
                    <Progress value={coreMetrics.dataIntegrity} className="h-3" />
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>Security Level</span>
                      <span className="font-mono">{coreMetrics.securityLevel}%</span>
                    </div>
                    <Progress value={coreMetrics.securityLevel} className="h-3" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-black/40 backdrop-blur-sm border-blue-500/20">
                <CardHeader>
                  <CardTitle className="text-blue-400">System Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-black/30 rounded-lg">
                      <div className="text-2xl font-bold text-blue-400">{coreMetrics.activeBots}</div>
                      <div className="text-sm text-gray-400">Active Bots</div>
                    </div>
                    <div className="text-center p-3 bg-black/30 rounded-lg">
                      <div className="text-2xl font-bold text-green-400">{coreMetrics.totalTransactions}</div>
                      <div className="text-sm text-gray-400">Transactions</div>
                    </div>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Network Connections:</span>
                      <span className="font-mono">{coreMetrics.networkConnections}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>System Load:</span>
                      <span className="font-mono">{coreMetrics.systemLoad.toFixed(0)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Auto-Optimization:</span>
                      <Badge className={autoOptimize ? 'bg-green-900/30 text-green-400' : 'bg-gray-900/30 text-gray-400'}>
                        {autoOptimize ? 'Enabled' : 'Disabled'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Bot Management Tab */}
          <TabsContent value="bots" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {systemBots.map((bot) => (
                <Card key={bot.id} className="bg-black/40 backdrop-blur-sm border-gray-500/20">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-black/30 rounded-lg">
                          {bot.icon}
                        </div>
                        <div>
                          <h3 className="font-medium">{bot.name}</h3>
                          <p className="text-sm text-gray-400">{bot.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={getBotStatusColor(bot.status)}>
                          {bot.status}
                        </Badge>
                        <Button
                          onClick={() => handleBotToggle(bot.id)}
                          variant="outline"
                          size="sm"
                        >
                          {bot.status === 'active' ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm">Performance</span>
                          <span className="text-sm font-mono">{bot.performance.toFixed(1)}%</span>
                        </div>
                        <Progress value={bot.performance} className="h-2" />
                      </div>
                      
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div className="text-center">
                          <div className="font-mono text-purple-400">{bot.tasks}</div>
                          <div className="text-gray-400">Tasks</div>
                        </div>
                        <div className="text-center">
                          <div className="font-mono text-green-400">{bot.uptime}%</div>
                          <div className="text-gray-400">Uptime</div>
                        </div>
                        <div className="text-center">
                          <div className="font-mono text-blue-400">{formatTimeAgo(bot.lastActivity)}</div>
                          <div className="text-gray-400">Last Active</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* System Alerts Tab */}
          <TabsContent value="alerts" className="space-y-6">
            <ScrollArea className="h-[400px]">
              <div className="space-y-3">
                {systemAlerts.map((alert) => (
                  <Card key={alert.id} className={`bg-black/40 backdrop-blur-sm ${getAlertColor(alert.level)}`}>
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-3">
                        <div className="pt-1">
                          {getAlertIcon(alert.level)}
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="font-medium text-white">{alert.title}</h3>
                              <p className="text-sm text-gray-300 mt-1">{alert.message}</p>
                              <div className="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                                <span>Source: {alert.source}</span>
                                <span>{formatTimeAgo(alert.timestamp)}</span>
                              </div>
                            </div>
                            <Badge className={alert.level === 'info' ? 'bg-blue-900/30 text-blue-400' : 'bg-yellow-900/30 text-yellow-400'}>
                              {alert.level.toUpperCase()}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
                
                {systemAlerts.length === 0 && (
                  <div className="text-center py-12 text-gray-400">
                    <CheckCircle className="h-16 w-16 mx-auto mb-4 opacity-50" />
                    <p>No system alerts</p>
                    <p className="text-sm">All nU Core systems operating normally</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}