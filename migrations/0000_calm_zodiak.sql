CREATE TABLE "ai_analysis" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"session_id" uuid NOT NULL,
	"analysis_type" varchar NOT NULL,
	"confidence_score" real,
	"insights" jsonb,
	"recommendations" jsonb,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "data_access_logs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"purchase_request_id" uuid NOT NULL,
	"accessed_data" jsonb,
	"access_method" varchar,
	"ip_address" varchar,
	"user_agent" text,
	"accessed_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "data_marketplace" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" varchar NOT NULL,
	"data_package_id" varchar NOT NULL,
	"package_name" varchar NOT NULL,
	"description" text,
	"data_types" jsonb,
	"price_per_access" integer,
	"monthly_price" integer,
	"access_count" integer DEFAULT 0,
	"total_earnings" integer DEFAULT 0,
	"is_active" boolean DEFAULT true,
	"privacy_level" integer DEFAULT 3,
	"expires_at" timestamp,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "data_marketplace_data_package_id_unique" UNIQUE("data_package_id")
);
--> statement-breakpoint
CREATE TABLE "data_purchase_requests" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"buyer_email" varchar NOT NULL,
	"buyer_company" varchar,
	"data_package_id" uuid NOT NULL,
	"request_type" varchar NOT NULL,
	"offered_price" integer,
	"message" text,
	"status" varchar DEFAULT 'pending',
	"user_response" text,
	"payment_status" varchar DEFAULT 'pending',
	"access_granted" boolean DEFAULT false,
	"access_expires_at" timestamp,
	"created_at" timestamp DEFAULT now(),
	"responded_at" timestamp,
	"completed_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "interaction_connections" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"source_node_id" uuid NOT NULL,
	"target_node_id" uuid NOT NULL,
	"connection_type" varchar NOT NULL,
	"strength" real DEFAULT 1,
	"encrypted" boolean DEFAULT true,
	"spunder_hash" text,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "interaction_nodes" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" varchar NOT NULL,
	"node_type" varchar NOT NULL,
	"strength" integer DEFAULT 1,
	"group_id" varchar,
	"metadata" jsonb,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "interaction_sessions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" varchar NOT NULL,
	"session_hash" text NOT NULL,
	"did_authentication" boolean DEFAULT false,
	"anonymous_mode" boolean DEFAULT false,
	"privacy_level" integer DEFAULT 3,
	"interaction_count" integer DEFAULT 0,
	"started_at" timestamp DEFAULT now(),
	"last_activity_at" timestamp DEFAULT now(),
	"ended_at" timestamp,
	CONSTRAINT "interaction_sessions_session_hash_unique" UNIQUE("session_hash")
);
--> statement-breakpoint
CREATE TABLE "memvid_storage" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" varchar NOT NULL,
	"chunk_id" varchar NOT NULL,
	"video_path" text,
	"index_path" text,
	"embedding" jsonb,
	"chunk_data" jsonb,
	"encrypted" boolean DEFAULT true,
	"compression_level" integer DEFAULT 1,
	"created_at" timestamp DEFAULT now(),
	CONSTRAINT "memvid_storage_chunk_id_unique" UNIQUE("chunk_id")
);
--> statement-breakpoint
CREATE TABLE "privacy_alerts" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" varchar NOT NULL,
	"alert_type" varchar NOT NULL,
	"severity" varchar NOT NULL,
	"message" text NOT NULL,
	"resolved" boolean DEFAULT false,
	"metadata" jsonb,
	"created_at" timestamp DEFAULT now(),
	"resolved_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "sessions" (
	"sid" varchar PRIMARY KEY NOT NULL,
	"sess" jsonb NOT NULL,
	"expire" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user_earnings" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" varchar NOT NULL,
	"purchase_request_id" uuid NOT NULL,
	"amount" integer NOT NULL,
	"platform_fee" integer DEFAULT 0,
	"net_amount" integer NOT NULL,
	"payout_method" varchar,
	"payout_status" varchar DEFAULT 'pending',
	"payout_reference" varchar,
	"earned_at" timestamp DEFAULT now(),
	"paid_out_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" varchar PRIMARY KEY NOT NULL,
	"email" varchar,
	"first_name" varchar,
	"last_name" varchar,
	"profile_image_url" varchar,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
ALTER TABLE "ai_analysis" ADD CONSTRAINT "ai_analysis_session_id_interaction_sessions_id_fk" FOREIGN KEY ("session_id") REFERENCES "public"."interaction_sessions"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "data_access_logs" ADD CONSTRAINT "data_access_logs_purchase_request_id_data_purchase_requests_id_fk" FOREIGN KEY ("purchase_request_id") REFERENCES "public"."data_purchase_requests"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "data_marketplace" ADD CONSTRAINT "data_marketplace_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "data_purchase_requests" ADD CONSTRAINT "data_purchase_requests_data_package_id_data_marketplace_id_fk" FOREIGN KEY ("data_package_id") REFERENCES "public"."data_marketplace"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "interaction_connections" ADD CONSTRAINT "interaction_connections_source_node_id_interaction_nodes_id_fk" FOREIGN KEY ("source_node_id") REFERENCES "public"."interaction_nodes"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "interaction_connections" ADD CONSTRAINT "interaction_connections_target_node_id_interaction_nodes_id_fk" FOREIGN KEY ("target_node_id") REFERENCES "public"."interaction_nodes"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "interaction_nodes" ADD CONSTRAINT "interaction_nodes_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "interaction_sessions" ADD CONSTRAINT "interaction_sessions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "memvid_storage" ADD CONSTRAINT "memvid_storage_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "privacy_alerts" ADD CONSTRAINT "privacy_alerts_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_earnings" ADD CONSTRAINT "user_earnings_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_earnings" ADD CONSTRAINT "user_earnings_purchase_request_id_data_purchase_requests_id_fk" FOREIGN KEY ("purchase_request_id") REFERENCES "public"."data_purchase_requests"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "IDX_session_expire" ON "sessions" USING btree ("expire");