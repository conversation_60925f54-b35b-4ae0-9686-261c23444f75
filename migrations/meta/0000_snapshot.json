{"id": "f3ee234d-144a-432b-9c9a-95a86204a06a", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.ai_analysis": {"name": "ai_analysis", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "analysis_type": {"name": "analysis_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "confidence_score": {"name": "confidence_score", "type": "real", "primaryKey": false, "notNull": false}, "insights": {"name": "insights", "type": "jsonb", "primaryKey": false, "notNull": false}, "recommendations": {"name": "recommendations", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ai_analysis_session_id_interaction_sessions_id_fk": {"name": "ai_analysis_session_id_interaction_sessions_id_fk", "tableFrom": "ai_analysis", "tableTo": "interaction_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.data_access_logs": {"name": "data_access_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "purchase_request_id": {"name": "purchase_request_id", "type": "uuid", "primaryKey": false, "notNull": true}, "accessed_data": {"name": "accessed_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "access_method": {"name": "access_method", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "accessed_at": {"name": "accessed_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"data_access_logs_purchase_request_id_data_purchase_requests_id_fk": {"name": "data_access_logs_purchase_request_id_data_purchase_requests_id_fk", "tableFrom": "data_access_logs", "tableTo": "data_purchase_requests", "columnsFrom": ["purchase_request_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.data_marketplace": {"name": "data_marketplace", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "data_package_id": {"name": "data_package_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "package_name": {"name": "package_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "data_types": {"name": "data_types", "type": "jsonb", "primaryKey": false, "notNull": false}, "price_per_access": {"name": "price_per_access", "type": "integer", "primaryKey": false, "notNull": false}, "monthly_price": {"name": "monthly_price", "type": "integer", "primaryKey": false, "notNull": false}, "access_count": {"name": "access_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "total_earnings": {"name": "total_earnings", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "privacy_level": {"name": "privacy_level", "type": "integer", "primaryKey": false, "notNull": false, "default": 3}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"data_marketplace_user_id_users_id_fk": {"name": "data_marketplace_user_id_users_id_fk", "tableFrom": "data_marketplace", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"data_marketplace_data_package_id_unique": {"name": "data_marketplace_data_package_id_unique", "nullsNotDistinct": false, "columns": ["data_package_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.data_purchase_requests": {"name": "data_purchase_requests", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "buyer_email": {"name": "buyer_email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "buyer_company": {"name": "buyer_company", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "data_package_id": {"name": "data_package_id", "type": "uuid", "primaryKey": false, "notNull": true}, "request_type": {"name": "request_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "offered_price": {"name": "offered_price", "type": "integer", "primaryKey": false, "notNull": false}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'pending'"}, "user_response": {"name": "user_response", "type": "text", "primaryKey": false, "notNull": false}, "payment_status": {"name": "payment_status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'pending'"}, "access_granted": {"name": "access_granted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "access_expires_at": {"name": "access_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "responded_at": {"name": "responded_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"data_purchase_requests_data_package_id_data_marketplace_id_fk": {"name": "data_purchase_requests_data_package_id_data_marketplace_id_fk", "tableFrom": "data_purchase_requests", "tableTo": "data_marketplace", "columnsFrom": ["data_package_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.interaction_connections": {"name": "interaction_connections", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "source_node_id": {"name": "source_node_id", "type": "uuid", "primaryKey": false, "notNull": true}, "target_node_id": {"name": "target_node_id", "type": "uuid", "primaryKey": false, "notNull": true}, "connection_type": {"name": "connection_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "strength": {"name": "strength", "type": "real", "primaryKey": false, "notNull": false, "default": 1}, "encrypted": {"name": "encrypted", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "spunder_hash": {"name": "spunder_hash", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"interaction_connections_source_node_id_interaction_nodes_id_fk": {"name": "interaction_connections_source_node_id_interaction_nodes_id_fk", "tableFrom": "interaction_connections", "tableTo": "interaction_nodes", "columnsFrom": ["source_node_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "interaction_connections_target_node_id_interaction_nodes_id_fk": {"name": "interaction_connections_target_node_id_interaction_nodes_id_fk", "tableFrom": "interaction_connections", "tableTo": "interaction_nodes", "columnsFrom": ["target_node_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.interaction_nodes": {"name": "interaction_nodes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "node_type": {"name": "node_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "strength": {"name": "strength", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "group_id": {"name": "group_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"interaction_nodes_user_id_users_id_fk": {"name": "interaction_nodes_user_id_users_id_fk", "tableFrom": "interaction_nodes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.interaction_sessions": {"name": "interaction_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "session_hash": {"name": "session_hash", "type": "text", "primaryKey": false, "notNull": true}, "did_authentication": {"name": "did_authentication", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "anonymous_mode": {"name": "anonymous_mode", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "privacy_level": {"name": "privacy_level", "type": "integer", "primaryKey": false, "notNull": false, "default": 3}, "interaction_count": {"name": "interaction_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "last_activity_at": {"name": "last_activity_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "ended_at": {"name": "ended_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"interaction_sessions_user_id_users_id_fk": {"name": "interaction_sessions_user_id_users_id_fk", "tableFrom": "interaction_sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"interaction_sessions_session_hash_unique": {"name": "interaction_sessions_session_hash_unique", "nullsNotDistinct": false, "columns": ["session_hash"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.memvid_storage": {"name": "memvid_storage", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "chunk_id": {"name": "chunk_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "video_path": {"name": "video_path", "type": "text", "primaryKey": false, "notNull": false}, "index_path": {"name": "index_path", "type": "text", "primaryKey": false, "notNull": false}, "embedding": {"name": "embedding", "type": "jsonb", "primaryKey": false, "notNull": false}, "chunk_data": {"name": "chunk_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "encrypted": {"name": "encrypted", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "compression_level": {"name": "compression_level", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"memvid_storage_user_id_users_id_fk": {"name": "memvid_storage_user_id_users_id_fk", "tableFrom": "memvid_storage", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"memvid_storage_chunk_id_unique": {"name": "memvid_storage_chunk_id_unique", "nullsNotDistinct": false, "columns": ["chunk_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.privacy_alerts": {"name": "privacy_alerts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "alert_type": {"name": "alert_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "severity": {"name": "severity", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "resolved": {"name": "resolved", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "resolved_at": {"name": "resolved_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"privacy_alerts_user_id_users_id_fk": {"name": "privacy_alerts_user_id_users_id_fk", "tableFrom": "privacy_alerts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"sid": {"name": "sid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "sess": {"name": "sess", "type": "jsonb", "primaryKey": false, "notNull": true}, "expire": {"name": "expire", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"IDX_session_expire": {"name": "IDX_session_expire", "columns": [{"expression": "expire", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_earnings": {"name": "user_earnings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "purchase_request_id": {"name": "purchase_request_id", "type": "uuid", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "platform_fee": {"name": "platform_fee", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "net_amount": {"name": "net_amount", "type": "integer", "primaryKey": false, "notNull": true}, "payout_method": {"name": "payout_method", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "payout_status": {"name": "payout_status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'pending'"}, "payout_reference": {"name": "payout_reference", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "earned_at": {"name": "earned_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "paid_out_at": {"name": "paid_out_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_earnings_user_id_users_id_fk": {"name": "user_earnings_user_id_users_id_fk", "tableFrom": "user_earnings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_earnings_purchase_request_id_data_purchase_requests_id_fk": {"name": "user_earnings_purchase_request_id_data_purchase_requests_id_fk", "tableFrom": "user_earnings", "tableTo": "data_purchase_requests", "columnsFrom": ["purchase_request_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "profile_image_url": {"name": "profile_image_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}