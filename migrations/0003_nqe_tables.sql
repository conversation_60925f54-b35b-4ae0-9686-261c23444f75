-- Create nqe_tasks table
CREATE TABLE nqe_tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id VARCHAR(36) NOT NULL UNIQUE,
  user_id VARCHAR NOT NULL,
  type VA<PERSON><PERSON>R NOT NULL,
  input J<PERSON><PERSON>B NOT NULL,
  status VARCHAR NOT NULL DEFAULT 'submitted',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create nqe_results table
CREATE TABLE nqe_results (
  task_id VARCHAR(36) PRIMARY KEY,
  output JSONB NOT NULL,
  energy_cost REAL NOT NULL,
  completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (task_id) REFERENCES nqe_tasks(task_id) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX idx_nqe_tasks_user_id ON nqe_tasks(user_id);
CREATE INDEX idx_nqe_tasks_status ON nqe_tasks(status);