
CREATE TABLE IF NOT EXISTS "biometric_readings" (
  "id" text PRIMARY KEY NOT NULL,
  "user_id" text NOT NULL,
  "timestamp" integer NOT NULL,
  "heart_rate" real,
  "energy_level" real NOT NULL,
  "stress_level" real NOT NULL,
  "focus_score" real NOT NULL,
  "ambient_light" real,
  "motion_intensity" real,
  "proximity_distance" real,
  "battery_level" real,
  "is_charging" integer,
  "created_at" integer DEFAULT (unixepoch()) NOT NULL
);

CREATE TABLE IF NOT EXISTS "energy_metrics" (
  "id" text PRIMARY KEY NOT NULL,
  "user_id" text NOT NULL,
  "timestamp" integer NOT NULL,
  "neural_power_watts" real NOT NULL,
  "device_power_watts" real NOT NULL,
  "total_power_watts" real NOT NULL,
  "battery_drain_wh" real NOT NULL,
  "data_output_mb" real NOT NULL,
  "umatter_generated" real NOT NULL,
  "tru_tokens" real NOT NULL,
  "nuva_tokens" real NOT NULL,
  "efficiency_score" real NOT NULL,
  "created_at" integer DEFAULT (unixepoch()) NOT NULL
);

CREATE TABLE IF NOT EXISTS "energy_balances" (
  "id" text PRIMARY KEY NOT NULL,
  "user_id" text NOT NULL UNIQUE,
  "umatter" real DEFAULT 0 NOT NULL,
  "tru" real DEFAULT 0 NOT NULL,
  "nuva" real DEFAULT 0 NOT NULL,
  "inurtia" real DEFAULT 0 NOT NULL,
  "created_at" integer DEFAULT (unixepoch()) NOT NULL,
  "updated_at" integer DEFAULT (unixepoch()) NOT NULL
);

CREATE TABLE IF NOT EXISTS "device_states" (
  "id" text PRIMARY KEY NOT NULL,
  "user_id" text NOT NULL,
  "timestamp" integer NOT NULL,
  "battery_level" real NOT NULL,
  "is_charging" integer NOT NULL,
  "cpu_usage" real NOT NULL,
  "memory_usage" real NOT NULL,
  "network_activity" real NOT NULL,
  "screen_brightness" real NOT NULL,
  "is_active" integer NOT NULL,
  "created_at" integer DEFAULT (unixepoch()) NOT NULL
);

CREATE TABLE IF NOT EXISTS "neural_states" (
  "id" text PRIMARY KEY NOT NULL,
  "user_id" text NOT NULL,
  "timestamp" integer NOT NULL,
  "attention_level" real NOT NULL,
  "cognitive_load" real NOT NULL,
  "emotional_state" real NOT NULL,
  "stress_level" real NOT NULL,
  "focus_depth" real NOT NULL,
  "created_at" integer DEFAULT (unixepoch()) NOT NULL
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "biometric_readings_user_timestamp" ON "biometric_readings" ("user_id", "timestamp");
CREATE INDEX IF NOT EXISTS "energy_metrics_user_timestamp" ON "energy_metrics" ("user_id", "timestamp");
CREATE INDEX IF NOT EXISTS "device_states_user_timestamp" ON "device_states" ("user_id", "timestamp");
CREATE INDEX IF NOT EXISTS "neural_states_user_timestamp" ON "neural_states" ("user_id", "timestamp");
