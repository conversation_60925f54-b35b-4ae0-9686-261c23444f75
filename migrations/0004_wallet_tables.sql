
-- Create energy_balances_detailed table
CREATE TABLE IF NOT EXISTS energy_balances_detailed (
  id SERIAL PRIMARY KEY,
  user_id VARCHAR(255) NOT NULL,
  umatter_balance DECIMAL(18, 8) DEFAULT 0,
  tru_balance DECIMAL(18, 8) DEFAULT 0,
  nuva_balance DECIMAL(18, 8) DEFAULT 0,
  inurtia_balance DECIMAL(18, 8) DEFAULT 0,
  ubits_balance DECIMAL(18, 8) DEFAULT 0,
  total_energy_generated DECIMAL(18, 8) DEFAULT 0,
  daily_earnings DECIMAL(10, 2) DEFAULT 0,
  weekly_earnings DECIMAL(10, 2) DEFAULT 0,
  monthly_earnings DECIMAL(10, 2) DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index for user lookups
CREATE INDEX IF NOT EXISTS idx_energy_balances_user_id ON energy_balances_detailed(user_id);

-- Insert sample data for testing
INSERT INTO energy_balances_detailed (
  user_id, umatter_balance, tru_balance, nuva_balance, 
  inurtia_balance, ubits_balance, total_energy_generated,
  daily_earnings, weekly_earnings, monthly_earnings
) VALUES (
  'demo-user', 0.394319, 0.001247, 0.0, 
  0.0, 1000, 12.847293,
  24.85, 173.95, 742.50
) ON CONFLICT DO NOTHING;
