
import { storage } from './storage-interface';

export interface NumentumState {
  userId: string;
  currentNumentum: number;
  dailyBase: number;
  compoundingRate: number;
  multipliers: {
    biometric: number;
    activity: number;
    social: number;
    focus: number;
    joy: number;
  };
  streakDays: number;
  lastUpdate: number;
}

export interface InurtiaCalculation {
  previousBalance: number;
  numentumContribution: number;
  compoundingBonus: number;
  streakBonus: number;
  newBalance: number;
  interestEarned: number;
}

export class NumentumProcessor {
  private readonly BASE_NUMENTUM = 1.0;
  private readonly MAX_MULTIPLIER = 3.0;
  private readonly COMPOUND_RATE = 0.01; // 1% daily compounding
  private readonly STREAK_BONUS_CAP = 0.5; // 50% max streak bonus

  /**
   * Calculate real-time nUmentum with all multipliers
   */
  async calculateRealTimeNumentum(
    userId: string,
    activityData: any
  ): Promise<{
    numentumScore: number;
    multipliers: any;
    compoundingEffect: number;
    nextInurtiaReward: number;
  }> {
    try {
      // Get current nUmentum state
      const currentState = await this.getNumentumState(userId);
      
      // Calculate base nUmentum from activity
      const baseNumentum = this.calculateBaseNumentum(activityData);
      
      // Apply all multipliers
      const multipliers = await this.calculateAllMultipliers(userId, activityData);
      
      // Calculate final nUmentum score
      const totalMultiplier = Object.values(multipliers).reduce((sum: number, mult: number) => sum * mult, 1);
      const finalNumentum = Math.min(baseNumentum * totalMultiplier, this.MAX_MULTIPLIER);
      
      // Calculate compounding effect for inUrtia
      const compoundingEffect = this.calculateCompoundingEffect(currentState, finalNumentum);
      
      // Update state
      await this.updateNumentumState(userId, finalNumentum, multipliers);
      
      // Calculate next inUrtia reward
      const nextInurtiaReward = await this.calculateNextInurtiaReward(userId, finalNumentum);

      return {
        numentumScore: finalNumentum,
        multipliers,
        compoundingEffect,
        nextInurtiaReward
      };

    } catch (error) {
      console.error('[Numentum] Calculation failed:', error);
      return {
        numentumScore: this.BASE_NUMENTUM,
        multipliers: {},
        compoundingEffect: 0,
        nextInurtiaReward: 0
      };
    }
  }

  /**
   * Process daily inUrtia compounding
   */
  async processInurtiaCompounding(userId: string): Promise<InurtiaCalculation> {
    try {
      // Get current inUrtia balance
      const inurtiaBalance = await storage.getUserInurtiaBalance(userId);
      const previousBalance = inurtiaBalance?.currentBalance || 0;
      
      // Get daily nUmentum score
      const dailyNumentum = await storage.getUserDailyNumentum(userId);
      
      // Calculate streak bonus
      const streakDays = await this.calculateStreakDays(userId);
      const streakBonus = Math.min(streakDays * 0.01, this.STREAK_BONUS_CAP); // 1% per day, capped at 50%
      
      // Calculate compounding
      const baseCompounding = previousBalance * this.COMPOUND_RATE;
      const numentumContribution = dailyNumentum * 10; // Convert nUmentum to inUrtia
      const compoundingBonus = baseCompounding * (1 + streakBonus);
      
      // Calculate new balance
      const newBalance = previousBalance + numentumContribution + compoundingBonus;
      const interestEarned = compoundingBonus;

      // Update database
      await storage.updateInurtiaBalance(userId, {
        currentBalance: newBalance,
        totalEarned: (inurtiaBalance?.totalEarned || 0) + numentumContribution + interestEarned,
        compoundRate: this.COMPOUND_RATE + (streakBonus / 10), // Slightly increase compound rate
        lastCompounded: Date.now()
      });

      const calculation: InurtiaCalculation = {
        previousBalance,
        numentumContribution,
        compoundingBonus,
        streakBonus,
        newBalance,
        interestEarned
      };

      // Broadcast update
      const { broadcastToUser } = await import('./websocket');
      broadcastToUser(userId, {
        type: 'inurtia-compounded',
        data: {
          calculation,
          streakDays,
          nextCompoundIn: this.getNextCompoundTime()
        }
      });

      return calculation;

    } catch (error) {
      console.error('[Numentum] inUrtia compounding failed:', error);
      throw error;
    }
  }

  /**
   * Calculate reward redemption value
   */
  calculateRedemptionValue(
    inurtiaAmount: number,
    rewardType: string
  ): {
    canRedeem: boolean;
    rewardValue: number;
    exchangeRate: number;
    requiresMinimum: boolean;
  } {
    const exchangeRates = {
      'umatter': 0.1,      // 1 inUrtia = 0.1 UMatter
      'tru': 0.01,         // 1 inUrtia = 0.01 trU
      'nuva': 1.0,         // 1 inUrtia = 1 nUva
      'premium_features': 100, // 100 inUrtia for premium month
      'data_boost': 50,    // 50 inUrtia for data monetization boost
      'privacy_shield': 75, // 75 inUrtia for enhanced privacy
      'energy_multiplier': 200 // 200 inUrtia for 2x energy for 24h
    };

    const minimumAmounts = {
      'umatter': 1,
      'tru': 10,
      'nuva': 1,
      'premium_features': 100,
      'data_boost': 50,
      'privacy_shield': 75,
      'energy_multiplier': 200
    };

    const exchangeRate = exchangeRates[rewardType as keyof typeof exchangeRates] || 0;
    const minimumRequired = minimumAmounts[rewardType as keyof typeof minimumAmounts] || 1;
    const canRedeem = inurtiaAmount >= minimumRequired;
    const rewardValue = canRedeem ? inurtiaAmount * exchangeRate : 0;

    return {
      canRedeem,
      rewardValue,
      exchangeRate,
      requiresMinimum: inurtiaAmount < minimumRequired
    };
  }

  /**
   * Process reward redemption
   */
  async processRewardRedemption(
    userId: string,
    inurtiaAmount: number,
    rewardType: string,
    metadata?: any
  ): Promise<{
    success: boolean;
    rewardValue: number;
    newInurtiaBalance: number;
    redemptionId: string;
  }> {
    try {
      // Validate redemption
      const redemptionCalc = this.calculateRedemptionValue(inurtiaAmount, rewardType);
      if (!redemptionCalc.canRedeem) {
        throw new Error('Insufficient inUrtia or invalid reward type');
      }

      // Get current balance
      const inurtiaBalance = await storage.getUserInurtiaBalance(userId);
      if (!inurtiaBalance || inurtiaBalance.currentBalance < inurtiaAmount) {
        throw new Error('Insufficient inUrtia balance');
      }

      // Process redemption
      const redemptionId = `redemption_${Date.now()}_${Math.random().toString(36).substring(2)}`;
      
      await storage.redeemInurtia({
        userId,
        balanceId: inurtiaBalance.id,
        rewardType,
        inurtiaSpent: inurtiaAmount,
        rewardAmount: redemptionCalc.rewardValue,
        rewardMetadata: metadata
      });

      const newBalance = inurtiaBalance.currentBalance - inurtiaAmount;

      // Apply reward effects
      await this.applyRewardEffects(userId, rewardType, redemptionCalc.rewardValue, metadata);

      // Broadcast update
      const { broadcastToUser } = await import('./websocket');
      broadcastToUser(userId, {
        type: 'reward-redeemed',
        data: {
          redemptionId,
          rewardType,
          rewardValue: redemptionCalc.rewardValue,
          inurtiaSpent: inurtiaAmount,
          newBalance
        }
      });

      return {
        success: true,
        rewardValue: redemptionCalc.rewardValue,
        newInurtiaBalance: newBalance,
        redemptionId
      };

    } catch (error) {
      console.error('[Numentum] Reward redemption failed:', error);
      return {
        success: false,
        rewardValue: 0,
        newInurtiaBalance: 0,
        redemptionId: ''
      };
    }
  }

  /**
   * Calculate base nUmentum from activity
   */
  private calculateBaseNumentum(activityData: any): number {
    let base = this.BASE_NUMENTUM;
    
    // Activity type multipliers
    const activityMultipliers = {
      'scroll': 0.5,
      'click': 1.0,
      'focus': 1.5,
      'create': 2.0,
      'share': 1.2,
      'connect': 1.3,
      'laugh': 1.8
    };

    const multiplier = activityMultipliers[activityData.activityType as keyof typeof activityMultipliers] || 1.0;
    
    // Apply time-based bonus
    const hour = new Date().getHours();
    let timeBonus = 1.0;
    if (hour >= 6 && hour <= 10) timeBonus = 1.2; // Morning boost
    if (hour >= 14 && hour <= 16) timeBonus = 1.1; // Afternoon focus
    if (hour >= 20 && hour <= 22) timeBonus = 0.9; // Evening wind-down

    return base * multiplier * timeBonus;
  }

  /**
   * Calculate all nUmentum multipliers
   */
  private async calculateAllMultipliers(userId: string, activityData: any): Promise<any> {
    // Biometric multiplier (from biometric APIs)
    const biometricMultiplier = await this.getBiometricMultiplier(userId);
    
    // Activity frequency multiplier
    const activityMultiplier = this.calculateActivityMultiplier(activityData);
    
    // Social interaction multiplier
    const socialMultiplier = await this.getSocialMultiplier(userId);
    
    // Focus level multiplier
    const focusMultiplier = this.calculateFocusMultiplier(activityData);
    
    // Joy/emotional state multiplier
    const joyMultiplier = this.calculateJoyMultiplier(activityData);

    return {
      biometric: biometricMultiplier,
      activity: activityMultiplier,
      social: socialMultiplier,
      focus: focusMultiplier,
      joy: joyMultiplier
    };
  }

  /**
   * Get biometric multiplier from recent health data
   */
  private async getBiometricMultiplier(userId: string): Promise<number> {
    // Get recent biometric data
    if (global.healthData) {
      const userHealthData = Array.from(global.healthData.values())
        .filter((data: any) => data.userId === userId)
        .sort((a: any, b: any) => b.createdAt - a.createdAt)
        .slice(0, 5); // Last 5 readings

      if (userHealthData.length > 0) {
        // Calculate average multiplier from recent readings
        // This would use the calculateBiometricNumentum function from biometric-routes.ts
        return 1.15; // Simplified for demo
      }
    }
    
    return 1.0; // Default multiplier
  }

  /**
   * Calculate activity frequency multiplier
   */
  private calculateActivityMultiplier(activityData: any): number {
    const activityCount = activityData.activityCount || 1;
    
    // Diminishing returns for excessive activity
    if (activityCount > 100) return 0.8;
    if (activityCount > 50) return 1.0;
    if (activityCount > 20) return 1.2;
    if (activityCount > 10) return 1.1;
    
    return 1.0;
  }

  /**
   * Get social interaction multiplier
   */
  private async getSocialMultiplier(userId: string): Promise<number> {
    // Check recent social interactions (messages, energy transfers, etc.)
    const recentInteractions = await this.getRecentSocialInteractions(userId);
    
    if (recentInteractions > 10) return 1.3;
    if (recentInteractions > 5) return 1.2;
    if (recentInteractions > 0) return 1.1;
    
    return 1.0;
  }

  /**
   * Calculate focus level multiplier
   */
  private calculateFocusMultiplier(activityData: any): number {
    const focusScore = activityData.focusScore || 0.5;
    
    return 0.7 + (focusScore * 0.6); // Range: 0.7 to 1.3
  }

  /**
   * Calculate joy/emotional state multiplier
   */
  private calculateJoyMultiplier(activityData: any): number {
    const joyLevel = activityData.joyLevel || 0.5;
    
    // Joy has exponential effect on nUmentum
    return 0.8 + (joyLevel * joyLevel * 0.8); // Range: 0.8 to 1.6
  }

  /**
   * Calculate compounding effect
   */
  private calculateCompoundingEffect(currentState: NumentumState, newNumentum: number): number {
    const daysSinceLastUpdate = (Date.now() - currentState.lastUpdate) / (24 * 60 * 60 * 1000);
    
    if (daysSinceLastUpdate >= 1) {
      // Daily compounding effect
      return currentState.currentNumentum * this.COMPOUND_RATE * Math.min(daysSinceLastUpdate, 7);
    }
    
    return 0;
  }

  /**
   * Calculate next inUrtia reward
   */
  private async calculateNextInurtiaReward(userId: string, currentNumentum: number): Promise<number> {
    const dailyTarget = 5.0; // Daily nUmentum target
    const progress = currentNumentum / dailyTarget;
    
    if (progress >= 1.0) {
      return currentNumentum * 10; // Full reward
    }
    
    return (dailyTarget - currentNumentum) * 10; // Remaining reward
  }

  /**
   * Update nUmentum state
   */
  private async updateNumentumState(userId: string, numentum: number, multipliers: any): Promise<void> {
    const state: NumentumState = {
      userId,
      currentNumentum: numentum,
      dailyBase: this.BASE_NUMENTUM,
      compoundingRate: this.COMPOUND_RATE,
      multipliers,
      streakDays: await this.calculateStreakDays(userId),
      lastUpdate: Date.now()
    };

    // Store in global state (in production, use database)
    if (!global.numentumStates) global.numentumStates = new Map();
    global.numentumStates.set(userId, state);
  }

  /**
   * Get current nUmentum state
   */
  private async getNumentumState(userId: string): Promise<NumentumState> {
    if (global.numentumStates?.has(userId)) {
      return global.numentumStates.get(userId);
    }
    
    // Default state
    return {
      userId,
      currentNumentum: this.BASE_NUMENTUM,
      dailyBase: this.BASE_NUMENTUM,
      compoundingRate: this.COMPOUND_RATE,
      multipliers: {},
      streakDays: 0,
      lastUpdate: Date.now()
    };
  }

  /**
   * Calculate consecutive activity streak days
   */
  private async calculateStreakDays(userId: string): Promise<number> {
    // Get user's daily activity for the last 30 days
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    
    // In production, query actual activity data
    // For demo, simulate streak calculation
    return Math.floor(Math.random() * 15) + 1; // 1-15 day streak
  }

  /**
   * Get recent social interactions
   */
  private async getRecentSocialInteractions(userId: string): Promise<number> {
    // Count messages, energy transfers, pool activities, etc.
    // In production, query actual interaction data
    return Math.floor(Math.random() * 20); // 0-19 interactions
  }

  /**
   * Apply reward effects
   */
  private async applyRewardEffects(userId: string, rewardType: string, rewardValue: number, metadata?: any): Promise<void> {
    switch (rewardType) {
      case 'energy_multiplier':
        // Apply 2x energy multiplier for 24 hours
        const expiration = Date.now() + (24 * 60 * 60 * 1000);
        if (!global.activeBoosts) global.activeBoosts = new Map();
        global.activeBoosts.set(userId, {
          type: 'energy_multiplier',
          multiplier: 2.0,
          expiresAt: expiration
        });
        break;
        
      case 'premium_features':
        // Enable premium features for specified duration
        const premiumExpiration = Date.now() + (30 * 24 * 60 * 60 * 1000); // 30 days
        if (!global.premiumUsers) global.premiumUsers = new Set();
        global.premiumUsers.add(userId);
        break;
        
      case 'data_boost':
        // Increase data monetization rates
        if (!global.dataBoosts) global.dataBoosts = new Map();
        global.dataBoosts.set(userId, {
          multiplier: 1.5,
          expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days
        });
        break;
    }
  }

  /**
   * Get next compound time
   */
  private getNextCompoundTime(): number {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0); // Midnight
    
    return tomorrow.getTime() - now.getTime();
  }
}

export const numentumProcessor = new NumentumProcessor();
