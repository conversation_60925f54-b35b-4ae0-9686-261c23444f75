import crypto from 'crypto';
import { storage } from './storage';
export interface SpUnderInteraction {
  id: string;
  did: string;
  hashedDID: string;
  type: string;
  target: string;
  timestamp: number;
  data: string;
  sessionId: string;
  hash: string;
  encrypted: boolean;
}
export interface SpUnderWeb {
  did: string;
  interactions: SpUnderInteraction[];
  merkleRoot: string;
  bundleHash: string;
  encrypted: boolean;
  timestamp: number;
}
export class SpUnderProcessor {
  private readonly ENCRYPTION_ALGORITHM = 'aes-256-gcm';
  private readonly HASH_ALGORITHM = 'sha256';
  /**
   * Process incoming SpUnder web data with real encryption
   */
  async processSpUnderWeb(webData: SpUnderWeb, userId: string): Promise<{
    processed: boolean;
    interactionCount: number;
    merkleVerified: boolean;
    stored: boolean;
  }> {
    try {
      // Verify Merkle tree integrity
      const merkleVerified = this.verifyMerkleTree(webData.interactions, webData.merkleRoot);
      if (!merkleVerified) {
        throw new Error('Merkle tree verification failed');
      }
      // Decrypt interactions if encrypted
      let decryptedInteractions = webData.interactions;
      if (webData.encrypted) {
        decryptedInteractions = await this.decryptInteractions(webData.interactions, webData.did);
      }
      // Process each interaction
      const processedInteractions = await Promise.all(
        decryptedInteractions.map(interaction => this.processInteraction(interaction, userId))
      );
      // Store in database
      await storage.storeWebAdInterception({
        userId,
        sessionId: webData.interactions[0]?.sessionId || '',
        interactions: processedInteractions,
        merkleRoot: webData.merkleRoot,
        bundleHash: webData.bundleHash,
        timestamp: webData.timestamp
      });
      return {
        processed: true,
        interactionCount: processedInteractions.length,
        merkleVerified,
        stored: true
      };
    } catch (error) {
      console.error('[SpUnder] Failed to process web:', error);
      return {
        processed: false,
        interactionCount: 0,
        merkleVerified: false,
        stored: false
      };
    }
  }
  /**
   * Real AES-256-GCM encryption for interaction data
   */
  encryptInteractionData(data: string, key: string): {
    encrypted: string;
    iv: string;
    tag: string;
  } {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.ENCRYPTION_ALGORITHM, key);
    cipher.setAAD(Buffer.from('spunder-aad'));
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    const tag = cipher.getAuthTag();
    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex')
    };
  }
  /**
   * Real AES-256-GCM decryption for interaction data
   */
  decryptInteractionData(encryptedData: string, key: string, iv: string, tag: string): string {
    const decipher = crypto.createDecipher(this.ENCRYPTION_ALGORITHM, key);
    decipher.setAAD(Buffer.from('spunder-aad'));
    decipher.setAuthTag(Buffer.from(tag, 'hex'));
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
  /**
   * Generate secure encryption key from DID
   */
  generateEncryptionKey(did: string): string {
    return crypto.createHash(this.HASH_ALGORITHM)
      .update(did + process.env.SPUNDER_SALT || 'default-salt')
      .digest('hex');
  }
  /**
   * Create Merkle tree for interaction integrity
   */
  createMerkleTree(interactions: SpUnderInteraction[]): string {
    if (interactions.length === 0) return '';
    let hashes = interactions.map(interaction => 
      crypto.createHash(this.HASH_ALGORITHM)
        .update(JSON.stringify({
          id: interaction.id,
          type: interaction.type,
          timestamp: interaction.timestamp,
          sessionId: interaction.sessionId
        }))
        .digest('hex')
    );
    while (hashes.length > 1) {
      const newHashes: string[] = [];
      for (let i = 0; i < hashes.length; i += 2) {
        const left = hashes[i];
        const right = hashes[i + 1] || left;
        const combined = crypto.createHash(this.HASH_ALGORITHM)
          .update(left + right)
          .digest('hex');
        newHashes.push(combined);
      }
      hashes = newHashes;
    }
    return hashes[0];
  }
  /**
   * Verify Merkle tree integrity
   */
  verifyMerkleTree(interactions: SpUnderInteraction[], expectedRoot: string): boolean {
    const computedRoot = this.createMerkleTree(interactions);
    return computedRoot === expectedRoot;
  }
  /**
   * Decrypt interactions array
   */
  private async decryptInteractions(interactions: SpUnderInteraction[], did: string): Promise<SpUnderInteraction[]> {
    const key = this.generateEncryptionKey(did);
    return interactions.map(interaction => {
      if (!interaction.encrypted) return interaction;
      try {
        // Extract encrypted data components (simplified for demo)
        const decryptedData = this.decryptInteractionData(
          interaction.data,
          key,
          interaction.hash.substring(0, 32), // Use part of hash as IV
          interaction.hash.substring(32, 64)  // Use part of hash as tag
        );
        return {
          ...interaction,
          data: decryptedData,
          encrypted: false
        };
      } catch (error) {
        console.error('[SpUnder] Failed to decrypt interaction:', error);
        return interaction;
      }
    });
  }
  /**
   * Process individual interaction with privacy analysis
   */
  private async processInteraction(interaction: SpUnderInteraction, userId: string): Promise<any> {
    // Analyze interaction for privacy violations
    const privacyAnalysis = this.analyzePrivacy(interaction);
    // Extract behavioral patterns
    const behavioralPattern = this.extractBehavioralPattern(interaction);
    // Calculate UMatter generation
    const umatterGenerated = this.calculateUMatterFromInteraction(interaction);
    return {
      ...interaction,
      userId,
      privacyAnalysis,
      behavioralPattern,
      umatterGenerated,
      processedAt: Date.now()
    };
  }
  /**
   * Analyze interaction for privacy compliance
   */
  private analyzePrivacy(interaction: SpUnderInteraction): {
    riskLevel: 'low' | 'medium' | 'high';
    dataTypes: string[];
    requiresConsent: boolean;
  } {
    const sensitivePatterns = ['password', 'email', 'phone', 'ssn', 'credit'];
    const dataContent = interaction.data.toLowerCase();
    const foundSensitiveData = sensitivePatterns.filter(pattern => 
      dataContent.includes(pattern)
    );
    return {
      riskLevel: foundSensitiveData.length > 2 ? 'high' : foundSensitiveData.length > 0 ? 'medium' : 'low',
      dataTypes: foundSensitiveData,
      requiresConsent: foundSensitiveData.length > 0
    };
  }
  /**
   * Extract behavioral patterns from interactions
   */
  private extractBehavioralPattern(interaction: SpUnderInteraction): {
    interactionType: string;
    frequency: number;
    timePattern: string;
    focusLevel: number;
  } {
    const hour = new Date(interaction.timestamp).getHours();
    let timePattern = 'night';
    if (hour >= 6 && hour < 12) timePattern = 'morning';
    else if (hour >= 12 && hour < 18) timePattern = 'afternoon';
    else if (hour >= 18 && hour < 22) timePattern = 'evening';
    return {
      interactionType: interaction.type,
      frequency: 1, // Would calculate from historical data
      timePattern,
      focusLevel: this.calculateFocusLevel(interaction)
    };
  }
  /**
   * Calculate focus level from interaction data
   */
  private calculateFocusLevel(interaction: SpUnderInteraction): number {
    // Simulate focus calculation based on interaction type and timing
    const focusMap = {
      'click': 0.6,
      'scroll': 0.4,
      'focus': 0.9,
      'navigation': 0.7,
      'api_call': 0.8
    };
    return focusMap[interaction.type as keyof typeof focusMap] || 0.5;
  }
  /**
   * Calculate UMatter from interaction energy
   */
  private calculateUMatterFromInteraction(interaction: SpUnderInteraction): number {
    const baseUMatter = 0.001; // Base UMatter per interaction
    const typeMultiplier = {
      'click': 1.0,
      'scroll': 0.5,
      'focus': 1.5,
      'navigation': 1.2,
      'api_call': 0.8
    };
    const multiplier = typeMultiplier[interaction.type as keyof typeof typeMultiplier] || 1.0;
    return baseUMatter * multiplier;
  }
}
export const spUnderProcessor = new SpUnderProcessor();