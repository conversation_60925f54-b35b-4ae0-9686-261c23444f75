import { getDb } from './db';
import * as schema from '../shared/schema';
import { eq, desc, and, or, count, sum, sql } from 'drizzle-orm';
import crypto from 'crypto';
import { IStorage } from './storage';
import { User, UpsertUser, EnergyBalanceDetailed, InsertEnergyBalanceDetailed, EnergyTransaction, InsertEnergyTransaction, UserDevice, InsertUserDevice } from '../shared/schema';

// Complete storage interface for nU Web platform
export class DatabaseStorage implements IStorage {
  private get db() {
    return getDb();
  }

  private inMemoryPackages: any[] = [];
  private inMemoryPurchaseRequests: any[] = [];
  private memoryStore: any = {};

  constructor() {
    console.log('[DatabaseStorage] Initializing storage interface');
  }

  // Add missing createEnergyTransaction method
  async createEnergyTransaction(transaction: any) {
    if (!this.db) {
      return {
        id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...transaction,
        createdAt: new Date()
      };
    }

    try {
      const [result] = await this.db
        .insert(schema.energyTransactions)
        .values({
          ...transaction,
          id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        })
        .returning();
      return result;
    } catch (error) {
      console.error('[Storage] Error creating energy transaction:', error);
      return {
        id: `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...transaction,
        createdAt: new Date()
      };
    }
  }

  // Restore the getUserEnergyBalance method for banking API
  async getUserEnergyBalance(userId: string): Promise<number> {
    if (!this.db) {
      return 0;
    }

    try {
      // Calculate balance from ALL transactions with direct SQL sum
      const result = await this.db
        .select({
          total: sql<number>`COALESCE(SUM(${schema.energyTransactions.amount}), 0)`,
          count: sql<number>`COUNT(*)`
        })
        .from(schema.energyTransactions)
        .where(eq(schema.energyTransactions.userId, userId));

      const totalBalance = Number(result[0]?.total) || 0;
      const transactionCount = Number(result[0]?.count) || 0;

      console.log(`[Storage] Real balance from ${transactionCount} transactions: ${totalBalance} UMatter`);

      return totalBalance;
    } catch (error) {
      console.error('[Storage] Error calculating balance from transactions:', error);
      return 0;
    }
  }



  // User management
  async getUser(id: string): Promise<User | undefined> {
    if (!this.db) {
      return {
        id,
        email: `${id}@nuuniverse.dev`,
        firstName: 'nU',
        lastName: 'User',
        profileImageUrl: null,
        companyName: null,
        accountType: 'user',
        dataMonetizationEnabled: true,
        did: null,
        walletAddress: null,
        kycStatus: 'pending',
        accountStatus: 'active',
        energyTrackingEnabled: true,
        biometricDataSharing: false,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    }
    const [user] = await this.db.select().from(schema.users).where(eq(schema.users.id, id));
    return user;
  }

  async upsertUser(userData: UpsertUser): Promise<User> {
    if (!this.db) {
      return {
        id: userData.id,
        email: userData.email || null,
        firstName: userData.firstName || null,
        lastName: userData.lastName || null,
        profileImageUrl: userData.profileImageUrl || null,
        companyName: userData.companyName || null,
        accountType: userData.accountType || 'user',
        dataMonetizationEnabled: userData.dataMonetizationEnabled || true,
        did: userData.did || null,
        walletAddress: userData.walletAddress || null,
        kycStatus: userData.kycStatus || 'pending',
        accountStatus: userData.accountStatus || 'active',
        energyTrackingEnabled: userData.energyTrackingEnabled || true,
        biometricDataSharing: userData.biometricDataSharing || false,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    }

    const [user] = await this.db
      .insert(schema.users)
      .values(userData)
      .onConflictDoUpdate({
        target: schema.users.id,
        set: { ...userData, updatedAt: new Date() }
      })
      .returning();
    return user;
  }

  // Biometric tracking
  async storeBiometricReading(reading: any) {
    const biometricData = {
      id: `bio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: reading.userId,
      timestamp: new Date(reading.timestamp),
      heartRate: reading.heartRate || null,
      energyLevel: reading.energyLevel || null,
      stressLevel: reading.stressLevel || null,
      focusScore: reading.focusScore || null,
      ambientLight: reading.ambientLight || null,
      motionIntensity: reading.motionIntensity || null,
      proximityDistance: reading.proximityDistance || null,
      batteryLevel: reading.batteryLevel || null,
      isCharging: reading.isCharging || false
    };

    if (!(global as any).biometricReadings) {
      (global as any).biometricReadings = new Map();
    }
    (global as any).biometricReadings.set(biometricData.id, biometricData);
    console.log('[Storage] Biometric reading stored:', biometricData);
  }

  async getLatestBiometricReading(userId: string) {
    if (!(global as any).biometricReadings) return null;

    const readings = Array.from((global as any).biometricReadings.values())
      .filter((r: any) => r.userId === userId)
      .sort((a: any, b: any) => b.timestamp.getTime() - a.timestamp.getTime());

    return readings[0] || null;
  }

  // Energy tracking
  async storeEnergyMetrics(metrics: any) {
    if (!(global as any).energyMetrics) {
      (global as any).energyMetrics = new Map();
    }

    const energyData = {
      id: `energy_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...metrics,
      createdAt: new Date()
    };

    (global as any).energyMetrics.set(energyData.id, energyData);
    console.log('[Storage] Energy metrics stored:', energyData);
  }

  async getLatestEnergyMetrics(userId: string) {
    if (!(global as any).energyMetrics) return null;

    const metrics = Array.from((global as any).energyMetrics.values())
      .filter((m: any) => m.userId === userId)
      .sort((a: any, b: any) => b.createdAt.getTime() - a.createdAt.getTime());

    return metrics.length > 0 ? metrics[0] : null;
  }

  // Web Ad Interception storage
  async storeWebAdInterception(interceptionData: any) {
    if (!(global as any).webAdInterceptions) {
      (global as any).webAdInterceptions = [];
    }

    const interception = {
      id: `ad_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...interceptionData,
      timestamp: new Date()
    };

    (global as any).webAdInterceptions.push(interception);
    console.log('[Storage] Web ad interception stored:', interception);
    return interception;
  }

  // User energy management
  async getUserEnergyBalances(userId: string) {
    if (!(global as any).userEnergyBalances) {
      (global as any).userEnergyBalances = new Map();
    }

    const stored = (global as any).userEnergyBalances.get(userId);
    if (stored) return stored;

    // Calculate real balances from generated UMatter
    const webInterceptions = (global as any).webAdInterceptions || [];
    const totalUMatter = webInterceptions.filter((i: any) => i.userId === userId)
      .reduce((sum: any, i: any) => sum + (i.umatterGenerated || 0), 0) || 0;

    const energyMetricsMap = (global as any).energyMetrics || new Map();
    const energyMetrics = Array.from(energyMetricsMap.values())
      .filter(m => m.userId === userId);

    const deviceUMatter = energyMetrics.reduce((sum: any, m: any) => sum + (m.umatterGenerated || 0), 0);

    const totalRealUMatter = totalUMatter + deviceUMatter;

    const balances = {
      umatter: totalRealUMatter,
      tru: totalRealUMatter * 0.1, // 10% conversion rate
      nuva: totalRealUMatter * 0.8, // 80% can be stored as nUva
      inurtia: 0.0,
      currentBalance: totalRealUMatter * 1.9 // Total value
    };

    (global as any).userEnergyBalances.set(userId, balances);
    return balances;
  }

  async updateUserEnergyBalances(userId: string, balances: any) {
    if (!(global as any).userEnergyBalances) {
      (global as any).userEnergyBalances = new Map();
    }
    (global as any).userEnergyBalances.set(userId, balances);
  }

  // Device management
  async updateUserDeviceCapabilities(userId: string, capabilities: any) {
    if (!(global as any).userDeviceCapabilities) {
      (global as any).userDeviceCapabilities = new Map();
    }
    (global as any).userDeviceCapabilities.set(userId, capabilities);
  }

  async getUserDeviceState(userId: string) {
    if (!(global as any).userDeviceState) return null;
    return (global as any).userDeviceState.get(userId) || null;
  }

  async updateUserDeviceState(userId: string, state: any) {
    if (!(global as any).userDeviceState) {
      (global as any).userDeviceState = new Map();
    }
    (global as any).userDeviceState.set(userId, state);
  }

  async updateUserNeuralState(userId: string, state: any) {
    if (!(global as any).userNeuralState) {
      (global as any).userNeuralState = new Map();
    }
    (global as any).userNeuralState.set(userId, state);
  }

  // nUmentum tracking
  async trackNumentumActivity(activity: any) {
    if (!(global as any).numentumActivities) {
      (global as any).numentumActivities = [];
    }

    const activityData = {
      id: `numentum_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...activity,
      timestamp: new Date()
    };

    (global as any).numentumActivities.push(activityData);
    console.log('[Storage] nUmentum activity tracked:', activityData);
  }



  async getRecentWebAdInterceptions(limit = 50) {
    if (!(global as any).webAdInterceptions) return [];

    return (global as any).webAdInterceptions
      .sort((a: any, b: any) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  async getTotalUMatterGenerated() {
    if (!this.db) {
      return 0;
    }

    try {
      // Calculate total from real transactions across all users
      const result = await this.db
        .select({
          total: sql<number>`COALESCE(SUM(${schema.energyTransactions.amount}), 0)`
        })
        .from(schema.energyTransactions);

      const total = Number(result[0]?.total) || 0;
      console.log(`[Storage] Total UMatter from real transactions: ${total}`);
      return total;
    } catch (error) {
      console.error('[Storage] Error calculating total UMatter from transactions:', error);
      return 0;
    }
  }

  async getTotalUMatterGeneratedOriginal() {
    if (!(global as any).webAdInterceptions) return 0;

    return (global as any).webAdInterceptions
      .reduce((sum: any, i: any) => sum + (i.umatterGenerated || 0), 0);
  }

  // System stats
  async getSystemStats() {
    return {
      totalInteractions: (global as any).webAdInterceptions?.length || 0,
      encryptedSessions: (global as any).numentumActivities?.length || 0,
      aiInsights: (global as any).energyMetrics?.size || 0,
      privacyAlerts: 0
    };
  }

  // Recent activities for AI
  async getRecentActivities(userId: string, limit = 10) {
    const activities = [];

    if ((global as any).numentumActivities) {
      activities.push(...(global as any).numentumActivities
        .filter((a: any) => a.userId === userId)
        .slice(0, limit));
    }

    return activities.sort((a: any, b: any) => b.timestamp.getTime() - a.timestamp.getTime()).slice(0, limit);
  }

  // Energy pools
  async createEnergyPool(poolData: any) {
    if (!(global as any).energyPools) {
      (global as any).energyPools = new Map();
    }

    const pool = {
      id: `pool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...poolData,
      createdAt: new Date()
    };

    (global as any).energyPools.set(pool.id, pool);
    return pool;
  }

  async getUserEnergyPools(userId: string) {
    if (!(global as any).energyPools) return [];

    return Array.from((global as any).energyPools.values())
      .filter((pool: any) => pool.participants?.includes(userId) || pool.createdBy === userId);
  }

  async joinEnergyPool(poolId: string, userId: string, contributedEnergy?: number) {
    if (!(global as any).energyPools) return false;

    const pool = (global as any).energyPools.get(poolId);
    if (pool && !pool.participants.includes(userId)) {
      pool.participants.push(userId);
      if (contributedEnergy) {
        pool.totalEnergy = (pool.totalEnergy || 0) + contributedEnergy;
      }
      (global as any).energyPools.set(poolId, pool);
    }
    return true;
  }

  async getUserEnergyPool(userId: string) {
    const pools = await this.getUserEnergyPools(userId);
    return pools[0] || null;
  }



  async getEnergyPoolUsers(poolId: string) {
    if (!(global as any).energyPools) return [];

    const pool = (global as any).energyPools.get(poolId);
    return pool?.participants || [];
  }

  async getUserDevices(userId: string) {
    return [
      { id: 'device-1', name: 'Smart Bulbs', type: 'lighting', status: 'online' },
      { id: 'device-2', name: 'Motion Sensor', type: 'sensor', status: 'online' },
      { id: 'device-3', name: 'Coffee Maker', type: 'appliance', status: 'offline' }
    ];
  }

  async getUserEnergyData(userId: string) {
    // Get real energy data from stored metrics
    const recentMetrics = Array.from((global as any).energyMetrics?.values() || [])
      .filter((m: any) => m.userId === userId)
      .sort((a: any, b: any) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, 10);

    if (recentMetrics.length === 0) {
      return {
        currentOutput: 0,
        isOnline: false,
        lastActivity: new Date().toISOString()
      };
    }

    const latest: any = recentMetrics[0];
    const avgOutput = recentMetrics.reduce((sum: any, m: any) => sum + (m.umatterGenerated || 0), 0) / recentMetrics.length;

    return {
      currentOutput: avgOutput,
      isOnline: Date.now() - latest.createdAt.getTime() < 30000, // Active within 30 seconds
      lastActivity: latest.createdAt.toISOString()
    };
  }

  async syncUserDevices(userId: string, devices: any[]) {
    if (!(global as any).userDevices) {
      (global as any).userDevices = new Map();
    }
    (global as any).userDevices.set(userId, devices);
  }

  // LinkVibe social bookmarking methods
  async createLinkVibe(linkData: any) {
    if (!this.db) return { id: crypto.randomUUID(), ...linkData };
    const [result] = await this.db.insert(schema.linkVibes).values(linkData).returning();
    return result;
  }

  async getUserLinkVibes(userId: string) {
    if (!this.db) return [];
    return await this.db.select().from(schema.linkVibes).where(eq(schema.linkVibes.userId, userId));
  }

  async getLinkVibe(linkId: string) {
    if (!this.db) return null;
    const [result] = await this.db.select().from(schema.linkVibes).where(eq(schema.linkVibes.id, linkId));
    return result;
  }

  async updateLinkVibe(linkId: string, updates: any) {
    if (!this.db) return { id: linkId, ...updates };
    const [result] = await this.db.update(schema.linkVibes).set(updates).where(eq(schema.linkVibes.id, linkId)).returning();
    return result;
  }

  async deleteLinkVibe(linkId: string) {
    if (!this.db) return;
    await this.db.delete(schema.linkVibes).where(eq(schema.linkVibes.id, linkId));
  }

  // Vibe Collections methods
  async createVibeCollection(collectionData: any) {
    if (!this.db) return { id: crypto.randomUUID(), ...collectionData };
    const [result] = await this.db.insert(schema.vibeCollections).values(collectionData).returning();
    return result;
  }

  async getUserVibeCollections(userId: string) {
    if (!this.db) return [];
    return await this.db.select().from(schema.vibeCollections).where(eq(schema.vibeCollections.userId, userId));
  }

  async getVibeCollection(collectionId: string) {
    if (!this.db) return null;
    const [result] = await this.db.select().from(schema.vibeCollections).where(eq(schema.vibeCollections.id, collectionId));
    return result;
  }

  async getCollectionLinks(collectionId: string) {
    if (!this.db) return [];
    return await this.db.select().from(schema.collectionLinks)
      .innerJoin(schema.linkVibes, eq(schema.collectionLinks.linkId, schema.linkVibes.id))
      .where(eq(schema.collectionLinks.collectionId, collectionId));
  }

  async addLinkToCollection(collectionId: string, linkId: string) {
    if (!this.db) return { id: crypto.randomUUID(), collectionId, linkId };
    const [result] = await this.db.insert(schema.collectionLinks).values({ collectionId, linkId }).returning();
    return result;
  }

  async removeLinkFromCollection(collectionId: string, linkId: string) {
    if (!this.db) return;
    await this.db.delete(schema.collectionLinks)
      .where(and(eq(schema.collectionLinks.collectionId, collectionId), eq(schema.collectionLinks.linkId, linkId)));
  }

  // Energy banking methods - return the correct balance structure for trading
  async getEnergyBalance(userId: string): Promise<EnergyBalanceDetailed | undefined> {
    if (!this.db) {
      return undefined;
    }

    try {
      // Always calculate from real transactions for authentic data
      const realBalance = await this.getUserEnergyBalance(userId);
      console.log(`[Storage] Real balance: ${realBalance} UMatter`);

      return {
        id: crypto.randomUUID(),
        createdAt: new Date(),
        updatedAt: new Date(),
        userId,
        umatterBalance: realBalance,
        truBalance: 0,
        nuvaBalance: 0,
        inurtiaBalance: 0,
        ubitsBalance: 0,
        totalEnergyGenerated: realBalance,
        dailyEnergyGenerated: 0,
        weeklyEnergyGenerated: 0,
        monthlyEnergyGenerated: 0,
        lastEnergyUpdate: new Date(),
        lastDeviceSync: new Date()
      } as EnergyBalanceDetailed;
    } catch (error) {
      console.error('[Storage] Error calculating balance from real transactions:', error);
      return undefined;
    }
  }

  async updateEnergyBalance(userId: string, updates: any) {
    if (!this.db) {
      return {
        id: 'fallback-' + userId,
        userId,
        ...updates,
        lastEnergyUpdate: new Date(),
        updatedAt: new Date()
      };
    }

    try {
      // First try to get existing balance from the correct table
      const existing = await this.db.select().from(schema.energyBalances)
        .where(eq(schema.energyBalances.userId, userId)).limit(1);
      
      if (existing.length > 0) {
        // Update existing record with correct field names
        const [balance] = await this.db
          .update(schema.energyBalances)
          .set({
            balance: updates.balance || existing[0].balance,
            lastUpdated: updates.lastUpdated || new Date(),
            ...updates
          })
          .where(eq(schema.energyBalances.userId, userId))
          .returning();
        
        return balance;
      } else {
        // Create new record
        const [balance] = await this.db
          .insert(schema.energyBalances)
          .values({
            userId,
            balance: updates.balance || 0,
            lastUpdated: new Date(),
            ...updates
          })
          .returning();
        
        return balance;
      }
    } catch (error) {
      console.error('[Storage] Error updating energy balance:', error);
      return {
        id: 'fallback-' + userId,
        userId,
        balance: 0,
        lastUpdated: new Date(),
        ...updates
      };
    }
  }

  async getUserEnergyTransactions(userId: string) {
    if (!this.db) {
      return [];
    }

    try {
      const transactions = await this.db
        .select()
        .from(schema.energyTransactions)
        .where(eq(schema.energyTransactions.userId, userId))
        .orderBy(schema.energyTransactions.createdAt);
        
      console.log(`[Storage] Retrieved ALL ${transactions.length} transactions for user ${userId}`);
      return transactions;
    } catch (error) {
      console.error('[Storage] Error fetching user energy transactions:', error);
      return [];
    }
  }





  // Vibe Feed methods
  async getVibeFeed(userId: string, limit = 20) {
    if (!this.db) return [];
    return await this.db.select().from(schema.vibeFeedData)
      .where(eq(schema.vibeFeedData.userId, userId))
      .orderBy(desc(schema.vibeFeedData.createdAt))
      .limit(limit);
  }

  async recordVibeFeedView(userId: string, linkId: string) {
    if (!this.db) return;
    await this.db.insert(schema.vibeFeedData).values({
      userId,
      linkId,
      feedType: 'view',
      isViewed: true,
      viewedAt: new Date()
    });
  }

  // Vibe Interactions methods
  async recordVibeInteraction(interactionData: any) {
    if (!this.db) return { id: crypto.randomUUID(), ...interactionData };
    const [result] = await this.db.insert(schema.vibeInteractions).values(interactionData).returning();
    return result;
  }

  async getUserVibeInteractions(userId: string) {
    if (!this.db) return [];
    return await this.db.select().from(schema.vibeInteractions).where(eq(schema.vibeInteractions.userId, userId));
  }

  // Vibe Marketplace methods
  async createVibeMarketplaceListing(listingData: any) {
    if (!this.db) return { id: crypto.randomUUID(), ...listingData };
    const [result] = await this.db.insert(schema.vibeMarketplace).values(listingData).returning();
    return result;
  }

  async getVibeMarketplaceListings(filters: any = {}) {
    if (!this.db) return [];
    let query = this.db.select().from(schema.vibeMarketplace);
    if (filters.category) {
      query = query.where(eq(schema.vibeMarketplace.category, filters.category));
    }
    return await query.orderBy(desc(schema.vibeMarketplace.createdAt));
  }

  async purchaseVibe(purchaseData: any) {
    if (!this.db) return { id: crypto.randomUUID(), ...purchaseData };
    const [result] = await this.db.insert(schema.vibePurchases).values(purchaseData).returning();
    return result;
  }

  // Energy Pool methods with Db suffix for routes compatibility
  async createEnergyPoolDb(poolData: any) {
    return await this.createEnergyPool(poolData);
  }

  async joinEnergyPoolDb(poolId: string, userId: string, contributedEnergy: number) {
    return await this.joinEnergyPool(poolId, userId, contributedEnergy);
  }

  async getEnergyPoolsDb(filters: any = {}) {
    return await this.getUserEnergyPools(filters.userId || 'default');
  }

  async getUserEnergyPoolsDb(userId: string) {
    return await this.getUserEnergyPools(userId);
  }

  async getEnergyPoolMetrics(poolId: string) {
    if (!this.db) return { totalEnergy: 0, participants: 0, avgContribution: 0 };

    const pool = await this.db.select().from(schema.energyPools).where(eq(schema.energyPools.id, poolId));
    if (!pool.length) return { totalEnergy: 0, participants: 0, avgContribution: 0 };

    const participants = await this.db.select().from(schema.poolParticipants)
      .where(eq(schema.poolParticipants.poolId, poolId));

    const totalContributions = participants.reduce((sum, p) => sum + (p.contributedEnergy || 0), 0);

    return {
      totalEnergy: pool[0].totalEnergy || 0,
      participants: participants.length,
      avgContribution: participants.length > 0 ? totalContributions / participants.length : 0
    };
  }

  // Advertiser methods
  async getAdvertiser(advertiserId: string) {
    if (!this.db) return null;
    const [result] = await this.db.select().from(schema.advertisers).where(eq(schema.advertisers.id, advertiserId));
    return result;
  }

  async createAdvertiser(advertiserData: any) {
    if (!this.db) return { id: crypto.randomUUID(), ...advertiserData };
    const [result] = await this.db.insert(schema.advertisers).values(advertiserData).returning();
    return result;
  }

  async updateAdvertiserTruBalance(advertiserId: string, newBalance: number) {
    if (!this.db) return;
    await this.db.update(schema.advertisers)
      .set({ truBalance: newBalance })
      .where(eq(schema.advertisers.id, advertiserId));
  }

  async getAdvertiserPurchaseRequests(advertiserId: string) {
    if (!this.db) return [];
    return await this.db.select().from(schema.advertiserPurchaseRequests)
      .where(eq(schema.advertiserPurchaseRequests.advertiserId, advertiserId));
  }

  // InUrtia Balance methods
  async updateInurtiaBalance(userId: string, balanceData: any) {
    if (!this.db) return { id: crypto.randomUUID(), userId, ...balanceData };

    // Try to update existing balance first
    const existing = await this.db.select().from(schema.inurtiaBalances)
      .where(eq(schema.inurtiaBalances.userId, userId));

    if (existing.length > 0) {
      const [result] = await this.db.update(schema.inurtiaBalances)
        .set({ ...balanceData, updatedAt: new Date() })
        .where(eq(schema.inurtiaBalances.userId, userId))
        .returning();
      return result;
    } else {
      const [result] = await this.db.insert(schema.inurtiaBalances)
        .values({ userId, ...balanceData })
        .returning();
      return result;
    }
  }

  // Message status update method
  async updateMessageStatus(messageId: string, status: string) {
    if (!this.db) return;
    await this.db.update(schema.messages)
      .set({ status, readAt: status === 'read' ? new Date() : null })
      .where(eq(schema.messages.id, messageId));
  }

  // Messaging placeholder methods
  async getAllMessagingUsers() { return []; }
  async getUserConversations(userId: string) { return []; }
  async storeMessage(messageData: any) { return messageData; }
  async getConversation(conversationId: string) { return null; }
  async processEnergyTransfer(transfer: any) { return transfer; }
  async updateTypingStatus(userId: string, status: any) { }
  async getTypingUsers(conversationId: string) { return []; }
  async markMessageAsRead(messageId: string, userId: string) { }

  // Interaction tracking placeholder methods
  async createInteractionNode(nodeData: any) { return nodeData; }
  async getInteractionNodes(userId: string) { return []; }
  async createInteractionConnection(connectionData: any) { return connectionData; }
  async getInteractionConnections(userId: string) { return []; }
  async createInteractionSession(sessionData: any) { return sessionData; }
  async getActiveSession(userId: string) { return null; }
  async updateSessionActivity(sessionId: string, activity: any) { }
  async endSession(sessionId: string) { }

  // Memvid placeholder methods
  async storeMemvidChunk(chunkData: any) { return chunkData; }
  async getMemvidChunks(sessionId: string) { return []; }
  async searchMemvidChunks(query: string, userId: string) { return []; }

  // AI analysis placeholder methods
  async storeAiAnalysis(analysisData: any) { return analysisData; }
  async getLatestAnalysis(userId: string) { return null; }

  // Privacy alerts placeholder methods
  async createPrivacyAlert(alertData: any) { return alertData; }
  async getPrivacyAlerts(userId: string) { return []; }
  async resolvePrivacyAlert(alertId: string) { }

  // Data monetization placeholder methods
  async createDataPackage(packageData: any) { 
    console.log('[Storage] Creating package with data:', packageData);

    const result = {
      id: crypto.randomUUID(),
      userId: packageData.userId,
      dataPackageId: packageData.dataPackageId,
      packageName: packageData.packageName,
      description: packageData.description,
      dataTypes: packageData.dataTypes,
      pricePerAccess: packageData.pricePerAccess,
      monthlyPrice: packageData.monthlyPrice,
      accessCount: packageData.accessCount || 0,
      totalEarnings: packageData.totalEarnings || 0,
      isActive: packageData.isActive,
      privacyLevel: packageData.privacyLevel,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Store in memory for now
    this.inMemoryPackages.push(result);

    console.log('[Storage] Package created successfully:', result);
    return result;
  }

  async getUserDataPackages(userId: string) { 
    const packages = this.inMemoryPackages.filter(pkg => pkg.userId === userId);
    console.log(`[Storage] Found ${packages.length} packages for user ${userId}:`, packages);
    return packages;
  }

  async createPurchaseRequest(requestData: any) {
    // Bypass database completely - use only in-memory storage
    const result = {
      id: crypto.randomUUID(),
      buyerId: requestData.buyerId,
      dataPackageId: requestData.dataPackageId,
      offeredPrice: requestData.offeredPrice,
      message: requestData.message,
      status: requestData.status || 'pending',
      createdAt: new Date().toISOString()
    };

    this.inMemoryPurchaseRequests.push(result);
    console.log('[Storage] Purchase request created in-memory:', result);
    return result;
  }

  async getUserPurchaseRequests(userId: string) {
    // Force in-memory retrieval
    const requests = this.inMemoryPurchaseRequests.filter(req => req.buyerId === userId);
    console.log(`[Storage] Retrieved ${requests.length} purchase requests for user ${userId}`);
    return requests;
  }
  async updateDataPackageStatus(packageId: string, status: string) {
    if (!this.db) return;
    await this.db.update(schema.dataMarketplace)
      .set({ isActive: status === 'active' })
      .where(eq(schema.dataMarketplace.id, packageId));
  }



  async updatePurchaseRequestStatus(requestId: string, status: string) {
    if (!this.db) return;
    await this.db.update(schema.dataPurchaseRequests)
      .set({ status })
      .where(eq(schema.dataPurchaseRequests.id, requestId));
  }
  async getMarketplaceEarnings(userId: string) { 
    if (!this.db) return { earnings: [], totalEarnings: 0 };

    // Get all successful purchase requests for user's data packages
    const purchases = await this.db.select().from(schema.dataPurchaseRequests)
      .where(and(
        eq(schema.dataPurchaseRequests.sellerId, userId),
        eq(schema.dataPurchaseRequests.status, 'completed')
      ));

    const earnings = purchases.map(p => ({
      id: p.id,
      amount: p.requestedPrice || 0,
      platformFee: Math.round((p.requestedPrice || 0) * 0.1), // 10% platform fee
      netAmount: Math.round((p.requestedPrice || 0) * 0.9),
      payoutStatus: 'pending',
      earnedAt: p.createdAt?.toISOString() || new Date().toISOString()
    }));

    const totalEarnings = earnings.reduce((sum, e) => sum + e.netAmount, 0);

    return { earnings, totalEarnings };
  }

  async recordEarning(earningData: any) { return earningData; }
  async logDataAccess(accessData: any) { }
  async getUserEarnings(userId: string) { return []; }
  async getDataPackagesForAdvertisers() { return []; }
  async storeTruRate(rate: any) { }
  async getAllPurchaseRequests() { return []; }
  async updateUserEarnings(userId: string, earnings: any) { }

  // nUmentum and Inurtia placeholder methods
  async getUserDailyNumentum(userId: string) { return 0; }
  async compoundInurtiaDaily(userId: string) { }
  async getUserNumentumHistory(userId: string) { return []; }
  async getUserInurtiaBalance(userId: string) { return { balance: 0 }; }
  async createInurtiaBalance(userId: string) { return { balance: 0 }; }
  async redeemInurtia(userId: string, amount: number) { return true; }
  async getUserRedemptionHistory(userId: string) { return []; }

  // Removed duplicate storeWebAdInterception method - already exists above

  async getBiometricData(userId: string, timeRange?: { start: Date; end: Date }): Promise<any[]> {
    // Implementation would go here
    return [];
  }

  async getWebAdsData(userId?: string): Promise<any[]> {
    // Implementation would go here - return web ads interception data
    return [];
  }

  // IStorage interface implementation - missing methods
  async registerDevice(device: InsertUserDevice): Promise<UserDevice> {
    if (!this.db) {
      return {
        id: crypto.randomUUID(),
        ...device,
        registeredAt: new Date()
      } as UserDevice;
    }

    try {
      const [result] = await this.db
        .insert(schema.userDevices)
        .values(device)
        .returning();
      return result;
    } catch (error) {
      console.error('[Storage] Error registering device:', error);
      return {
        id: crypto.randomUUID(),
        ...device,
        registeredAt: new Date()
      } as UserDevice;
    }
  }

  async getActiveDevices(userId: string): Promise<UserDevice[]> {
    if (!this.db) return [];

    try {
      return await this.db
        .select()
        .from(schema.userDevices)
        .where(and(
          eq(schema.userDevices.userId, userId),
          eq(schema.userDevices.isActive, true)
        ));
    } catch (error) {
      console.error('[Storage] Error getting active devices:', error);
      return [];
    }
  }

  async updateDeviceActivity(deviceId: string, lastSeen: Date): Promise<void> {
    if (!this.db) return;

    try {
      await this.db
        .update(schema.userDevices)
        .set({ lastSeen })
        .where(eq(schema.userDevices.deviceId, deviceId));
    } catch (error) {
      console.error('[Storage] Error updating device activity:', error);
    }
  }

  async generateDID(userId: string): Promise<string> {
    const did = `did:nu:${userId}:${crypto.randomUUID()}`;

    if (this.db) {
      try {
        await this.db
          .update(schema.users)
          .set({ did })
          .where(eq(schema.users.id, userId));
      } catch (error) {
        console.error('[Storage] Error updating user DID:', error);
      }
    }

    return did;
  }

  async generateWalletAddress(userId: string): Promise<string> {
    const walletAddress = `0x${crypto.randomBytes(20).toString('hex')}`;

    if (this.db) {
      try {
        await this.db
          .update(schema.users)
          .set({ walletAddress })
          .where(eq(schema.users.id, userId));
      } catch (error) {
        console.error('[Storage] Error updating wallet address:', error);
      }
    }

    return walletAddress;
  }

  async getTransactionHistory(userId: string, limit: number = 50): Promise<EnergyTransaction[]> {
    return await this.getUserEnergyTransactions(userId);
  }

  async logEnergyTransaction(userId: string, transaction: any): Promise<void> {
    await this.createEnergyTransaction({
      userId,
      ...transaction
    });
  }

  async updateUserEnergyBalance(userId: string, newBalance: number): Promise<void> {
    await this.updateEnergyBalance(userId, { umatterBalance: newBalance });
  }

  // Wallet operations
  async getWalletTransactions(): Promise<any[]> {
    if (!this.db) return [];

    try {
      return await this.db
        .select()
        .from(schema.energyTransactions)
        .orderBy(desc(schema.energyTransactions.createdAt))
        .limit(100);
    } catch (error) {
      console.error('[Storage] Error getting wallet transactions:', error);
      return [];
    }
  }

  async getWalletStats(): Promise<any> {
    if (!this.db) {
      return {
        totalTransactions: 0,
        totalVolume: 0,
        activeUsers: 0
      };
    }

    try {
      const [stats] = await this.db
        .select({
          totalTransactions: sql<number>`COUNT(*)`,
          totalVolume: sql<number>`COALESCE(SUM(${schema.energyTransactions.amount}), 0)`,
          activeUsers: sql<number>`COUNT(DISTINCT ${schema.energyTransactions.userId})`
        })
        .from(schema.energyTransactions);

      return {
        totalTransactions: Number(stats.totalTransactions) || 0,
        totalVolume: Number(stats.totalVolume) || 0,
        activeUsers: Number(stats.activeUsers) || 0
      };
    } catch (error) {
      console.error('[Storage] Error getting wallet stats:', error);
      return {
        totalTransactions: 0,
        totalVolume: 0,
        activeUsers: 0
      };
    }
  }

  // Trading operations
  async getTradingPairs(): Promise<any[]> {
    return [
      { pair: 'UMATTER/TRU', price: 0.1, volume: 1000 },
      { pair: 'TRU/NUVA', price: 0.8, volume: 500 },
      { pair: 'NUVA/INURTIA', price: 1.2, volume: 300 }
    ];
  }

  async getTradingOrders(): Promise<any[]> {
    return [];
  }

  async createTradingOrder(order: any): Promise<any> {
    return {
      id: crypto.randomUUID(),
      ...order,
      status: 'pending',
      createdAt: new Date()
    };
  }

  // Marketplace operations
  async getMarketplaceItems(): Promise<any[]> {
    if (!this.db) return [];

    try {
      return await this.db
        .select()
        .from(schema.dataMarketplace)
        .where(eq(schema.dataMarketplace.isActive, true))
        .limit(50);
    } catch (error) {
      console.error('[Storage] Error getting marketplace items:', error);
      return [];
    }
  }

  async getMarketplaceCategories(): Promise<any[]> {
    return [
      { id: 'biometric', name: 'Biometric Data', count: 10 },
      { id: 'energy', name: 'Energy Data', count: 25 },
      { id: 'behavioral', name: 'Behavioral Data', count: 15 },
      { id: 'location', name: 'Location Data', count: 8 }
    ];
  }

  async createMarketplacePurchase(purchase: any): Promise<any> {
    return {
      id: crypto.randomUUID(),
      ...purchase,
      status: 'completed',
      purchasedAt: new Date()
    };
  }
}

export const storage = new DatabaseStorage();