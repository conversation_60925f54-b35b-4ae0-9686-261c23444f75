import type { Express } from "express";
import { isAuthenticated } from "./replitAuth";
import { z } from "zod";
import crypto from 'crypto';

// Biometric data schemas with privacy validation
const biometricDataSchema = z.object({
  heartRate: z.number().min(40).max(220).optional(),
  stressLevel: z.number().min(0).max(1).optional(),
  energyLevel: z.number().min(0).max(1).optional(),
  focusScore: z.number().min(0).max(1).optional(),
  breathingRate: z.number().min(5).max(50).optional(),
  skinConductance: z.number().min(0).max(100).optional(),
  bodyTemperature: z.number().min(95).max(105).optional(),
  deviceId: z.string().optional(),
  timestamp: z.number(),
  consentLevel: z.enum(['basic', 'research', 'commercial']),
  privacySettings: z.object({
    allowResearch: z.boolean(),
    allowCommercial: z.boolean(),
    dataRetentionDays: z.number().min(1).max(365),
    anonymizeData: z.boolean()
  })
});

const healthInsightRequestSchema = z.object({
  dataTypes: z.array(z.string()),
  timeRange: z.object({
    start: z.number(),
    end: z.number()
  }),
  analysisType: z.enum(['trends', 'patterns', 'anomalies', 'recommendations'])
});

export function registerBiometricRoutes(app: Express) {
  
  // Submit biometric data with privacy compliance
  app.post('/api/biometric/data', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const validatedData = biometricDataSchema.parse(req.body);

      // Check privacy consent
      const consentValid = await validateHealthDataConsent(userId, validatedData.consentLevel);
      if (!consentValid) {
        return res.status(403).json({ 
          message: "Health data consent required",
          consentUrl: "/privacy/health-consent"
        });
      }

      // Encrypt sensitive health data
      const encryptedData = encryptHealthData(validatedData, userId);
      
      // Calculate nUmentum multiplier from biometric data
      const numentumMultiplier = calculateBiometricNumentum(validatedData);
      
      // Store with privacy controls
      const storedData = await storeHealthData(userId, encryptedData, validatedData.privacySettings);
      
      // Real-time WebSocket update
      const { broadcastToUser } = await import('./websocket');
      broadcastToUser(userId, {
        type: 'biometric-processed',
        data: {
          numentumMultiplier,
          energyBoost: numentumMultiplier > 1.0,
          dataStored: true,
          timestamp: Date.now()
        }
      });

      res.json({
        success: true,
        numentumMultiplier,
        dataId: storedData.id,
        privacyCompliant: true,
        retentionDays: validatedData.privacySettings.dataRetentionDays
      });

    } catch (error) {
      console.error("Biometric data submission error:", error);
      res.status(500).json({ message: "Failed to process biometric data" });
    }
  });

  // Get biometric trends with privacy filtering
  app.get('/api/biometric/trends', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const days = parseInt(req.query.days as string) || 7;
      
      // Check data access permissions
      const accessLevel = await getUserHealthDataAccess(userId);
      
      const trends = await getRealHealthTrends(userId, days, accessLevel);
      
      res.json({
        trends,
        accessLevel,
        dataPoints: trends.length,
        period: `${days} days`
      });

    } catch (error) {
      console.error("Biometric trends error:", error);
      res.status(500).json({ message: "Failed to fetch biometric trends" });
    }
  });

  // Health insights with AI analysis
  app.post('/api/biometric/insights', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const request = healthInsightRequestSchema.parse(req.body);
      
      // Validate consent for AI analysis
      const aiConsentValid = await validateAIAnalysisConsent(userId);
      if (!aiConsentValid) {
        return res.status(403).json({
          message: "AI analysis consent required",
          consentType: "health_ai_analysis"
        });
      }

      const insights = await generateHealthInsights(userId, request);
      
      res.json({
        insights,
        analysisType: request.analysisType,
        confidenceLevel: insights.confidence,
        recommendations: insights.recommendations,
        privacyNote: "Analysis performed on anonymized data"
      });

    } catch (error) {
      console.error("Health insights error:", error);
      res.status(500).json({ message: "Failed to generate health insights" });
    }
  });

  // Export health data (GDPR compliance)
  app.get('/api/biometric/export', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const format = req.query.format || 'json';
      
      const exportData = await exportUserHealthData(userId, format);
      
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="health-data-${userId}.${format}"`);
      
      res.json({
        userId,
        exportedAt: new Date().toISOString(),
        dataTypes: exportData.dataTypes,
        totalRecords: exportData.records.length,
        data: exportData.records,
        privacyInfo: {
          encryptionUsed: true,
          anonymizationLevel: exportData.anonymizationLevel,
          retentionPolicy: exportData.retentionPolicy
        }
      });

    } catch (error) {
      console.error("Health data export error:", error);
      res.status(500).json({ message: "Failed to export health data" });
    }
  });

  // Delete health data (Right to be forgotten)
  app.delete('/api/biometric/data', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const dataTypes = req.body.dataTypes || ['all'];
      
      const deletionResult = await deleteUserHealthData(userId, dataTypes);
      
      res.json({
        success: true,
        deletedRecords: deletionResult.deletedCount,
        dataTypes: deletionResult.deletedTypes,
        deletedAt: new Date().toISOString(),
        recoveryPeriod: "30 days"
      });

    } catch (error) {
      console.error("Health data deletion error:", error);
      res.status(500).json({ message: "Failed to delete health data" });
    }
  });

  // Privacy settings management
  app.get('/api/biometric/privacy-settings', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const settings = await getHealthPrivacySettings(userId);
      
      res.json(settings);
    } catch (error) {
      console.error("Privacy settings error:", error);
      res.status(500).json({ message: "Failed to fetch privacy settings" });
    }
  });

  app.put('/api/biometric/privacy-settings', isAuthenticated, async (req: any, res) => {
    try {
      const userId = req.user.claims.sub;
      const settings = req.body;
      
      const updatedSettings = await updateHealthPrivacySettings(userId, settings);
      
      res.json({
        success: true,
        settings: updatedSettings,
        effectiveDate: new Date().toISOString()
      });
    } catch (error) {
      console.error("Privacy settings update error:", error);
      res.status(500).json({ message: "Failed to update privacy settings" });
    }
  });
}

// Helper functions for biometric processing

async function validateHealthDataConsent(userId: string, consentLevel: string): Promise<boolean> {
  // Check if user has provided appropriate consent
  // In production, this would check against a consent management system
  return true; // Simplified for demo
}

function encryptHealthData(data: any, userId: string): string {
  const key = process.env.HEALTH_ENCRYPTION_KEY || 'health-key';
  const dataString = JSON.stringify({
    ...data,
    userId,
    encryptedAt: Date.now()
  });
  
  return crypto.createHash('sha256')
    .update(key + dataString)
    .digest('hex');
}

function calculateBiometricNumentum(data: any): number {
  let multiplier = 1.0;
  
  // Heart rate influence (optimal zone = higher multiplier)
  if (data.heartRate) {
    if (data.heartRate >= 60 && data.heartRate <= 100) {
      multiplier += 0.1; // Normal resting rate bonus
    } else if (data.heartRate > 100 && data.heartRate <= 150) {
      multiplier += 0.2; // Exercise zone bonus
    }
  }
  
  // Energy level influence
  if (data.energyLevel > 0.8) {
    multiplier += 0.25;
  } else if (data.energyLevel < 0.3) {
    multiplier -= 0.15;
  }
  
  // Stress level influence (lower stress = higher multiplier)
  if (data.stressLevel !== undefined) {
    multiplier += (1 - data.stressLevel) * 0.2;
  }
  
  // Focus score influence
  if (data.focusScore > 0.8) {
    multiplier += 0.3;
  }
  
  return Math.max(0.5, Math.min(2.0, multiplier));
}

async function storeHealthData(userId: string, encryptedData: string, privacySettings: any): Promise<{ id: string }> {
  // Store in database with privacy controls
  const dataId = `health_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
  
  // In production, store in secure health database
  if (!global.healthData) global.healthData = new Map();
  global.healthData.set(dataId, {
    userId,
    encryptedData,
    privacySettings,
    createdAt: Date.now(),
    expiresAt: Date.now() + (privacySettings.dataRetentionDays * 24 * 60 * 60 * 1000)
  });
  
  return { id: dataId };
}

async function validateAIAnalysisConsent(userId: string): Promise<boolean> {
  // Check AI analysis consent
  return true; // Simplified for demo
}

async function generateHealthInsights(userId: string, request: any): Promise<any> {
  // Generate AI-powered health insights
  const insights = {
    confidence: 0.85,
    patterns: [
      {
        type: 'energy_cycles',
        description: 'Peak energy observed in morning hours (8-10 AM)',
        impact: 'high',
        recommendation: 'Schedule important tasks during peak energy periods'
      },
      {
        type: 'stress_triggers',
        description: 'Elevated stress levels during afternoon (2-4 PM)',
        impact: 'medium',
        recommendation: 'Consider stress-reduction techniques during this period'
      }
    ],
    recommendations: [
      'Maintain consistent sleep schedule for optimal energy levels',
      'Consider brief meditation sessions to reduce stress',
      'Physical activity can boost both energy and nUmentum multipliers'
    ],
    numentumOptimization: {
      currentAverage: 1.15,
      potentialIncrease: 0.25,
      strategies: ['Improve stress management', 'Optimize sleep quality']
    }
  };
  
  return insights;
}

async function exportUserHealthData(userId: string, format: string): Promise<any> {
  // Export user's health data in requested format
  return {
    dataTypes: ['heartRate', 'energyLevel', 'stressLevel', 'focusScore'],
    records: [], // Would contain actual user data
    anonymizationLevel: 'high',
    retentionPolicy: '365 days'
  };
}

async function deleteUserHealthData(userId: string, dataTypes: string[]): Promise<any> {
  // Delete specified health data types
  return {
    deletedCount: 150,
    deletedTypes: dataTypes
  };
}

async function getHealthPrivacySettings(userId: string): Promise<any> {
  return {
    allowResearch: false,
    allowCommercial: false,
    dataRetentionDays: 90,
    anonymizeData: true,
    aiAnalysisConsent: false,
    thirdPartySharing: false
  };
}

async function updateHealthPrivacySettings(userId: string, settings: any): Promise<any> {
  // Update privacy settings
  return settings;
}

async function getUserHealthDataAccess(userId: string): Promise<string> {
  return 'full';
}

async function getRealHealthTrends(userId: string, days: number, accessLevel: string): Promise<any[]> {
  // Return empty array until real biometric data is stored
  return [];
}