import type { Request, Response, NextFunction } from "express";

// Simple authentication middleware for development
export function isAuthenticated(req: any, res: Response, next: NextFunction) {
  // Development mode - create mock user
  req.user = {
    claims: {
      sub: 'dev-user-123'
    }
  };
  next();
}

// Extended Request type with user
export interface AuthenticatedRequest extends Request {
  user?: {
    claims: {
      sub: string;
    };
  };
}