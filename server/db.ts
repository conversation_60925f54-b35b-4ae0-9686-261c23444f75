import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as schema from "@shared/schema";

let pool: postgres.Sql | null = null;
let db: ReturnType<typeof drizzle> | null = null;

function initializeConnection() {
  if (!process.env.DATABASE_URL) {
    throw new Error("DATABASE_URL environment variable is required");
  }

  try {
    pool = postgres(process.env.DATABASE_URL, {
      max: 10,
      idle_timeout: 20,
      connect_timeout: 10,
    });
    
    db = drizzle(pool, { schema });
    console.log("[Database] ✅ Real PostgreSQL connection initialized");
    return db;
  } catch (error) {
    console.error("[Database] ❌ Failed to connect to PostgreSQL:", error);
    throw error;
  }
}

// Initialize connection immediately
if (!db) {
  initializeConnection();
}

export { pool, db };

export function getDb() {
  if (!db) {
    initializeConnection();
  }
  return db!;
}