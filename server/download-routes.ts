import express from 'express';
import archiver from 'archiver';
import { createReadStream, statSync } from 'fs';
import { join } from 'path';

const router = express.Router();

router.get('/complete-project', async (req, res) => {
  try {
    const projectRoot = process.cwd();
    
    // Set headers for file download
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', 'attachment; filename="nu-universe-complete.zip"');
    
    // Create zip archive
    const archive = archiver('zip', {
      zlib: { level: 9 } // Maximum compression
    });

    archive.on('error', (err) => {
      console.error('Archive error:', err);
      res.status(500).send('Error creating archive');
    });

    // Pipe archive to response
    archive.pipe(res);

    // Add all files except node_modules and other excluded directories
    const excludePaths = [
      'node_modules',
      '.git',
      'dist',
      '.cache',
      '.replit_cache',
      '.upm',
      '.config'
    ];

    // Add files with exclusions
    archive.glob('**/*', {
      cwd: projectRoot,
      ignore: excludePaths.map(path => `${path}/**`),
      dot: true
    });

    // Finalize the archive
    await archive.finalize();

  } catch (error) {
    console.error('Download error:', error);
    res.status(500).json({ error: 'Failed to create download' });
  }
});

// Direct file download for extension
router.get('/extension', (req, res) => {
  try {
    const extensionPath = join(process.cwd(), 'browser-extension', 'nu-universe-quantum-extension.zip');
    
    // Check if file exists
    try {
      statSync(extensionPath);
    } catch {
      return res.status(404).json({ error: 'Extension file not found' });
    }

    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', 'attachment; filename="nu-universe-quantum-extension.zip"');
    
    const fileStream = createReadStream(extensionPath);
    fileStream.pipe(res);

  } catch (error) {
    console.error('Extension download error:', error);
    res.status(500).json({ error: 'Failed to download extension' });
  }
});

export default router;