import { Router } from 'express';
import { z } from 'zod';
import { validateRequest } from '../lib/request-validation';

const router = Router();

// Mobile device metrics schema
const mobileMetricsSchema = z.object({
  amount: z.number().positive(),
  source: z.literal('mobile_device'),
  deviceMetrics: z.object({
    touchInteractions: z.number().nonnegative(),
    deviceMotion: z.object({
      acceleration: z.object({
        x: z.number(),
        y: z.number(),
        z: z.number()
      }),
      rotationRate: z.object({
        alpha: z.number(),
        beta: z.number(),
        gamma: z.number()
      })
    }),
    batteryLevel: z.number().min(0).max(1),
    isCharging: z.boolean(),
    networkConnection: z.object({
      effectiveType: z.string(),
      downlink: z.number(),
      rtt: z.number()
    })
  }),
  timestamp: z.number()
});

// Mobile UMatter deposit with enhanced device metrics
router.post('/deposit-mobile-umatter', validateRequest(mobileMetricsSchema), async (req, res) => {
  try {
    const { amount, deviceMetrics, timestamp } = req.body;
    
    // Calculate authentic mobile energy bonus
    const motionMagnitude = Math.sqrt(
      deviceMetrics.deviceMotion.acceleration.x ** 2 +
      deviceMetrics.deviceMotion.acceleration.y ** 2 +
      deviceMetrics.deviceMotion.acceleration.z ** 2
    );
    
    const touchBonus = Math.min(deviceMetrics.touchInteractions * 0.01, 1.0);
    const motionBonus = Math.min(motionMagnitude * 0.1, 1.5);
    const chargingBonus = deviceMetrics.isCharging ? 1.25 : 1.0;
    const networkBonus = deviceMetrics.networkConnection.downlink > 5 ? 1.1 : 1.0;
    
    const finalAmount = amount * touchBonus * motionBonus * chargingBonus * networkBonus;
    
    // Store in energy banking system
    const storage = req.app.get('storage');
    const userId = req.session?.userId || 'anonymous';
    
    // Get current balance
    const currentBalance = await storage.getUserEnergyBalance(userId);
    const newBalance = currentBalance + finalAmount;
    
    // Update balance
    await storage.updateUserEnergyBalance(userId, newBalance);
    
    // Log mobile energy transaction
    await storage.logEnergyTransaction(userId, {
      type: 'mobile_generation',
      amount: finalAmount,
      source: 'mobile_device',
      details: {
        rawAmount: amount,
        touchBonus,
        motionBonus,
        chargingBonus,
        networkBonus,
        deviceMetrics: {
          touchInteractions: deviceMetrics.touchInteractions,
          motionMagnitude,
          batteryLevel: deviceMetrics.batteryLevel,
          isCharging: deviceMetrics.isCharging,
          networkSpeed: deviceMetrics.networkConnection.downlink
        }
      },
      timestamp
    });
    
    console.log(`[MobileSync] ${finalAmount.toFixed(6)} UMatter generated from mobile device`);
    
    res.json({
      success: true,
      amount: finalAmount,
      newBalance,
      bonuses: {
        touch: touchBonus,
        motion: motionBonus,
        charging: chargingBonus,
        network: networkBonus
      },
      timestamp: Date.now()
    });
    
  } catch (error) {
    console.error('[MobileSync] Deposit failed:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Mobile sync failed' 
    });
  }
});

// Get mobile device capabilities
router.get('/capabilities', (req, res) => {
  // Check what mobile APIs are available in the user's browser
  const capabilities = {
    battery: 'getBattery' in navigator,
    deviceMotion: 'DeviceMotionEvent' in globalThis,
    deviceOrientation: 'DeviceOrientationEvent' in globalThis,
    geolocation: 'geolocation' in navigator,
    vibration: 'vibrate' in navigator,
    share: 'share' in navigator,
    bluetooth: 'bluetooth' in navigator,
    nfc: 'nfc' in navigator,
    wakeLock: 'wakeLock' in navigator
  };
  
  res.json({
    success: true,
    capabilities,
    timestamp: Date.now()
  });
});

// Mobile device status endpoint
router.get('/status', async (req, res) => {
  try {
    const storage = req.app.get('storage');
    const userId = req.session?.userId || 'anonymous';
    
    const energyBalance = await storage.getUserEnergyBalance(userId);
    const recentTransactions = await storage.getRecentEnergyTransactions(userId, 10);
    
    // Filter mobile-specific transactions
    const mobileTransactions = recentTransactions.filter(tx => 
      tx.source === 'mobile_device' || tx.type === 'mobile_generation'
    );
    
    res.json({
      success: true,
      energyBalance,
      mobileTransactions,
      totalMobileGenerated: mobileTransactions.reduce((sum, tx) => sum + tx.amount, 0),
      lastSync: mobileTransactions[0]?.timestamp || 0,
      timestamp: Date.now()
    });
    
  } catch (error) {
    console.error('[MobileSync] Status check failed:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Status check failed' 
    });
  }
});

export default router;