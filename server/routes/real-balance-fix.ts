import { Router } from 'express';
import { db } from '../db';
import { energyBalancesDetailed } from '../../shared/schema';

const router = Router();

// Real balance synchronization endpoint
router.post('/sync-real-balance', async (req, res) => {
  try {
    if (!db) {
      return res.status(500).json({
        success: false,
        error: 'Database not initialized'
      });
    }

    // Sync balance with real transaction data from database
    const balanceData = await db.select().from(energyBalancesDetailed).limit(1);
    
    if (balanceData.length > 0) {
      const currentBalance = balanceData[0];
      res.json({
        success: true,
        balance: {
          umatter: currentBalance.umatterBalance || 0,
          tru: currentBalance.truBalance || 0,
          nuva: currentBalance.nuvaBalance || 0,
          inurtia: currentBalance.inurtiaBalance || 0,
          ubits: currentBalance.ubitsBalance || 0,
          totalGenerated: currentBalance.totalEnergyGenerated || 0
        },
        synchronized: true,
        timestamp: new Date().toISOString()
      });
    } else {
      res.json({
        success: true,
        balance: {
          umatter: 0,
          tru: 0,
          nuva: 0,
          inurtia: 0,
          ubits: 0,
          totalGenerated: 0
        },
        synchronized: true,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('[Real Balance Fix] Error syncing balance:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to sync real balance'
    });
  }
});

export { router as realBalanceFixRoutes };