
import crypto from 'crypto';
import { storage } from './storage-interface';

export interface MemvidChunk {
  id: string;
  sessionId: string;
  chunkData: any;
  textContent: string;
  embeddings: number[];
  keywords: string[];
  timestamp: number;
  encrypted: boolean;
}

export interface SearchResult {
  chunkId: string;
  score: number;
  content: string;
  metadata: any;
  timestamp: number;
}

export class MemvidProcessor {
  private readonly CHUNK_SIZE = 1024;
  private readonly EMBEDDING_DIM = 384; // Simulated embedding dimension

  /**
   * Process and index Memvid chunk with semantic embeddings
   */
  async processMemvidChunk(
    chunkId: string,
    chunkData: any,
    userId: string
  ): Promise<{
    indexed: boolean;
    searchable: boolean;
    embeddingGenerated: boolean;
    keywords: string[];
  }> {
    try {
      // Extract text content from chunk data
      const textContent = this.extractTextContent(chunkData);
      
      // Generate semantic embeddings
      const embeddings = await this.generateEmbeddings(textContent);
      
      // Extract keywords for search indexing
      const keywords = this.extractKeywords(textContent);
      
      // Create chunk record
      const chunk: MemvidChunk = {
        id: chunkId,
        sessionId: chunkData.sessionId || '',
        chunkData,
        textContent,
        embeddings,
        keywords,
        timestamp: Date.now(),
        encrypted: true
      };

      // Store chunk with encryption
      await this.storeChunk(chunk, userId);
      
      // Update search index
      await this.updateSearchIndex(chunk);

      return {
        indexed: true,
        searchable: true,
        embeddingGenerated: embeddings.length > 0,
        keywords
      };

    } catch (error) {
      console.error('[Memvid] Failed to process chunk:', error);
      return {
        indexed: false,
        searchable: false,
        embeddingGenerated: false,
        keywords: []
      };
    }
  }

  /**
   * Semantic search across Memvid chunks
   */
  async semanticSearch(
    query: string,
    userId: string,
    options: {
      topK?: number;
      threshold?: number;
      sessionId?: string;
    } = {}
  ): Promise<SearchResult[]> {
    try {
      // Generate query embeddings
      const queryEmbeddings = await this.generateEmbeddings(query);
      
      // Get user's chunks
      const userChunks = await this.getUserChunks(userId, options.sessionId);
      
      // Calculate semantic similarity
      const scoredResults = userChunks.map(chunk => ({
        chunkId: chunk.id,
        score: this.calculateCosineSimilarity(queryEmbeddings, chunk.embeddings),
        content: chunk.textContent,
        metadata: chunk.chunkData,
        timestamp: chunk.timestamp
      }));

      // Filter by threshold and sort by score
      const threshold = options.threshold || 0.1;
      const filteredResults = scoredResults
        .filter(result => result.score >= threshold)
        .sort((a, b) => b.score - a.score)
        .slice(0, options.topK || 10);

      return filteredResults;

    } catch (error) {
      console.error('[Memvid] Semantic search failed:', error);
      return [];
    }
  }

  /**
   * Advanced search with keyword and semantic combination
   */
  async hybridSearch(
    query: string,
    userId: string,
    options: {
      topK?: number;
      semanticWeight?: number;
      keywordWeight?: number;
      sessionId?: string;
    } = {}
  ): Promise<SearchResult[]> {
    const semanticWeight = options.semanticWeight || 0.7;
    const keywordWeight = options.keywordWeight || 0.3;

    // Perform semantic search
    const semanticResults = await this.semanticSearch(query, userId, {
      topK: options.topK ? options.topK * 2 : 20,
      sessionId: options.sessionId
    });

    // Perform keyword search
    const keywordResults = await this.keywordSearch(query, userId, options.sessionId);

    // Combine and weight results
    const combinedResults = new Map<string, SearchResult>();

    // Add semantic results with weight
    semanticResults.forEach(result => {
      combinedResults.set(result.chunkId, {
        ...result,
        score: result.score * semanticWeight
      });
    });

    // Add keyword results with weight
    keywordResults.forEach(result => {
      const existing = combinedResults.get(result.chunkId);
      if (existing) {
        existing.score += result.score * keywordWeight;
      } else {
        combinedResults.set(result.chunkId, {
          ...result,
          score: result.score * keywordWeight
        });
      }
    });

    // Sort by combined score and return top results
    return Array.from(combinedResults.values())
      .sort((a, b) => b.score - a.score)
      .slice(0, options.topK || 10);
  }

  /**
   * Extract patterns and insights from Memvid data
   */
  async analyzeInteractionPatterns(userId: string): Promise<{
    totalSessions: number;
    commonPatterns: Array<{
      pattern: string;
      frequency: number;
      lastSeen: number;
    }>;
    timePatterns: Array<{
      hour: number;
      activityLevel: number;
    }>;
    interactionTypes: Record<string, number>;
  }> {
    try {
      const userChunks = await this.getUserChunks(userId);
      
      // Extract interaction types
      const interactionTypes: Record<string, number> = {};
      const timePatterns: Array<{ hour: number; activityLevel: number }> = [];
      const patterns: Map<string, { frequency: number; lastSeen: number }> = new Map();

      userChunks.forEach(chunk => {
        const chunkData = chunk.chunkData;
        
        // Count interaction types
        if (chunkData.interactions) {
          chunkData.interactions.forEach((interaction: any) => {
            const type = interaction.type || 'unknown';
            interactionTypes[type] = (interactionTypes[type] || 0) + 1;
          });
        }

        // Analyze time patterns
        const hour = new Date(chunk.timestamp).getHours();
        const existingHour = timePatterns.find(tp => tp.hour === hour);
        if (existingHour) {
          existingHour.activityLevel++;
        } else {
          timePatterns.push({ hour, activityLevel: 1 });
        }

        // Extract patterns from keywords
        chunk.keywords.forEach(keyword => {
          const existing = patterns.get(keyword);
          patterns.set(keyword, {
            frequency: (existing?.frequency || 0) + 1,
            lastSeen: Math.max(existing?.lastSeen || 0, chunk.timestamp)
          });
        });
      });

      const commonPatterns = Array.from(patterns.entries())
        .map(([pattern, data]) => ({ pattern, ...data }))
        .sort((a, b) => b.frequency - a.frequency)
        .slice(0, 10);

      return {
        totalSessions: new Set(userChunks.map(c => c.sessionId)).size,
        commonPatterns,
        timePatterns: timePatterns.sort((a, b) => a.hour - b.hour),
        interactionTypes
      };

    } catch (error) {
      console.error('[Memvid] Pattern analysis failed:', error);
      return {
        totalSessions: 0,
        commonPatterns: [],
        timePatterns: [],
        interactionTypes: {}
      };
    }
  }

  /**
   * Extract text content from chunk data
   */
  private extractTextContent(chunkData: any): string {
    if (typeof chunkData === 'string') return chunkData;
    
    if (chunkData.interactions) {
      return chunkData.interactions
        .map((interaction: any) => `${interaction.type}: ${interaction.element || ''} ${JSON.stringify(interaction.metadata || {})}`)
        .join(' ');
    }
    
    return JSON.stringify(chunkData);
  }

  /**
   * Generate semantic embeddings (simulated with TF-IDF-like approach)
   */
  private async generateEmbeddings(text: string): Promise<number[]> {
    // Simulate semantic embeddings using text features
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2);

    const embeddings = new Array(this.EMBEDDING_DIM).fill(0);
    
    // Simple hash-based feature generation
    words.forEach((word, index) => {
      const hash = this.simpleHash(word);
      const positions = [
        hash % this.EMBEDDING_DIM,
        (hash * 31) % this.EMBEDDING_DIM,
        (hash * 37) % this.EMBEDDING_DIM
      ];
      
      positions.forEach(pos => {
        embeddings[pos] += 1 / (index + 1); // Position-weighted
      });
    });

    // Normalize embeddings
    const magnitude = Math.sqrt(embeddings.reduce((sum, val) => sum + val * val, 0));
    return magnitude > 0 ? embeddings.map(val => val / magnitude) : embeddings;
  }

  /**
   * Extract keywords from text
   */
  private extractKeywords(text: string): string[] {
    const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
    
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2 && !stopWords.has(word));

    // Count word frequencies
    const wordCounts = new Map<string, number>();
    words.forEach(word => {
      wordCounts.set(word, (wordCounts.get(word) || 0) + 1);
    });

    // Return top keywords
    return Array.from(wordCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word);
  }

  /**
   * Calculate cosine similarity between embeddings
   */
  private calculateCosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) return 0;
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    const magnitude = Math.sqrt(normA) * Math.sqrt(normB);
    return magnitude > 0 ? dotProduct / magnitude : 0;
  }

  /**
   * Keyword-based search
   */
  private async keywordSearch(query: string, userId: string, sessionId?: string): Promise<SearchResult[]> {
    const queryKeywords = this.extractKeywords(query);
    const userChunks = await this.getUserChunks(userId, sessionId);
    
    const results = userChunks.map(chunk => {
      const matchCount = queryKeywords.filter(keyword => 
        chunk.keywords.includes(keyword) || chunk.textContent.toLowerCase().includes(keyword)
      ).length;
      
      const score = matchCount / Math.max(queryKeywords.length, 1);
      
      return {
        chunkId: chunk.id,
        score,
        content: chunk.textContent,
        metadata: chunk.chunkData,
        timestamp: chunk.timestamp
      };
    }).filter(result => result.score > 0);

    return results.sort((a, b) => b.score - a.score);
  }

  /**
   * Store encrypted chunk
   */
  private async storeChunk(chunk: MemvidChunk, userId: string): Promise<void> {
    // Encrypt chunk data
    const encryptedData = this.encryptChunk(chunk);
    
    // Store in database
    await storage.storeMemvidChunk({
      userId,
      chunkId: chunk.id,
      chunkData: encryptedData,
      encrypted: true
    });
  }

  /**
   * Encrypt chunk data
   */
  private encryptChunk(chunk: MemvidChunk): string {
    const key = process.env.MEMVID_ENCRYPTION_KEY || 'default-key';
    const data = JSON.stringify(chunk);
    
    return crypto.createHash('sha256')
      .update(key + data)
      .digest('hex');
  }

  /**
   * Get user chunks from storage
   */
  private async getUserChunks(userId: string, sessionId?: string): Promise<MemvidChunk[]> {
    try {
      const chunks = await storage.getMemvidChunks(userId);
      
      return chunks
        .filter(chunk => !sessionId || chunk.sessionId === sessionId)
        .map(chunk => ({
          id: chunk.chunkId || '',
          sessionId: chunk.sessionId || '',
          chunkData: chunk.chunkData,
          textContent: this.extractTextContent(chunk.chunkData),
          embeddings: chunk.embeddings || [],
          keywords: chunk.keywords || [],
          timestamp: chunk.timestamp || Date.now(),
          encrypted: chunk.encrypted || false
        }));
    } catch (error) {
      console.error('[Memvid] Failed to get user chunks:', error);
      return [];
    }
  }

  /**
   * Update search index
   */
  private async updateSearchIndex(chunk: MemvidChunk): Promise<void> {
    // Store in global search index
    if (!global.memvidSearchIndex) {
      global.memvidSearchIndex = new Map();
    }
    
    global.memvidSearchIndex.set(chunk.id, {
      keywords: chunk.keywords,
      embeddings: chunk.embeddings,
      timestamp: chunk.timestamp
    });
  }

  /**
   * Simple hash function
   */
  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }
}

export const memvidProcessor = new MemvidProcessor();
