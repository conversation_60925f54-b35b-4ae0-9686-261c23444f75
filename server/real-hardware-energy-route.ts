import { Router } from 'express';
import { RealDataConnector } from './real-data-connector';
import crypto from 'crypto';

const router = Router();

/**
 * DIRECT REAL HARDWARE ENERGY GENERATION
 * Connects MacBook hardware APIs to UMatter without simulation
 */
router.get('/energy/real-hardware-metrics', async (req, res) => {
  try {
    const realDataConnector = RealDataConnector.getInstance();
    
    // GET ACTUAL NODE.JS PROCESS METRICS
    const realMetrics = await realDataConnector.getRealSystemMetrics();
    
    // CALCULATE UMATTER FROM REAL HARDWARE ONLY
    const authenticEnergy = await realDataConnector.calculateRealEnergyGeneration();
    
    console.log(`[RealHardwareAPI] AUTHENTIC: ${authenticEnergy.toFixed(6)} UMatter from real Node.js APIs`);
    
    res.json({
      success: true,
      authenticEnergy: authenticEnergy,
      source: 'real_nodejs_hardware',
      authentic: true,
      metrics: realMetrics
    });
    
  } catch (error) {
    console.error('[RealHardwareAPI] Failed:', error);
    res.status(500).json({ error: 'Failed to connect to real hardware' });
  }
});

router.post('/energy/real-hardware-direct', async (req, res) => {
  try {
    const realDataConnector = RealDataConnector.getInstance();
    
    // GET ACTUAL NODE.JS PROCESS METRICS
    const realMetrics = await realDataConnector.getRealSystemMetrics();
    
    // CALCULATE UMATTER FROM REAL HARDWARE ONLY
    const realUMatter = (realMetrics.powerConsumption * 0.1) + 
                       (realMetrics.cpuUsage * 0.05) + 
                       (realMetrics.memoryUsage * 0.01);
    
    console.log(`[RealHardwareRoute] AUTHENTIC: ${realUMatter.toFixed(6)} UMatter from real Node.js APIs`);
    
    // STORE DIRECTLY TO DATABASE - NO STORAGE LAYER, DIRECT INSERT
    const { db } = await import('./db');
    const schema = await import('../shared/schema');
    
    // Skip database storage for now - just return the authentic energy
    // await db.insert(schema.energyTransactions).values({...});
    console.log(`[RealHardwareRoute] ✅ GENERATED (not stored): ${realUMatter.toFixed(6)} UMatter from real Node.js hardware`);
    

    
    res.json({
      success: true,
      authenticEnergy: realUMatter,
      source: 'real_nodejs_hardware',
      authentic: true,
      metrics: realMetrics
    });
    
  } catch (error) {
    console.error('[RealHardwareRoute] Failed:', error);
    res.status(500).json({ error: 'Failed to connect to real hardware' });
  }
});

export { router as realHardwareEnergyRouter };