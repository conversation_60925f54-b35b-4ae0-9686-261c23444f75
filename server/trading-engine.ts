
/**
 * Real-Time Trading Engine for nU Universe Energy Tokens
 * Handles live trading of UMatter, TRU, NUVA, InUrtia, and Ubits
 */

import { EventEmitter } from 'events';
import { db } from './db';
import { storage } from './storage';
import crypto from 'crypto';

interface TradingOrder {
  id: string;
  userId: string;
  orderType: 'buy' | 'sell';
  tokenType: 'umatter' | 'tru' | 'nuva' | 'inurtia' | 'ubits';
  amount: number;
  price: number;
  status: 'pending' | 'filled' | 'cancelled' | 'partial';
  createdAt: Date;
  filledAt?: Date;
  filledAmount?: number;
}

interface MarketData {
  tokenType: string;
  currentPrice: number;
  volume24h: number;
  priceChange24h: number;
  lastUpdate: Date;
  bids: TradingOrder[];
  asks: TradingOrder[];
}

class RealTimeTradingEngine extends EventEmitter {
  private orderBook: Map<string, MarketData> = new Map();
  private activeOrders: Map<string, TradingOrder> = new Map();
  private priceHistory: Map<string, number[]> = new Map();
  
  private readonly TRADING_PAIRS = ['umatter', 'tru', 'nuva', 'inurtia', 'ubits'];
  
  constructor() {
    super();
    this.initializeMarkets();
    this.startRealTimePriceUpdates();
    this.startOrderMatching();
  }

  /**
   * Initialize trading markets for all energy tokens
   */
  private initializeMarkets(): void {
    this.TRADING_PAIRS.forEach(tokenType => {
      this.orderBook.set(tokenType, {
        tokenType,
        currentPrice: this.getInitialPrice(tokenType),
        volume24h: 0,
        priceChange24h: 0,
        lastUpdate: new Date(),
        bids: [],
        asks: []
      });
      
      this.priceHistory.set(tokenType, []);
    });
    
    console.log('[Trading] Real-time trading engine initialized for all energy tokens');
  }

  /**
   * Get initial price for token type
   */
  private getInitialPrice(tokenType: string): number {
    const basePrices = {
      umatter: 1.0,    // Base unit
      tru: 0.85,       // 15% discount to UMatter
      nuva: 1.25,      // 25% premium
      inurtia: 2.10,   // Compound interest premium
      ubits: 0.001     // Micro-unit pricing
    };
    
    return basePrices[tokenType] || 1.0;
  }

  /**
   * Start real-time price updates based on market activity
   */
  private startRealTimePriceUpdates(): void {
    setInterval(() => {
      this.updateMarketPrices();
      this.emit('priceUpdate', this.getAllMarketData());
    }, 2000); // Update every 2 seconds
  }

  /**
   * Start order matching engine
   */
  private startOrderMatching(): void {
    setInterval(() => {
      this.TRADING_PAIRS.forEach(tokenType => {
        this.matchOrders(tokenType);
      });
    }, 1000); // Match orders every second
  }

  /**
   * Place a trading order
   */
  async placeOrder(userId: string, orderData: Omit<TradingOrder, 'id' | 'createdAt' | 'status'>): Promise<TradingOrder> {
    // Verify user has sufficient balance
    const hasBalance = await this.verifyUserBalance(userId, orderData);
    if (!hasBalance) {
      throw new Error('Insufficient balance for trading order');
    }

    const order: TradingOrder = {
      ...orderData,
      id: crypto.randomUUID(),
      userId,
      status: 'pending',
      createdAt: new Date()
    };

    // Add to order book
    const market = this.orderBook.get(orderData.tokenType);
    if (market) {
      if (order.orderType === 'buy') {
        market.bids.push(order);
        market.bids.sort((a, b) => b.price - a.price); // Highest bid first
      } else {
        market.asks.push(order);
        market.asks.sort((a, b) => a.price - b.price); // Lowest ask first
      }
    }

    this.activeOrders.set(order.id, order);
    
    // Try immediate matching
    await this.matchOrders(orderData.tokenType);
    
    this.emit('orderPlaced', order);
    return order;
  }

  /**
   * Cancel a trading order
   */
  async cancelOrder(userId: string, orderId: string): Promise<boolean> {
    const order = this.activeOrders.get(orderId);
    if (!order || order.userId !== userId) {
      return false;
    }

    order.status = 'cancelled';
    this.removeFromOrderBook(order);
    this.activeOrders.delete(orderId);
    
    this.emit('orderCancelled', order);
    return true;
  }

  /**
   * Match buy and sell orders
   */
  private async matchOrders(tokenType: string): Promise<void> {
    const market = this.orderBook.get(tokenType);
    if (!market || market.bids.length === 0 || market.asks.length === 0) {
      return;
    }

    const topBid = market.bids[0];
    const topAsk = market.asks[0];

    // Check if orders can be matched
    if (topBid.price >= topAsk.price) {
      await this.executeTrade(topBid, topAsk, tokenType);
    }
  }

  /**
   * Execute a trade between two orders
   */
  private async executeTrade(buyOrder: TradingOrder, sellOrder: TradingOrder, tokenType: string): Promise<void> {
    const tradeAmount = Math.min(buyOrder.amount, sellOrder.amount);
    const tradePrice = sellOrder.price; // Use seller's price
    
    try {
      // Execute the trade atomically
      await this.processTradeTransaction(buyOrder, sellOrder, tradeAmount, tradePrice, tokenType);
      
      // Update order statuses
      buyOrder.amount -= tradeAmount;
      sellOrder.amount -= tradeAmount;
      
      if (buyOrder.amount === 0) {
        buyOrder.status = 'filled';
        buyOrder.filledAt = new Date();
        this.activeOrders.delete(buyOrder.id);
      }
      
      if (sellOrder.amount === 0) {
        sellOrder.status = 'filled';
        sellOrder.filledAt = new Date();
        this.activeOrders.delete(sellOrder.id);
      }
      
      // Remove completed orders from order book
      if (buyOrder.status === 'filled') {
        this.removeFromOrderBook(buyOrder);
      }
      if (sellOrder.status === 'filled') {
        this.removeFromOrderBook(sellOrder);
      }
      
      // Update market data
      this.updateMarketData(tokenType, tradePrice, tradeAmount);
      
      this.emit('tradeExecuted', {
        tokenType,
        buyOrder,
        sellOrder,
        tradeAmount,
        tradePrice,
        timestamp: new Date()
      });
      
      console.log(`[Trading] Trade executed: ${tradeAmount} ${tokenType} at ${tradePrice}`);
      
    } catch (error) {
      console.error('[Trading] Trade execution failed:', error);
    }
  }

  /**
   * Process the actual trade transaction
   */
  private async processTradeTransaction(
    buyOrder: TradingOrder,
    sellOrder: TradingOrder,
    amount: number,
    price: number,
    tokenType: string
  ): Promise<void> {
    // Transfer tokens from seller to buyer
    await storage.createEnergyTransaction({
      userId: sellOrder.userId,
      transactionType: 'transfer_out',
      tokenType,
      amount: -amount,
      balanceBefore: 0, // Will be calculated
      balanceAfter: 0,  // Will be calculated
      source: 'trading_engine',
      metadata: { 
        tradeId: crypto.randomUUID(),
        counterparty: buyOrder.userId,
        price,
        orderType: 'sell'
      }
    });

    await storage.createEnergyTransaction({
      userId: buyOrder.userId,
      transactionType: 'transfer_in',
      tokenType,
      amount,
      balanceBefore: 0, // Will be calculated
      balanceAfter: 0,  // Will be calculated
      source: 'trading_engine',
      metadata: { 
        tradeId: crypto.randomUUID(),
        counterparty: sellOrder.userId,
        price,
        orderType: 'buy'
      }
    });
  }

  /**
   * Verify user has sufficient balance for order
   */
  private async verifyUserBalance(userId: string, orderData: any): Promise<boolean> {
    const balance = await storage.getEnergyBalance(userId);
    if (!balance) return false;

    if (orderData.orderType === 'sell') {
      const userBalance = balance[`${orderData.tokenType}Balance`] || 0;
      return userBalance >= orderData.amount;
    } else {
      // For buy orders, check if user has enough TRU (base currency)
      return (balance.truBalance || 0) >= (orderData.amount * orderData.price);
    }
  }

  /**
   * Update market data after trade
   */
  private updateMarketData(tokenType: string, price: number, volume: number): void {
    const market = this.orderBook.get(tokenType);
    if (market) {
      const oldPrice = market.currentPrice;
      market.currentPrice = price;
      market.volume24h += volume;
      market.priceChange24h = ((price - oldPrice) / oldPrice) * 100;
      market.lastUpdate = new Date();
      
      // Update price history
      const history = this.priceHistory.get(tokenType) || [];
      history.push(price);
      if (history.length > 1000) { // Keep last 1000 prices
        history.shift();
      }
      this.priceHistory.set(tokenType, history);
    }
  }

  /**
   * Remove order from order book
   */
  private removeFromOrderBook(order: TradingOrder): void {
    const market = this.orderBook.get(order.tokenType);
    if (market) {
      if (order.orderType === 'buy') {
        market.bids = market.bids.filter(bid => bid.id !== order.id);
      } else {
        market.asks = market.asks.filter(ask => ask.id !== order.id);
      }
    }
  }

  /**
   * Update market prices based on REAL market data and supply/demand
   */
  private async updateMarketPrices(): Promise<void> {
    for (const tokenType of this.TRADING_PAIRS) {
      const market = this.orderBook.get(tokenType);
      if (market) {
        try {
          // Get AUTHENTIC market data from real crypto APIs
          const realMarketData = await this.fetchRealMarketData(tokenType);
          
          if (realMarketData) {
            // Use real market price as base
            market.currentPrice = realMarketData.price;
          } else {
            // Only if real data unavailable, use order book dynamics
            const bidPressure = market.bids.length;
            const askPressure = market.asks.length;
            const totalVolume = market.bids.reduce((sum, bid) => sum + bid.amount, 0) +
                              market.asks.reduce((sum, ask) => sum + ask.amount, 0);
            
            if (bidPressure > askPressure && totalVolume > 0) {
              const priceAdjustment = (bidPressure - askPressure) / (bidPressure + askPressure) * 0.001;
              market.currentPrice *= (1 + priceAdjustment);
            }
          }
          
          market.lastUpdate = new Date();
        } catch (error) {
          console.error(`[Trading] Failed to update ${tokenType} price:`, error);
        }
      }
    }
  }

  /**
   * Fetch real market data from external APIs
   */
  private async fetchRealMarketData(tokenType: string): Promise<any> {
    try {
      // Map nU tokens to real crypto equivalents for price reference
      const tokenMapping = {
        'umatter': 'ethereum', // Use ETH as UMatter base
        'tru': 'bitcoin',      // Use BTC as TRU base
        'nuva': 'solana',      // Use SOL as NUVA base
        'inurtia': 'chainlink', // Use LINK as InUrtia base
        'ubits': 'litecoin'    // Use LTC as Ubits base
      };
      
      const realToken = tokenMapping[tokenType];
      if (!realToken) return null;
      
      const response = await fetch(
        `https://api.coingecko.com/api/v3/simple/price?ids=${realToken}&vs_currencies=usd`,
        { 
          headers: { 'Accept': 'application/json' },
          timeout: 5000 
        }
      );
      
      if (response.ok) {
        const data = await response.json();
        return {
          price: data[realToken]?.usd || null,
          timestamp: Date.now()
        };
      }
    } catch (error) {
      console.error(`[Trading] Real market data fetch failed for ${tokenType}:`, error);
    }
    
    return null;
  }

  /**
   * Get all market data
   */
  getAllMarketData(): MarketData[] {
    return Array.from(this.orderBook.values());
  }

  /**
   * Get market data for specific token
   */
  getMarketData(tokenType: string): MarketData | undefined {
    return this.orderBook.get(tokenType);
  }

  /**
   * Get user's active orders
   */
  getUserOrders(userId: string): TradingOrder[] {
    return Array.from(this.activeOrders.values())
      .filter(order => order.userId === userId);
  }

  /**
   * Get price history for token
   */
  getPriceHistory(tokenType: string): number[] {
    return this.priceHistory.get(tokenType) || [];
  }
}

export const tradingEngine = new RealTimeTradingEngine();
