import { Router } from "express";
import { z } from "zod";
import { storage } from "./storage";

const router = Router();

// Get user earnings summary
router.get("/api/user-earnings", async (req, res) => {
  try {
    const userId = req.session?.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    // Mock earnings data for demonstration
    const earnings = {
      totalEarnings: 245.67,
      monthlyEarnings: 52.34,
      weeklyEarnings: 12.45,
      pendingPayments: 15.20,
      subscriptionEarnings: 180.50,
      adViewEarnings: 65.17
    };

    res.json(earnings);
  } catch (error) {
    console.error("Error fetching user earnings:", error);
    res.status(500).json({ message: "Failed to fetch earnings" });
  }
});

// Get user data plans
router.get("/api/user-data-plans", async (req, res) => {
  try {
    const userId = req.session?.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    // Mock data plans for demonstration
    const plans = [
      {
        id: "plan-1",
        planName: "Premium Insights",
        description: "Comprehensive browsing and shopping data",
        weeklyPrice: 5.00,
        monthlyPrice: 15.00,
        yearlyPrice: 150.00,
        dataCategories: ["browsing", "shopping", "social"],
        privacyLevel: "selective",
        maxSubscribers: 50,
        currentSubscribers: 12,
        isActive: true
      },
      {
        id: "plan-2", 
        planName: "Basic Package",
        description: "General interest and demographic data",
        weeklyPrice: 2.00,
        monthlyPrice: 6.00,
        yearlyPrice: 60.00,
        dataCategories: ["demographics", "interests"],
        privacyLevel: "minimal",
        maxSubscribers: 100,
        currentSubscribers: 45,
        isActive: true
      }
    ];

    res.json(plans);
  } catch (error) {
    console.error("Error fetching data plans:", error);
    res.status(500).json({ message: "Failed to fetch data plans" });
  }
});

// Create new data plan
router.post("/api/user-data-plans", async (req, res) => {
  try {
    const userId = req.session?.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    const planSchema = z.object({
      planName: z.string(),
      description: z.string().optional(),
      weeklyPrice: z.number(),
      monthlyPrice: z.number(),
      yearlyPrice: z.number(),
      dataCategories: z.array(z.string()),
      privacyLevel: z.string(),
      maxSubscribers: z.number()
    });

    const planData = planSchema.parse(req.body);
    
    // For now, return success with the created plan
    const newPlan = {
      id: `plan-${Date.now()}`,
      ...planData,
      currentSubscribers: 0,
      isActive: true
    };

    res.json(newPlan);
  } catch (error) {
    console.error("Error creating data plan:", error);
    res.status(500).json({ message: "Failed to create data plan" });
  }
});

// Get user ad preferences
router.get("/api/user-ad-preferences", async (req, res) => {
  try {
    const userId = req.session?.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    // Mock ad preferences
    const preferences = {
      id: "pref-1",
      preferredCategories: ["Technology", "Fashion", "Travel"],
      blockedCategories: ["Politics", "Finance"],
      minPayPerView: 0.05,
      maxAdsPerDay: 15,
      preferredAdTypes: ["video", "interactive"],
      allowLocationAds: false,
      allowPersonalizedAds: true,
      isActive: true
    };

    res.json(preferences);
  } catch (error) {
    console.error("Error fetching ad preferences:", error);
    res.status(500).json({ message: "Failed to fetch ad preferences" });
  }
});

// Update ad preferences
router.put("/api/user-ad-preferences", async (req, res) => {
  try {
    const userId = req.session?.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    const preferencesSchema = z.object({
      preferredCategories: z.array(z.string()),
      minPayPerView: z.number(),
      maxAdsPerDay: z.number(),
      allowPersonalizedAds: z.boolean(),
      allowLocationAds: z.boolean(),
      isActive: z.boolean()
    });

    const preferences = preferencesSchema.parse(req.body);
    
    // Return updated preferences
    res.json({
      id: "pref-1",
      ...preferences
    });
  } catch (error) {
    console.error("Error updating ad preferences:", error);
    res.status(500).json({ message: "Failed to update preferences" });
  }
});

// Get available ads for user
router.get("/api/available-ads", async (req, res) => {
  try {
    const userId = req.session?.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    // Mock available ads based on user preferences
    const ads = [
      {
        id: "ad-1",
        company: "TechCorp Innovation",
        title: "Revolutionary AI Assistant Launch",
        payPerView: 0.25,
        payPerClick: 0.75,
        category: "Technology",
        duration: "45s",
        type: "video",
        description: "Experience the future of AI with our groundbreaking assistant"
      },
      {
        id: "ad-2",
        company: "EcoFashion Brand",
        title: "Sustainable Summer Collection 2024",
        payPerView: 0.18,
        payPerClick: 0.45,
        category: "Fashion",
        duration: "30s",
        type: "interactive",
        description: "Discover eco-friendly fashion that doesn't compromise on style"
      },
      {
        id: "ad-3",
        company: "Adventure Travel Co",
        title: "Hidden Gems of Southeast Asia",
        payPerView: 0.20,
        payPerClick: 0.60,
        category: "Travel",
        duration: "60s",
        type: "video",
        description: "Explore breathtaking destinations off the beaten path"
      },
      {
        id: "ad-4",
        company: "Local Artisan Coffee",
        title: "Grand Opening Special Offer",
        payPerView: 0.12,
        payPerClick: 0.35,
        category: "Food & Dining",
        duration: "20s",
        type: "image",
        description: "Premium coffee roasted daily - 20% off opening week"
      }
    ];

    res.json(ads);
  } catch (error) {
    console.error("Error fetching available ads:", error);
    res.status(500).json({ message: "Failed to fetch available ads" });
  }
});

// Record ad view and process payment
router.post("/api/ads/:adId/view", async (req, res) => {
  try {
    const userId = req.session?.user?.id;
    if (!userId) {
      return res.status(401).json({ message: "Unauthorized" });
    }

    const { adId } = req.params;
    const { viewDuration, wasClicked } = req.body;

    // Calculate earnings based on view
    let earnedAmount = 0.15; // Base pay per view
    if (wasClicked) {
      earnedAmount += 0.45; // Additional for click
    }

    // Mock recording the view
    const adView = {
      id: `view-${Date.now()}`,
      adId,
      userId,
      viewDuration,
      wasClicked,
      earnedAmount,
      viewedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      earnedAmount,
      message: `You earned $${earnedAmount.toFixed(2)} for viewing this ad!`
    });
  } catch (error) {
    console.error("Error recording ad view:", error);
    res.status(500).json({ message: "Failed to record ad view" });
  }
});

export default router;