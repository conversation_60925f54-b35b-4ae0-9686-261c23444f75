import { EventEmitter } from 'events';
import { db } from './db';
import { eq, and } from 'drizzle-orm';
import { marketplaceListings, marketplaceBids, userMarketplaceStats } from '../shared/marketplace-schema';
import { nqeTasks, nqeResults } from '../shared/schema';

interface TaskBid {
  userId: string;
  ubitAmount: number;
  batteryPercent: number;
  reputation: number;
}

interface ContractEvent {
  taskSubmitted: { taskId: string; type: string; input: any; ubitCost: number; nuvaReward: number };
  bidPlaced: { taskId: string; userId: string; ubitAmount: number };
  taskExecuted: { taskId: string; bids: TaskBid[] };
  taskCompleted: { taskId: string; result: any; rewards: Array<{ userId: string; nuvaAmount: number }> };
  consensusReached: { taskId: string; validatorCount: number };
}

/**
 * QuantumMarketContract - Decentralized smart contract for quantum task marketplace
 * Manages task lifecycle, bidding, execution, and reward distribution using P2P consensus
 */
export class QuantumMarketContract extends EventEmitter {
  private activeBids: Map<string, TaskBid[]> = new Map();
  private executingTasks: Set<string> = new Set();
  private contractConfig = {
    MIN_CONSENSUS_VALIDATORS: 5,
    EXECUTION_THRESHOLD: 0.5, // 50% of required Ubits to start execution
    REWARD_DISTRIBUTION_RATE: 0.1, // 10% of NUVA reward as TrU
    REPUTATION_WEIGHT: 0.2, // 20% influence on bid selection
    PREMIUM_BOOST: 1.25 // 25% efficiency boost for premium users
  };

  constructor() {
    super();
    this.initializeContract();
  }

  private async initializeContract() {
    console.log('[SmartContract] Quantum Market Contract initialized');
    console.log('[SmartContract] P2P consensus enabled with', this.contractConfig.MIN_CONSENSUS_VALIDATORS, 'validators');
  }

  /**
   * Submit a quantum task to the marketplace with NUVA reward
   */
  async submitTask(
    taskId: string, 
    userId: string, 
    type: 'factor' | 'search' | 'qaoa' | 'hhl', 
    input: any, 
    ubitCost: number, 
    nuvaReward: number
  ): Promise<void> {
    try {
      // Validate user has sufficient NUVA for reward
      const userStats = await this.getUserStats(userId);
      const availableNuva = parseFloat(userStats.totalNuvaEarned) - parseFloat(userStats.totalNuvaSpent);
      
      if (availableNuva < nuvaReward) {
        throw new Error('Insufficient NUVA balance for reward');
      }

      // Lock NUVA reward in escrow (simulated)
      await this.lockNuvaEscrow(userId, nuvaReward);

      // Initialize bid tracking
      this.activeBids.set(taskId, []);

      // Emit contract event for P2P network
      this.emit('taskSubmitted', { taskId, type, input, ubitCost, nuvaReward });

      console.log(`[SmartContract] Task ${taskId} submitted by ${userId} with ${nuvaReward} NUVA reward`);

    } catch (error) {
      console.error('[SmartContract] Error submitting task:', error);
      throw error;
    }
  }

  /**
   * Place a bid on a quantum task
   */
  async placeBid(taskId: string, userId: string, ubitAmount: number, batteryPercent: number): Promise<void> {
    try {
      // Get user reputation
      const userStats = await this.getUserStats(userId);
      const reputation = userStats.reputation;

      // Validate bid
      if (ubitAmount <= 0 || batteryPercent <= 0) {
        throw new Error('Invalid bid parameters');
      }

      // Reserve user's Ubits (simulated)
      await this.reserveUbits(userId, ubitAmount);

      // Add bid to active bids
      const bids = this.activeBids.get(taskId) || [];
      bids.push({ userId, ubitAmount, batteryPercent, reputation });
      this.activeBids.set(taskId, bids);

      // Emit contract event
      this.emit('bidPlaced', { taskId, userId, ubitAmount });

      // Check if task should be executed
      if (await this.shouldExecuteTask(taskId)) {
        await this.executeTask(taskId);
      }

      console.log(`[SmartContract] Bid placed: ${userId} -> ${ubitAmount} Ubits on task ${taskId}`);

    } catch (error) {
      console.error('[SmartContract] Error placing bid:', error);
      throw error;
    }
  }

  /**
   * Execute a quantum task using distributed computation
   */
  async executeTask(taskId: string): Promise<void> {
    try {
      if (this.executingTasks.has(taskId)) {
        console.log(`[SmartContract] Task ${taskId} already executing`);
        return;
      }

      this.executingTasks.add(taskId);

      // Get task details
      const task = await db.select()
        .from(marketplaceListings)
        .where(eq(marketplaceListings.taskId, taskId))
        .limit(1);

      if (!task.length) {
        throw new Error('Task not found');
      }

      const bids = this.activeBids.get(taskId) || [];
      
      // Select optimal bidders using consensus algorithm
      const selectedBidders = this.selectOptimalBidders(bids, task[0].ubitCost);

      // Update task status to running
      await db.update(marketplaceListings)
        .set({ status: 'running' })
        .where(eq(marketplaceListings.taskId, taskId));

      await db.update(nqeTasks)
        .set({ status: 'running' })
        .where(eq(nqeTasks.taskId, taskId));

      // Emit execution event for P2P network
      this.emit('taskExecuted', { taskId, bids: selectedBidders });

      // Shard task across selected devices
      await this.shardTaskExecution(taskId, task[0], selectedBidders);

      console.log(`[SmartContract] Task ${taskId} executing with ${selectedBidders.length} bidders`);

    } catch (error) {
      console.error('[SmartContract] Error executing task:', error);
      this.executingTasks.delete(taskId);
      throw error;
    }
  }

  /**
   * Complete a task and distribute rewards
   */
  async completeTask(taskId: string, result: any): Promise<void> {
    try {
      const bids = this.activeBids.get(taskId) || [];
      
      // Get task listing for reward info
      const listing = await db.select()
        .from(marketplaceListings)
        .where(eq(marketplaceListings.taskId, taskId))
        .limit(1);

      if (!listing.length) {
        throw new Error('Task listing not found');
      }

      const nuvaReward = parseFloat(listing[0].nuvaReward);
      const totalUbits = bids.reduce((sum, bid) => sum + bid.ubitAmount, 0);

      // Calculate rewards for each contributor
      const rewards = bids.map(bid => {
        const contributionRatio = bid.ubitAmount / totalUbits;
        const baseReward = nuvaReward * contributionRatio * this.contractConfig.REWARD_DISTRIBUTION_RATE;
        
        // Apply reputation bonus
        const reputationBonus = (bid.reputation - 100) / 1000; // -0.1 to +0.9 multiplier
        const finalReward = baseReward * (1 + reputationBonus);

        return { userId: bid.userId, nuvaAmount: finalReward };
      });

      // Distribute rewards
      for (const reward of rewards) {
        await this.distributeTrUReward(reward.userId, reward.nuvaAmount);
        await this.updateUserReputation(reward.userId, 5); // +5 reputation for successful completion
      }

      // Store result
      await db.insert(nqeResults).values({
        taskId,
        output: result,
        energyCost: nuvaReward / 100, // Convert to UMatter equivalent
        completedAt: new Date()
      });

      // Update task status
      await db.update(marketplaceListings)
        .set({ 
          status: 'complete',
          completedAt: new Date()
        })
        .where(eq(marketplaceListings.taskId, taskId));

      await db.update(nqeTasks)
        .set({ status: 'complete' })
        .where(eq(nqeTasks.taskId, taskId));

      // Release escrow and clean up
      await this.releaseNuvaEscrow(listing[0].submitterId, nuvaReward);
      this.activeBids.delete(taskId);
      this.executingTasks.delete(taskId);

      // Emit completion event
      this.emit('taskCompleted', { taskId, result, rewards });

      console.log(`[SmartContract] Task ${taskId} completed, ${rewards.length} contributors rewarded`);

    } catch (error) {
      console.error('[SmartContract] Error completing task:', error);
      throw error;
    }
  }

  /**
   * Validate task completion using P2P consensus
   */
  async validateTaskConsensus(taskId: string, result: any, validatorNodes: string[]): Promise<boolean> {
    try {
      // Simulate P2P consensus validation
      const validatorCount = validatorNodes.length;
      
      if (validatorCount < this.contractConfig.MIN_CONSENSUS_VALIDATORS) {
        throw new Error('Insufficient validators for consensus');
      }

      // Simulate consensus algorithm (simplified)
      const consensusReached = validatorCount >= this.contractConfig.MIN_CONSENSUS_VALIDATORS;

      if (consensusReached) {
        this.emit('consensusReached', { taskId, validatorCount });
        await this.completeTask(taskId, result);
      }

      return consensusReached;

    } catch (error) {
      console.error('[SmartContract] Consensus validation failed:', error);
      return false;
    }
  }

  // Private helper methods
  private async shouldExecuteTask(taskId: string): Promise<boolean> {
    const bids = this.activeBids.get(taskId) || [];
    const totalUbits = bids.reduce((sum, bid) => sum + bid.ubitAmount, 0);

    const task = await db.select()
      .from(marketplaceListings)
      .where(eq(marketplaceListings.taskId, taskId))
      .limit(1);

    if (!task.length) return false;

    // Execute if we have enough Ubits committed (threshold-based)
    const requiredUbits = task[0].ubitCost * this.contractConfig.EXECUTION_THRESHOLD;
    return totalUbits >= requiredUbits;
  }

  private selectOptimalBidders(bids: TaskBid[], requiredUbits: number): TaskBid[] {
    // Sort bids by efficiency score (Ubits + reputation weight)
    const scoredBids = bids.map(bid => ({
      ...bid,
      score: bid.ubitAmount + (bid.reputation * this.contractConfig.REPUTATION_WEIGHT)
    })).sort((a, b) => b.score - a.score);

    // Select top bidders until we have enough Ubits
    const selected: TaskBid[] = [];
    let totalUbits = 0;

    for (const bid of scoredBids) {
      selected.push(bid);
      totalUbits += bid.ubitAmount;
      
      if (totalUbits >= requiredUbits) break;
    }

    return selected;
  }

  private async shardTaskExecution(taskId: string, task: any, bidders: TaskBid[]): Promise<void> {
    // Distribute quantum computation chunks across bidders
    const chunkSize = Math.ceil(1000000 / bidders.length); // Distribute 1M operations
    
    for (let i = 0; i < bidders.length; i++) {
      const bidder = bidders[i];
      const chunk = {
        chunkId: i,
        operations: chunkSize,
        ubitAllocation: bidder.ubitAmount,
        batteryPercent: bidder.batteryPercent
      };

      // In real implementation, this would send chunks to nquf-client.js via P2P
      console.log(`[SmartContract] Assigned chunk ${i} to ${bidder.userId}: ${chunk.operations} ops`);
    }
  }

  private async getUserStats(userId: string) {
    const stats = await db.select()
      .from(userMarketplaceStats)
      .where(eq(userMarketplaceStats.userId, userId))
      .limit(1);

    if (!stats.length) {
      // Create default stats
      const defaultStats = {
        userId,
        totalUbitsEarned: 0,
        totalNuvaEarned: '0.00000000',
        totalNuvaSpent: '0.00000000',
        tasksSubmitted: 0,
        tasksCompleted: 0,
        bidsMade: 0,
        bidsAccepted: 0,
        reputation: 100,
        biometricBoost: false,
        premiumMember: false
      };

      await db.insert(userMarketplaceStats).values(defaultStats);
      return defaultStats;
    }

    return stats[0];
  }

  private async lockNuvaEscrow(userId: string, amount: number): Promise<void> {
    console.log(`[SmartContract] Locking ${amount} NUVA in escrow for user ${userId}`);
    // Integration with nUmentum escrow system would go here
  }

  private async releaseNuvaEscrow(userId: string, amount: number): Promise<void> {
    console.log(`[SmartContract] Releasing ${amount} NUVA from escrow for user ${userId}`);
    // Integration with nUmentum escrow system would go here
  }

  private async reserveUbits(userId: string, amount: number): Promise<void> {
    console.log(`[SmartContract] Reserving ${amount} Ubits for user ${userId}`);
    // Integration with nUmentum Ubit reservation would go here
  }

  private async distributeTrUReward(userId: string, nuvaAmount: number): Promise<void> {
    const trUAmount = nuvaAmount * this.contractConfig.REWARD_DISTRIBUTION_RATE;
    console.log(`[SmartContract] Distributing ${trUAmount} TrU to user ${userId}`);
    
    // Update user stats
    await db.update(userMarketplaceStats)
      .set({ 
        totalNuvaEarned: (parseFloat((await this.getUserStats(userId)).totalNuvaEarned) + nuvaAmount).toString(),
        totalUbitsEarned: (await this.getUserStats(userId)).totalUbitsEarned + Math.floor(nuvaAmount * 100000) // Convert to Ubits
      })
      .where(eq(userMarketplaceStats.userId, userId));
  }

  private async updateUserReputation(userId: string, change: number): Promise<void> {
    const current = await this.getUserStats(userId);
    const newReputation = Math.max(0, Math.min(1000, current.reputation + change));
    
    await db.update(userMarketplaceStats)
      .set({ 
        reputation: newReputation,
        updatedAt: new Date()
      })
      .where(eq(userMarketplaceStats.userId, userId));
  }

  // Public getters for marketplace stats
  getActiveBids(taskId: string): TaskBid[] {
    return this.activeBids.get(taskId) || [];
  }

  getExecutingTasks(): string[] {
    return Array.from(this.executingTasks);
  }

  getContractConfig() {
    return { ...this.contractConfig };
  }
}

export const quantumMarketContract = new QuantumMarketContract();