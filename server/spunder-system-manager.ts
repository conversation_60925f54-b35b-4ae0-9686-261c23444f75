/**
 * AUTHENTIC SPUNDER BACKEND - Real Data Only
 * Manages authentic SpUnder operations with real browser data
 */

import { Router } from 'express';

const router = Router();

interface AuthenticSpUnderData {
  realAdInterceptions: number;
  realEnergyHarvested: number;
  realBrowserActions: number;
  realNetworkRequests: number;
  realDOMModifications: number;
  authenticOperations: Array<{
    type: string;
    energy: number;
    timestamp: number;
    url: string;
  }>;
}

let globalSpUnderData: AuthenticSpUnderData = {
  realAdInterceptions: 0,
  realEnergyHarvested: 0,
  realBrowserActions: 0,
  realNetworkRequests: 0,
  realDOMModifications: 0,
  authenticOperations: []
};

/**
 * Get authentic SpUnder status - real data only
 */
router.get('/status', (req, res) => {
  try {
    // Return only real SpUnder data
    const authenticStatus = {
      active: true,
      authenticData: globalSpUnderData,
      systemHealth: {
        adInterceptionActive: globalSpUnderData.realAdInterceptions > 0,
        energyHarvestingActive: globalSpUnderData.realEnergyHarvested > 0,
        browserMonitoringActive: globalSpUnderData.realBrowserActions > 0
      },
      realTimeMetrics: {
        totalEnergyHarvested: globalSpUnderData.realEnergyHarvested,
        totalOperations: globalSpUnderData.authenticOperations.length,
        averageEnergyPerOperation: globalSpUnderData.authenticOperations.length > 0 
          ? globalSpUnderData.realEnergyHarvested / globalSpUnderData.authenticOperations.length 
          : 0
      }
    };

    console.log(`[AuthenticSpUnder] Status requested - ${globalSpUnderData.authenticOperations.length} real operations`);
    res.json(authenticStatus);
  } catch (error) {
    console.error('[AuthenticSpUnder] Status error:', error);
    res.status(500).json({ error: 'Failed to get authentic SpUnder status' });
  }
});

/**
 * Process authentic SpUnder energy - real browser data only
 */
router.post('/process-energy', async (req, res) => {
  try {
    const { energy, source, timestamp, authentic } = req.body;

    // Only process if marked as authentic
    if (!authentic) {
      return res.status(400).json({ error: 'Only authentic energy data accepted' });
    }

    // Validate real energy data
    if (!energy || energy <= 0) {
      return res.status(400).json({ error: 'Invalid energy amount' });
    }

    // Update authentic SpUnder data
    globalSpUnderData.realEnergyHarvested += energy;

    switch (source) {
      case 'ad_intercept':
        globalSpUnderData.realAdInterceptions++;
        break;
      case 'network_activity':
        globalSpUnderData.realNetworkRequests++;
        break;
      case 'user_interactions':
        globalSpUnderData.realBrowserActions++;
        break;
      case 'dom_modifications':
        globalSpUnderData.realDOMModifications++;
        break;
    }

    // Record authentic operation
    globalSpUnderData.authenticOperations.push({
      type: source,
      energy,
      timestamp,
      url: req.get('referer') || 'unknown'
    });

    // Keep only recent operations (last 1000)
    if (globalSpUnderData.authenticOperations.length > 1000) {
      globalSpUnderData.authenticOperations = globalSpUnderData.authenticOperations.slice(-1000);
    }

    console.log(`[AuthenticSpUnder] Processed ${energy.toFixed(6)} UMatter from ${source}`);

    res.json({
      success: true,
      energyProcessed: energy,
      source,
      totalEnergyHarvested: globalSpUnderData.realEnergyHarvested,
      authentic: true
    });

  } catch (error) {
    console.error('[AuthenticSpUnder] Energy processing error:', error);
    res.status(500).json({ error: 'Failed to process authentic energy' });
  }
});

/**
 * Get authentic operations history
 */
router.get('/operations', (req, res) => {
  try {
    const limit = parseInt(req.query.limit as string) || 100;
    const operations = globalSpUnderData.authenticOperations
      .slice(-limit)
      .map(op => ({
        ...op,
        authentic: true
      }));

    res.json({
      operations,
      totalOperations: globalSpUnderData.authenticOperations.length,
      totalEnergyHarvested: globalSpUnderData.realEnergyHarvested
    });
  } catch (error) {
    console.error('[AuthenticSpUnder] Operations error:', error);
    res.status(500).json({ error: 'Failed to get operations' });
  }
});

/**
 * Reset authentic SpUnder data (for testing)
 */
router.post('/reset', (req, res) => {
  try {
    globalSpUnderData = {
      realAdInterceptions: 0,
      realEnergyHarvested: 0,
      realBrowserActions: 0,
      realNetworkRequests: 0,
      realDOMModifications: 0,
      authenticOperations: []
    };

    console.log('[AuthenticSpUnder] Data reset - authentic system reinitialized');
    res.json({ success: true, message: 'Authentic SpUnder data reset' });
  } catch (error) {
    console.error('[AuthenticSpUnder] Reset error:', error);
    res.status(500).json({ error: 'Failed to reset data' });
  }
});

/**
 * Get real-time authentic metrics
 */
router.get('/metrics', (req, res) => {
  try {
    const now = Date.now();
    const recentOperations = globalSpUnderData.authenticOperations
      .filter(op => now - op.timestamp < 60000); // Last minute

    const metrics = {
      realTime: {
        operationsPerMinute: recentOperations.length,
        energyPerMinute: recentOperations.reduce((sum, op) => sum + op.energy, 0),
        averageEnergyPerOperation: recentOperations.length > 0 
          ? recentOperations.reduce((sum, op) => sum + op.energy, 0) / recentOperations.length 
          : 0
      },
      cumulative: {
        totalOperations: globalSpUnderData.authenticOperations.length,
        totalEnergy: globalSpUnderData.realEnergyHarvested,
        adInterceptions: globalSpUnderData.realAdInterceptions,
        networkRequests: globalSpUnderData.realNetworkRequests,
        browserActions: globalSpUnderData.realBrowserActions
      },
      authentic: true
    };

    res.json(metrics);
  } catch (error) {
    console.error('[AuthenticSpUnder] Metrics error:', error);
    res.status(500).json({ error: 'Failed to get metrics' });
  }
});

export { router as authenticSpUnderRouter };