interface LogLevel {
  ERROR: 0;
  WARN: 1;
  INFO: 2;
  DEBUG: 3;
}

const LOG_LEVELS: LogLevel = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
};

const currentLogLevel = process.env.NODE_ENV === 'production' ? LOG_LEVELS.INFO : LOG_LEVELS.DEBUG;

class Logger {
  private shouldLog(level: number): boolean {
    return level <= currentLogLevel;
  }

  error(message: string, error?: any): void {
    if (this.shouldLog(LOG_LEVELS.ERROR)) {
      console.error(`[ERROR] ${new Date().toISOString()} - ${message}`, error || '');
    }
  }

  warn(message: string, data?: any): void {
    if (this.shouldLog(LOG_LEVELS.WARN)) {
      console.warn(`[WARN] ${new Date().toISOString()} - ${message}`, data || '');
    }
  }

  info(message: string, data?: any): void {
    if (this.shouldLog(LOG_LEVELS.INFO)) {
      console.log(`[INFO] ${new Date().toISOString()} - ${message}`, data || '');
    }
  }

  debug(message: string, data?: any): void {
    if (this.shouldLog(LOG_LEVELS.DEBUG)) {
      console.log(`[DEBUG] ${new Date().toISOString()} - ${message}`, data || '');
    }
  }
}

interface LogEntry {
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  timestamp: Date;
  metadata?: any;
  source?: string;
}

class ProductionLogger {
  private logs: LogEntry[] = [];
  private maxLogs: number = 10000;

  private createLogEntry(level: LogEntry['level'], message: string, metadata?: any, source?: string): LogEntry {
    return {
      level,
      message,
      timestamp: new Date(),
      metadata,
      source
    };
  }

  info(message: string, metadata?: any, source?: string): void {
    const entry = this.createLogEntry('info', message, metadata, source);
    this.logs.push(entry);
    this.trimLogs();
    console.log(`[INFO ${entry.timestamp.toISOString()}] ${source ? `[${source}] ` : ''}${message}`, metadata || '');
    
    // Forward to browser console in development
    if (process.env.NODE_ENV === 'development') {
      this.forwardToBrowser('info', message, metadata, source);
    }
  }

  warn(message: string, metadata?: any, source?: string): void {
    const entry = this.createLogEntry('warn', message, metadata, source);
    this.logs.push(entry);
    this.trimLogs();
    console.warn(`[WARN ${entry.timestamp.toISOString()}] ${source ? `[${source}] ` : ''}${message}`, metadata || '');
    
    if (process.env.NODE_ENV === 'development') {
      this.forwardToBrowser('warn', message, metadata, source);
    }
  }

  error(message: string, metadata?: any, source?: string): void {
    const entry = this.createLogEntry('error', message, metadata, source);
    this.logs.push(entry);
    this.trimLogs();
    console.error(`[ERROR ${entry.timestamp.toISOString()}] ${source ? `[${source}] ` : ''}${message}`, metadata || '');
    
    if (process.env.NODE_ENV === 'development') {
      this.forwardToBrowser('error', message, metadata, source);
    }
  }

  debug(message: string, metadata?: any, source?: string): void {
    if (process.env.NODE_ENV === 'development') {
      const entry = this.createLogEntry('debug', message, metadata, source);
      this.logs.push(entry);
      this.trimLogs();
      console.debug(`[DEBUG ${entry.timestamp.toISOString()}] ${source ? `[${source}] ` : ''}${message}`, metadata || '');
      this.forwardToBrowser('debug', message, metadata, source);
    }
  }

  private trimLogs(): void {
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
  }

  getLogs(level?: LogEntry['level'], limit: number = 100): LogEntry[] {
    let filteredLogs = this.logs;

    if (level) {
      filteredLogs = this.logs.filter(log => log.level === level);
    }

    return filteredLogs.slice(-limit);
  }

  private forwardToBrowser(level: string, message: string, metadata?: any, source?: string): void {
    // Send logs to connected WebSocket clients for real-time viewing
    if (global.wsClients) {
      const logData = {
        type: 'server_log',
        level,
        message,
        metadata,
        source,
        timestamp: new Date().toISOString()
      };
      
      global.wsClients.forEach((client: any) => {
        if (client.readyState === 1) { // WebSocket.OPEN
          client.send(JSON.stringify(logData));
        }
      });
    }
  }

  getSystemHealth(): { errorRate: number; logCount: number; lastError?: LogEntry } {
    const errors = this.logs.filter(log => log.level === 'error');
    const recentLogs = this.logs.filter(log => 
      Date.now() - log.timestamp.getTime() < 3600000 // Last hour
    );

    return {
      errorRate: recentLogs.length > 0 ? errors.length / recentLogs.length : 0,
      logCount: this.logs.length,
      lastError: errors[errors.length - 1]
    };
  }
}

export const logger = new ProductionLogger();

// Global error handling for production
export function setupProductionErrorHandling() {
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught exception:', { error: error.message, stack: error.stack }, 'SYSTEM');
    // Don't exit in production - log and continue
  });

  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled rejection:', { reason, promise }, 'SYSTEM');
  });
}