
import { Request, Response, NextFunction } from 'express';
import { logger } from './logger';

// Advanced rate limiter with sliding window and IP-based tracking
const rateLimitStore = new Map<string, { count: number; resetTime: number; violations: number }>();
const ipBlacklist = new Set<string>();
const suspiciousIPs = new Map<string, { score: number; lastActivity: number }>();

// DDoS protection configuration
const DDOS_CONFIG = {
  maxRequestsPerMinute: 200,
  maxRequestsPerHour: 2000,
  suspiciousThreshold: 100,
  banDuration: 3600000, // 1 hour
  windowSize: 60000, // 1 minute
  
  // Advanced threat detection
  maliciousPatterns: [
    /(?:union|select|insert|delete|drop|exec|script)/i,
    /(?:javascript:|data:|vbscript:)/i,
    /(?:\.\.\/|\.\.\\|\/etc\/|\/proc\/)/i
  ],
  
  // Rate limiting by endpoint
  endpointLimits: {
    '/api/ai': { requests: 20, window: 60000 },
    '/api/biometric': { requests: 1000, window: 60000 },
    '/api/extension': { requests: 500, window: 60000 }
  }
};

// Enhanced security middleware
export function createAdvancedSecurityMiddleware() {
  const requestTracker = new Map();
  const threatDetector = new Map();
  
  return (req: any, res: any, next: any) => {
    const ip = req.ip || req.connection.remoteAddress;
    const userAgent = req.get('User-Agent') || '';
    const path = req.path;
    
    // Check for malicious patterns
    const urlToCheck = req.url + JSON.stringify(req.body);
    for (const pattern of DDOS_CONFIG.maliciousPatterns) {
      if (pattern.test(urlToCheck)) {
        console.warn(`[Security] Malicious pattern detected from ${ip}: ${pattern}`);
        return res.status(403).json({ error: 'Request blocked by security filter' });
      }
    }
    
    // Advanced rate limiting per endpoint
    const endpointLimit = DDOS_CONFIG.endpointLimits[path];
    if (endpointLimit) {
      const key = `${ip}:${path}`;
      const now = Date.now();
      const requests = requestTracker.get(key) || [];
      
      // Remove old requests outside window
      const validRequests = requests.filter((time: number) => now - time < endpointLimit.window);
      
      if (validRequests.length >= endpointLimit.requests) {
        console.warn(`[Security] Rate limit exceeded for ${path} from ${ip}`);
        return res.status(429).json({ error: 'Rate limit exceeded for this endpoint' });
      }
      
      validRequests.push(now);
      requestTracker.set(key, validRequests);
    }
    
    // Threat scoring system
    let threatScore = 0;
    
    // Suspicious user agents
    if (/(?:bot|crawler|spider|scraper)/i.test(userAgent) && !path.startsWith('/api/extension')) {
      threatScore += 20;
    }
    
    // Rapid requests
    const recentRequests = requestTracker.get(ip) || [];
    if (recentRequests.length > 50) {
      threatScore += 30;
    }
    
    // Update threat score
    threatDetector.set(ip, (threatDetector.get(ip) || 0) + threatScore);
    
    // Block high-threat IPs
    if (threatDetector.get(ip) > 100) {
      console.warn(`[Security] High threat score for ${ip}: ${threatDetector.get(ip)}`);
      return res.status(403).json({ error: 'Access denied due to suspicious activity' });
    }
    
    next();
  };
}

export function advancedRateLimit(windowMs: number = 60000, maxRequests: number = 100) {
  return (req: Request, res: Response, next: NextFunction) => {
    const clientIp = getClientIP(req);
    const now = Date.now();
    
    // Check if IP is blacklisted
    if (ipBlacklist.has(clientIp)) {
      logger.warn(`Blocked request from blacklisted IP: ${clientIp}`);
      return res.status(429).json({ 
        error: 'Access denied',
        code: 'IP_BLOCKED'
      });
    }
    
    // Clean up expired entries
    cleanupExpiredEntries(now);
    
    // Get or create client data
    const clientData = rateLimitStore.get(clientIp) || { 
      count: 0, 
      resetTime: now + windowMs,
      violations: 0
    };
    
    // Reset window if expired
    if (now > clientData.resetTime) {
      clientData.count = 0;
      clientData.resetTime = now + windowMs;
    }
    
    // Check rate limit
    if (clientData.count >= maxRequests) {
      clientData.violations++;
      
      // Add to suspicious IPs if too many violations
      if (clientData.violations > 3) {
        addSuspiciousIP(clientIp, now);
      }
      
      logger.warn(`Rate limit exceeded for IP: ${clientIp}, violations: ${clientData.violations}`);
      return res.status(429).json({ 
        error: 'Too many requests',
        retryAfter: Math.ceil((clientData.resetTime - now) / 1000),
        code: 'RATE_LIMIT_EXCEEDED'
      });
    }
    
    // Update request count
    clientData.count++;
    rateLimitStore.set(clientIp, clientData);
    
    // Add security headers
    res.setHeader('X-RateLimit-Limit', maxRequests.toString());
    res.setHeader('X-RateLimit-Remaining', (maxRequests - clientData.count).toString());
    res.setHeader('X-RateLimit-Reset', clientData.resetTime.toString());
    
    next();
  };
}

export function ddosProtection(req: Request, res: Response, next: NextFunction) {
  const clientIp = getClientIP(req);
  const now = Date.now();
  
  // Update suspicious IP scoring
  updateSuspiciousScore(clientIp, req, now);
  
  // Check if IP should be banned
  const suspiciousData = suspiciousIPs.get(clientIp);
  if (suspiciousData && suspiciousData.score > DDOS_CONFIG.suspiciousThreshold) {
    ipBlacklist.add(clientIp);
    logger.error(`IP ${clientIp} added to blacklist due to suspicious activity (score: ${suspiciousData.score})`);
    
    // Auto-remove from blacklist after ban duration
    setTimeout(() => {
      ipBlacklist.delete(clientIp);
      suspiciousIPs.delete(clientIp);
      logger.info(`IP ${clientIp} removed from blacklist`);
    }, DDOS_CONFIG.banDuration);
    
    return res.status(429).json({
      error: 'Suspicious activity detected',
      code: 'DDOS_PROTECTION'
    });
  }
  
  next();
}

export function securityHeaders(req: Request, res: Response, next: NextFunction) {
  // Enhanced security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  res.setHeader('Content-Security-Policy', 
    "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;"
  );
  
  // Remove server fingerprinting
  res.removeHeader('X-Powered-By');
  res.setHeader('Server', 'nU-Universe');
  
  next();
}

export function requestValidator(req: Request, res: Response, next: NextFunction) {
  // Validate request size
  const contentLength = parseInt(req.get('content-length') || '0');
  if (contentLength > 10 * 1024 * 1024) { // 10MB limit
    logger.warn(`Large request detected from ${getClientIP(req)}: ${contentLength} bytes`);
    return res.status(413).json({ 
      error: 'Request too large',
      code: 'PAYLOAD_TOO_LARGE'
    });
  }
  
  // Validate HTTP method
  const allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'];
  if (!allowedMethods.includes(req.method)) {
    logger.warn(`Invalid HTTP method ${req.method} from ${getClientIP(req)}`);
    return res.status(405).json({ 
      error: 'Method not allowed',
      code: 'METHOD_NOT_ALLOWED'
    });
  }
  
  // Validate User-Agent (basic bot detection)
  const userAgent = req.get('User-Agent') || '';
  if (!userAgent || userAgent.length < 10) {
    addSuspiciousIP(getClientIP(req), Date.now(), 5);
  }
  
  next();
}

export function errorHandler(err: any, req: Request, res: Response, next: NextFunction) {
  // Log error with request context
  logger.error('Request error:', {
    error: err.message,
    stack: err.stack,
    ip: getClientIP(req),
    method: req.method,
    url: req.url,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });
  
  // Don't leak error details in production
  if (process.env.NODE_ENV === 'production') {
    res.status(500).json({ 
      error: 'Internal server error',
      code: 'INTERNAL_ERROR',
      requestId: generateRequestId()
    });
  } else {
    res.status(500).json({ 
      error: err.message, 
      stack: err.stack,
      code: 'DEVELOPMENT_ERROR'
    });
  }
}

// Helper functions
function getClientIP(req: Request): string {
  return (
    req.get('x-forwarded-for')?.split(',')[0] ||
    req.get('x-real-ip') ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    'unknown'
  );
}

function cleanupExpiredEntries(now: number): void {
  for (const [ip, data] of rateLimitStore.entries()) {
    if (now > data.resetTime && data.count === 0) {
      rateLimitStore.delete(ip);
    }
  }
  
  for (const [ip, data] of suspiciousIPs.entries()) {
    if (now - data.lastActivity > DDOS_CONFIG.banDuration) {
      suspiciousIPs.delete(ip);
    }
  }
}

function addSuspiciousIP(ip: string, timestamp: number, score: number = 10): void {
  const existing = suspiciousIPs.get(ip) || { score: 0, lastActivity: timestamp };
  existing.score += score;
  existing.lastActivity = timestamp;
  suspiciousIPs.set(ip, existing);
}

function updateSuspiciousScore(ip: string, req: Request, timestamp: number): void {
  let score = 0;
  
  // Check for suspicious patterns
  if (req.url.includes('..') || req.url.includes('<script>')) {
    score += 20; // Path traversal or XSS attempt
  }
  
  if (req.url.length > 500) {
    score += 10; // Unusually long URL
  }
  
  const userAgent = req.get('User-Agent') || '';
  if (userAgent.toLowerCase().includes('bot') && !userAgent.includes('Googlebot')) {
    score += 5; // Non-whitelisted bot
  }
  
  if (score > 0) {
    addSuspiciousIP(ip, timestamp, score);
  }
}

function generateRequestId(): string {
  return Math.random().toString(36).substring(2, 15);
}

// Export the enhanced rate limiter as default
export const rateLimit = advancedRateLimit;
