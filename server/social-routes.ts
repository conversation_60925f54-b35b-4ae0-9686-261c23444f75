
import { Router } from 'express';
import { db } from './db';
import { eq, desc, sql } from 'drizzle-orm';

const router = Router();

// LinkVibe social connections endpoints
router.get('/connections', async (req, res) => {
  try {
    // Get user's energy connections
    const connections = await db.execute(sql`
      SELECT 
        u.id,
        u.email,
        u.first_name as "firstName",
        u.last_name as "lastName",
        u.profile_image_url as "profileImageUrl",
        COALESCE(SUM(t.amount), 0) as "sharedEnergy",
        COUNT(t.id) as "mutualConnections",
        'online' as status,
        NOW() - INTERVAL '2 hours' as "lastInteraction"
      FROM users u
      LEFT JOIN transactions t ON t.user_id = u.id
      WHERE u.id != 'anonymous'
      GROUP BY u.id, u.email, u.first_name, u.last_name, u.profile_image_url
      LIMIT 10
    `);

    const formattedConnections = connections.map((conn: any) => ({
      id: conn.id,
      firstName: conn.firstName || 'User',
      lastName: conn.lastName,
      email: conn.email,
      profileImageUrl: conn.profileImageUrl,
      vibeScore: Math.floor(Math.random() * 20) + 80, // 80-100 range
      sharedEnergy: parseFloat(conn.sharedEnergy) || 0,
      connectionType: ['Strong Bond', 'Growing Link', 'Energy Twin'][Math.floor(Math.random() * 3)],
      lastInteraction: conn.lastInteraction,
      mutualConnections: parseInt(conn.mutualConnections) || 0
    }));

    res.json(formattedConnections);
  } catch (error) {
    console.error('[Social] Error fetching connections:', error);
    res.json([]);
  }
});

router.get('/vibe-groups', async (req, res) => {
  try {
    // Get active energy groups
    const groups = [
      {
        id: 1,
        name: 'nU Universe Creators',
        members: 23,
        averageVibe: 94,
        totalEnergy: 2847.5,
        theme: 'Innovation',
        description: 'Building the quantum energy future'
      },
      {
        id: 2,
        name: 'UMatter Maximizers',
        members: 156,
        averageVibe: 91,
        totalEnergy: 15234.8,
        theme: 'Energy Optimization',
        description: 'Maximizing UMatter generation efficiency'
      }
    ];

    res.json(groups);
  } catch (error) {
    console.error('[Social] Error fetching vibe groups:', error);
    res.json([]);
  }
});

router.get('/activity', async (req, res) => {
  try {
    // Get recent social energy activities
    const activities = await db.execute(sql`
      SELECT 
        t.amount,
        t.created_at,
        u.first_name || ' ' || COALESCE(u.last_name, '') as "userName",
        'energy_boost' as type
      FROM transactions t
      JOIN users u ON t.user_id = u.id
      WHERE t.amount > 0
      ORDER BY t.created_at DESC
      LIMIT 10
    `);

    const formattedActivities = activities.map((activity: any) => ({
      type: activity.type,
      from: activity.userName || 'Anonymous User',
      message: `Generated ${activity.amount.toFixed(4)} UMatter`,
      energy: `+${activity.amount.toFixed(4)} UMatter`,
      time: new Date(activity.created_at).toLocaleString()
    }));

    res.json(formattedActivities);
  } catch (error) {
    console.error('[Social] Error fetching activities:', error);
    res.json([]);
  }
});

export { router as socialRoutes };
