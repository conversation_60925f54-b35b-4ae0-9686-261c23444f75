import sgMail from '@sendgrid/mail';

interface EmailInvitation {
  to: string;
  recipientName: string;
  senderName: string;
  invitationLink: string;
  nuvaBonus: number;
  message?: string;
}

interface ProximityNotification {
  to: string;
  deviceName: string;
  senderDevice: string;
  connectionType: string;
  invitationLink: string;
}

class EmailService {
  private initialized = false;

  constructor() {
    this.initialize();
  }

  private initialize() {
    if (process.env.SENDGRID_API_KEY) {
      sgMail.setApiKey(process.env.SENDGRID_API_KEY);
      this.initialized = true;
      console.log('[EmailService] SendGrid initialized successfully');
    } else {
      console.warn('[EmailService] SENDGRID_API_KEY not found - email delivery disabled');
    }
  }

  async sendSocialInvitation(invitation: EmailInvitation): Promise<boolean> {
    if (!this.initialized) {
      console.log('[EmailService] Email service not initialized - invitation not sent');
      return false;
    }

    const htmlContent = `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%); color: white; border-radius: 12px; overflow: hidden;">
        <div style="background: linear-gradient(90deg, #06b6d4 0%, #3b82f6 100%); padding: 30px; text-align: center;">
          <h1 style="margin: 0; font-size: 32px; font-weight: bold;">nU Universe</h1>
          <p style="margin: 10px 0 0 0; font-size: 18px; opacity: 0.9;">Energy Tracking Revolution</p>
        </div>
        
        <div style="padding: 40px 30px;">
          <h2 style="color: #06b6d4; font-size: 24px; margin-bottom: 20px;">
            🎉 You're Invited to Join nU Universe!
          </h2>
          
          <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            Hi ${invitation.recipientName}!
          </p>
          
          <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            <strong>${invitation.senderName}</strong> has invited you to join nU Universe, 
            a revolutionary platform that converts your device's energy usage into tradeable tokens.
          </p>
          
          ${invitation.message ? `
            <div style="background: rgba(6, 182, 212, 0.1); border-left: 4px solid #06b6d4; padding: 16px; margin: 20px 0; border-radius: 4px;">
              <p style="margin: 0; font-style: italic;">"${invitation.message}"</p>
            </div>
          ` : ''}
          
          <div style="background: rgba(34, 197, 94, 0.1); border: 1px solid #22c55e; border-radius: 8px; padding: 20px; margin: 25px 0; text-align: center;">
            <h3 style="color: #22c55e; margin: 0 0 10px 0; font-size: 20px;">
              🚀 Special Launch Bonus
            </h3>
            <p style="font-size: 18px; font-weight: bold; margin: 0; color: #fbbf24;">
              Get ${invitation.nuvaBonus * 100}% NUVA Tokens Bonus!
            </p>
            <p style="font-size: 14px; margin: 5px 0 0 0; opacity: 0.8;">
              Just for being invited by ${invitation.senderName}
            </p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${invitation.invitationLink}" 
               style="display: inline-block; background: linear-gradient(90deg, #06b6d4 0%, #3b82f6 100%); 
                      color: white; text-decoration: none; padding: 16px 32px; border-radius: 8px; 
                      font-weight: bold; font-size: 16px; box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);">
              Join nU Universe Now
            </a>
          </div>
          
          <div style="border-top: 1px solid #374151; padding-top: 20px; margin-top: 30px;">
            <h4 style="color: #06b6d4; margin-bottom: 15px;">What is nU Universe?</h4>
            <ul style="list-style: none; padding: 0;">
              <li style="margin-bottom: 10px;">⚡ Track real device energy consumption</li>
              <li style="margin-bottom: 10px;">🪙 Convert energy data into UMatter tokens</li>
              <li style="margin-bottom: 10px;">💰 Trade tokens and monetize your data</li>
              <li style="margin-bottom: 10px;">🔒 Complete privacy and data control</li>
              <li style="margin-bottom: 10px;">🌐 Connect with a global energy network</li>
            </ul>
          </div>
        </div>
        
        <div style="background: #0f172a; padding: 20px; text-align: center; font-size: 12px; color: #6b7280;">
          <p style="margin: 0;">This invitation expires in 30 days. Join now to claim your bonus!</p>
          <p style="margin: 5px 0 0 0;">nU Universe - Converting digital energy into real value</p>
        </div>
      </div>
    `;

    const textContent = `
      nU Universe - Energy Tracking Revolution
      
      Hi ${invitation.recipientName}!
      
      ${invitation.senderName} has invited you to join nU Universe, a revolutionary platform that converts your device's energy usage into tradeable tokens.
      
      ${invitation.message ? `Personal message: "${invitation.message}"` : ''}
      
      🚀 SPECIAL BONUS: Get ${invitation.nuvaBonus * 100}% NUVA Tokens just for being invited!
      
      Join now: ${invitation.invitationLink}
      
      What is nU Universe?
      - Track real device energy consumption
      - Convert energy data into UMatter tokens  
      - Trade tokens and monetize your data
      - Complete privacy and data control
      - Connect with a global energy network
      
      This invitation expires in 30 days. Join now to claim your bonus!
    `;

    const msg = {
      to: invitation.to,
      from: '<EMAIL>', // This should be verified with SendGrid
      subject: `🎉 ${invitation.senderName} invited you to nU Universe - Get ${invitation.nuvaBonus * 100}% NUVA Bonus!`,
      text: textContent,
      html: htmlContent,
    };

    try {
      await sgMail.send(msg);
      console.log(`[EmailService] Social invitation sent successfully to ${invitation.to}`);
      return true;
    } catch (error) {
      console.error('[EmailService] Failed to send social invitation:', error);
      return false;
    }
  }

  async sendProximityNotification(notification: ProximityNotification): Promise<boolean> {
    if (!this.initialized) {
      console.log('[EmailService] Email service not initialized - proximity notification not sent');
      return false;
    }

    const htmlContent = `
      <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%); color: white; border-radius: 12px; overflow: hidden;">
        <div style="background: linear-gradient(90deg, #8b5cf6 0%, #06b6d4 100%); padding: 30px; text-align: center;">
          <h1 style="margin: 0; font-size: 32px; font-weight: bold;">nU Universe</h1>
          <p style="margin: 10px 0 0 0; font-size: 18px; opacity: 0.9;">Proximity Invitation</p>
        </div>
        
        <div style="padding: 40px 30px;">
          <h2 style="color: #8b5cf6; font-size: 24px; margin-bottom: 20px;">
            📱 Nearby Device Invitation
          </h2>
          
          <p style="font-size: 16px; line-height: 1.6; margin-bottom: 20px;">
            A nearby device wants to connect with you through nU Universe!
          </p>
          
          <div style="background: rgba(139, 92, 246, 0.1); border: 1px solid #8b5cf6; border-radius: 8px; padding: 20px; margin: 25px 0;">
            <h3 style="color: #8b5cf6; margin: 0 0 15px 0;">Connection Details</h3>
            <p style="margin: 5px 0;"><strong>Device:</strong> ${notification.deviceName}</p>
            <p style="margin: 5px 0;"><strong>Sender:</strong> ${notification.senderDevice}</p>
            <p style="margin: 5px 0;"><strong>Connection:</strong> ${notification.connectionType}</p>
          </div>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${notification.invitationLink}" 
               style="display: inline-block; background: linear-gradient(90deg, #8b5cf6 0%, #06b6d4 100%); 
                      color: white; text-decoration: none; padding: 16px 32px; border-radius: 8px; 
                      font-weight: bold; font-size: 16px; box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);">
              Accept Proximity Invitation
            </a>
          </div>
          
          <div style="border-top: 1px solid #374151; padding-top: 20px; margin-top: 30px;">
            <p style="font-size: 14px; color: #9ca3af; text-align: center;">
              This proximity invitation was sent via ${notification.connectionType} from a nearby device.
              Join nU Universe to start sharing energy data and earning tokens together!
            </p>
          </div>
        </div>
      </div>
    `;

    const msg = {
      to: notification.to,
      from: '<EMAIL>',
      subject: `📱 Proximity Invitation from ${notification.senderDevice}`,
      html: htmlContent,
    };

    try {
      await sgMail.send(msg);
      console.log(`[EmailService] Proximity notification sent successfully to ${notification.to}`);
      return true;
    } catch (error) {
      console.error('[EmailService] Failed to send proximity notification:', error);
      return false;
    }
  }

  isAvailable(): boolean {
    return this.initialized;
  }
}

export const emailService = new EmailService();
export type { EmailInvitation, ProximityNotification };