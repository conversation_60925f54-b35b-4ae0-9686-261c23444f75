
import { Router } from 'express';
import { db } from './db';
import { sql } from 'drizzle-orm';

const router = Router();

router.post('/analyze-energy', async (req, res) => {
  try {
    // Get real device metrics for analysis
    const deviceMetrics = await db.execute(sql`
      SELECT 
        AVG(amount) as avg_energy,
        COUNT(*) as transaction_count,
        MAX(created_at) as last_activity
      FROM transactions 
      WHERE created_at > NOW() - INTERVAL '24 hours'
    `);

    const metrics = deviceMetrics[0] || {};
    const avgEnergy = parseFloat(metrics.avg_energy) || 0;
    const transactionCount = parseInt(metrics.transaction_count) || 0;

    // Calculate efficiency score
    const energyEfficiency = Math.min(100, Math.max(0, (avgEnergy * 1000 + transactionCount * 2)));
    
    // Detect anomalies based on real data patterns
    const anomalies = [];
    if (avgEnergy < 0.001) {
      anomalies.push('Low energy generation detected - check device connections');
    }
    if (transactionCount < 10) {
      anomalies.push('Insufficient transaction volume for optimal analysis');
    }

    // Generate recommendations
    const recommendations = [];
    if (avgEnergy < 0.002) {
      recommendations.push('Increase device activity to boost UMatter generation');
    }
    if (transactionCount > 100) {
      recommendations.push('Excellent transaction volume - maintain current activity levels');
    }
    recommendations.push('Enable background energy harvesting for 24/7 generation');

    const analysis = {
      energyEfficiency: Math.round(energyEfficiency),
      anomalies,
      recommendations,
      realTimeScore: Math.round(energyEfficiency * 0.9),
      lastAnalysis: new Date().toISOString(),
      dataPoints: transactionCount
    };

    res.json(analysis);
  } catch (error) {
    console.error('[AI Analysis] Error:', error);
    res.status(500).json({ error: 'Analysis failed' });
  }
});

export { router as aiAnalysisRoutes };
