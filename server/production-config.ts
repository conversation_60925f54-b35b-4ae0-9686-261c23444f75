
interface ProductionConfig {
  database: {
    maxConnections: number;
    connectionTimeout: number;
    idleTimeout: number;
    queryTimeout: number;
  };
  performance: {
    enableCaching: boolean;
    cacheTimeout: number;
    enableCompression: boolean;
    enableRateLimit: boolean;
  };
  monitoring: {
    enableMetrics: boolean;
    enableHealthChecks: boolean;
    enableErrorTracking: boolean;
  };
}

export const productionConfig: ProductionConfig = {
  database: {
    maxConnections: 50,
    connectionTimeout: 30000,
    idleTimeout: 300000,
    queryTimeout: 30000
  },
  performance: {
    enableCaching: true,
    cacheTimeout: 300000, // 5 minutes
    enableCompression: true,
    enableRateLimit: true
  },
  monitoring: {
    enableMetrics: true,
    enableHealthChecks: true,
    enableErrorTracking: true
  }
};

// Production database connection pool
export function configureProductionDatabase() {
  const config = {
    max: productionConfig.database.maxConnections,
    connectionTimeoutMillis: productionConfig.database.connectionTimeout,
    idleTimeoutMillis: productionConfig.database.idleTimeout,
    statement_timeout: productionConfig.database.queryTimeout,
    query_timeout: productionConfig.database.queryTimeout
  };

  console.log('[Production] Database configured for production scale');
  return config;
}

// Performance monitoring
export function setupProductionMonitoring() {
  if (productionConfig.monitoring.enableHealthChecks) {
    setInterval(() => {
      // Health check implementation
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      console.log('[Production] Health check:', {
        memory: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(1)}MB`,
        cpu: `${((cpuUsage.user + cpuUsage.system) / 1000000).toFixed(2)}s`,
        uptime: `${(process.uptime() / 60).toFixed(1)}min`
      });
    }, 300000); // Every 5 minutes
  }
}
