
import { Router } from 'express';
import { db } from './db';
import { sql } from 'drizzle-orm';

const router = Router();

router.get('/nodes', async (req, res) => {
  try {
    // Get real interaction nodes from database
    const nodes = await db.execute(sql`
      SELECT 
        u.id,
        u.first_name as label,
        'user' as type,
        COALESCE(SUM(t.amount), 0) as strength,
        CASE 
          WHEN MAX(t.created_at) > NOW() - INTERVAL '5 minutes' THEN 'active'
          WHEN MAX(t.created_at) > NOW() - INTERVAL '1 hour' THEN 'processing'
          ELSE 'idle'
        END as status,
        COUNT(t.id) as activity,
        EXTRACT(EPOCH FROM MAX(t.created_at)) * 1000 as timestamp
      FROM users u
      LEFT JOIN transactions t ON t.user_id = u.id
      GROUP BY u.id, u.first_name
      LIMIT 20
    `);

    const formattedNodes = nodes.map((node: any, index: number) => ({
      id: node.id,
      group: Math.floor(index / 5),
      strength: Math.max(1, parseFloat(node.strength) * 100),
      type: node.type,
      activity: parseInt(node.activity) || 0,
      timestamp: parseInt(node.timestamp) || Date.now(),
      label: node.label || `User-${index}`,
      status: node.status
    }));

    res.json(formattedNodes);
  } catch (error) {
    console.error('[Network] Error fetching nodes:', error);
    res.json([]);
  }
});

router.get('/connections', async (req, res) => {
  try {
    // Get real connections between users based on transaction patterns
    const connections = await db.execute(sql`
      SELECT DISTINCT
        t1.user_id as source,
        t2.user_id as target,
        COUNT(*) as value
      FROM transactions t1
      JOIN transactions t2 ON DATE(t1.created_at) = DATE(t2.created_at)
        AND t1.user_id != t2.user_id
      WHERE t1.created_at > NOW() - INTERVAL '24 hours'
      GROUP BY t1.user_id, t2.user_id
      LIMIT 50
    `);

    res.json(connections);
  } catch (error) {
    console.error('[Network] Error fetching connections:', error);
    res.json([]);
  }
});

export { router as networkRoutes };
