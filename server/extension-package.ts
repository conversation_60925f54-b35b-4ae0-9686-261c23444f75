import type { Express } from "express";

export function setupExtensionDownload(app: Express) {
  // Extension download endpoint
  app.get('/api/extension/download', (req, res) => {
    try {
      res.json({
        success: true,
        downloadUrl: '/browser-extension/nu-universe-quantum-extension.zip',
        version: '3.0.0',
        message: 'Extension package ready for download'
      });
    } catch (error) {
      console.error('Extension download error:', error);
      res.status(500).json({ error: 'Failed to prepare extension download' });
    }
  });
}