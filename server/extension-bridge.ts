
import type { Express } from "express";
import { storage } from "./storage-interface";

export function registerExtensionRoutes(app: Express) {
  // Extension bridge routes for browser extension communication
  
  // Extension handshake endpoint
  app.post('/api/extension/handshake', async (req, res) => {
    try {
      const { extensionId, version, capabilities } = req.body;
      
      console.log(`[Extension Bridge] Handshake from extension ${extensionId} v${version}`);
      
      res.json({
        success: true,
        serverVersion: '1.0.0',
        supportedFeatures: ['umatter-tracking', 'biometric-sync', 'energy-calculation'],
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('[Extension Bridge] Handshake error:', error);
      res.status(500).json({ error: 'Handshake failed' });
    }
  });

  // Real-time extension data streaming endpoint
  app.post('/api/extension/stream-data', async (req, res) => {
    try {
      const { sessionId, umatter, rawData, browsing, ads, timestamp } = req.body;
      
      console.log(`[Extension Bridge] Real-time data stream for session ${sessionId}`);
      console.log(`[Extension Bridge] UMatter: ${umatter}, Data size: ${rawData?.length || 0} bytes`);
      
      // Stream UMatter to app in real-time
      if (umatter > 0) {
        await streamUMatterToApp(sessionId, umatter, timestamp);
      }
      
      // Process and package raw data for marketplace
      if (rawData && rawData.length > 0) {
        const marketplacePackage = await processDataForMarketplace(sessionId, rawData, browsing, ads);
        await queueForMarketplace(marketplacePackage);
      }
      
      // Broadcast real-time updates to connected clients
      broadcastToApp({
        type: 'extension_stream',
        sessionId,
        umatter,
        dataSize: rawData?.length || 0,
        marketplaceReady: rawData?.length > 0,
        timestamp: Date.now()
      });
      
      res.json({
        success: true,
        umatterReceived: umatter,
        dataProcessed: rawData?.length || 0,
        marketplaceQueued: rawData?.length > 0,
        nextStreamIn: 5000 // Stream every 5 seconds
      });
    } catch (error) {
      console.error('[Extension Bridge] Stream error:', error);
      res.status(500).json({ error: 'Stream failed' });
    }
  });

  // Extension health check
  app.get('/api/extension/health', (req, res) => {
    res.json({
      status: 'healthy',
      timestamp: Date.now(),
      uptime: process.uptime()
    });
  });

  console.log('[Extension Bridge] Routes registered');
}

// Real-time UMatter streaming to app
async function streamUMatterToApp(sessionId: string, umatter: number, timestamp: number) {
  try {
    // Store UMatter in user's balance
    const userId = 'dev-user-123'; // Get from session
    
    // Update real-time UMatter balance
    if (!global.userUMatterBalance) {
      global.userUMatterBalance = new Map();
    }
    
    const currentBalance = global.userUMatterBalance.get(userId) || 0;
    const newBalance = currentBalance + umatter;
    global.userUMatterBalance.set(userId, newBalance);
    
    console.log(`[UMatter Stream] User ${userId}: +${umatter} UMatter (Total: ${newBalance})`);
    
    // Store in database for persistence
    await storage.recordWebAdInterception({
      userId,
      url: 'extension-stream',
      adType: 'real_time_stream',
      domain: 'browser-extension',
      umatterGenerated: umatter,
      timestamp: new Date(timestamp).toISOString()
    });
    
  } catch (error) {
    console.error('[UMatter Stream] Error:', error);
  }
}

// Process raw data for marketplace listing
async function processDataForMarketplace(sessionId: string, rawData: any[], browsing: any, ads: any) {
  try {
    const userId = 'dev-user-123';
    
    // Create marketplace-ready data package
    const marketplacePackage = {
      id: `pkg-${Date.now()}-${sessionId.slice(-8)}`,
      userId,
      sessionId,
      packageType: 'browsing_data',
      dataSize: rawData.length,
      categories: extractDataCategories(rawData, browsing, ads),
      qualityScore: calculateDataQuality(rawData, browsing),
      estimatedValue: calculateDataValue(rawData, browsing, ads),
      timestamp: Date.now(),
      status: 'ready_for_listing',
      metadata: {
        browsing: {
          sites: browsing?.sites || [],
          duration: browsing?.duration || 0,
          interactions: browsing?.interactions || 0
        },
        ads: {
          blocked: ads?.blocked || 0,
          categories: ads?.categories || [],
          value: ads?.value || 0
        },
        privacy: {
          anonymized: true,
          encrypted: true,
          noPersonalInfo: true
        }
      }
    };
    
    console.log(`[Marketplace Processor] Package created: ${marketplacePackage.id} ($${marketplacePackage.estimatedValue})`);
    
    return marketplacePackage;
    
  } catch (error) {
    console.error('[Marketplace Processor] Error:', error);
    return null;
  }
}

// Queue data package for marketplace
async function queueForMarketplace(packageData: any) {
  if (!packageData) return;
  
  try {
    // Store in marketplace queue
    if (!global.marketplaceQueue) {
      global.marketplaceQueue = [];
    }
    
    global.marketplaceQueue.push(packageData);
    
    // Auto-list if package meets criteria
    if (packageData.qualityScore > 0.7 && packageData.estimatedValue > 0.01) {
      await autoListOnMarketplace(packageData);
    }
    
    console.log(`[Marketplace Queue] Package queued for listing: ${packageData.id}`);
    
  } catch (error) {
    console.error('[Marketplace Queue] Error:', error);
  }
}

// Auto-list high-quality packages on marketplace
async function autoListOnMarketplace(packageData: any) {
  try {
    // Store in database as marketplace listing
    await storage.createDataMarketplaceListing({
      id: packageData.id,
      userId: packageData.userId,
      title: `${packageData.categories.join(', ')} Data Package`,
      description: `Real browsing data collected via nU Extension. ${packageData.dataSize} data points.`,
      category: packageData.categories[0] || 'general',
      dataSize: packageData.dataSize,
      price: packageData.estimatedValue,
      tags: packageData.categories,
      privacyLevel: 5,
      qualityScore: packageData.qualityScore,
      isActive: true
    });
    
    console.log(`[Auto-List] Package auto-listed: ${packageData.id} for $${packageData.estimatedValue}`);
    
  } catch (error) {
    console.error('[Auto-List] Error:', error);
  }
}

// Extract data categories from raw data
function extractDataCategories(rawData: any[], browsing: any, ads: any): string[] {
  const categories = new Set<string>();
  
  // Add categories based on browsing patterns
  if (browsing?.sites) {
    browsing.sites.forEach((site: string) => {
      if (site.includes('shop') || site.includes('buy')) categories.add('shopping');
      if (site.includes('news')) categories.add('news');
      if (site.includes('social')) categories.add('social');
      if (site.includes('video') || site.includes('youtube')) categories.add('entertainment');
    });
  }
  
  // Add categories based on ad data
  if (ads?.categories) {
    ads.categories.forEach((cat: string) => categories.add(cat));
  }
  
  // Default category
  if (categories.size === 0) {
    categories.add('general');
  }
  
  return Array.from(categories);
}

// Calculate data quality score
function calculateDataQuality(rawData: any[], browsing: any): number {
  let score = 0.5; // Base score
  
  // Increase score based on data richness
  if (rawData.length > 100) score += 0.2;
  if (browsing?.duration > 300) score += 0.1; // 5+ minutes
  if (browsing?.interactions > 10) score += 0.1;
  if (browsing?.sites?.length > 5) score += 0.1;
  
  return Math.min(score, 1.0);
}

// Calculate estimated data value
function calculateDataValue(rawData: any[], browsing: any, ads: any): number {
  let baseValue = 0.01; // Base value per package
  
  // Value increases with data quantity and quality
  baseValue += (rawData.length / 1000) * 0.005; // $0.005 per 1000 data points
  baseValue += (browsing?.duration || 0) / 3600 * 0.02; // $0.02 per hour of browsing
  baseValue += (ads?.blocked || 0) * 0.001; // $0.001 per blocked ad
  
  return Math.round(baseValue * 100) / 100; // Round to cents
}

// Broadcast updates to connected app clients
function broadcastToApp(data: any) {
  // This will be handled by WebSocket in websocket.ts
  if (global.websocketServer) {
    global.websocketServer.clients.forEach((client: any) => {
      if (client.readyState === client.OPEN) {
        client.send(JSON.stringify({
          type: 'extension_update',
          ...data
        }));
      }
    });
  }
}
